/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.AddressDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.PaymentTypeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Citizen update request
 */
@JsonPropertyOrder({
  CitizenUpdateRequestDTO.JSON_PROPERTY_ADDRESS,
  CitizenUpdateRequestDTO.JSON_PROPERTY_NATIONALITY_CODE,
  CitizenUpdateRequestDTO.JSON_PROPERTY_PAYMENT_TYPE,
  CitizenUpdateRequestDTO.JSON_PROPERTY_UNION_DUE,
  CitizenUpdateRequestDTO.JSON_PROPERTY_VALUE_DATE,
  CitizenUpdateRequestDTO.JSON_PROPERTY_CORRELATION_ID
})
@JsonTypeName("CitizenUpdateRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenUpdateRequestDTO {
  public static final String JSON_PROPERTY_ADDRESS = "address";
  private AddressDTO address;

  public static final String JSON_PROPERTY_NATIONALITY_CODE = "nationalityCode";
  private Integer nationalityCode;

  public static final String JSON_PROPERTY_PAYMENT_TYPE = "paymentType";
  private PaymentTypeDTO paymentType;

  public static final String JSON_PROPERTY_UNION_DUE = "unionDue";
  private Boolean unionDue;

  public static final String JSON_PROPERTY_VALUE_DATE = "valueDate";
  private LocalDate valueDate;

  public static final String JSON_PROPERTY_CORRELATION_ID = "correlationId";
  private String correlationId;

  public CitizenUpdateRequestDTO() {
  }

  public CitizenUpdateRequestDTO address(AddressDTO address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public AddressDTO getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAddress(AddressDTO address) {
    this.address = address;
  }

  public CitizenUpdateRequestDTO nationalityCode(Integer nationalityCode) {
    
    this.nationalityCode = nationalityCode;
    return this;
  }

  /**
   * CBSS Nationality code (see Lookups)
   * minimum: 100
   * maximum: 999
   * @return nationalityCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNationalityCode() {
    return nationalityCode;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationalityCode(Integer nationalityCode) {
    this.nationalityCode = nationalityCode;
  }

  public CitizenUpdateRequestDTO paymentType(PaymentTypeDTO paymentType) {
    
    this.paymentType = paymentType;
    return this;
  }

  /**
   * Get paymentType
   * @return paymentType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PaymentTypeDTO getPaymentType() {
    return paymentType;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentType(PaymentTypeDTO paymentType) {
    this.paymentType = paymentType;
  }

  public CitizenUpdateRequestDTO unionDue(Boolean unionDue) {
    
    this.unionDue = unionDue;
    return this;
  }

  /**
   * Get unionDue
   * @return unionDue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUnionDue() {
    return unionDue;
  }


  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnionDue(Boolean unionDue) {
    this.unionDue = unionDue;
  }

  public CitizenUpdateRequestDTO valueDate(LocalDate valueDate) {
    
    this.valueDate = valueDate;
    return this;
  }

  /**
   * Get valueDate
   * @return valueDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_VALUE_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getValueDate() {
    return valueDate;
  }


  @JsonProperty(JSON_PROPERTY_VALUE_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setValueDate(LocalDate valueDate) {
    this.valueDate = valueDate;
  }

  public CitizenUpdateRequestDTO correlationId(String correlationId) {
    
    this.correlationId = correlationId;
    return this;
  }

  /**
   * An optional ID of your choice to correlate with.
   * @return correlationId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCorrelationId() {
    return correlationId;
  }


  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenUpdateRequestDTO citizenUpdateRequest = (CitizenUpdateRequestDTO) o;
    return Objects.equals(this.address, citizenUpdateRequest.address) &&
        Objects.equals(this.nationalityCode, citizenUpdateRequest.nationalityCode) &&
        Objects.equals(this.paymentType, citizenUpdateRequest.paymentType) &&
        Objects.equals(this.unionDue, citizenUpdateRequest.unionDue) &&
        Objects.equals(this.valueDate, citizenUpdateRequest.valueDate) &&
        Objects.equals(this.correlationId, citizenUpdateRequest.correlationId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(address, nationalityCode, paymentType, unionDue, valueDate, correlationId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenUpdateRequestDTO {\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    nationalityCode: ").append(toIndentedString(nationalityCode)).append("\n");
    sb.append("    paymentType: ").append(toIndentedString(paymentType)).append("\n");
    sb.append("    unionDue: ").append(toIndentedString(unionDue)).append("\n");
    sb.append("    valueDate: ").append(toIndentedString(valueDate)).append("\n");
    sb.append("    correlationId: ").append(toIndentedString(correlationId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

