/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ReferenceType
 */
@JsonPropertyOrder({
  ReferenceType.JSON_PROPERTY_REFERENCE_TYPE,
  ReferenceType.JSON_PROPERTY_REFERENCE_ORIGIN,
  ReferenceType.JSON_PROPERTY_REFERENCE_NBR
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ReferenceType {
  public static final String JSON_PROPERTY_REFERENCE_TYPE = "referenceType";
  private String referenceType;

  public static final String JSON_PROPERTY_REFERENCE_ORIGIN = "referenceOrigin";
  private String referenceOrigin;

  public static final String JSON_PROPERTY_REFERENCE_NBR = "referenceNbr";
  private String referenceNbr;

  public ReferenceType() {
  }

  public ReferenceType referenceType(String referenceType) {
    
    this.referenceType = referenceType;
    return this;
  }

  /**
   * Get referenceType
   * @return referenceType
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REFERENCE_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getReferenceType() {
    return referenceType;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setReferenceType(String referenceType) {
    this.referenceType = referenceType;
  }

  public ReferenceType referenceOrigin(String referenceOrigin) {
    
    this.referenceOrigin = referenceOrigin;
    return this;
  }

  /**
   * Get referenceOrigin
   * @return referenceOrigin
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REFERENCE_ORIGIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getReferenceOrigin() {
    return referenceOrigin;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE_ORIGIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setReferenceOrigin(String referenceOrigin) {
    this.referenceOrigin = referenceOrigin;
  }

  public ReferenceType referenceNbr(String referenceNbr) {
    
    this.referenceNbr = referenceNbr;
    return this;
  }

  /**
   * Get referenceNbr
   * @return referenceNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REFERENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getReferenceNbr() {
    return referenceNbr;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setReferenceNbr(String referenceNbr) {
    this.referenceNbr = referenceNbr;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReferenceType referenceType = (ReferenceType) o;
    return Objects.equals(this.referenceType, referenceType.referenceType) &&
        Objects.equals(this.referenceOrigin, referenceType.referenceOrigin) &&
        Objects.equals(this.referenceNbr, referenceType.referenceNbr);
  }

  @Override
  public int hashCode() {
    return Objects.hash(referenceType, referenceOrigin, referenceNbr);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReferenceType {\n");
    sb.append("    referenceType: ").append(toIndentedString(referenceType)).append("\n");
    sb.append("    referenceOrigin: ").append(toIndentedString(referenceOrigin)).append("\n");
    sb.append("    referenceNbr: ").append(toIndentedString(referenceNbr)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

