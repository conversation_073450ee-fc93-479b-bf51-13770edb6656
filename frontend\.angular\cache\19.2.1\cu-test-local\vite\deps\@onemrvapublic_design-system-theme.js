import {
  MAT_FORM_FIELD_DEFAULT_OPTIONS
} from "./chunk-T76J6HQT.js";
import "./chunk-UPOW3STX.js";
import "./chunk-IE44L42K.js";
import "./chunk-QNFKXUK7.js";
import {
  NgModule,
  setClassMetadata,
  ɵɵdefineInjector,
  ɵɵdefineNgModule
} from "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system-theme/fesm2022/onemrvapublic-design-system-theme.mjs
var OnemrvaThemeModule = class _OnemrvaThemeModule {
  static {
    this.ɵfac = function OnemrvaThemeModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaThemeModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaThemeModule
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      providers: [{
        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
        useValue: {
          appearance: "outline",
          floatLabel: "always"
        }
      }]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaThemeModule, [{
    type: NgModule,
    args: [{
      providers: [{
        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
        useValue: {
          appearance: "outline",
          floatLabel: "always"
        }
      }],
      imports: []
    }]
  }], null, null);
})();
export {
  OnemrvaThemeModule
};
//# sourceMappingURL=@onemrvapublic_design-system-theme.js.map
