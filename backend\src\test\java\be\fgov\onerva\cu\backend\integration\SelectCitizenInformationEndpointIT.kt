package be.fgov.onerva.cu.backend.integration

import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.CuBaseIntegration
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.cu.backend.lookup.CityDTO
import be.fgov.onerva.cu.backend.lookup.NationalityDTO
import be.fgov.onerva.cu.backend.lookup.PostalCodeLanguageRegimeDTO
import be.fgov.onerva.cu.backend.lookup.StreetDTO
import be.fgov.onerva.cu.backend.security.Roles
import be.fgov.onerva.cu.backend.security.WithMockedJwtToken
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.FieldSource
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.registerproxyservice.rest.model.Period
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterNationality
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPersonNames
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every

@Sql(
    scripts = [
        "classpath:/cleanup.sql",
        "classpath:/data-change-personal-data.sql"
    ]
)
class SelectCitizenInformationEndpointIT() : CuBaseIntegration() {
    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    private val requestId = UUID.fromString("F1DA3F30-10A5-4124-AA5E-2D4E46A09B16")
    private val c9Id = 12346L
    private val ssin = "***********"

    @Test
    @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
    fun `test select source for citizen information`() {
        // Given
        every {
            citizenInfoApi?.searchCitizenInfo(
                listOf("***********"),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns CitizenInfoPageDTO(
            pageNumber = 0,
            pageSize = 10,
            totalElements = 1,
            isFirst = true,
            isLast = true,
            content = listOf(
                CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(151),
                    iban = "****************",
                    address = "Main Street 123 Box A",
                    postalCode = "1000",
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = null,
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = true,
                        validFrom = LocalDate.of(2022, 1, 1),
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 150,
                        zip = "1000",
                        number = "123",
                    )
                )
            )
        )

        every {
            registryApi?.getCitizenInfosBySsin(
                "***********",
                null, true, false, false, null, true, true, false
            )
        } returns RegisterPerson().apply {
            addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod =
                        Period().apply {
                            beginDate = 1490565600000
                            endDate = null
                        }
                    radiated = false
                    cityCode = 21015
                    streetCode = 329
                    postalCode = "1070"
                    houseNumber = "37"
                    boxNumber = "ET01"
                    countryCode = 1
                    regionCode = "9"
                }
            )
            birthdate = LocalDate.of(2000, 1, 1)
            deceaseDate = null
            gender = null
            identitification = 0
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                },
                RegisterPersonNames().apply {
                    firstName = "Deere"
                    seq = 2
                }
            )
            nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            replacedSsin = null
            ssin = "85050599890"
            status = null
        }

        val streetDTO = StreetDTO(
            lookupId = 1,
            code = "1234",
            descNl = "Hoofdstraat",
            descFr = "Rue Principale"
        )

        every { lookupService?.getStreetNameByStreetCode(329, 1070) } returns streetDTO

        every { lookupService?.lookupPostalCodeLanguageRegime(any()) } returns PostalCodeLanguageRegimeDTO(
            languageRegimeCode = "N1",
            beginDate = LocalDate.of(2020, 1, 1),
            endDate = LocalDate.of(2023, 1, 1),
            lookupId = 21,
            code = "1000",
            descFr = "Bruxelles",
            descNl = "Brussel"
        )

        every { lookupService?.getCityByPostalCode(any()) } returns CityDTO(
            code = "1000",
            descNl = "Brussel",
            descFr = "Bruxelles",
            lookupId = 50,
            nisCode = "4542456",
            beginDate = LocalDate.of(2020, 1, 1),
        )


        every { lookupService?.getNationalityByCode(any()) } returns NationalityDTO(
            code = "150",
            descNl = "België",
            descFr = "Belgique",
            lookupId = 5455
        )

        // When
        val result = mockMvc.perform(
            put("/api/requests/$requestId/citizen-information/select")
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    objectMapper.writeValueAsString(
                        SelectFieldSourcesRequest(
                            listOf(
                                FieldSource().also {
                                    it.fieldName = "birthDate"
                                    it.source = ExternalSource.C1
                                },
                                FieldSource().also {
                                    it.fieldName = "nationality"
                                    it.source = ExternalSource.ONEM
                                },
                                FieldSource().also {
                                    it.fieldName = "address"
                                    it.source = ExternalSource.ONEM
                                }
                            )
                        )
                    )
                )
        )
            .andExpect(status().`is`(204))
            .andReturn()

        val citizenInformation = jdbcHelper.getCitizenInformation(requestId)
        assertThat(citizenInformation)
            .containsEntry("birth_date", java.sql.Date.valueOf(LocalDate.of(1975, 6, 9)))
            .containsEntry("nationality", "151")
            .containsEntry("street", "Main Street")
            .containsEntry("box_number", "Box A")
            .containsEntry("house_number", "123")
            .containsEntry("zip_code", "1000")
            .containsEntry("city", "Brussels")
    }
}