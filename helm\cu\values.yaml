---
global: {}
onerva-metadata:
  team:
    name: "URRR"
    contact: "<EMAIL>"
  service:
    contact: "<EMAIL>"
    projectKey: "a27ef123-b4f3-4103-8c62-5a0a95746a14"
    type: "BUSINESS"
    technicalStack:
      name: "NEW_TECHNICAL_STACK"
      version: ""
  enabled: true
backend:
  image:
    repository: "onemrva/cu-backend"
  enabled: true
  route:
    paths:
      - "/dashboard"
      - "/api"
      - "/oauth2"
bff:
  image:
    repository: "onemrva/cu-bff"
  enabled: true
  route:
    paths:
      - "/bff"
cu:
  image:
    repository: "onemrva/cu-cu"
  angularBundle:
    name: "cu"
    webComponents:
      - "elements"
  enabled: true
infra:
  keycloak:
    enabled: false
    image:
      repository: "docker-release.onemrva.priv/onemrva/keycloak"
      tag: "17.0.0-20230825T071436Z"
  mssql:
    enabled: false
    sapassword: "D3vD3vD3v$"
    acceptEula:
      value: "y"
    edition:
      value: "Express"
    persistence:
      enabled: false
    resources:
      limits:
        cpu: "500m"
        memory: "3036Mi"
      requests:
        cpu: "500m"
        memory: "3036Mi"
    init:
      configmap:
        nametemplate: ""
kcconfig:
  image:
    registry: "docker-release.onemrva.priv"
  enabled: true
  jobAnnotations:
    helm.sh/hook: "post-install,post-upgrade,post-rollback"
    helm.sh/hook-delete-policy: "before-hook-creation"
    helm.sh/hook-weight: "10"
  keycloak:
    url: "http://cu-infra-keycloak-http.cu.svc.cluster.local"
    user: "admin"
    password: "admin"
    availabilityCheck:
      enabled: true
  realm:
    name: "onemrva-agents"
    roles:
      cu_role_admin: { }
      cu_role_user: { }
    clients:
      cu-backend:
        description: "cu backend API"
        standardFlowEnabled: false
        directAccessGrantsEnabled: true
        serviceAccountsEnabled: true
        defaultClientScopes:
          - scope:rva-onem:workenvironmentgateway-rest:user
          - scope:rva-onem:workenvironmentgateway-rest:advanceduser
        protocolMappers:
          - name: aud
            protocol: openid-connect
            protocolMapper: oidc-audience-mapper
            consentRequired: "false"
            config:
              included.custom.audience: rabbitmq
              id.token.claim: "false"
              access.token.claim: "true"
          - name: "Rabbit Permissions"
            protocol: openid-connect
            protocolMapper: oidc-hardcoded-claim-mapper
            consentRequired: "false"
            config:
              id.token.claim: "false"
              access.token.claim: "true"
              claim.name: extra_scope
              jsonType.label: "String"
              claim.value: "rabbitmq.configure:onemrva/c9.* rabbitmq.read:onemrva/c9.* rabbitmq.write:onemrva/c9.* rabbitmq.configure:onemrva/cu.c9.* rabbitmq.write:onemrva/cu.c9.* rabbitmq.read:onemrva/cu.c9.* rabbitmq.configure:onemrva/person.* rabbitmq.read:onemrva/person.* rabbitmq.write:onemrva/person.* rabbitmq.configure:onemrva/cu.person.* rabbitmq.read:onemrva/cu.person.* rabbitmq.write:onemrva/cu.person.*"
      cu-bff:
        description: "cu bff API"
        standardFlowEnabled: false
        directAccessGrantsEnabled: true
        serviceAccountsEnabled: true
      cu-frontend:
        public: true
        directAccessGrantsEnabled: true
        redirectUris:
          - "http://localhost:4300/*"
          - "http://localhost:9091/*"
          - "https://cu-ci.test.paas.onemrva.priv/*"
          - "https://cu.test.paas.onemrva.priv/*"
        defaultClientScopes:
          - email
          - profile
          - roles
          - web-origins
          - onemrva
          - ssin
        protocolMappers:
          - name: realm roles
            protocol: openid-connect
            protocolMapper: oidc-usermodel-realm-role-mapper
            config:
              multivalued: "true"
              userinfo.token.claim: "true"
              id.token.claim: "true"
              access.token.claim: "true"
              claim.name: "realm_access.roles"
              jsonType.label: "String"
              add.to.userinfo: "true"
          - name: ssin
            protocol: openid-connect
            protocolMapper: oidc-usermodel-attribute-mapper
            config:
              user.attribute: ssin
              claim.name: ssin
              jsonType.label: String
              id.token.claim: "true"
              access.token.claim: "true"
              userinfo.token.claim: "true"
              multivalued: "true"
    groups:
      GCuAdmin:
        realmRoles:
          - cu_role_admin
          - cu_role_user
      GCuUser:
        realmRoles:
          - cu_role_user
    users:
      cu_admin:
        enabled: false
        firstName: "The"
        lastName: "Admin"
        email: "<EMAIL>"
        password: "password"
        attributes:
          ouCode:
            - 24000
      cu_user:
        enabled: false
        firstName: "simple"
        lastName: "user"
        email: "<EMAIL>"
        password: "password"
        attributes:
          ssin: 85050599890
          ouCode:
            - 24000
          language:
            - fr
    clientScopes:
      openid:
        protocol: openid-connect
        displayOnConsentScreen: true
        includeInTokenScope: true
      profile:
        protocol: openid-connect
        displayOnConsentScreen: true
        includeInTokenScope: true
      ssin:
        protocol: openid-connect
        displayOnConsentScreen: true
        includeInTokenScope: true


neometadata:
  enabled: true
e2e:
  enabled: false
  webcomponent:
    image:
      repository: "onemrva/cu-cu-e2e"
karate:
  enabled: false
  resources:
    requests:
      cpu: "500m"
      memory: "1Gi"
    limits:
      memory: "2Gi"
  image:
    repository: "onemrva/cu-e2e-karate"

woconfig:
  enabled: true
  wo_backend:
    enabled: false
  configClient:
    enabled: true
    oauth:
      enabled: true
      issuerUrl: ""
      clientId: cu-backend
      secretName: cu-backend
      secretCredentialKey: spring_security_oauth2_client_registration_keycloak_clientsecret
  wo-fake:
    enabled: false
    wo_api:
      enabled: false
    rabbitmq:
      enabled: false
    rabbitmq-pilot:
      enabled: false
    keycloak:
      enabled: false
    keycloakportal:
      enabled: false
    imposter:
      enabled: false
    mockserver:
      enabled: false
    mssql:
      enabled: false
    common:
      enabled: false
    activemq:
      enabled: false
  processTypes:
    - processType:
        code: CU_CHANGE_PERSONAL_DATA
        businessDomainTypes:
          items:
            code: ADMI
        label:
          nl: Wijziging van persoonsgegevens van de burger
          fr: Changement de données personnelles du citoyen
        businessDataTypes:
          - businessDataType:
              code: CU_REQUEST_ID
              label:
                fr: "Référence de la requête chômage complet pour les employés"
                nl: "Referentie van de aanvraag volledige werkloosheid werknemer"
              formatType: shorttext
            category: INPUT
            order: 0
            isEditable: true
            isFilter: false
            isHidden: true
            isRequired: false
          - businessDataType:
              code: CU_RECEPTION_DATE
              label:
                fr: "Date d'entrée C9"
                nl: "Datum indiening"
              formatType: timestamp
            category: INPUT
            order: 1
            isEditable: false
            isFilter: true
            filterNbr: 1
            isHidden: false
            isRequired: true
          - businessDataType:
              code: CU_REQUEST_DATE
              label:
                fr: "Date de la demande"
                nl: "Datum uitkeringsaanvraag"
              formatType: timestamp
            category: INPUT
            order: 2
            isEditable: false
            isFilter: false
            isHidden: false
            isRequired: true
          - businessDataType:
              code: CU_C9_TYPE
              label:
                fr: "Type C9"
                nl: "C9 type"
              formatType: shorttext
            category: INPUT
            order: 3
            isEditable: false
            isFilter: true
            filterNbr: 2
            isHidden: false
            isRequired: false
          - businessDataType:
              code: CU_DOSSIER_ID
              label:
                fr: "ID du Dossier"
                nl: "Dossier ID"
              formatType: shorttext
            category: INPUT
            order: 4
            isEditable: false
            isFilter: false
            isHidden: false
            isRequired: true
          - businessDataType:
              code: CU_ENTITY
              label:
                fr: "Bureau"
                nl: "Kantor"
              formatType: shorttext
              values:
                - code: "711"
                  label:
                    fr: "711 - Anvers"
                    nl: "711 - Antwerpen"
                - code: "713"
                  label:
                    fr: "713 - Turnhout"
                    nl: "713 - Turnhout"
                - code: "722"
                  label:
                    fr: "722 - Louvain"
                    nl: "722 - Leuven"
                - code: "731"
                  label:
                    fr: "731 - Brugges"
                    nl: "731 - Brugge"
                - code: "734"
                  label:
                    fr: "734 - Courtrai"
                    nl: "734 - Kortrijk"
                - code: "744"
                  label:
                    fr: "744 - Gand"
                    nl: "744 - Gent"
                - code: "771"
                  label:
                    fr: "771 - Hasselt"
                    nl: "771 - Hasselt"
                - code: "823"
                  label:
                    fr: "823 - Nivelles"
                    nl: "823 - Nijvel"
                - code: "852"
                  label:
                    fr: "852 - Charleroi"
                    nl: "852 - Charleroi"
                - code: "853"
                  label:
                    fr: "853 - Mons"
                    nl: "853 - Bergen"
                - code: "854"
                  label:
                    fr: "854 - La Louvière"
                    nl: "854 - La Louvière"
                - code: "862"
                  label:
                    fr: "862 - Liège"
                    nl: "862 - Luik"
                - code: "863"
                  label:
                    fr: "863 - Verviers"
                    nl: "863 - Verviers"
                - code: "881"
                  label:
                    fr: "881 - Arlon"
                    nl: "881 - Aarlen"
                - code: "892"
                  label:
                    fr: "892 - Namur"
                    nl: "892 - Namen"
                - code: "921"
                  label:
                    fr: "921 - Bruxelles"
                    nl: "921 - Brussel"
            category: INPUT
            order: 5
            isEditable: false
            isFilter: true
            filterNbr: 3
            isHidden: false
            isRequired: false
          - businessDataType:
              code: CU_PAYMENT_INSTITUTION
              label:
                fr: "Organisme de paiement"
                nl: "Uitbetalingsinstelling"
              formatType: shorttext
              values:
                - code: OP_SECT_CODE_LIST
            category: INPUT
            order: 6
            isEditable: false
            isFilter: false
            isHidden: false
            isRequired: true
          - businessDataType:
              code: CU_DECISION_TYPE
              label:
                fr: "Décision prise"
                nl: "Beslissing"
              formatType: shorttext
              values:
                - code: "C9NA"
                  label:
                    fr: "C9NA - C9 non accepté"
                    nl: "C9NA - Niet-aanvaarde C9"
                - code: "C2Y"
                  label:
                    fr: "Validé et accepté"
                    nl: "Gevalideerd en aanvaard"
                - code: "C2N"
                  label:
                    fr: "Validé et refusé"
                    nl: "Gevalideerd en geweigerd"
                - code: "C2F"
                  label:
                    fr: "Validé pour une date future"
                    nl: "Gevalideerd voor een datum in de toekomst"
                - code: "C2P"
                  label:
                    fr: "Demande sans impact"
                    nl: "Aanvraag zonder impact"
                - code: "C51"
                  label:
                    fr: "Renvoi à l’OP"
                    nl: "Terugzending aan UI"
                - code: "C9B"
                  label:
                    fr: "En attente le traitement d’une demande en cours"
                    nl: "Wachtend op de verwerking van een andere aanvraag"
                - code: "C2"
                  label:
                    fr: "Validé"
                    nl: "Gevalideerd"
            category: OUTPUT
            order: 7
            filterNbr: 4
            isEditable: false
            isFilter: true
            isHidden: false
            isRequired: false
          - businessDataType:
              code: CU_DECISION_BAREMA
              label:
                fr: "Barème"
                nl: "Barema"
              formatType: shorttext
            category: OUTPUT
            order: 8
            isEditable: false
            isFilter: false
            isHidden: false
            isRequired: true
        taskTypes:
          - code: CHANGE_PERSONAL_DATA_CAPTURE
            label:
              fr: Encoder les données du citoyen
              nl: Codeer burgersgegevens
            defaultPriority:
              code: "1"
            displayOrder: "10"
            processingTime: "3"
            dueDateEditability: true
            businessDataTypes:
              - businessDataType:
                  code: CU_REQUEST_ID
                  label:
                    fr: "Référence de la requête chômage complet pour les employés"
                    nl: "Referentie van de aanvraag volledige werkloosheid werknemer"
                  formatType: shorttext
                category: INPUT
                order: 0
                isEditable: true
                isFilter: false
                isHidden: true
                isRequired: false
              - businessDataType:
                  code: CU_C9_TYPE
                  label:
                    fr: "Type C9"
                    nl: "C9 type"
                  formatType: shorttext
                category: INPUT
                order: 1
                isEditable: false
                isFilter: true
                filterNbr: 1
                isHidden: false
                isRequired: false
              - businessDataType:
                  code: CU_ENTITY
                  label:
                    fr: "Bureau"
                    nl: "Kantor"
                  formatType: shorttext
                  values:
                    - code: "711"
                      label:
                        fr: "711 - Anvers"
                        nl: "711 - Antwerpen"
                    - code: "713"
                      label:
                        fr: "713 - Turnhout"
                        nl: "713 - Turnhout"
                    - code: "722"
                      label:
                        fr: "722 - Louvain"
                        nl: "722 - Leuven"
                    - code: "731"
                      label:
                        fr: "731 - Brugges"
                        nl: "731 - Brugge"
                    - code: "734"
                      label:
                        fr: "734 - Courtrai"
                        nl: "734 - Kortrijk"
                    - code: "744"
                      label:
                        fr: "744 - Gand"
                        nl: "744 - Gent"
                    - code: "771"
                      label:
                        fr: "771 - Hasselt"
                        nl: "771 - Hasselt"
                    - code: "823"
                      label:
                        fr: "823 - Nivelles"
                        nl: "823 - Nijvel"
                    - code: "852"
                      label:
                        fr: "852 - Charleroi"
                        nl: "852 - Charleroi"
                    - code: "853"
                      label:
                        fr: "853 - Mons"
                        nl: "853 - Bergen"
                    - code: "854"
                      label:
                        fr: "854 - La Louvière"
                        nl: "854 - La Louvière"
                    - code: "862"
                      label:
                        fr: "862 - Liège"
                        nl: "862 - Luik"
                    - code: "863"
                      label:
                        fr: "863 - Verviers"
                        nl: "863 - Verviers"
                    - code: "881"
                      label:
                        fr: "881 - Arlon"
                        nl: "881 - Aarlen"
                    - code: "892"
                      label:
                        fr: "892 - Namur"
                        nl: "892 - Namen"
                    - code: "921"
                      label:
                        fr: "921 - Bruxelles"
                        nl: "921 - Brussel"
                category: INPUT
                order: 2
                isEditable: false
                isFilter: true
                filterNbr: 2
                isHidden: false
                isRequired: false
              - businessDataType:
                  code: CU_RECEPTION_DATE
                  label:
                    fr: "Date d'entrée C9"
                    nl: "Datum ontvangen C9"
                  formatType: timestamp
                category: INPUT
                order: 3
                isEditable: false
                isFilter: true
                filterNbr: 3
                isHidden: true
                isRequired: false
          - code: VALIDATION_DATA
            label:
              fr: Vérifier la cohérence des données
              nl: Controleer de consistentie van de gegevens
            defaultPriority:
              code: "1"
            displayOrder: "20"
            processingTime: "3"
            dueDateEditability: true
            businessDataTypes:
              - businessDataType:
                  code: CU_REQUEST_ID
                  label:
                    fr: "Référence de la requête chômage complet pour les employés"
                    nl: "Referentie van de aanvraag volledige werkloosheid werknemer"
                  formatType: shorttext
                category: INPUT
                order: 0
                isEditable: true
                isFilter: false
                isHidden: true
                isRequired: false
              - businessDataType:
                  code: CU_C9_TYPE
                  label:
                    fr: "Type C9"
                    nl: "C9 type"
                  formatType: shorttext
                category: INPUT
                order: 1
                isEditable: false
                isFilter: true
                filterNbr: 1
                isHidden: false
                isRequired: false
              - businessDataType:
                  code: CU_ENTITY
                  label:
                    fr: "Bureau"
                    nl: "Kantor"
                  formatType: shorttext
                  values:
                    - code: "711"
                      label:
                        fr: "711 - Anvers"
                        nl: "711 - Antwerpen"
                    - code: "713"
                      label:
                        fr: "713 - Turnhout"
                        nl: "713 - Turnhout"
                    - code: "722"
                      label:
                        fr: "722 - Louvain"
                        nl: "722 - Leuven"
                    - code: "731"
                      label:
                        fr: "731 - Brugges"
                        nl: "731 - Brugge"
                    - code: "734"
                      label:
                        fr: "734 - Courtrai"
                        nl: "734 - Kortrijk"
                    - code: "744"
                      label:
                        fr: "744 - Gand"
                        nl: "744 - Gent"
                    - code: "771"
                      label:
                        fr: "771 - Hasselt"
                        nl: "771 - Hasselt"
                    - code: "823"
                      label:
                        fr: "823 - Nivelles"
                        nl: "823 - Nijvel"
                    - code: "852"
                      label:
                        fr: "852 - Charleroi"
                        nl: "852 - Charleroi"
                    - code: "853"
                      label:
                        fr: "853 - Mons"
                        nl: "853 - Bergen"
                    - code: "854"
                      label:
                        fr: "854 - La Louvière"
                        nl: "854 - La Louvière"
                    - code: "862"
                      label:
                        fr: "862 - Liège"
                        nl: "862 - Luik"
                    - code: "863"
                      label:
                        fr: "863 - Verviers"
                        nl: "863 - Verviers"
                    - code: "881"
                      label:
                        fr: "881 - Arlon"
                        nl: "881 - Aarlen"
                    - code: "892"
                      label:
                        fr: "892 - Namur"
                        nl: "892 - Namen"
                    - code: "921"
                      label:
                        fr: "921 - Bruxelles"
                        nl: "921 - Brussel"
                category: INPUT
                order: 2
                isEditable: false
                isFilter: true
                filterNbr: 2
                isHidden: false
                isRequired: false
              - businessDataType:
                  code: CU_RECEPTION_DATE
                  label:
                    fr: "Date d'entrée C9"
                    nl: "Datum ontvangen C9"
                  formatType: timestamp
                category: INPUT
                order: 3
                isEditable: false
                isFilter: true
                filterNbr: 3
                isHidden: true
                isRequired: false
        taskMiniApps:
          - taskCode: CHANGE_PERSONAL_DATA_CAPTURE
            linkCode: CHANGE_PERSONAL_DATA_CAPTURE_COMPONENT
            preferenceOrder: "1"
            type: "MINIAPP_INPUT"
          - taskCode: VALIDATION_DATA
            linkCode: VALIDATION_DATA_COMPONENT
            preferenceOrder: "2"
            type: "MINIAPP_INPUT"
  linkedResources:
    list:
      - code: CU_LINKED_RESOURCES
        uriTest: "https://cu.test.paas.onemrva.priv"
        uriInt: "https://cu.test.paas.onemrva.priv"
        uriAcc: "https://cu.val.paas.onemrva.priv"
        uriProd: "https://cu.prod.paas.onemrva.priv"
        links:
          - code: CHANGE_PERSONAL_DATA_CAPTURE_COMPONENT
            nameFr: Web component pour CU Data Capture
            nameNl: Web component voor CU Data Capture
            path: /elements/elements.js
            parameters: ?taskId=${Task.id}&status=${Task.taskStatus}&taskStep=${Task.taskState}&task=${Task}&decisionType=${Task.CU_DECISION_TYPE}&decisionBarema=&{Task.CU_DECISION_BAREMA}&requestId=${Task.taskSpecificMetadata[0].CU_REQUEST_ID}
            webcomponent: true
          - code: VALIDATION_DATA_COMPONENT
            nameFr: Web component pour CU Validation Data
            nameNl: Web component voor CU Validation Data
            path: /elements/elements.js
            parameters: ?taskId=${Task.id}&status=${Task.taskStatus}&taskStep=${Task.taskState=${Task}&decisionType=${Task.CU_DECISION_TYPE}&decisionBarema=&{Task.CU_DECISION_BAREMA}&task=${Task}&requestId=${Task.taskSpecificMetadata[0].CU_REQUEST_ID}
            webcomponent: true

security:
  realm: onemrva-agents
  authserverurl: nil
  backendBasePath: nil

microcks:
  enabled: false
  resources:
    requests:
      cpu: 250m
      memory: 1500Mi
    limits:
      cpu: 350m
      memory: 3Gi
  microcksInit:
    enabled: true
    fromConfigMap:
      enabled: true
      configMapName: microcks-init-cm
    fromApiRegistry:
      enabled: true
      env: test
      apiContracts:
        - groupid: be.fgov.onerva.person.backend
          artifactid: person-rest-api
          version: latest
        - groupid: be.fgov.onerva.unemployment
          artifactid: c9-public-api
          version: latest
        - groupid: be.fgov.onerva.bareme
          artifactid: bareme-rest-api
          version: latest
        - groupid: be.fgov.onerva.woconfigurator
          artifactid: wo-facade-api
          version: latest
        - groupid: be.fgov.onerva.registerproxyservice
          artifactid: registerproxyservice-public-api
          version: latest

wiremock:
  enabled: false
  consumer:
    name: cu-wiremock
    args_include_default: true
    args:
      - "--global-response-templating"
      - "--local-response-templating"
    stubs:
      barema-stubs: /mnt/barema-stubs
  service:
    type: ClusterIP
    port: 80
  ingress:
    enabled: false
  resources:
    requests:
      cpu: 200m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 1Gi
  java:
    xms: 400M
    xmx: 800M
    mms: 128M
  replicas: 1