import { StringUtils } from './string.utils';
import { Address } from '@rest-client/cu-bff';

describe('StringUtils', () => {
  describe('trimIfString', () => {
    it('should return the trimmed string when given a non-empty string', () => {
      const result = StringUtils.trimIfString('  hello world  ');
      expect(result).toBe('hello world');
    });

    it('should return the same string when there are no leading or trailing spaces', () => {
      const result = StringUtils.trimIfString('hello');
      expect(result).toBe('hello');
    });

    it('should return null if the original value is null', () => {
      const result = StringUtils.trimIfString(null);
      expect(result).toBeNull();
    });

    it('should return the same value if it is not a string', () => {
      const result = (StringUtils.trimIfString as any)(123);
      expect(result).toBe(123);
    });

    it('should return empty string when given an empty string', () => {
      const result = StringUtils.trimIfString('');
      expect(result).toBe('');
    });

    it('should return empty string when given a string with only spaces', () => {
      const result = StringUtils.trimIfString('   ');
      expect(result).toBe('');
    });

    it('should handle undefined input', () => {
      const result = (StringUtils.trimIfString as any)(undefined);
      expect(result).toBeUndefined();
    });

    it('should handle strings with tabs and newlines', () => {
      const result = StringUtils.trimIfString('\t\n  hello  \n\t');
      expect(result).toBe('hello');
    });

    it('should handle boolean values', () => {
      const result = (StringUtils.trimIfString as any)(true);
      expect(result).toBe(true);
    });

    it('should handle array values', () => {
      const arr = [1, 2, 3];
      const result = (StringUtils.trimIfString as any)(arr);
      expect(result).toBe(arr);
    });

    it('should handle object values', () => {
      const obj = { key: 'value' };
      const result = (StringUtils.trimIfString as any)(obj);
      expect(result).toBe(obj);
    });
  });

  describe('formatAddress', () => {
    it('should return "-" when address is null', () => {
      const result = StringUtils.formatAddress(null as any);
      expect(result).toBe('-');
    });

    it('should return "-" when address is undefined', () => {
      const result = StringUtils.formatAddress(undefined as any);
      expect(result).toBe('-');
    });

    it('should format a complete address correctly', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        boxNumber: '4A',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123/4A, <br>1000 Brussels');
    });

    it('should format address without box number', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>1000 Brussels');
    });

    it('should handle empty street', () => {
      const address: Address = {
        country: 'Belgium',
        street: '',
        houseNumber: '123',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('123, <br>1000 Brussels');
    });

    it('should handle empty house number', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street, <br>1000 Brussels');
    });

    it('should handle empty zip code', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        zipCode: '',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>Brussels');
    });

    it('should handle empty city', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        zipCode: '1000',
        city: ''
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>1000');
    });

    it('should handle address with only street and city', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '',
        zipCode: '',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street, <br>Brussels');
    });

    it('should handle address with only zip code and city', () => {
      const address: Address = {
        country: 'Belgium',
        street: '',
        houseNumber: '',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('<br>1000 Brussels');
    });

    it('should return "-" when all fields are empty strings', () => {
      const address: Address = {
        country: '',
        street: '',
        houseNumber: '',
        boxNumber: '',
        zipCode: '',
        city: ''
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('-');
    });

    it('should handle extra spaces in address fields', () => {
      const address: Address = {
        country: 'Belgium',
        street: '  Main   Street  ',
        houseNumber: '  123  ',
        zipCode: '  1000  ',
        city: '  Brussels  '
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>1000 Brussels');
    });

    it('should handle address with multiple consecutive spaces', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main    Street',
        houseNumber: '123',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>1000 Brussels');
    });

    it('should handle only street without house number', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '',
        zipCode: '',
        city: ''
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street, <br>');
    });

    it('should handle only street with house number', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        zipCode: '',
        city: ''
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>');
    });

    it('should handle only city without zip code', () => {
      const address: Address = {
        country: 'Belgium',
        street: '',
        houseNumber: '',
        zipCode: '',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('<br>Brussels');
    });

    it('should handle only zip code without city', () => {
      const address: Address = {
        country: 'Belgium',
        street: '',
        houseNumber: '',
        zipCode: '1000',
        city: ''
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('<br>1000');
    });

    it('should handle box number with special characters', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        boxNumber: 'B-12',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123/B-12, <br>1000 Brussels');
    });

    it('should handle empty box number differently from missing box number', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        boxNumber: '',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123, <br>1000 Brussels');
    });

    it('should handle box number with spaces', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        boxNumber: '  4A  ',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Main Street 123/4A, <br>1000 Brussels');
    });

    it('should handle house number with box number but no street', () => {
      const address: Address = {
        country: 'Belgium',
        street: '',
        houseNumber: '123',
        boxNumber: '4A',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('123/4A, <br>1000 Brussels');
    });

    it('should handle spaces within field values correctly', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Rue de la    Loi',
        houseNumber: '16',
        zipCode: '1000',
        city: 'Brussels   Capital'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Rue de la Loi 16, <br>1000 Brussels Capital');
    });

    it('should not include country in the formatted output', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Main Street',
        houseNumber: '123',
        zipCode: '1000',
        city: 'Brussels'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).not.toContain('Belgium');
    });

    it('should handle partial address with type assertion for edge cases', () => {
      // Testing the method's defensive programming against partial data
      const partialAddress = {
        street: 'Main Street',
        city: 'Brussels'
      } as Address;
      const result = StringUtils.formatAddress(partialAddress);
      expect(result).toBe('Main Street, <br>Brussels');
    });

    it('should handle address with null/undefined fields using type assertion', () => {
      // Testing defensive programming for runtime data that might not match the interface
      const addressWithNulls = {
        country: 'Belgium',
        street: null,
        houseNumber: undefined,
        zipCode: '1000',
        city: 'Brussels'
      } as any as Address;
      const result = StringUtils.formatAddress(addressWithNulls);
      expect(result).toBe('<br>1000 Brussels');
    });

    it('should handle all fields being whitespace only', () => {
      const address: Address = {
        country: '   ',
        street: '   ',
        houseNumber: '   ',
        boxNumber: '   ',
        zipCode: '   ',
        city: '   '
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('-');
    });

    it('should handle very long address components', () => {
      const address: Address = {
        country: 'Belgium',
        street: 'Very Long Street Name That Goes On And On',
        houseNumber: '12345',
        boxNumber: 'A-123-B',
        zipCode: '1000-1234',
        city: 'Brussels Capital Region Metropolitan Area'
      };
      const result = StringUtils.formatAddress(address);
      expect(result).toBe('Very Long Street Name That Goes On And On 12345/A-123-B, <br>1000-1234 Brussels Capital Region Metropolitan Area');
    });
  });
});