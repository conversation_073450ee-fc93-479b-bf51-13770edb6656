package be.fgov.onerva.cu.backend.adapter.out.persistence.model

import java.time.LocalDate
import jakarta.persistence.Column
import jakarta.persistence.DiscriminatorColumn
import jakarta.persistence.DiscriminatorType
import jakarta.persistence.DiscriminatorValue
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.Inheritance
import jakarta.persistence.InheritanceType
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToMany
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.envers.Audited
import org.hibernate.envers.NotAudited
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType

@Entity
@Table(name = "request")
@DiscriminatorColumn(discriminatorType = DiscriminatorType.STRING, name = "type")
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
abstract class RequestEntity(
    @Column(name = "c9_id")
    open var c9Id: Long,

    @Column(name = "c9_type")
    open var c9Type: String,

    @Column(name = "op_key")
    open var opKey: String,

    @Column(name = "sect_op")
    open var sectOp: String,

    @Column(name = "request_date")
    open var requestDate: LocalDate,

    @Column(name = "ssin")
    open var ssin: String,

    @Column(name = "decision_type")
    @Enumerated(EnumType.STRING)
    open var decisionType: DecisionType? = null,

    @Column(name = "decision_from_mfx")
    open var decisionFromMfx: Boolean? = null,

    @Column(name = "decision_user")
    open var decisionUser: String? = null,

    @Column(name = "decision_date")
    open var decisionDate: LocalDate? = null,

    @Column(name = "decision_barema", length = 25)
    open var decisionBarema: String? = null,

    @OneToMany(mappedBy = "request", fetch = FetchType.LAZY)
    open var waveTasks: MutableSet<WaveTaskEntity> = mutableSetOf(),
) : BaseEntity()

@Entity
@Table(name = "request_information")
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
class RequestInformationEntity(
    @OneToOne
    @JoinColumn(name = "request_id")
    @NotAudited
    val request: RequestEntity,

    @Audited
    @Column(name = "request_date")
    var requestDate: LocalDate,
) : BaseEntityAudited()

@Entity
@DiscriminatorValue("CHANGE_PERSONAL_DATA")
class ChangePersonalDataRequestEntity(
    @OneToOne(mappedBy = "request")
    var citizenInformation: CitizenInformationEntity? = null,

    @OneToOne(mappedBy = "request")
    var modeOfPayment: ModeOfPaymentEntity? = null,

    @OneToOne(mappedBy = "request")
    var unionContribution: UnionContributionEntity? = null,

    @OneToOne(mappedBy = "request")
    var requestInformation: RequestInformationEntity? = null,

    @Column(name = "document_type")
    @Enumerated(EnumType.STRING)
    var documentType: IdentityDocumentType,

    c9Id: Long,
    c9Type: String,
    opKey: String,
    sectOp: String,
    requestDate: LocalDate,
    ssin: String,
) : RequestEntity(c9Id, c9Type, opKey, sectOp, requestDate, ssin)

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "citizen_information")
class CitizenInformationEntity(
    @OneToOne
    @JoinColumn(name = "request_id")
    @NotAudited
    val request: RequestEntity,

    @Column(name = "first_name", nullable = false)
    var firstName: String,

    @Column(name = "last_name", nullable = false)
    var lastName: String,

    @Column(name = "birth_date", nullable = false)
    var birthDate: LocalDate,

    // Mutable fields
    @Audited
    @Column(name = "nationality", nullable = false)
    var nationality: String,

    @Audited
    @Embedded
    var address: Address,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,

    ) : BaseEntityAudited()

@Embeddable
@Audited
data class Address(
    @Column(name = "country", nullable = false)
    var country: String,

    @Column(name = "street", nullable = false)
    var street: String,

    @Column(name = "house_number", nullable = false)
    var houseNumber: String,

    @Column(name = "box_number", nullable = true)
    var boxNumber: String? = null,

    @Column(name = "zip_code", nullable = false)
    var zipCode: String,

    @Column(name = "city", nullable = false)
    var city: String,
)

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "mode_of_payment")
class ModeOfPaymentEntity(
    @OneToOne
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "other_person_name", nullable = true)
    var otherPersonName: String?,

    @Column(name = "iban", nullable = false)
    var iban: String,

    @Column(name = "bic", nullable = true)
    var bic: String?,

    @Column(name = "valid_from", nullable = true)
    var validFrom: LocalDate?,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,

    ) : BaseEntityAudited()

@Entity
@Audited
@EntityListeners(
    AuditingEntityListener::class
)
@Table(name = "union_contribution")
class UnionContributionEntity(
    @OneToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "authorized", nullable = false)
    var authorized: Boolean?,

    @Column(name = "effective_date", nullable = false)
    var effectiveDate: LocalDate?,

    @Column(name = "update_status")
    @Enumerated(EnumType.STRING)
    var updateStatus: UpdateStatus,
) : BaseEntityAudited()

enum class UpdateStatus {
    FROM_C9,
    EDITED
}
