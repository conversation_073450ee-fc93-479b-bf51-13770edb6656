/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ContactInfo
 */
@JsonPropertyOrder({
  ContactInfo.JSON_PROPERTY_SOURCE,
  ContactInfo.JSON_PROPERTY_TEL_NBR,
  ContactInfo.JSON_PROPERTY_MOBILE_NBR,
  ContactInfo.JSON_PROPERTY_EMAIL_ADDRESS,
  ContactInfo.JSON_PROPERTY_FAX_NBR
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ContactInfo {
  public static final String JSON_PROPERTY_SOURCE = "source";
  private String source;

  public static final String JSON_PROPERTY_TEL_NBR = "telNbr";
  private String telNbr;

  public static final String JSON_PROPERTY_MOBILE_NBR = "mobileNbr";
  private String mobileNbr;

  public static final String JSON_PROPERTY_EMAIL_ADDRESS = "emailAddress";
  private String emailAddress;

  public static final String JSON_PROPERTY_FAX_NBR = "faxNbr";
  private String faxNbr;

  public ContactInfo() {
  }

  public ContactInfo source(String source) {
    
    this.source = source;
    return this;
  }

  /**
   * Get source
   * @return source
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSource() {
    return source;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSource(String source) {
    this.source = source;
  }

  public ContactInfo telNbr(String telNbr) {
    
    this.telNbr = telNbr;
    return this;
  }

  /**
   * Get telNbr
   * @return telNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TEL_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTelNbr() {
    return telNbr;
  }


  @JsonProperty(JSON_PROPERTY_TEL_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTelNbr(String telNbr) {
    this.telNbr = telNbr;
  }

  public ContactInfo mobileNbr(String mobileNbr) {
    
    this.mobileNbr = mobileNbr;
    return this;
  }

  /**
   * Get mobileNbr
   * @return mobileNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MOBILE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMobileNbr() {
    return mobileNbr;
  }


  @JsonProperty(JSON_PROPERTY_MOBILE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMobileNbr(String mobileNbr) {
    this.mobileNbr = mobileNbr;
  }

  public ContactInfo emailAddress(String emailAddress) {
    
    this.emailAddress = emailAddress;
    return this;
  }

  /**
   * Get emailAddress
   * @return emailAddress
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmailAddress() {
    return emailAddress;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmailAddress(String emailAddress) {
    this.emailAddress = emailAddress;
  }

  public ContactInfo faxNbr(String faxNbr) {
    
    this.faxNbr = faxNbr;
    return this;
  }

  /**
   * Get faxNbr
   * @return faxNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FAX_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFaxNbr() {
    return faxNbr;
  }


  @JsonProperty(JSON_PROPERTY_FAX_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFaxNbr(String faxNbr) {
    this.faxNbr = faxNbr;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ContactInfo contactInfo = (ContactInfo) o;
    return Objects.equals(this.source, contactInfo.source) &&
        Objects.equals(this.telNbr, contactInfo.telNbr) &&
        Objects.equals(this.mobileNbr, contactInfo.mobileNbr) &&
        Objects.equals(this.emailAddress, contactInfo.emailAddress) &&
        Objects.equals(this.faxNbr, contactInfo.faxNbr);
  }

  @Override
  public int hashCode() {
    return Objects.hash(source, telNbr, mobileNbr, emailAddress, faxNbr);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ContactInfo {\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    telNbr: ").append(toIndentedString(telNbr)).append("\n");
    sb.append("    mobileNbr: ").append(toIndentedString(mobileNbr)).append("\n");
    sb.append("    emailAddress: ").append(toIndentedString(emailAddress)).append("\n");
    sb.append("    faxNbr: ").append(toIndentedString(faxNbr)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

