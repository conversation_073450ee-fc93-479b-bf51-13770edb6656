/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressNullable } from './addressNullable';
import { UnionContributionFields } from './unionContributionFields';


export interface HistoricalCitizenOnemResponse { 
    otherPersonName?: string;
    iban?: string;
    bic?: string;
    /**
     * The first name of the person
     */
    firstName?: string;
    /**
     * The last name of the person
     */
    lastName?: string;
    /**
     * The birth date of the employee (format YYYY-MM-DD)
     */
    birthDate?: string;
    /**
     * The nationality of the employee
     */
    nationality?: string;
    address?: AddressNullable;
    /**
     * The value date for the bank account
     */
    addressValueDate?: string;
    /**
     * The value date for the bank account
     */
    bankAccountValueDate?: string;
    /**
     * The value date for the bank account
     */
    unionContributionValueDate?: string;
    unionDue?: UnionContributionFields;
}

