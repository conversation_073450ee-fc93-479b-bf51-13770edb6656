--liquibase formatted sql
--changeset bernard:drop-existing-wave-task-status-constraint context:ddl

ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__wave_task__statu__36D11DD4;

--changeset bernard:add-wave-task-waiting-status context:ddl
--validCheckSum: 9:ae8630bd19cb382fcd5d152a94642f6b
--validCheckSum: 9:dac38472326d841377447408dbe85c8a

-- First drop the existing check constraint
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS CK__wave_task__status;

-- Add new check constraint with WAITING status
ALTER TABLE wave_task
    ADD CONSTRAINT CK__wave_task__status
        CHECK (status in ('OPEN', 'CLOSED', 'WAITING'));

--changeset bernard:drop-wave-task-status-constraint-test context:ddl-test
--validCheckSum: 9:171cc72e0accf856929db6c78d302da5
--validCheckSum: 9:99d782c3b31365b16b696da433835afc
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS
        CK__wave_task__statu__5DCAEF64;

--changeset bernard:drop-wave-task-status-constraint-val context:ddl-val
ALTER TABLE wave_task
    DROP CONSTRAINT IF EXISTS
        CK__wave_task__statu__6E01572D;

