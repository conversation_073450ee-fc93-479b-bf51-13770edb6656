package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.KeycloakConfigResponseConfig
import be.fgov.onerva.cu.bff.rest.server.priv.model.KeycloakConfigResponseInitOptions
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param config 
 * @param initOptions 
 */
data class KeycloakConfigResponse(

    @field:Valid
    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("config", required = true) val config: KeycloakConfigResponseConfig,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("initOptions") val initOptions: KeycloakConfigResponseInitOptions? = null
    ) {

}

