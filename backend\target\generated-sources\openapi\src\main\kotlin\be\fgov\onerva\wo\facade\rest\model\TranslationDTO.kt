/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param fr 
 * @param nl 
 */


data class TranslationDTO (

    @get:JsonProperty("fr")
    val fr: kotlin.String,

    @get:JsonProperty("nl")
    val nl: kotlin.String

) {


}

