import {bootstrapApplication} from "@angular/platform-browser";
import {appConfig} from "./app/app.config";
import {AppComponent} from "./app/app.component";
import {environment} from "./app/environments/environment";
import {KeycloakConfigResponse} from "@rest-client/cu-config";

fetch(environment.apiBasePath + '/config/keycloak')
    .then((response) => response.json())
    .then((config : KeycloakConfigResponse) => {
        bootstrapApplication(AppComponent, appConfig(config))
            .catch((err) => console.error(err));
    })