/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo.organizational.chart.rest.model.Node;
import be.fgov.onerva.wo.organizational.chart.rest.model.TranslatedString;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Group
 */
@JsonPropertyOrder({
  Group.JSON_PROPERTY_NAME,
  Group.JSON_PROPERTY_NATURE,
  Group.JSON_PROPERTY_RESPONSIBLE,
  Group.JSON_PROPERTY_MEMBERS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Group {
  public static final String JSON_PROPERTY_NAME = "name";
  private TranslatedString name;

  public static final String JSON_PROPERTY_NATURE = "nature";
  private String nature;

  public static final String JSON_PROPERTY_RESPONSIBLE = "responsible";
  private Node responsible;

  public static final String JSON_PROPERTY_MEMBERS = "members";
  private List<Node> members = new ArrayList<>();

  public Group() {
  }

  public Group name(TranslatedString name) {
    
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public TranslatedString getName() {
    return name;
  }


  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setName(TranslatedString name) {
    this.name = name;
  }

  public Group nature(String nature) {
    
    this.nature = nature;
    return this;
  }

  /**
   * The nature of the group (department, service or workteam).
   * @return nature
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNature() {
    return nature;
  }


  @JsonProperty(JSON_PROPERTY_NATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNature(String nature) {
    this.nature = nature;
  }

  public Group responsible(Node responsible) {
    
    this.responsible = responsible;
    return this;
  }

  /**
   * Get responsible
   * @return responsible
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESPONSIBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Node getResponsible() {
    return responsible;
  }


  @JsonProperty(JSON_PROPERTY_RESPONSIBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResponsible(Node responsible) {
    this.responsible = responsible;
  }

  public Group members(List<Node> members) {
    
    this.members = members;
    return this;
  }

  public Group addMembersItem(Node membersItem) {
    if (this.members == null) {
      this.members = new ArrayList<>();
    }
    this.members.add(membersItem);
    return this;
  }

  /**
   * Get members
   * @return members
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MEMBERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Node> getMembers() {
    return members;
  }


  @JsonProperty(JSON_PROPERTY_MEMBERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMembers(List<Node> members) {
    this.members = members;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Group group = (Group) o;
    return Objects.equals(this.name, group.name) &&
        Objects.equals(this.nature, group.nature) &&
        Objects.equals(this.responsible, group.responsible) &&
        Objects.equals(this.members, group.members);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, nature, responsible, members);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Group {\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    nature: ").append(toIndentedString(nature)).append("\n");
    sb.append("    responsible: ").append(toIndentedString(responsible)).append("\n");
    sb.append("    members: ").append(toIndentedString(members)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

