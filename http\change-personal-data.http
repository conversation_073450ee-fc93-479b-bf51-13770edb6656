
### Get basic request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

> {%
    let ssin = response.body.ssin
    console.log("Found ssin: ", ssin)
    client.global.set('latestSsin', ssin)
%}

### Get request information non existing request
GET {{backend-url}}/api/requests/bbe368f3-acac-4450-ba78-7f7f73a1883b
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/request-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get citizen information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get citizen information for a request - non existing - SHOULD FAIL
GET {{backend-url}}/api/requests/bbe368f3-acac-4450-ba78-7f7f73a1883b/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get mode of payment for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get union contribution for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Update employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "birthDate": "1980-01-01",
  "nationality": "150",
  "address": {
    "street": "Main Street",
    "city": "Springfield",
    "zipCode": "12345",
    "country": "173",
    "houseNumber": "333",
    "boxNumber": "4444"
  }
}

### Update employee information for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/citizen-information/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "birthDate",
      "source": "C1"
    },
    {
      "fieldName": "nationality",
      "source": "ONEM"
    },
    {
      "fieldName": "address",
      "source": "ONEM"
    }
  ]
}


### Update employee information for a request - Invalid birthdate
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "birthDate": "2026-01-01",
  "nationality": "150",
  "address": {
    "street": "Main Street",
    "city": "Springfield",
    "zipCode": "12345",
    "country": "US",
    "houseNumber": "333",
    "boxNumber": "4444"
  }
}


### Update employee information for a request - same data as EC1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/citizen-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "birthDate": "1975-06-09",
  "nationality": "150",
  "address": {
    "country": "150",
    "street": "Pasteur",
    "houseNumber": "37",
    "boxNumber": null,
    "zipCode": "1000",
    "city": "92250"
  }
}

### Update mode of payment for a request - own account in belgium

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "iban": "****************"
}

### Update mode of payment for a request - duplicate field name - SHOULD FAIL

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "iban": "****************"
}

### Update mode of payment for a request - other person account in belgium

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "iban": "****************",
  "otherPersonName": "The other person"
}

### Update mode of payment for a request - own account foreign


< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "iban": "***************************",
  "bic": "BBRUBEBB"
}

### Update mode of payment for a request - other person account foreign

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "otherPersonName": "The other person",
  "iban": "***************************",
  "bic": "BBRUBEBB"
}

### Select fields sources for mode-of-payment
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/mode-of-payment/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "account",
      "source": "ONEM"
    }
  ]
}

### Update union contribution for a request - authorized

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "authorized": false,
  "effectiveDate": "2025-01-01"
}

### Update union contribution for a request - not authorized

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "authorized": false,
  "effectiveDate": "2025-01-01"
}

### Update union contribution for a request - EC1 data not changed

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Update union contribution for a request - EC1 data not changed

< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "authorized": null
}

### Select fields sources for union contribution
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/union-contribution/select
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "fieldSources": [
    {
      "fieldName": "contribution",
      "source": "C1"
    }
  ]
}


### Close the CHANGE_PERSONAL_DATA_CAPTURE task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/CHANGE_PERSONAL_DATA_CAPTURE/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Close the VALIDATION_DATA task for a request
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/VALIDATION_DATA/close
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Assign the user to the current task
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/assign-user
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/request-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Update request information
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
PUT {{backend-url}}/api/requests/{{latestRequestId}}/request-information
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

{
  "requestDate": "2025-01-01"
}

### Create basic E2E cases
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
POST {{backend-url}}/e2e/create-basic-cases
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json
