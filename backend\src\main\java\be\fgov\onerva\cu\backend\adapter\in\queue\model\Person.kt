package be.fgov.onerva.cu.backend.adapter.`in`.queue.model

data class PersonUpdated(
    val id: Long,
    val correlationId: String,
    val ssin: String,
    val success: Boolean,
    val names: String,
    val additionalProperties: Map<String, Any>? = mapOf(),
)

open class BasePersonPayload(
    open val id: String,
    open val type: String,
)

data class PersonUpdatedPayload(
    val data: PersonUpdated,
    override val id: String,
    override val type: String,
    val source: String,
    val specversion: String,
    val datacontenttype: String,
    val dataschema: String?,
    val subject: String?,
    val time: String,
    val additionalProperties: Map<String, Any>? = mapOf(),
) : BasePersonPayload(id, type)
