// frontend/projects/cu/src/lib/webcomponents/common/base-web-component.ts

import { Directive, effect, EventEmitter, Input, OnDestroy, OnInit, Output, signal } from '@angular/core';
import { HttpHeaders } from '@angular/common/http';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ConfigService } from '../../config/config.service';
import { GeoLookupService } from '../../http/geo-lookup.service';

@Directive()
export abstract class BaseWebComponent implements OnInit, OnDestroy {
    readonly destroy$: Subject<void> = new Subject<void>();

    // Common signals
    readonly _requestIdSignal = signal<string | undefined>(undefined);
    readonly _tokenSignal = signal<string | undefined>(undefined);
    readonly _headersReady = signal<boolean>(false);
    readonly headers = signal<HttpHeaders>(new HttpHeaders());

    // Properties from inputs
    private _requestId!: string;
    private _taskId!: string;
    private _language!: string;
    private _token!: string;
    private _task!: string;
    private _status!: string;
    private _taskStep!: string;
    private _decisionType!: string;
    private _decisionBarema!: string;

    protected woTaskIsCalled: boolean = false;

    @Output() protected action = new EventEmitter<any>();

    // Common getters and setters
    @Input() set requestId(value: string) {
        this._requestId = value;
        this._requestIdSignal.set(value);
    }

    get requestId(): string {
        return this._requestId;
    }

    @Input() set taskId(value: string) {
        this._taskId = value;
    }

    get taskId(): string {
        return this._taskId;
    }

    @Input() set language(value: string) {
        this._language = value;
    }

    get language(): string {
        return this._language;
    }

    @Input() set token(value: string) {
        this._token = value;
        this._tokenSignal.set(value);
    }

    get token(): string {
        return this._token;
    }

    @Input() set task(value: string) {
        if (value) {
            try {
                this._task = JSON.parse(value);
            } catch (error) {
                this._task = value;
            }
        } else {
            this._task = value;
        }
        console.log("task from WAVE: " + value)
    }

    get task(): any {
        return this._task;
    }

    @Input() set status(value: string) { // &taskStep=${Task.taskStep}
        this._status = value;
        console.log("Status from WAVE: " + value)
    }

    get status(): string {
        return this._status;
    }


    @Input() set taskStep(value: string) { // &taskStep=${Task.taskStep}
        this._taskStep = value;
        console.log("taskStep from WAVE: " + value)
    }

    get taskStep(): string {
        return this._taskStep;
    }

    @Input() set decisionType(value: string) {
        this._decisionType = value;
        console.log("DecisionType from WAVE: " + value)
    }

    get decisionType(): string {
        return this._decisionType;
    }

    @Input() set decisionBarema(value: string) {
        this._decisionBarema = value;
        console.log("DecisionBarema from WAVE: " + value)
    }

    get decisionBarema(): string {
        return this._decisionBarema;
    }

    constructor(
        protected readonly translate: TranslateService,
        protected readonly geoLookupService: GeoLookupService,
        protected readonly configService: ConfigService
    ) {
        if (!this.configService.isOnWO()) {
            this._headersReady.set(true);
        }

        this.translate.setDefaultLang('NL');
        this.setupEffects();
    }

    ngOnInit(): void {
        this.action.emit({ messageType: 'appReady' });

        // Set language if available
        const currentLang = this.language;
        if (currentLang) {
            this.translate.use(currentLang.toUpperCase());
        }
    }

    ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    protected setupEffects(): void {
        // Request ID effect
        effect(() => {
            const currentRequestId = this._requestIdSignal();
            const currentToken = this._tokenSignal();
            const headersReady = this._headersReady();

            if (!this.configService.isOnWO()) {
                if (currentRequestId) {
                    setTimeout(() => this.getWoTaskById(currentRequestId), 0);
                }
            } else if (currentRequestId && currentToken && headersReady && !this.woTaskIsCalled) {
                setTimeout(() => {
                    this.woTaskIsCalled = true;
                    this.getWoTaskById(currentRequestId);
                }, 0);
            }
        });

        // Token effect
        effect(() => {
            const currentToken = this._tokenSignal();
            if (currentToken) {
                this.initializeBaseServices(currentToken);
                this.initializeComponentServices(currentToken);
            }
        });

        // Language effect
        effect(() => {
            const currentLang = this.language;
            if (currentLang) {
                this.translate.use(currentLang.toUpperCase());
            }
        });
    }

    protected initializeBaseServices(token: string): void {
        this.geoLookupService.initializeService(token);

        const newHeaders = new HttpHeaders()
            .set('Authorization', `Bearer ${token}`)
            .set('Content-Type', 'application/json');
        this.headers.set(newHeaders);
        this._headersReady.set(true);
    }

    protected abstract initializeComponentServices(token: string): void;

    protected abstract getWoTaskById(requestId: string): Promise<void>;
}