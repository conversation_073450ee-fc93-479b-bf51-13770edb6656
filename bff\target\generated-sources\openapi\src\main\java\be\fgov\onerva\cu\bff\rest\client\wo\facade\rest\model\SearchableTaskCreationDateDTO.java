/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * This field will be replace with the SearchableRange in the future, so the &#39;format: date&#39; will disappear
 * @deprecated
 */
@Deprecated
@JsonPropertyOrder({
  SearchableTaskCreationDateDTO.JSON_PROPERTY_FROM,
  SearchableTaskCreationDateDTO.JSON_PROPERTY_TO
})
@JsonTypeName("SearchableTaskCreationDate")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SearchableTaskCreationDateDTO {
  public static final String JSON_PROPERTY_FROM = "from";
  private LocalDate from;

  public static final String JSON_PROPERTY_TO = "to";
  private LocalDate to;

  public SearchableTaskCreationDateDTO() {
  }

  public SearchableTaskCreationDateDTO from(LocalDate from) {
    
    this.from = from;
    return this;
  }

  /**
   * Get from
   * @return from
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getFrom() {
    return from;
  }


  @JsonProperty(JSON_PROPERTY_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFrom(LocalDate from) {
    this.from = from;
  }

  public SearchableTaskCreationDateDTO to(LocalDate to) {
    
    this.to = to;
    return this;
  }

  /**
   * Get to
   * @return to
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getTo() {
    return to;
  }


  @JsonProperty(JSON_PROPERTY_TO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTo(LocalDate to) {
    this.to = to;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SearchableTaskCreationDateDTO searchableTaskCreationDate = (SearchableTaskCreationDateDTO) o;
    return Objects.equals(this.from, searchableTaskCreationDate.from) &&
        Objects.equals(this.to, searchableTaskCreationDate.to);
  }

  @Override
  public int hashCode() {
    return Objects.hash(from, to);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SearchableTaskCreationDateDTO {\n");
    sb.append("    from: ").append(toIndentedString(from)).append("\n");
    sb.append("    to: ").append(toIndentedString(to)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

