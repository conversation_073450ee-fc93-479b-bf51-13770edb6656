import {FormatBaremaPipe} from './format-barema.pipe';

describe('FormatBaremaPipe', () => {
    let pipe: FormatBaremaPipe;

    beforeEach(() => {
        pipe = new FormatBaremaPipe();
    });

    it('should create an instance', () => {
        expect(pipe).toBeTruthy();
    });

    it('should return an empty string if input is undefined', () => {
        const result = pipe.transform(undefined);
        expect(result).toBe('');
    });

    it('should return an empty string if input is an empty string', () => {
        const result = pipe.transform('');
        expect(result).toBe('');
    });

    it('should return the input if it does not start with two digits', () => {
        const input = 'AB123';
        const result = pipe.transform(input);
        expect(result).toBe(input);
    });

    it('should format the input as XX/XX if it starts with two digits', () => {
        const input = '12345';
        const result = pipe.transform(input);
        expect(result).toBe('12/345');
    });

    it('should format the input correctly if it starts with more than two digits', () => {
        const input = '123456789';
        const result = pipe.transform(input);
        expect(result).toBe('12/3456789');
    });

    it('should return the input if it starts with one digit', () => {
        const input = '1AB123';
        const result = pipe.transform(input);
        expect(result).toBe(input);
    });

    it('should return the input if it starts with special characters', () => {
        const input = '!@12345';
        const result = pipe.transform(input);
        expect(result).toBe(input);
    });

    it('should return the input if it is null', () => {
        const result = pipe.transform(undefined);
        expect(result).toBe('');
    });
});
