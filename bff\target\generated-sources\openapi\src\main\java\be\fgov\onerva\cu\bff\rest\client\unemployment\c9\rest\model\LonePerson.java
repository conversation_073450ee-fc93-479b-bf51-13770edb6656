/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.LonePersonCopyJoin;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * <PERSON><PERSON><PERSON>
 */
@JsonPropertyOrder({
  LonePerson.JSON_PROPERTY_PAY_COST_OF_MAINTENANCE,
  LonePerson.JSON_PROPERTY_DIVORCED_AND_PARTNER_RECEIVES_PART_OF_INCOME,
  LonePerson.JSON_PROPERTY_REMARKS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class LonePerson {
  public static final String JSON_PROPERTY_PAY_COST_OF_MAINTENANCE = "payCostOfMaintenance";
  private LonePersonCopyJoin payCostOfMaintenance;

  public static final String JSON_PROPERTY_DIVORCED_AND_PARTNER_RECEIVES_PART_OF_INCOME = "divorcedAndPartnerReceivesPartOfIncome";
  private LonePersonCopyJoin divorcedAndPartnerReceivesPartOfIncome;

  public static final String JSON_PROPERTY_REMARKS = "remarks";
  private String remarks;

  public LonePerson() {
  }

  public LonePerson payCostOfMaintenance(LonePersonCopyJoin payCostOfMaintenance) {
    
    this.payCostOfMaintenance = payCostOfMaintenance;
    return this;
  }

  /**
   * Get payCostOfMaintenance
   * @return payCostOfMaintenance
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAY_COST_OF_MAINTENANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LonePersonCopyJoin getPayCostOfMaintenance() {
    return payCostOfMaintenance;
  }


  @JsonProperty(JSON_PROPERTY_PAY_COST_OF_MAINTENANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPayCostOfMaintenance(LonePersonCopyJoin payCostOfMaintenance) {
    this.payCostOfMaintenance = payCostOfMaintenance;
  }

  public LonePerson divorcedAndPartnerReceivesPartOfIncome(LonePersonCopyJoin divorcedAndPartnerReceivesPartOfIncome) {
    
    this.divorcedAndPartnerReceivesPartOfIncome = divorcedAndPartnerReceivesPartOfIncome;
    return this;
  }

  /**
   * Get divorcedAndPartnerReceivesPartOfIncome
   * @return divorcedAndPartnerReceivesPartOfIncome
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DIVORCED_AND_PARTNER_RECEIVES_PART_OF_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LonePersonCopyJoin getDivorcedAndPartnerReceivesPartOfIncome() {
    return divorcedAndPartnerReceivesPartOfIncome;
  }


  @JsonProperty(JSON_PROPERTY_DIVORCED_AND_PARTNER_RECEIVES_PART_OF_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDivorcedAndPartnerReceivesPartOfIncome(LonePersonCopyJoin divorcedAndPartnerReceivesPartOfIncome) {
    this.divorcedAndPartnerReceivesPartOfIncome = divorcedAndPartnerReceivesPartOfIncome;
  }

  public LonePerson remarks(String remarks) {
    
    this.remarks = remarks;
    return this;
  }

  /**
   * Get remarks
   * @return remarks
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMARKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemarks() {
    return remarks;
  }


  @JsonProperty(JSON_PROPERTY_REMARKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemarks(String remarks) {
    this.remarks = remarks;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LonePerson lonePerson = (LonePerson) o;
    return Objects.equals(this.payCostOfMaintenance, lonePerson.payCostOfMaintenance) &&
        Objects.equals(this.divorcedAndPartnerReceivesPartOfIncome, lonePerson.divorcedAndPartnerReceivesPartOfIncome) &&
        Objects.equals(this.remarks, lonePerson.remarks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(payCostOfMaintenance, divorcedAndPartnerReceivesPartOfIncome, remarks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LonePerson {\n");
    sb.append("    payCostOfMaintenance: ").append(toIndentedString(payCostOfMaintenance)).append("\n");
    sb.append("    divorcedAndPartnerReceivesPartOfIncome: ").append(toIndentedString(divorcedAndPartnerReceivesPartOfIncome)).append("\n");
    sb.append("    remarks: ").append(toIndentedString(remarks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

