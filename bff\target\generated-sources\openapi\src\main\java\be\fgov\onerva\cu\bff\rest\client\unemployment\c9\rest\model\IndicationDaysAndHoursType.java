/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * IndicationDaysAndHoursType
 */
@JsonPropertyOrder({
  IndicationDaysAndHoursType.JSON_PROPERTY_DAYS_INDICATOR_ABOUT_WORK_SCHEDULE,
  IndicationDaysAndHoursType.JSON_PROPERTY_HOURS_NUMBER_ABOUT_WORK_SCHEDULE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class IndicationDaysAndHoursType {
  public static final String JSON_PROPERTY_DAYS_INDICATOR_ABOUT_WORK_SCHEDULE = "daysIndicatorAboutWorkSchedule";
  private String daysIndicatorAboutWorkSchedule;

  public static final String JSON_PROPERTY_HOURS_NUMBER_ABOUT_WORK_SCHEDULE = "hoursNumberAboutWorkSchedule";
  private String hoursNumberAboutWorkSchedule;

  public IndicationDaysAndHoursType() {
  }

  public IndicationDaysAndHoursType daysIndicatorAboutWorkSchedule(String daysIndicatorAboutWorkSchedule) {
    
    this.daysIndicatorAboutWorkSchedule = daysIndicatorAboutWorkSchedule;
    return this;
  }

  /**
   * Get daysIndicatorAboutWorkSchedule
   * @return daysIndicatorAboutWorkSchedule
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DAYS_INDICATOR_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDaysIndicatorAboutWorkSchedule() {
    return daysIndicatorAboutWorkSchedule;
  }


  @JsonProperty(JSON_PROPERTY_DAYS_INDICATOR_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDaysIndicatorAboutWorkSchedule(String daysIndicatorAboutWorkSchedule) {
    this.daysIndicatorAboutWorkSchedule = daysIndicatorAboutWorkSchedule;
  }

  public IndicationDaysAndHoursType hoursNumberAboutWorkSchedule(String hoursNumberAboutWorkSchedule) {
    
    this.hoursNumberAboutWorkSchedule = hoursNumberAboutWorkSchedule;
    return this;
  }

  /**
   * Get hoursNumberAboutWorkSchedule
   * @return hoursNumberAboutWorkSchedule
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HOURS_NUMBER_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getHoursNumberAboutWorkSchedule() {
    return hoursNumberAboutWorkSchedule;
  }


  @JsonProperty(JSON_PROPERTY_HOURS_NUMBER_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHoursNumberAboutWorkSchedule(String hoursNumberAboutWorkSchedule) {
    this.hoursNumberAboutWorkSchedule = hoursNumberAboutWorkSchedule;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    IndicationDaysAndHoursType indicationDaysAndHoursType = (IndicationDaysAndHoursType) o;
    return Objects.equals(this.daysIndicatorAboutWorkSchedule, indicationDaysAndHoursType.daysIndicatorAboutWorkSchedule) &&
        Objects.equals(this.hoursNumberAboutWorkSchedule, indicationDaysAndHoursType.hoursNumberAboutWorkSchedule);
  }

  @Override
  public int hashCode() {
    return Objects.hash(daysIndicatorAboutWorkSchedule, hoursNumberAboutWorkSchedule);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IndicationDaysAndHoursType {\n");
    sb.append("    daysIndicatorAboutWorkSchedule: ").append(toIndentedString(daysIndicatorAboutWorkSchedule)).append("\n");
    sb.append("    hoursNumberAboutWorkSchedule: ").append(toIndentedString(hoursNumberAboutWorkSchedule)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

