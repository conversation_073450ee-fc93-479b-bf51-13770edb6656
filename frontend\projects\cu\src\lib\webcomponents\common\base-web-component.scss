@use "@onemrvapublic/design-system-theme" as theme;
@use "@onemrvapublic/design-system" as onemrvaMat;

@mixin baseCuWebComponent-theme {
  @include theme.base();
  @include onemrvaMat.sticker(theme.$onemrva-theme);
  @include onemrvaMat.panel(theme.$onemrva-theme);
  @include theme.button-theme(theme.$onemrva-theme);
  @include theme.links-theme(theme.$onemrva-theme);
  @include theme.icon-theme(theme.$onemrva-theme);
  @include theme.form-field-theme(theme.$onemrva-theme);
  @include theme.input-theme(theme.$onemrva-theme);
  @include theme.datepicker-theme(theme.$onemrva-theme);
  @include theme.radio-theme(theme.$onemrva-theme);
  @include onemrvaMat.toast(theme.$onemrva-theme);
  @include onemrvaMat.spinner(theme.$onemrva-theme);

  @include onemrvaMat.messageBox(theme.$onemrva-theme);
}

@mixin commonCuWebComponent-styles {
  .S24Action {
    display: inline-block;
    text-align: right;
    float: right;
  }

  .question-result-icon {
    margin-left: 4px;
  }

  mat-form-field {
    margin-bottom: 32px;
    margin-right: 16px;
  }

  .mat-expansion-panel-body {
    border: none;
    margin-left: 24px;
  }

  mat-radio-button {
    margin-left: -10px;
    margin-right: 10px
  }

  .actions {
    margin-left: 4px;
    margin-top: 48px;
  }
  .actions button {
    margin: 0 4px
  }
}

@mixin commonExpansionPanelAsRegularTable-styles {
  // expansion-panel-as-regular-table a mat-accordion mat-expansion-panel without lines and borders

  .expansion-panel-as-regular-table .not-expandable-header .mat-expansion-indicator { /* no icon if panel is not expandable */
    visibility: hidden;
  }

  .mat-accordion.expansion-panel-as-regular-table .mat-expansion-panel-body, .mat-accordion.expansion-panel-as-regular-table .mat-expansion-panel-header, .not-expandable-header { /* no border around expandable */
    border: none;
  }

  .mat-accordion.expansion-panel-as-regular-table .mat-expansion-panel-header-title:after { /* no line in panel title */
    content: none;
  }

  .expansion-panel-as-regular-table mat-expansion-panel:nth-of-type(odd) {
    background-color: lavender;
  }

  .expansion-panel-as-regular-table .mat-expanded {
    border: 1px solid #D7D5ED;
  }

  .expansion-panel-as-regular-table mat-expansion-panel:nth-of-type(odd).mat-expanded {
    border: none;
  }

  .expansion-panel-as-regular-table .mat-expansion-panel-header-description {
    align-items: center;
    flex: calc(20% - 12px);
    line-height: 3em;
  }

  .expansion-panel-as-regular-table .mat-expansion-panel {
    padding: 0 16px;
    margin: 0;
  }

  .expansion-panel-as-regular-table .not-expandable-header {
    cursor: default;
    pointer-events: none;
  }

  .expansion-panel-as-regular-table .not-expandable-header:hover {
    background-color: lavender;
  }

  .onemrva-theme .mat-expansion-indicator::after { // so the expansion arrows do not change position
    width: inherit !important;
  }

  .mat-expansion-panel-header {
    height: min-content !important;
    min-height: var(--mat-expansion-header-collapsed-state-height);
  }
}