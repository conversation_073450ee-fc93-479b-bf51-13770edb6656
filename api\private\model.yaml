components:
  schemas:
    RequestBasicInfoResponse:
      type: object
      required:
        - requestDate
        - firstName
        - lastName
        - ssin
        - c9Id
        - documentType
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted
        firstName:
          type: string
          description: The first name of the person
        lastName:
          type: string
          description: The last name of the person
        ssin:
          type: string
          description: The Social Security Identification Number
        introductionDate:
          type: string
          format: date
          description: The introduction date of the request
        dateValid:
          type: string
          format: date
          description: The valid date of the request
        annexes:
          type: array
          items:
            $ref: '#/components/schemas/Annex'
        decisionType:
          type: string
          description: The decision type of the request
          enum:
            - C2Y
            - C2N
            - C2F
            - C2P
            - C51
            - C9B
            - C2
            - C9NA
        decisionBarema:
          type: string
        c9Id:
          type: string
          description: The ID of the related C9 request
        documentType:
          type: string
          description: The type of identity document used for the request
          enum:
            - ELECTRONIC
            - PAPER
        pushbackStatus:
          type: string
          description: The pushback status of the request
          enum:
            - PENDING
            - OK
            - NOK

    Address:
      type: object
      required:
        - country
        - street
        - houseNumber
        - zipCode
        - city
      properties:
        country:
          type: string
          description: The country of residence
        street:
          type: string
          description: The street name
        houseNumber:
          type: string
          description: The house number
        boxNumber:
          type: string
          description: The box number (optional)
        zipCode:
          type: string
          description: The postal/zip code
        city:
          type: string
          description: The city name

    # Field source object to track source for individual fields
    FieldSource:
      type: object
      properties:
        fieldName:
          type: string
          description: The name of the field this source applies to
        source:
          $ref: '#/components/schemas/ExternalSource'

    # Updated schema with field sources
    UpdateCitizenInformationRequest:
      type: object
      required:
        - birthDate
        - address
        - fieldSources
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationality:
          type: string
          description: The nationality of the employee
        address:
          $ref: '#/components/schemas/Address'

    CitizenInformationDetailResponse:
      type: object
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationality:
          type: string
          description: The nationality of the employee
        address:
          $ref: '#/components/schemas/Address'
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'

    ModeOfPaymentFields:
      type: object
      properties:
        otherPersonName:
          type: string
        iban:
          type: string
        bic:
          type: string

    UpdateModeOfPaymentRequest:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'

    ModeOfPaymentDetailResponse:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            fieldSources:
              type: array
              description: Sources for individual fields
              items:
                $ref: '#/components/schemas/FieldSource'

    UnionContributionFields:
      type: object
      properties:
        authorized:
          type: boolean
        effectiveDate:
          type: string
          format: date
          description: The effective date of the union contribution (format YYYY-MM-DD)

    UpdateUnionContributionRequest:
      allOf:
        - $ref: '#/components/schemas/UnionContributionFields'

    UnionContributionDetailResponse:
      allOf:
        - $ref: '#/components/schemas/UnionContributionFields'
        - type: object
          properties:
            fieldSources:
              type: array
              description: Sources for individual fields
              items:
                $ref: '#/components/schemas/FieldSource'

    AnnexType:
      type: string
      enum:
        - SCANNED_DOCUMENTS
        - EC1

    ExternalSource:
      type: string
      enum:
        - C1
        - ONEM
        - AUTHENTIC_SOURCES

    Annex:
      type: object
      description: Represents an attachment of the C9 and its display url
      properties:
        type:
          $ref: '#/components/schemas/AnnexType'
        url:
          type: string

    WaveTaskResponse:
      type: object
      required:
        - processId
        - taskId
        - status
        - waveUrl
      properties:
        processId:
          type: string
        taskId:
          type: string
        status:
          $ref: '#/components/schemas/WaveTaskStatus'
        waveUrl:
          type: string
          readOnly: true

    WaveTaskStatus:
      type: string
      enum:
        - OPEN
        - CLOSED
        - WAITING
        - DELETED

    KeycloakConfigResponse:
      type: object
      title: KeycloakConfigResponse
      required:
        - config
      properties:
        config:
          required:
            - clientId
            - url
            - realm
          type: object
          properties:
            url:
              type: string
              description: Base uri of the realm
              example: 'http://host.docker.internal:8082/auth/'
            clientId:
              type: string
              description: The id of the frontend client
            realm:
              type: string
              description: The keycloak realm
        initOptions:
          required:
            - redirectUri
            - checkLoginIframe
            - onLoad
          type: object
          properties:
            redirectUri:
              type: string
              description: The uri where keycloak must return after login
              example: "http://localhost:4300/"
            checkLoginIframe:
              type: boolean
            onLoad:
              type: string
              example: check-sso

    FlagsmithConfigResponse:
      type: object
      title: FlagsmithConfigResponse
      required:
        - environmentID
        - identity
        - api
      properties:
        environmentID:
          type: string
        identity:
          type: string
        api:
          type: string

    Environment:
      type: string
      enum:
        - LOCAL
        - TEST
        - VAL
        - PROD

    RequestInformationResponse:
      type: object
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted

    UpdateRequestInformationRequest:
      type: object
      properties:
        requestDate:
          type: string
          format: date
          description: The date when the request was submitted

    CitizenInformationDetailNullableResponse:
      type: object
      properties:
        birthDate:
          type: string
          format: date
          description: The birth date of the employee (format YYYY-MM-DD)
        nationality:
          type: string
          description: The nationality of the employee
        address:
          $ref: '#/components/schemas/AddressNullable'
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'

    HistoricalCitizenAuthenticSourcesResponse:
      allOf:
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationality:
              type: string
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            valueDate:
              type: string
              format: date
              description: The value date for the authentic source

    HistoricalCitizenOnemResponse:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationality:
              type: string
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            addressValueDate:
              type: string
              format: date
              description: The value date for the bank account
            bankAccountValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionContributionValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionDue:
              $ref: '#/components/schemas/UnionContributionFields'

    HistoricalCitizenC1Response:
      allOf:
        - $ref: '#/components/schemas/ModeOfPaymentFields'
        - type: object
          properties:
            firstName:
              type: string
              description: The first name of the person
            lastName:
              type: string
              description: The last name of the person
            birthDate:
              type: string
              format: date
              description: The birth date of the employee (format YYYY-MM-DD)
            nationality:
              type: string
              description: The nationality of the employee
            address:
              $ref: '#/components/schemas/AddressNullable'
            addressValueDate:
              type: string
              format: date
              description: The value date for the bank account
            bankAccountValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionContributionValueDate:
              type: string
              format: date
              description: The value date for the bank account
            unionDue:
              $ref: '#/components/schemas/UnionContributionFields'

    HistoricalBaremaResponse:
      type: object
      properties:
        barema:
          type: string
        article:
          type: string

    AddressNullable:
      type: object
      properties:
        country:
          type: string
          description: The country of residence
        street:
          type: string
          description: The street name
        houseNumber:
          type: string
          description: The house number
        boxNumber:
          type: string
          description: The box number (optional)
        zipCode:
          type: string
          description: The postal/zip code
        city:
          type: string
          description: The city name
    SelectFieldSourcesRequest:
      type: object
      required:
        - fieldSources
      properties:
        fieldSources:
          type: array
          description: Sources for individual fields
          items:
            $ref: '#/components/schemas/FieldSource'
          example:
            - fieldName: "birthDate"
              source: "AUTHENTIC_SOURCES"
            - fieldName: "nationality"
              source: "C1"
            - fieldName: "address.street"
              source: "ONEM"
