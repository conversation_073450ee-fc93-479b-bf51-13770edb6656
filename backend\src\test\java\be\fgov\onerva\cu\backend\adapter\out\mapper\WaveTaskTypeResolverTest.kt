package be.fgov.onerva.cu.backend.adapter.out.mapper

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import io.mockk.junit5.MockKExtension

@ExtendWith(MockKExtension::class)
class WaveTaskTypeResolverTest {

    private val resolver = WaveTaskTypeResolver()

    @Test
    fun `resolve should return correct class for CHANGE_PERSONAL_DATA_CAPTURE`() {
        // When
        val result = resolver.resolve("CHANGE_PERSONAL_DATA_CAPTURE")

        // Then
        assertThat(result).isEqualTo(ChangePersonalDataCaptureWaveTaskEntity::class.java)
    }

    @Test
    fun `resolve should return correct class for VALIDATION_DATA`() {
        // When
        val result = resolver.resolve("VALIDATION_DATA")

        // Then
        assertThat(result).isEqualTo(ChangePersonalDataValidateWaveTaskEntity::class.java)
    }

    @Test
    fun `resolve should throw IllegalArgumentException for unknown type`() {
        // When & Then
        assertThatThrownBy { resolver.resolve("UNKNOWN_TYPE") }
            .isInstanceOf(IllegalArgumentException::class.java)
            .hasMessage("Unknown task type: UNKNOWN_TYPE")
    }
}