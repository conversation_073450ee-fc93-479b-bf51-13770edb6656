/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * DepartmentType
 */
@JsonPropertyOrder({
  DepartmentType.JSON_PROPERTY_DEPARTMENT_NAME,
  DepartmentType.JSON_PROPERTY_DEPARTMENT_RECEPTION_NBR
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class DepartmentType {
  public static final String JSON_PROPERTY_DEPARTMENT_NAME = "departmentName";
  private String departmentName;

  public static final String JSON_PROPERTY_DEPARTMENT_RECEPTION_NBR = "departmentReceptionNbr";
  private String departmentReceptionNbr;

  public DepartmentType() {
  }

  public DepartmentType departmentName(String departmentName) {
    
    this.departmentName = departmentName;
    return this;
  }

  /**
   * Get departmentName
   * @return departmentName
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DEPARTMENT_NAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDepartmentName() {
    return departmentName;
  }


  @JsonProperty(JSON_PROPERTY_DEPARTMENT_NAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDepartmentName(String departmentName) {
    this.departmentName = departmentName;
  }

  public DepartmentType departmentReceptionNbr(String departmentReceptionNbr) {
    
    this.departmentReceptionNbr = departmentReceptionNbr;
    return this;
  }

  /**
   * Get departmentReceptionNbr
   * @return departmentReceptionNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DEPARTMENT_RECEPTION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDepartmentReceptionNbr() {
    return departmentReceptionNbr;
  }


  @JsonProperty(JSON_PROPERTY_DEPARTMENT_RECEPTION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDepartmentReceptionNbr(String departmentReceptionNbr) {
    this.departmentReceptionNbr = departmentReceptionNbr;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DepartmentType departmentType = (DepartmentType) o;
    return Objects.equals(this.departmentName, departmentType.departmentName) &&
        Objects.equals(this.departmentReceptionNbr, departmentType.departmentReceptionNbr);
  }

  @Override
  public int hashCode() {
    return Objects.hash(departmentName, departmentReceptionNbr);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DepartmentType {\n");
    sb.append("    departmentName: ").append(toIndentedString(departmentName)).append("\n");
    sb.append("    departmentReceptionNbr: ").append(toIndentedString(departmentReceptionNbr)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

