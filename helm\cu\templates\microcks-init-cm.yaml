{{- if and .Values.microcks.enabled .Values.microcks.microcksInit.fromConfigMap.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: microcks-init-cm
data:
  secondary-person-examples: |-
    {{- .Files.Get "files/microcks/secondary-person-examples.yaml"  | printf "%s" | nindent 4 }}
  secondary-c9-examples: |-
    {{- .Files.Get "files/microcks/secondary-c9-examples.yaml"  | printf "%s" | nindent 4 }}
  secondary-barema-examples: |-
    {{- .Files.Get "files/microcks/secondary-barema-examples.yaml"  | printf "%s" | nindent 4 }}
  secondary-user-examples: |-
    {{- .Files.Get "files/microcks/secondary-user-examples.yaml"  | printf "%s" | nindent 4 }}
  secondary-register-examples: |-
    {{- .Files.Get "files/microcks/secondary-registry-examples.yaml"  | printf "%s" | nindent 4 }}
{{- end }}