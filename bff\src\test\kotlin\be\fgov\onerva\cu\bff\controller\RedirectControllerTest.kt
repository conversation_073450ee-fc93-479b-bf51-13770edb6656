package be.fgov.onerva.cu.bff.controller

import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.MediaType
import be.fgov.onerva.cu.bff.adapter.out.RedirectService
import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RedirectControllerTest {

    @MockK
    private lateinit var redirectService: RedirectService

    @InjectMockKs
    private lateinit var controller: RedirectController

    @Test
    fun `getC51Redirect should return redirect URL from service`() {
        // Given
        val requestId = UUID.randomUUID()
        val expectedUrl = "https://example.com/redirect"
        every { redirectService.getC51Link(requestId) } returns expectedUrl

        // When
        val response = controller.getC51Redirect(requestId)

        // Then
        assertThat(response.statusCode.is2xxSuccessful).isTrue()
        assertThat(response.body).isEqualTo(expectedUrl)
    }

    @Test
    fun `openS24Session should return response from service`() {
        // Given
        val requestId = UUID.randomUUID()
        every { redirectService.openS24Session(requestId) } returns Unit

        // When
        val response = controller.openS24Session(requestId)

        // Then
        assertThat(response.statusCode.is2xxSuccessful).isTrue()
        verify(exactly = 1) { redirectService.openS24Session(requestId) }
    }


    @Test
    fun `getRegisRedirect should return URL from service`() {
        // Given
        val requestId = UUID.randomUUID()
        val languageCode = "fr"
        val expectedUrl = "https://regis.example.com?c9Id=C9-123&lang=fr"

        coEvery { redirectService.getRegisRedirectUrl(requestId, languageCode) } returns expectedUrl

        // When
        val result = controller.getRegisRedirect(requestId, languageCode)

        // Then
        assertThat(result.statusCode.value()).isEqualTo(200)
        assertThat(result.headers.contentType).isEqualTo(MediaType.TEXT_PLAIN)
        assertThat(result.body).isEqualTo(expectedUrl)
        coVerify(exactly = 1) { redirectService.getRegisRedirectUrl(requestId, languageCode) }
    }

    @Test
    fun `getRegisRedirect should propagate exceptions from service`() {
        // Given
        val requestId = UUID.randomUUID()
        val languageCode = "fr"
        val errorMessage = "No C9 ID found for request $requestId"

        coEvery { redirectService.getRegisRedirectUrl(requestId, languageCode) } throws
                C9NotFoundException(errorMessage)

        // When/Then
        try {
            controller.getRegisRedirect(requestId, languageCode)
        } catch (e: C9NotFoundException) {
            assertThat(e.message).isEqualTo(errorMessage)
        }

        coVerify(exactly = 1) { redirectService.getRegisRedirectUrl(requestId, languageCode) }
    }
}