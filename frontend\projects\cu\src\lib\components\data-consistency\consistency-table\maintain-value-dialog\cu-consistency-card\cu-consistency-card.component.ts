import {Component, Input, OnInit, ViewEncapsulation} from "@angular/core";
import {MatCardModule} from "@angular/material/card";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {MatDialogModule} from "@angular/material/dialog";
import {CommonModule} from "@angular/common";
import {MatIconModule} from "@angular/material/icon";
import {MatButtonModule} from "@angular/material/button";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatInputModule} from "@angular/material/input";
import {MatGridListModule} from "@angular/material/grid-list";
import {Origin} from "../../../../../model/types";
import {OnemrvaMatSelectableBoxModule} from "@onemrvapublic/design-system/mat-selectable-box";
import {OnemrvaThemeModule} from "@onemrvapublic/design-system-theme";

@Component({
    selector: "lib-cu-consistency-card",
    standalone: true,
    imports: [
        OnemrvaThemeModule,
        MatDialogModule,
        CommonModule,
        MatIconModule,
        MatButtonModule,
        TranslateModule,
        MatFormFieldModule,
        MatInputModule,
        MatCardModule,
        MatGridListModule,
        OnemrvaMatSelectableBoxModule,

    ],
    templateUrl: "./cu-consistency-card.component.html",
    styleUrl: "./cu-consistency-card.component.scss",
    encapsulation: ViewEncapsulation.None,
})
export class CuConsistencyCardComponent implements OnInit {

    @Input() value!: string;
    @Input() origin!: Origin;
    @Input() language!: string;
    @Input() valueDate!: string;

    constructor(readonly translate: TranslateService) {
    }

    ngOnInit() {
        this.translate.use(this.language);
        this.translate.reloadLang(this.language);
    }
}
