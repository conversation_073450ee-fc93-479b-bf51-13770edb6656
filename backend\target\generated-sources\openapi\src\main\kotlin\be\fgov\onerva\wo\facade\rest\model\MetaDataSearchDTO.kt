/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * This is a list where you can describe your search criteria. The logic between each search criteria is 'AND'. The 'value' and the 'valueRange' fields are mutually exclusive.
 *
 * @param code 
 * @param `value` 
 */


data class MetaDataSearchDTO (

    @get:JsonProperty("code")
    val code: kotlin.String? = null,

    @get:JsonProperty("value")
    val `value`: kotlin.collections.List<kotlin.String>? = null

) {


}

