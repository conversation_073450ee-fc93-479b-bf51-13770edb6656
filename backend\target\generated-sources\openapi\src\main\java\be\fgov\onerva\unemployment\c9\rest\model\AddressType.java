/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AddressType
 */
@JsonPropertyOrder({
  AddressType.JSON_PROPERTY_STREET,
  AddressType.JSON_PROPERTY_HOUSE_NBR,
  AddressType.JSON_PROPERTY_POST_BOX,
  AddressType.JSON_PROPERTY_ZIP_CODE,
  AddressType.JSON_PROPERTY_CITY,
  AddressType.JSON_PROPERTY_COUNTRY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class AddressType {
  public static final String JSON_PROPERTY_STREET = "street";
  private String street;

  public static final String JSON_PROPERTY_HOUSE_NBR = "houseNbr";
  private String houseNbr;

  public static final String JSON_PROPERTY_POST_BOX = "postBox";
  private String postBox;

  public static final String JSON_PROPERTY_ZIP_CODE = "zipCode";
  private String zipCode;

  public static final String JSON_PROPERTY_CITY = "city";
  private String city;

  public static final String JSON_PROPERTY_COUNTRY = "country";
  private String country;

  public AddressType() {
  }

  public AddressType street(String street) {
    
    this.street = street;
    return this;
  }

  /**
   * Get street
   * @return street
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getStreet() {
    return street;
  }


  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setStreet(String street) {
    this.street = street;
  }

  public AddressType houseNbr(String houseNbr) {
    
    this.houseNbr = houseNbr;
    return this;
  }

  /**
   * Get houseNbr
   * @return houseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getHouseNbr() {
    return houseNbr;
  }


  @JsonProperty(JSON_PROPERTY_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHouseNbr(String houseNbr) {
    this.houseNbr = houseNbr;
  }

  public AddressType postBox(String postBox) {
    
    this.postBox = postBox;
    return this;
  }

  /**
   * Get postBox
   * @return postBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostBox() {
    return postBox;
  }


  @JsonProperty(JSON_PROPERTY_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostBox(String postBox) {
    this.postBox = postBox;
  }

  public AddressType zipCode(String zipCode) {
    
    this.zipCode = zipCode;
    return this;
  }

  /**
   * Get zipCode
   * @return zipCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getZipCode() {
    return zipCode;
  }


  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setZipCode(String zipCode) {
    this.zipCode = zipCode;
  }

  public AddressType city(String city) {
    
    this.city = city;
    return this;
  }

  /**
   * Get city
   * @return city
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCity(String city) {
    this.city = city;
  }

  public AddressType country(String country) {
    
    this.country = country;
    return this;
  }

  /**
   * Get country
   * @return country
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCountry() {
    return country;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCountry(String country) {
    this.country = country;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddressType addressType = (AddressType) o;
    return Objects.equals(this.street, addressType.street) &&
        Objects.equals(this.houseNbr, addressType.houseNbr) &&
        Objects.equals(this.postBox, addressType.postBox) &&
        Objects.equals(this.zipCode, addressType.zipCode) &&
        Objects.equals(this.city, addressType.city) &&
        Objects.equals(this.country, addressType.country);
  }

  @Override
  public int hashCode() {
    return Objects.hash(street, houseNbr, postBox, zipCode, city, country);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddressType {\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    houseNbr: ").append(toIndentedString(houseNbr)).append("\n");
    sb.append("    postBox: ").append(toIndentedString(postBox)).append("\n");
    sb.append("    zipCode: ").append(toIndentedString(zipCode)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    country: ").append(toIndentedString(country)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

