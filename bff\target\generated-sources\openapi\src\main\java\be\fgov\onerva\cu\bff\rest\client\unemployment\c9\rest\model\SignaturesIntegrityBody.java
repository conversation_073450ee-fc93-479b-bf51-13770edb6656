/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SignaturesIntegrityBody
 */
@JsonPropertyOrder({
  SignaturesIntegrityBody.JSON_PROPERTY_XML_SIGNATURE,
  SignaturesIntegrityBody.JSON_PROPERTY_FORM
})
@JsonTypeName("signatures_integrity_body")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SignaturesIntegrityBody {
  public static final String JSON_PROPERTY_XML_SIGNATURE = "xmlSignature";
  private String xmlSignature;

  public static final String JSON_PROPERTY_FORM = "form";
  private String form;

  public SignaturesIntegrityBody() {
  }

  public SignaturesIntegrityBody xmlSignature(String xmlSignature) {
    
    this.xmlSignature = xmlSignature;
    return this;
  }

  /**
   * Get xmlSignature
   * @return xmlSignature
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_XML_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getXmlSignature() {
    return xmlSignature;
  }


  @JsonProperty(JSON_PROPERTY_XML_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setXmlSignature(String xmlSignature) {
    this.xmlSignature = xmlSignature;
  }

  public SignaturesIntegrityBody form(String form) {
    
    this.form = form;
    return this;
  }

  /**
   * Get form
   * @return form
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getForm() {
    return form;
  }


  @JsonProperty(JSON_PROPERTY_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForm(String form) {
    this.form = form;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SignaturesIntegrityBody signaturesIntegrityBody = (SignaturesIntegrityBody) o;
    return Objects.equals(this.xmlSignature, signaturesIntegrityBody.xmlSignature) &&
        Objects.equals(this.form, signaturesIntegrityBody.form);
  }

  @Override
  public int hashCode() {
    return Objects.hash(xmlSignature, form);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SignaturesIntegrityBody {\n");
    sb.append("    xmlSignature: ").append(toIndentedString(xmlSignature)).append("\n");
    sb.append("    form: ").append(toIndentedString(form)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

