package be.fgov.onerva.cu.bff.adapter.out

import java.time.LocalDate
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.bff.exceptions.S24Exception
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.NavigateMainframeToS24Api
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.InlineResponse2001
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.NavigateS24Body

@Service
class C9NavigateMainframeToS24Service(val c9NavigateMainframeToS24Api: NavigateMainframeToS24Api) {
    fun openS24Session(ssin: String, dateValid: LocalDate) {
        val navigateBody = NavigateS24Body()
        navigateBody.ssin = ssin
        navigateBody.dateValid = dateValid

        val response = c9NavigateMainframeToS24Api.navigateMainframeToS24(navigateBody)
        if (response.status != InlineResponse2001.StatusEnum.SUCCESS) {
            throw S24Exception("Failed to open S24 session for SSIN: $ssin")
        }
    }
}