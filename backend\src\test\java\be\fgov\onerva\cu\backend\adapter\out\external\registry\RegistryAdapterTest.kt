package be.fgov.onerva.cu.backend.adapter.out.external.registry

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException
import be.fgov.onerva.cu.backend.lookup.CityDTO
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.cu.backend.lookup.NationalityDTO
import be.fgov.onerva.cu.backend.lookup.PostalCodeLanguageRegimeDTO
import be.fgov.onerva.cu.backend.lookup.StreetDTO
import be.fgov.onerva.registerproxyservice.api.CitizenApi
import be.fgov.onerva.registerproxyservice.rest.model.Period
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterNationality
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPersonNames
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RegistryAdapterTest {

    companion object {
        private const val TEST_SSIN = "85050599890"
    }

    @MockK
    private lateinit var registryService: CitizenApi

    @MockK
    private lateinit var lookupService: LookupService

    @InjectMockKs
    private lateinit var registryAdapter: RegistryAdapter

    @Test
    fun `getCitizenAddressesBySsin should return enriched citizen info with Dutch language`() {
        // Given
        val registerPerson = RegisterPerson().apply {
            addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod =
                        Period().apply {
                            beginDate = 1503352800000
                            endDate = null
                        }
                    radiated = false
                    cityCode = 21015
                    streetCode = 329
                    postalCode = "1070"
                    houseNumber = "37"
                    boxNumber = "ET01"
                    countryCode = 1
                    regionCode = "9"
                }
            )
            birthdate = LocalDate.of(2000, 1, 1)
            deceaseDate = null
            gender = null
            identitification = 0
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                },
                RegisterPersonNames().apply {
                    firstName = "Deere"
                    seq = 2
                }
            )
            nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            replacedSsin = null
            ssin = TEST_SSIN
            status = null
        }

        // Create proper DTOs without named parameters
        val streetDTO = StreetDTO(
            lookupId = 1,
            code = "1234",
            descNl = "Hoofdstraat",
            descFr = "Rue Principale"
        )

        val cityDTO = CityDTO(
            lookupId = 2,
            code = "1000",
            nisCode = "",
            beginDate = LocalDate.now(),
            descNl = "Brussel",
            descFr = "Bruxelles"
        )

        val languageDTO = PostalCodeLanguageRegimeDTO(
            lookupId = 3,
            code = "N0",
            languageRegimeCode = "N0",
            beginDate = null,
            endDate = null,
            descNl = "antwerpen",
            descFr = "antwerpen"
        )

        val nationalityDTO = NationalityDTO(
            lookupId = 3,
            code = "150",
            descNl = "Belg",
            descFr = "Belge"
        )

        every {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN,
                null, true, false, false, null, true, true, false
            )
        } returns registerPerson
        every { lookupService.getStreetNameByStreetCode(329, 1070) } returns streetDTO
        every { lookupService.lookupPostalCodeLanguageRegime("1070") } returns languageDTO
        every { lookupService.getCityByPostalCode("1070") } returns cityDTO
        every { lookupService.getNationalityByCode("1") } returns nationalityDTO

        // When
        val result = registryAdapter.getRegistryInformationForCitizen(TEST_SSIN)

        // Then
        assertThat(result).isNotNull
        assertThat(result.address.street).isEqualTo("Hoofdstraat")
        assertThat(result.address.city).isEqualTo("Brussel")
        assertThat(result.address.country).isEqualTo("Belg")
        assertThat(result.address.valueDate).isEqualTo(LocalDate.of(2017, 8, 22))
        assertThat(result.nationality).isEqualTo("150")


        verify {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN, null, true, false, false, null, true, true, false
            )
        }
        verify { lookupService.getStreetNameByStreetCode(329, 1070) }
        verify { lookupService.lookupPostalCodeLanguageRegime("1070") }
        verify { lookupService.getCityByPostalCode("1070") }
    }

    @Test
    fun `getCitizenAddressesBySsin should return enriched citizen info with French language`() {
        // Given
        val ssin2 = "85050599890"
        val citizenInfoPageDTO = RegisterPerson().apply {
            addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod = Period().apply {
                        beginDate = 1490565600000
                        endDate = null
                    }
                    radiated = false
                    cityCode = 21015
                    streetCode = 329
                    postalCode = "1070"
                    houseNumber = "37"
                    boxNumber = "ET01"
                    countryCode = 1
                    regionCode = "9"
                }
            )
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                }
            )
            nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            ssin = "85050599890"
            birthdate = LocalDate.of(2000, 1, 1)
        }

        val streetDTO = StreetDTO(
            lookupId = 1,
            code = "1234",
            descNl = "Hoofdstraat",
            descFr = "Rue Principale"
        )

        val cityDTO = CityDTO(
            lookupId = 2,
            code = "1070",
            nisCode = "",
            beginDate = LocalDate.now(),
            descNl = "Brussel",
            descFr = "Bruxelles"
        )

        val languageDTO = PostalCodeLanguageRegimeDTO(
            lookupId = 3,
            code = "F0",
            languageRegimeCode = "F0",
            beginDate = null,
            endDate = null,
            descNl = "brussel",
            descFr = "bruxelles"
        )

        val nationalityDTO = NationalityDTO(
            lookupId = 3,
            code = "150",
            descNl = "Belg",
            descFr = "Belge"
        )

        every {
            registryService.getCitizenInfosBySsin(
                "85050599890",
                null, true, false, false, null, true, true, false
            )
        } returns citizenInfoPageDTO
        every { lookupService.getStreetNameByStreetCode(329, 1070) } returns streetDTO
        every { lookupService.lookupPostalCodeLanguageRegime("1070") } returns languageDTO
        every { lookupService.getCityByPostalCode("1070") } returns cityDTO
        every { lookupService.getNationalityByCode("1") } returns nationalityDTO

        // When
        val result = registryAdapter.getRegistryInformationForCitizen(ssin2)

        // Then
        assertThat(result).isNotNull
        assertThat(result.address.street).isEqualTo("Rue Principale")
        assertThat(result.address.city).isEqualTo("Bruxelles")
        assertThat(result.address.country).isEqualTo("Belge")
        assertThat(result.nationality).isEqualTo("150")
        assertThat(result.birthDate).isEqualTo(LocalDate.of(2000, 1, 1))

        verify {
            registryService.getCitizenInfosBySsin(
                "85050599890", null, true, false, false, null, true, true, false
            )
        }
    }

    @Test
    fun `getCitizenAddressesBySsin should throw CitizenNotFoundException when registry returns null`() {
        // Given
        every {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN,
                null, true, false, false, null, true, true, false
            )
        } returns null

        // When/Then
        assertThrows<CitizenNotFoundException> {
            registryAdapter.getRegistryInformationForCitizen(TEST_SSIN)
        }

        verify {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN, null, true, false, false, null, true, true, false
            )
        }
    }

    @Test
    fun `getCitizenAddressesBySsin should throw CitizenNotFoundException when registerPerson ssin is null`() {
        // Given
        val citizenInfoPageDTO = RegisterPerson().apply {
            addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod = Period().apply {
                        beginDate = 1490565600000
                        endDate = null
                    }
                    postalCode = "1070"
                    houseNumber = "37"
                    countryCode = 1
                }
            )
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                }
            )
            ssin = null // Setting SSIN to null
        }

        every {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN,
                null, true, false, false, null, true, true, false
            )
        } returns citizenInfoPageDTO

        // When/Then
        assertThrows<CitizenNotFoundException> {
            registryAdapter.getRegistryInformationForCitizen(TEST_SSIN)
        }

        verify {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN, null, true, false, false, null, true, true, false
            )
        }
    }

    @Test
    fun `getCitizenAddressesBySsin should handle case when there is no residential address`() {
        // Given
        val citizenInfoPageDTO = RegisterPerson().apply {
            // Empty addresses list - no residential address
            addresses = emptyList()
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                }
            )
            nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            ssin = TEST_SSIN
        }

        every {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN,
                null, true, false, false, null, true, true, false
            )
        } returns citizenInfoPageDTO

        // When/Then
        assertThrows<InvalidExternalDataException> {
            registryAdapter.getRegistryInformationForCitizen(TEST_SSIN)
        }

        verify {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN, null, true, false, false, null, true, true, false
            )
        }
    }

    @Test
    fun `getCitizenAddressesBySsin should handle case when lookupService returns null`() {
        // Given
        val citizenInfoPageDTO = RegisterPerson().apply {
            addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod = Period().apply {
                        beginDate = 1490565600000
                        endDate = null
                    }
                    radiated = false
                    cityCode = 21015
                    streetCode = 329
                    postalCode = "1070"
                    houseNumber = "37"
                    boxNumber = "ET01"
                    countryCode = 1
                    regionCode = "9"
                }
            )
            lastName = "Doe"
            names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                }
            )
            nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            ssin = TEST_SSIN
            birthdate = LocalDate.of(2000, 1, 1)
        }

        every {
            registryService.getCitizenInfosBySsin(
                TEST_SSIN,
                null, true, false, false, null, true, true, false
            )
        } returns citizenInfoPageDTO

        // Mock lookup services to return null
        every { lookupService.getStreetNameByStreetCode(329, 1070) } returns null
        every { lookupService.lookupPostalCodeLanguageRegime("1070") } returns null
        every { lookupService.getCityByPostalCode("1070") } returns null
        every { lookupService.getNationalityByCode("1") } returns null

        // When
        assertThrows<CitizenNotFoundException> {
            registryAdapter.getRegistryInformationForCitizen(TEST_SSIN)
        }
    }
}
