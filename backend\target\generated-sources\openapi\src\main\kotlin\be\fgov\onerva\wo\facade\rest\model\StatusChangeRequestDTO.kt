/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param reason Reason why you want to change the status of the task.
 */


data class StatusChangeRequestDTO (

    /* Reason why you want to change the status of the task. */
    @get:JsonProperty("reason")
    val reason: kotlin.String

) {


}

