package be.fgov.onerva.cu.bff.adapter.out

import jakarta.servlet.http.HttpServletRequest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.HttpEntity
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.web.client.RestTemplate
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RestTemplateUtilTest {

    @MockK
    private lateinit var restTemplate: RestTemplate

    @InjectMockKs
    private lateinit var restTemplateUtil: RestTemplateUtil

    @Nested
    inner class ExecuteWithAuthTest {

        @Test
        fun `executeWithAuth should call exchange with correct parameters and return response body`() {
            // Given
            val url = "http://example.com/api"
            val method = HttpMethod.GET
            val authHeader = "Bearer token123"
            val responseType = String::class.java
            val responseBody = "Response data"
            val responseEntity = mockk<ResponseEntity<String>>()

            every { responseEntity.body } returns responseBody
            every {
                restTemplate.exchange(
                    url,
                    method,
                    any<HttpEntity<Void>>(),
                    responseType
                )
            } returns responseEntity

            // When
            val result = restTemplateUtil.executeWithAuth<String, Void>(url, method, authHeader, responseType)

            // Then
            assertThat(result).isEqualTo(responseBody)
            verify {
                restTemplate.exchange(
                    url,
                    method,
                    match<HttpEntity<Void>> {
                        it.headers.getFirst("Authorization") == authHeader
                    },
                    responseType
                )
            }
        }

        @Test
        fun `executeWithAuth should handle null auth header`() {
            // Given
            val url = "http://example.com/api"
            val method = HttpMethod.GET
            val responseType = String::class.java
            val responseBody = "Response data"
            val responseEntity = mockk<ResponseEntity<String>>()

            every { responseEntity.body } returns responseBody
            every {
                restTemplate.exchange(
                    url,
                    method,
                    any<HttpEntity<Void>>(),
                    responseType
                )
            } returns responseEntity

            // When
            val result = restTemplateUtil.executeWithAuth<String, Void>(url, method, null, responseType)

            // Then
            assertThat(result).isEqualTo(responseBody)
            verify {
                restTemplate.exchange(
                    url,
                    method,
                    match<HttpEntity<Void>> {
                        it.headers.getFirst("Authorization") == null
                    },
                    responseType
                )
            }
        }

        @Test
        fun `executeWithAuth with request body should call exchange with correct parameters`() {
            // Given
            val url = "http://example.com/api"
            val method = HttpMethod.POST
            val authHeader = "Bearer token123"
            val requestBody = "Request data"
            val responseType = String::class.java
            val responseBody = "Response data"
            val responseEntity = mockk<ResponseEntity<String>>()

            every { responseEntity.body } returns responseBody
            every {
                restTemplate.exchange(
                    url,
                    method,
                    any<HttpEntity<String>>(),
                    responseType
                )
            } returns responseEntity

            // When
            val result =
                restTemplateUtil.executeWithAuth<String, String>(url, method, authHeader, responseType, requestBody)

            // Then
            assertThat(result).isEqualTo(responseBody)
            verify {
                restTemplate.exchange(
                    url,
                    method,
                    match<HttpEntity<String>> {
                        it.headers.getFirst("Authorization") == authHeader &&
                                it.body == requestBody
                    },
                    responseType
                )
            }
        }
    }

    @Nested
    inner class GetForObjectTest {

        @Test
        fun `getForObject should call restTemplate getForObject and return result`() {
            // Given
            val url = "http://example.com/api"
            val responseType = String::class.java
            val responseBody = "Response data"

            every { restTemplate.getForObject(url, responseType) } returns responseBody

            // When
            val result = restTemplateUtil.getForObject(url, responseType)

            // Then
            assertThat(result).isEqualTo(responseBody)
            verify { restTemplate.getForObject(url, responseType) }
        }
    }

    @Nested
    inner class ExecuteWithAuthVoidTest {

        @Test
        fun `executeWithAuth with void return should call exchange with correct parameters`() {
            // Given
            val url = "http://example.com/api"
            val method = HttpMethod.PUT
            val authHeader = "Bearer token123"
            val requestBody = "Request data"
            val responseEntity = mockk<ResponseEntity<Void>>()

            every {
                restTemplate.exchange(
                    url,
                    method,
                    any<HttpEntity<String>>(),
                    Void::class.java
                )
            } returns responseEntity

            // When
            restTemplateUtil.executeWithAuth(url, method, authHeader, requestBody)

            // Then
            verify {
                restTemplate.exchange(
                    url,
                    method,
                    match<HttpEntity<String>> {
                        it.headers.getFirst("Authorization") == authHeader &&
                                it.body == requestBody
                    },
                    Void::class.java
                )
            }
        }
    }

    @Nested
    inner class CaptureAuthorizationHeaderTest {

        @Test
        fun `captureAuthorizationHeader should return authorization header from request`() {
            // Given
            val authHeader = "Bearer token123"
            val request = mockk<HttpServletRequest>()
            val attributes = mockk<ServletRequestAttributes>()

            every { request.getHeader("Authorization") } returns authHeader
            every { attributes.request } returns request

            try {
                RequestContextHolder.setRequestAttributes(attributes)

                // When
                val result = restTemplateUtil.captureAuthorizationHeader()

                // Then
                assertThat(result).isEqualTo(authHeader)
            } finally {
                RequestContextHolder.resetRequestAttributes()
            }
        }

        @Test
        fun `captureAuthorizationHeader should return null when no request attributes`() {
            // Given
            RequestContextHolder.resetRequestAttributes()

            // When
            val result = restTemplateUtil.captureAuthorizationHeader()

            // Then
            assertThat(result).isNull()
        }
    }
}