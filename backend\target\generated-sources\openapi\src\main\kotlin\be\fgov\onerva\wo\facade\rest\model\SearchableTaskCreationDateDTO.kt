/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * This field will be replace with the SearchableRange in the future, so the 'format: date' will disappear
 *
 * @param from 
 * @param to 
 */

@Deprecated(message = "This schema is deprecated.")

data class SearchableTaskCreationDateDTO (

    @get:JsonProperty("from")
    val from: java.time.LocalDate? = null,

    @get:JsonProperty("to")
    val to: java.time.LocalDate? = null

) {


}

