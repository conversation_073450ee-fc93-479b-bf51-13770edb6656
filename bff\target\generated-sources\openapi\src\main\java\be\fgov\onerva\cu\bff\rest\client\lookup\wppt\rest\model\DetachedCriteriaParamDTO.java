/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.AliasParamDTO;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.FetchModeParamDTO;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.LockModeParamDTO;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.OrderParamDTO;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.RestrictionsParamDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * DetachedCriteriaParamDTO
 */
@JsonPropertyOrder({
  DetachedCriteriaParamDTO.JSON_PROPERTY_ALIAS,
  DetachedCriteriaParamDTO.JSON_PROPERTY_ALIASES,
  DetachedCriteriaParamDTO.JSON_PROPERTY_ENTITY_OR_CLASS_NAME,
  DetachedCriteriaParamDTO.JSON_PROPERTY_FETCH_MODE,
  DetachedCriteriaParamDTO.JSON_PROPERTY_LOCK_MODE,
  DetachedCriteriaParamDTO.JSON_PROPERTY_ORDERS,
  DetachedCriteriaParamDTO.JSON_PROPERTY_RESTRICTIONS,
  DetachedCriteriaParamDTO.JSON_PROPERTY_RESULT_TRANSFORMER
})
@JsonTypeName("DetachedCriteriaParam")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class DetachedCriteriaParamDTO {
  public static final String JSON_PROPERTY_ALIAS = "alias";
  private String alias;

  public static final String JSON_PROPERTY_ALIASES = "aliases";
  private List<AliasParamDTO> aliases = new ArrayList<>();

  public static final String JSON_PROPERTY_ENTITY_OR_CLASS_NAME = "entityOrClassName";
  private String entityOrClassName;

  public static final String JSON_PROPERTY_FETCH_MODE = "fetchMode";
  private FetchModeParamDTO fetchMode;

  public static final String JSON_PROPERTY_LOCK_MODE = "lockMode";
  private LockModeParamDTO lockMode;

  public static final String JSON_PROPERTY_ORDERS = "orders";
  private List<OrderParamDTO> orders = new ArrayList<>();

  public static final String JSON_PROPERTY_RESTRICTIONS = "restrictions";
  private List<RestrictionsParamDTO> restrictions = new ArrayList<>();

  /**
   * Gets or Sets resultTransformer
   */
  public enum ResultTransformerEnum {
    ALIAS_TO_ENTITY_MAP("ALIAS_TO_ENTITY_MAP"),
    
    DISTINCT_ROOT_ENTITY("DISTINCT_ROOT_ENTITY"),
    
    PROJECTION("PROJECTION"),
    
    ROOT_ENTITY("ROOT_ENTITY");

    private String value;

    ResultTransformerEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static ResultTransformerEnum fromValue(String value) {
      for (ResultTransformerEnum b : ResultTransformerEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_RESULT_TRANSFORMER = "resultTransformer";
  private ResultTransformerEnum resultTransformer;

  public DetachedCriteriaParamDTO() {
  }

  public DetachedCriteriaParamDTO alias(String alias) {
    
    this.alias = alias;
    return this;
  }

  /**
   * Get alias
   * @return alias
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALIAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAlias() {
    return alias;
  }


  @JsonProperty(JSON_PROPERTY_ALIAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAlias(String alias) {
    this.alias = alias;
  }

  public DetachedCriteriaParamDTO aliases(List<AliasParamDTO> aliases) {
    
    this.aliases = aliases;
    return this;
  }

  public DetachedCriteriaParamDTO addAliasesItem(AliasParamDTO aliasesItem) {
    if (this.aliases == null) {
      this.aliases = new ArrayList<>();
    }
    this.aliases.add(aliasesItem);
    return this;
  }

  /**
   * Get aliases
   * @return aliases
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALIASES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<AliasParamDTO> getAliases() {
    return aliases;
  }


  @JsonProperty(JSON_PROPERTY_ALIASES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAliases(List<AliasParamDTO> aliases) {
    this.aliases = aliases;
  }

  public DetachedCriteriaParamDTO entityOrClassName(String entityOrClassName) {
    
    this.entityOrClassName = entityOrClassName;
    return this;
  }

  /**
   * Get entityOrClassName
   * @return entityOrClassName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITY_OR_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEntityOrClassName() {
    return entityOrClassName;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_OR_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntityOrClassName(String entityOrClassName) {
    this.entityOrClassName = entityOrClassName;
  }

  public DetachedCriteriaParamDTO fetchMode(FetchModeParamDTO fetchMode) {
    
    this.fetchMode = fetchMode;
    return this;
  }

  /**
   * Get fetchMode
   * @return fetchMode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FETCH_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public FetchModeParamDTO getFetchMode() {
    return fetchMode;
  }


  @JsonProperty(JSON_PROPERTY_FETCH_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFetchMode(FetchModeParamDTO fetchMode) {
    this.fetchMode = fetchMode;
  }

  public DetachedCriteriaParamDTO lockMode(LockModeParamDTO lockMode) {
    
    this.lockMode = lockMode;
    return this;
  }

  /**
   * Get lockMode
   * @return lockMode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LOCK_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LockModeParamDTO getLockMode() {
    return lockMode;
  }


  @JsonProperty(JSON_PROPERTY_LOCK_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLockMode(LockModeParamDTO lockMode) {
    this.lockMode = lockMode;
  }

  public DetachedCriteriaParamDTO orders(List<OrderParamDTO> orders) {
    
    this.orders = orders;
    return this;
  }

  public DetachedCriteriaParamDTO addOrdersItem(OrderParamDTO ordersItem) {
    if (this.orders == null) {
      this.orders = new ArrayList<>();
    }
    this.orders.add(ordersItem);
    return this;
  }

  /**
   * Get orders
   * @return orders
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ORDERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<OrderParamDTO> getOrders() {
    return orders;
  }


  @JsonProperty(JSON_PROPERTY_ORDERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOrders(List<OrderParamDTO> orders) {
    this.orders = orders;
  }

  public DetachedCriteriaParamDTO restrictions(List<RestrictionsParamDTO> restrictions) {
    
    this.restrictions = restrictions;
    return this;
  }

  public DetachedCriteriaParamDTO addRestrictionsItem(RestrictionsParamDTO restrictionsItem) {
    if (this.restrictions == null) {
      this.restrictions = new ArrayList<>();
    }
    this.restrictions.add(restrictionsItem);
    return this;
  }

  /**
   * Get restrictions
   * @return restrictions
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESTRICTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<RestrictionsParamDTO> getRestrictions() {
    return restrictions;
  }


  @JsonProperty(JSON_PROPERTY_RESTRICTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRestrictions(List<RestrictionsParamDTO> restrictions) {
    this.restrictions = restrictions;
  }

  public DetachedCriteriaParamDTO resultTransformer(ResultTransformerEnum resultTransformer) {
    
    this.resultTransformer = resultTransformer;
    return this;
  }

  /**
   * Get resultTransformer
   * @return resultTransformer
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESULT_TRANSFORMER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ResultTransformerEnum getResultTransformer() {
    return resultTransformer;
  }


  @JsonProperty(JSON_PROPERTY_RESULT_TRANSFORMER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResultTransformer(ResultTransformerEnum resultTransformer) {
    this.resultTransformer = resultTransformer;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DetachedCriteriaParamDTO detachedCriteriaParam = (DetachedCriteriaParamDTO) o;
    return Objects.equals(this.alias, detachedCriteriaParam.alias) &&
        Objects.equals(this.aliases, detachedCriteriaParam.aliases) &&
        Objects.equals(this.entityOrClassName, detachedCriteriaParam.entityOrClassName) &&
        Objects.equals(this.fetchMode, detachedCriteriaParam.fetchMode) &&
        Objects.equals(this.lockMode, detachedCriteriaParam.lockMode) &&
        Objects.equals(this.orders, detachedCriteriaParam.orders) &&
        Objects.equals(this.restrictions, detachedCriteriaParam.restrictions) &&
        Objects.equals(this.resultTransformer, detachedCriteriaParam.resultTransformer);
  }

  @Override
  public int hashCode() {
    return Objects.hash(alias, aliases, entityOrClassName, fetchMode, lockMode, orders, restrictions, resultTransformer);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DetachedCriteriaParamDTO {\n");
    sb.append("    alias: ").append(toIndentedString(alias)).append("\n");
    sb.append("    aliases: ").append(toIndentedString(aliases)).append("\n");
    sb.append("    entityOrClassName: ").append(toIndentedString(entityOrClassName)).append("\n");
    sb.append("    fetchMode: ").append(toIndentedString(fetchMode)).append("\n");
    sb.append("    lockMode: ").append(toIndentedString(lockMode)).append("\n");
    sb.append("    orders: ").append(toIndentedString(orders)).append("\n");
    sb.append("    restrictions: ").append(toIndentedString(restrictions)).append("\n");
    sb.append("    resultTransformer: ").append(toIndentedString(resultTransformer)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

