package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.FieldSource

/**
 * Use case interface for managing field source information.
 *
 * This interface defines the contract for retrieving and updating source information
 * at a field level, rather than at an entity level.
 */
fun interface FieldSourceUseCase {
    /**
     * Retrieves all field sources for a specific entity.
     *
     * @param entityType The type of entity (e.g., "citizen_information", "mode_of_payment")
     * @param entityId The unique identifier of the entity
     * @return A list of field sources for the entity
     */
    fun getAllFieldSources(entityType: String, entityId: UUID): List<FieldSource>
}