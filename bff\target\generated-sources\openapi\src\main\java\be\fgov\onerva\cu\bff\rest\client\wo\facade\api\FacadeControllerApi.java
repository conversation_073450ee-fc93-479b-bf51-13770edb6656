package be.fgov.onerva.cu.bff.rest.client.wo.facade.api;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.AskReviewRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.AssignTaskRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.AwakenRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CloseProcessOptionsDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CloseTaskOptionsDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CommentDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CreatableTaskDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.EndReviewRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.InputMetaDataDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.PageableDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ProblemDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ReopenRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SearchableTaskDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SearchedTasksPageDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SleepRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.TaskDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.UpdatableTaskDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class FacadeControllerApi extends BaseApi {

    public FacadeControllerApi() {
        super(new ApiClient());
    }

    public FacadeControllerApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * 
     * <p><b>200</b> - comment added successfully
     * @param taskId  (required)
     * @param commentDTO  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO addCommentToTask(Long taskId, CommentDTO commentDTO) throws RestClientException {
        return addCommentToTaskWithHttpInfo(taskId, commentDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - comment added successfully
     * @param taskId  (required)
     * @param commentDTO  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> addCommentToTaskWithHttpInfo(Long taskId, CommentDTO commentDTO) throws RestClientException {
        Object localVarPostBody = commentDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling addCommentToTask");
        }
        
        // verify the required parameter 'commentDTO' is set
        if (commentDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'commentDTO' when calling addCommentToTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/comment", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param askReviewRequestDTO  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO askReview(Long taskId, AskReviewRequestDTO askReviewRequestDTO) throws RestClientException {
        return askReviewWithHttpInfo(taskId, askReviewRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param askReviewRequestDTO  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> askReviewWithHttpInfo(Long taskId, AskReviewRequestDTO askReviewRequestDTO) throws RestClientException {
        Object localVarPostBody = askReviewRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling askReview");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/askReview", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Process cannot be closed because there is still an open task
     * @param taskId  (required)
     * @param assignee  (required)
     * @param closeProcess  (optional, default to false)
     * @param assignTaskRequestDTO  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO assignAndCloseTask(Long taskId, String assignee, Boolean closeProcess, AssignTaskRequestDTO assignTaskRequestDTO) throws RestClientException {
        return assignAndCloseTaskWithHttpInfo(taskId, assignee, closeProcess, assignTaskRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Process cannot be closed because there is still an open task
     * @param taskId  (required)
     * @param assignee  (required)
     * @param closeProcess  (optional, default to false)
     * @param assignTaskRequestDTO  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> assignAndCloseTaskWithHttpInfo(Long taskId, String assignee, Boolean closeProcess, AssignTaskRequestDTO assignTaskRequestDTO) throws RestClientException {
        Object localVarPostBody = assignTaskRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling assignAndCloseTask");
        }
        
        // verify the required parameter 'assignee' is set
        if (assignee == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'assignee' when calling assignAndCloseTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "assignee", assignee));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "closeProcess", closeProcess));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/assignAndClose", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - assignee not found
     * @param taskId  (required)
     * @param assignee  (required)
     * @param assignTaskRequestDTO  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO assignTask(Long taskId, String assignee, AssignTaskRequestDTO assignTaskRequestDTO) throws RestClientException {
        return assignTaskWithHttpInfo(taskId, assignee, assignTaskRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - assignee not found
     * @param taskId  (required)
     * @param assignee  (required)
     * @param assignTaskRequestDTO  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> assignTaskWithHttpInfo(Long taskId, String assignee, AssignTaskRequestDTO assignTaskRequestDTO) throws RestClientException {
        Object localVarPostBody = assignTaskRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling assignTask");
        }
        
        // verify the required parameter 'assignee' is set
        if (assignee == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'assignee' when calling assignTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "assignee", assignee));
        

        final String[] localVarAccepts = { 
            "application/json", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/assign", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param awakenRequestDTO  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO awaken(Long taskId, AwakenRequestDTO awakenRequestDTO) throws RestClientException {
        return awakenWithHttpInfo(taskId, awakenRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param awakenRequestDTO  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> awakenWithHttpInfo(Long taskId, AwakenRequestDTO awakenRequestDTO) throws RestClientException {
        Object localVarPostBody = awakenRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling awaken");
        }
        
        // verify the required parameter 'awakenRequestDTO' is set
        if (awakenRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'awakenRequestDTO' when calling awaken");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/awaken", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>204</b> - OK
     * @param processId  (required)
     * @param option  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void closeProcess(Long processId, CloseProcessOptionsDTO option) throws RestClientException {
        closeProcessWithHttpInfo(processId, option);
    }

    /**
     * 
     * 
     * <p><b>204</b> - OK
     * @param processId  (required)
     * @param option  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> closeProcessWithHttpInfo(Long processId, CloseProcessOptionsDTO option) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'processId' is set
        if (processId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'processId' when calling closeProcess");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("processId", processId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        
        if (option != null) {
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "forceClose", option.getForceClose()));
        }

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/process/{processId}/close", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Process cannot be closed because there is still an open task
     * @param taskId  (required)
     * @param options  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO closeTask(Long taskId, CloseTaskOptionsDTO options) throws RestClientException {
        return closeTaskWithHttpInfo(taskId, options).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Process cannot be closed because there is still an open task
     * @param taskId  (required)
     * @param options  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> closeTaskWithHttpInfo(Long taskId, CloseTaskOptionsDTO options) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling closeTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        
        if (options != null) {
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "closeProcess", options.getCloseProcess()));
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "forceCloseTask", options.getForceCloseTask()));
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "forceCloseProcess", options.getForceCloseProcess()));
        }

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/close", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>201</b> - task created
     * <p><b>404</b> - taskType, concerned entities or assignee not found
     * <p><b>400</b> - There are errors in the payload
     * @param creatableTaskDTO  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO createTask(CreatableTaskDTO creatableTaskDTO) throws RestClientException {
        return createTaskWithHttpInfo(creatableTaskDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>201</b> - task created
     * <p><b>404</b> - taskType, concerned entities or assignee not found
     * <p><b>400</b> - There are errors in the payload
     * @param creatableTaskDTO  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> createTaskWithHttpInfo(CreatableTaskDTO creatableTaskDTO) throws RestClientException {
        Object localVarPostBody = creatableTaskDTO;
        
        // verify the required parameter 'creatableTaskDTO' is set
        if (creatableTaskDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'creatableTaskDTO' when calling createTask");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param endReviewRequestDTO  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO endReview(Long taskId, EndReviewRequestDTO endReviewRequestDTO) throws RestClientException {
        return endReviewWithHttpInfo(taskId, endReviewRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param endReviewRequestDTO  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> endReviewWithHttpInfo(Long taskId, EndReviewRequestDTO endReviewRequestDTO) throws RestClientException {
        Object localVarPostBody = endReviewRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling endReview");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/endReview", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO getTask(Long taskId) throws RestClientException {
        return getTaskWithHttpInfo(taskId).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> getTaskWithHttpInfo(Long taskId) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling getTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO preClose(Long taskId) throws RestClientException {
        return preCloseWithHttpInfo(taskId).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> preCloseWithHttpInfo(Long taskId) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling preClose");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/preClose", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param reopenRequestDTO  (optional)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO reopen(Long taskId, ReopenRequestDTO reopenRequestDTO) throws RestClientException {
        return reopenWithHttpInfo(taskId, reopenRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param reopenRequestDTO  (optional)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> reopenWithHttpInfo(Long taskId, ReopenRequestDTO reopenRequestDTO) throws RestClientException {
        Object localVarPostBody = reopenRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling reopen");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/reopen", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - tasks retrieved
     * @param searchableTaskDTO  (required)
     * @param pageable  (optional)
     * @return SearchedTasksPageDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public SearchedTasksPageDTO searchTasks(SearchableTaskDTO searchableTaskDTO, PageableDTO pageable) throws RestClientException {
        return searchTasksWithHttpInfo(searchableTaskDTO, pageable).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - tasks retrieved
     * @param searchableTaskDTO  (required)
     * @param pageable  (optional)
     * @return ResponseEntity&lt;SearchedTasksPageDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<SearchedTasksPageDTO> searchTasksWithHttpInfo(SearchableTaskDTO searchableTaskDTO, PageableDTO pageable) throws RestClientException {
        Object localVarPostBody = searchableTaskDTO;
        
        // verify the required parameter 'searchableTaskDTO' is set
        if (searchableTaskDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'searchableTaskDTO' when calling searchTasks");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        
        if (pageable != null) {
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "size", pageable.getSize()));
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", pageable.getPage()));
        }

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<SearchedTasksPageDTO> localReturnType = new ParameterizedTypeReference<SearchedTasksPageDTO>() {};
        return apiClient.invokeAPI("/tasks/search", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param sleepRequestDTO  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO sleep(Long taskId, SleepRequestDTO sleepRequestDTO) throws RestClientException {
        return sleepWithHttpInfo(taskId, sleepRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * @param taskId  (required)
     * @param sleepRequestDTO  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> sleepWithHttpInfo(Long taskId, SleepRequestDTO sleepRequestDTO) throws RestClientException {
        Object localVarPostBody = sleepRequestDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling sleep");
        }
        
        // verify the required parameter 'sleepRequestDTO' is set
        if (sleepRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'sleepRequestDTO' when calling sleep");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}/sleep", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>204</b> - OK
     * @param processId  (required)
     * @param inputMetaDataDTO  (required)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void updateProcess(Long processId, List<InputMetaDataDTO> inputMetaDataDTO) throws RestClientException {
        updateProcessWithHttpInfo(processId, inputMetaDataDTO);
    }

    /**
     * 
     * 
     * <p><b>204</b> - OK
     * @param processId  (required)
     * @param inputMetaDataDTO  (required)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> updateProcessWithHttpInfo(Long processId, List<InputMetaDataDTO> inputMetaDataDTO) throws RestClientException {
        Object localVarPostBody = inputMetaDataDTO;
        
        // verify the required parameter 'processId' is set
        if (processId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'processId' when calling updateProcess");
        }
        
        // verify the required parameter 'inputMetaDataDTO' is set
        if (inputMetaDataDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'inputMetaDataDTO' when calling updateProcess");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("processId", processId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/process/{processId}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - task updated
     * @param taskId  (required)
     * @param updatableTaskDTO  (required)
     * @return TaskDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public TaskDTO updateTask(Long taskId, UpdatableTaskDTO updatableTaskDTO) throws RestClientException {
        return updateTaskWithHttpInfo(taskId, updatableTaskDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - task updated
     * @param taskId  (required)
     * @param updatableTaskDTO  (required)
     * @return ResponseEntity&lt;TaskDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<TaskDTO> updateTaskWithHttpInfo(Long taskId, UpdatableTaskDTO updatableTaskDTO) throws RestClientException {
        Object localVarPostBody = updatableTaskDTO;
        
        // verify the required parameter 'taskId' is set
        if (taskId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'taskId' when calling updateTask");
        }
        
        // verify the required parameter 'updatableTaskDTO' is set
        if (updatableTaskDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'updatableTaskDTO' when calling updateTask");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("taskId", taskId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<TaskDTO> localReturnType = new ParameterizedTypeReference<TaskDTO>() {};
        return apiClient.invokeAPI("/tasks/{taskId}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
