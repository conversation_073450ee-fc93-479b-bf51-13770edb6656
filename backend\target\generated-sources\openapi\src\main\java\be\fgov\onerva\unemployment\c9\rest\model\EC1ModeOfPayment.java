/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1ModeOfPayment
 */
@JsonPropertyOrder({
  EC1ModeOfPayment.JSON_PROPERTY_HAS_TRANSFER_MONEY_ON_BANK_ACCOUNT,
  EC1ModeOfPayment.JSON_PROPERTY_IS_MY_BANK_ACCOUNT,
  EC1ModeOfPayment.JSON_PROPERTY_IS_NOT_MY_BANK_ACCOUNT,
  EC1ModeOfPayment.JSON_PROPERTY_BANK_ACCOUNT_FOR_OTHER_PERSON_NAME,
  EC1ModeOfPayment.JSON_PROPERTY_BELGIAN_S_E_P_A_BANK_ACCOUNT,
  EC1ModeOfPayment.JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_I_B_A_N,
  EC1ModeOfPayment.JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_B_I_C
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1ModeOfPayment {
  public static final String JSON_PROPERTY_HAS_TRANSFER_MONEY_ON_BANK_ACCOUNT = "hasTransferMoneyOnBankAccount";
  private Boolean hasTransferMoneyOnBankAccount;

  public static final String JSON_PROPERTY_IS_MY_BANK_ACCOUNT = "isMyBankAccount";
  private Boolean isMyBankAccount;

  public static final String JSON_PROPERTY_IS_NOT_MY_BANK_ACCOUNT = "isNotMyBankAccount";
  private Boolean isNotMyBankAccount;

  public static final String JSON_PROPERTY_BANK_ACCOUNT_FOR_OTHER_PERSON_NAME = "bankAccountForOtherPersonName";
  private String bankAccountForOtherPersonName;

  public static final String JSON_PROPERTY_BELGIAN_S_E_P_A_BANK_ACCOUNT = "belgianSEPABankAccount";
  private String belgianSEPABankAccount;

  public static final String JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_I_B_A_N = "foreignBankAccountIBAN";
  private String foreignBankAccountIBAN;

  public static final String JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_B_I_C = "foreignBankAccountBIC";
  private String foreignBankAccountBIC;

  public EC1ModeOfPayment() {
  }

  public EC1ModeOfPayment hasTransferMoneyOnBankAccount(Boolean hasTransferMoneyOnBankAccount) {
    
    this.hasTransferMoneyOnBankAccount = hasTransferMoneyOnBankAccount;
    return this;
  }

  /**
   * Get hasTransferMoneyOnBankAccount
   * @return hasTransferMoneyOnBankAccount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_TRANSFER_MONEY_ON_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasTransferMoneyOnBankAccount() {
    return hasTransferMoneyOnBankAccount;
  }


  @JsonProperty(JSON_PROPERTY_HAS_TRANSFER_MONEY_ON_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasTransferMoneyOnBankAccount(Boolean hasTransferMoneyOnBankAccount) {
    this.hasTransferMoneyOnBankAccount = hasTransferMoneyOnBankAccount;
  }

  public EC1ModeOfPayment isMyBankAccount(Boolean isMyBankAccount) {
    
    this.isMyBankAccount = isMyBankAccount;
    return this;
  }

  /**
   * Get isMyBankAccount
   * @return isMyBankAccount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_MY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsMyBankAccount() {
    return isMyBankAccount;
  }


  @JsonProperty(JSON_PROPERTY_IS_MY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsMyBankAccount(Boolean isMyBankAccount) {
    this.isMyBankAccount = isMyBankAccount;
  }

  public EC1ModeOfPayment isNotMyBankAccount(Boolean isNotMyBankAccount) {
    
    this.isNotMyBankAccount = isNotMyBankAccount;
    return this;
  }

  /**
   * Get isNotMyBankAccount
   * @return isNotMyBankAccount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_NOT_MY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsNotMyBankAccount() {
    return isNotMyBankAccount;
  }


  @JsonProperty(JSON_PROPERTY_IS_NOT_MY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsNotMyBankAccount(Boolean isNotMyBankAccount) {
    this.isNotMyBankAccount = isNotMyBankAccount;
  }

  public EC1ModeOfPayment bankAccountForOtherPersonName(String bankAccountForOtherPersonName) {
    
    this.bankAccountForOtherPersonName = bankAccountForOtherPersonName;
    return this;
  }

  /**
   * Get bankAccountForOtherPersonName
   * @return bankAccountForOtherPersonName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_FOR_OTHER_PERSON_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBankAccountForOtherPersonName() {
    return bankAccountForOtherPersonName;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT_FOR_OTHER_PERSON_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAccountForOtherPersonName(String bankAccountForOtherPersonName) {
    this.bankAccountForOtherPersonName = bankAccountForOtherPersonName;
  }

  public EC1ModeOfPayment belgianSEPABankAccount(String belgianSEPABankAccount) {
    
    this.belgianSEPABankAccount = belgianSEPABankAccount;
    return this;
  }

  /**
   * Get belgianSEPABankAccount
   * @return belgianSEPABankAccount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BELGIAN_S_E_P_A_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBelgianSEPABankAccount() {
    return belgianSEPABankAccount;
  }


  @JsonProperty(JSON_PROPERTY_BELGIAN_S_E_P_A_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBelgianSEPABankAccount(String belgianSEPABankAccount) {
    this.belgianSEPABankAccount = belgianSEPABankAccount;
  }

  public EC1ModeOfPayment foreignBankAccountIBAN(String foreignBankAccountIBAN) {
    
    this.foreignBankAccountIBAN = foreignBankAccountIBAN;
    return this;
  }

  /**
   * Get foreignBankAccountIBAN
   * @return foreignBankAccountIBAN
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_I_B_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getForeignBankAccountIBAN() {
    return foreignBankAccountIBAN;
  }


  @JsonProperty(JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_I_B_A_N)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForeignBankAccountIBAN(String foreignBankAccountIBAN) {
    this.foreignBankAccountIBAN = foreignBankAccountIBAN;
  }

  public EC1ModeOfPayment foreignBankAccountBIC(String foreignBankAccountBIC) {
    
    this.foreignBankAccountBIC = foreignBankAccountBIC;
    return this;
  }

  /**
   * Get foreignBankAccountBIC
   * @return foreignBankAccountBIC
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_B_I_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getForeignBankAccountBIC() {
    return foreignBankAccountBIC;
  }


  @JsonProperty(JSON_PROPERTY_FOREIGN_BANK_ACCOUNT_B_I_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForeignBankAccountBIC(String foreignBankAccountBIC) {
    this.foreignBankAccountBIC = foreignBankAccountBIC;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1ModeOfPayment ec1ModeOfPayment = (EC1ModeOfPayment) o;
    return Objects.equals(this.hasTransferMoneyOnBankAccount, ec1ModeOfPayment.hasTransferMoneyOnBankAccount) &&
        Objects.equals(this.isMyBankAccount, ec1ModeOfPayment.isMyBankAccount) &&
        Objects.equals(this.isNotMyBankAccount, ec1ModeOfPayment.isNotMyBankAccount) &&
        Objects.equals(this.bankAccountForOtherPersonName, ec1ModeOfPayment.bankAccountForOtherPersonName) &&
        Objects.equals(this.belgianSEPABankAccount, ec1ModeOfPayment.belgianSEPABankAccount) &&
        Objects.equals(this.foreignBankAccountIBAN, ec1ModeOfPayment.foreignBankAccountIBAN) &&
        Objects.equals(this.foreignBankAccountBIC, ec1ModeOfPayment.foreignBankAccountBIC);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hasTransferMoneyOnBankAccount, isMyBankAccount, isNotMyBankAccount, bankAccountForOtherPersonName, belgianSEPABankAccount, foreignBankAccountIBAN, foreignBankAccountBIC);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1ModeOfPayment {\n");
    sb.append("    hasTransferMoneyOnBankAccount: ").append(toIndentedString(hasTransferMoneyOnBankAccount)).append("\n");
    sb.append("    isMyBankAccount: ").append(toIndentedString(isMyBankAccount)).append("\n");
    sb.append("    isNotMyBankAccount: ").append(toIndentedString(isNotMyBankAccount)).append("\n");
    sb.append("    bankAccountForOtherPersonName: ").append(toIndentedString(bankAccountForOtherPersonName)).append("\n");
    sb.append("    belgianSEPABankAccount: ").append(toIndentedString(belgianSEPABankAccount)).append("\n");
    sb.append("    foreignBankAccountIBAN: ").append(toIndentedString(foreignBankAccountIBAN)).append("\n");
    sb.append("    foreignBankAccountBIC: ").append(toIndentedString(foreignBankAccountBIC)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

