/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param environmentID 
 * @param identity 
 * @param api 
 */


data class FlagsmithConfigResponse (

    @get:JsonProperty("environmentID")
    val environmentID: kotlin.String,

    @get:JsonProperty("identity")
    val identity: kotlin.String,

    @get:JsonProperty("api")
    val api: kotlin.String

) {


}

