package be.fgov.onerva.cu.common.utils

import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * Utility object providing date formatting and parsing functions.
 */
object DateUtils {
    /**
     * Formats a nullable LocalDate to a "yyyy-MM-dd" string format.
     *
     * @receiver The LocalDate to format, can be null
     * @return A formatted date string in "yyyy-MM-dd" format, or null if the receiver is null
     *
     * Example:
     * ```
     * val date = LocalDate.of(2024, 12, 16)
     * val formatted = date.formatToYYYYMMDD() // Returns "2024-12-16"
     * ```
     */
    fun LocalDate?.formatToYYYYMMDD(): String? =
        this?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

    fun LocalDate?.formatToYYYYMMDDNoDash(): String? =
        this?.format(DateTimeFormatter.ofPattern("yyyyMMdd"))

    /**
     * Parses a date string in "yyyy-MM-dd" format to a LocalDate object.
     *
     * @param dateStr The date string to parse in "yyyy-MM-dd" format
     * @return The parsed LocalDate, or null if parsing fails
     *
     * Example:
     * ```
     * val date = parseDate("2024-12-16") // Returns LocalDate representing December 16, 2024
     * val invalid = parseDate("invalid") // Returns null
     * ```
     */
    fun parseDate(dateStr: String): LocalDate? = runCatching {
        LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"))
    }.getOrNull()
}