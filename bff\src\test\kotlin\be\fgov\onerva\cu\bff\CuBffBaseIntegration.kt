package be.fgov.onerva.cu.bff

import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.test.context.ActiveProfiles
import be.fgov.onerva.cu.bff.annotation.CuBffSpringBootTest
import be.fgov.onerva.cu.bff.lookup.LookupService
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenApi
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoApi
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoV2Api
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api.LookupApi
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.C9Api
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.NavigateMainframeToS24Api
import be.fgov.onerva.cu.bff.rest.client.wo.facade.api.UserApi
import com.ninjasquad.springmockk.MockkBean

@CuBffSpringBootTest
@ActiveProfiles("unit")
class CuBffBaseIntegration {
    @MockkBean
    val jwtDecoder: JwtDecoder? = null

    @MockkBean
    val lookupApi: LookupApi? = null

    @MockkBean
    val citizenApi: CitizenApi? = null

    @MockkBean
    val citizenInfoApi: CitizenInfoApi? = null

    @MockkBean
    val citizenInfoV2Api: CitizenInfoV2Api? = null

    @MockkBean
    val waveFacadeUserApi: UserApi? = null

    @MockkBean
    val c9NavigateMainframeToS24Api: NavigateMainframeToS24Api? = null

    @MockkBean
    val lookupService: LookupService? = null

    @MockkBean
    val c9Api: C9Api? = null
}