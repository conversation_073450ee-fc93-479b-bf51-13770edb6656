package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers

interface WaveTaskPersistencePort {
    fun persistChangePersonalDataCaptureWaveTask(requestId: UUID, waveTask: WaveTask)

    fun persistChangePersonalDataValidateWaveTask(
        requestId: UUID, waveTask: WaveTask,
    )

    fun closeWaveTaskChangePersonalDataCapture(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    )

    fun sleepWaveTaskChangePersonalDataCapture(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    )

    fun closeWaveTaskChangePersonalDataValidate(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    )

    fun sleepWaveTaskChangePersonalDataValidate(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    )

    fun softDeleteTaskById(taskId: String)

    fun openTask(task: WaveTask)

    fun getOpenWaveTaskByRequestId(requestId: UUID): WaveTask

    fun getCitizenInformationRevision(requestId: UUID, taskCode: String): WaveTaskRevisionNumbers
    fun getWaitingWaveTaskByRequestId(requestId: UUID, taskCode: String): WaveTask

    fun getWaveTaskEntityByRequestId(
        requestId: UUID,
        taskType: String
    ): WaveTask

    fun removeWaveTaskEntityByRequestId(
        requestId: UUID,
        taskType: String)
}