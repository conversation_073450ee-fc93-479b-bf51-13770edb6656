/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CloseTaskOptionsDTO
 */
@JsonPropertyOrder({
  CloseTaskOptionsDTO.JSON_PROPERTY_CLOSE_PROCESS,
  CloseTaskOptionsDTO.JSON_PROPERTY_FORCE_CLOSE_TASK,
  CloseTaskOptionsDTO.JSON_PROPERTY_FORCE_CLOSE_PROCESS
})
@JsonTypeName("CloseTaskOptions")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CloseTaskOptionsDTO {
  public static final String JSON_PROPERTY_CLOSE_PROCESS = "closeProcess";
  private Boolean closeProcess = false;

  public static final String JSON_PROPERTY_FORCE_CLOSE_TASK = "forceCloseTask";
  private Boolean forceCloseTask = false;

  public static final String JSON_PROPERTY_FORCE_CLOSE_PROCESS = "forceCloseProcess";
  private Boolean forceCloseProcess = false;

  public CloseTaskOptionsDTO() {
  }

  public CloseTaskOptionsDTO closeProcess(Boolean closeProcess) {
    
    this.closeProcess = closeProcess;
    return this;
  }

  /**
   * Get closeProcess
   * @return closeProcess
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSE_PROCESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCloseProcess() {
    return closeProcess;
  }


  @JsonProperty(JSON_PROPERTY_CLOSE_PROCESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCloseProcess(Boolean closeProcess) {
    this.closeProcess = closeProcess;
  }

  public CloseTaskOptionsDTO forceCloseTask(Boolean forceCloseTask) {
    
    this.forceCloseTask = forceCloseTask;
    return this;
  }

  /**
   * Get forceCloseTask
   * @return forceCloseTask
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FORCE_CLOSE_TASK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getForceCloseTask() {
    return forceCloseTask;
  }


  @JsonProperty(JSON_PROPERTY_FORCE_CLOSE_TASK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForceCloseTask(Boolean forceCloseTask) {
    this.forceCloseTask = forceCloseTask;
  }

  public CloseTaskOptionsDTO forceCloseProcess(Boolean forceCloseProcess) {
    
    this.forceCloseProcess = forceCloseProcess;
    return this;
  }

  /**
   * Get forceCloseProcess
   * @return forceCloseProcess
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FORCE_CLOSE_PROCESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getForceCloseProcess() {
    return forceCloseProcess;
  }


  @JsonProperty(JSON_PROPERTY_FORCE_CLOSE_PROCESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForceCloseProcess(Boolean forceCloseProcess) {
    this.forceCloseProcess = forceCloseProcess;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CloseTaskOptionsDTO closeTaskOptions = (CloseTaskOptionsDTO) o;
    return Objects.equals(this.closeProcess, closeTaskOptions.closeProcess) &&
        Objects.equals(this.forceCloseTask, closeTaskOptions.forceCloseTask) &&
        Objects.equals(this.forceCloseProcess, closeTaskOptions.forceCloseProcess);
  }

  @Override
  public int hashCode() {
    return Objects.hash(closeProcess, forceCloseTask, forceCloseProcess);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CloseTaskOptionsDTO {\n");
    sb.append("    closeProcess: ").append(toIndentedString(closeProcess)).append("\n");
    sb.append("    forceCloseTask: ").append(toIndentedString(forceCloseTask)).append("\n");
    sb.append("    forceCloseProcess: ").append(toIndentedString(forceCloseProcess)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

