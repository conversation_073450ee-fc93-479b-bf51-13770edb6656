/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param requestDate The date when the request was submitted
 */


data class RequestInformationResponse (

    /* The date when the request was submitted */
    @get:JsonProperty("requestDate")
    val requestDate: java.time.LocalDate? = null

) {


}

