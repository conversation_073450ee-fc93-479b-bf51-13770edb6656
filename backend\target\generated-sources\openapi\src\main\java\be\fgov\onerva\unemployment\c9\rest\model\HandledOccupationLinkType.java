/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HandledOccupationLinkType
 */
@JsonPropertyOrder({
  HandledOccupationLinkType.JSON_PROPERTY_OCCUPATION_STARTING_DATE,
  HandledOccupationLinkType.JSON_PROPERTY_OCCUPATION_ENDING_DATE,
  HandledOccupationLinkType.JSON_PROPERTY_JOINT_COMMISSION_NBR,
  HandledOccupationLinkType.JSON_PROPERTY_WORKING_DAYS_SYSTEM,
  HandledOccupationLinkType.JSON_PROPERTY_MEAN_WORKING_HOURS,
  HandledOccupationLinkType.JSON_PROPERTY_REF_MEAN_WORKING_HOURS,
  HandledOccupationLinkType.JSON_PROPERTY_WORKER_STATUS,
  HandledOccupationLinkType.JSON_PROPERTY_RETIRED,
  HandledOccupationLinkType.JSON_PROPERTY_APPRENTICESHIP,
  HandledOccupationLinkType.JSON_PROPERTY_CONTRACT_TYPE,
  HandledOccupationLinkType.JSON_PROPERTY_REMUN_METHOD,
  HandledOccupationLinkType.JSON_PROPERTY_INTERNAL_OCCUPATION_NBR,
  HandledOccupationLinkType.JSON_PROPERTY_OCCUPATION_VALIDATION_CODE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HandledOccupationLinkType {
  public static final String JSON_PROPERTY_OCCUPATION_STARTING_DATE = "occupationStartingDate";
  private LocalDate occupationStartingDate;

  public static final String JSON_PROPERTY_OCCUPATION_ENDING_DATE = "occupationEndingDate";
  private LocalDate occupationEndingDate;

  public static final String JSON_PROPERTY_JOINT_COMMISSION_NBR = "jointCommissionNbr";
  private String jointCommissionNbr;

  public static final String JSON_PROPERTY_WORKING_DAYS_SYSTEM = "workingDaysSystem";
  private String workingDaysSystem;

  public static final String JSON_PROPERTY_MEAN_WORKING_HOURS = "meanWorkingHours";
  private String meanWorkingHours;

  public static final String JSON_PROPERTY_REF_MEAN_WORKING_HOURS = "refMeanWorkingHours";
  private String refMeanWorkingHours;

  public static final String JSON_PROPERTY_WORKER_STATUS = "workerStatus";
  private String workerStatus;

  public static final String JSON_PROPERTY_RETIRED = "retired";
  private String retired;

  public static final String JSON_PROPERTY_APPRENTICESHIP = "apprenticeship";
  private String apprenticeship;

  public static final String JSON_PROPERTY_CONTRACT_TYPE = "contractType";
  private String contractType;

  public static final String JSON_PROPERTY_REMUN_METHOD = "remunMethod";
  private String remunMethod;

  public static final String JSON_PROPERTY_INTERNAL_OCCUPATION_NBR = "internalOccupationNbr";
  private String internalOccupationNbr;

  public static final String JSON_PROPERTY_OCCUPATION_VALIDATION_CODE = "occupationValidationCode";
  private String occupationValidationCode;

  public HandledOccupationLinkType() {
  }

  public HandledOccupationLinkType occupationStartingDate(LocalDate occupationStartingDate) {
    
    this.occupationStartingDate = occupationStartingDate;
    return this;
  }

  /**
   * Get occupationStartingDate
   * @return occupationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getOccupationStartingDate() {
    return occupationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationStartingDate(LocalDate occupationStartingDate) {
    this.occupationStartingDate = occupationStartingDate;
  }

  public HandledOccupationLinkType occupationEndingDate(LocalDate occupationEndingDate) {
    
    this.occupationEndingDate = occupationEndingDate;
    return this;
  }

  /**
   * Get occupationEndingDate
   * @return occupationEndingDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getOccupationEndingDate() {
    return occupationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationEndingDate(LocalDate occupationEndingDate) {
    this.occupationEndingDate = occupationEndingDate;
  }

  public HandledOccupationLinkType jointCommissionNbr(String jointCommissionNbr) {
    
    this.jointCommissionNbr = jointCommissionNbr;
    return this;
  }

  /**
   * Get jointCommissionNbr
   * @return jointCommissionNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getJointCommissionNbr() {
    return jointCommissionNbr;
  }


  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setJointCommissionNbr(String jointCommissionNbr) {
    this.jointCommissionNbr = jointCommissionNbr;
  }

  public HandledOccupationLinkType workingDaysSystem(String workingDaysSystem) {
    
    this.workingDaysSystem = workingDaysSystem;
    return this;
  }

  /**
   * Get workingDaysSystem
   * @return workingDaysSystem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkingDaysSystem() {
    return workingDaysSystem;
  }


  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkingDaysSystem(String workingDaysSystem) {
    this.workingDaysSystem = workingDaysSystem;
  }

  public HandledOccupationLinkType meanWorkingHours(String meanWorkingHours) {
    
    this.meanWorkingHours = meanWorkingHours;
    return this;
  }

  /**
   * Get meanWorkingHours
   * @return meanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMeanWorkingHours() {
    return meanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMeanWorkingHours(String meanWorkingHours) {
    this.meanWorkingHours = meanWorkingHours;
  }

  public HandledOccupationLinkType refMeanWorkingHours(String refMeanWorkingHours) {
    
    this.refMeanWorkingHours = refMeanWorkingHours;
    return this;
  }

  /**
   * Get refMeanWorkingHours
   * @return refMeanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getRefMeanWorkingHours() {
    return refMeanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefMeanWorkingHours(String refMeanWorkingHours) {
    this.refMeanWorkingHours = refMeanWorkingHours;
  }

  public HandledOccupationLinkType workerStatus(String workerStatus) {
    
    this.workerStatus = workerStatus;
    return this;
  }

  /**
   * Get workerStatus
   * @return workerStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStatus() {
    return workerStatus;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStatus(String workerStatus) {
    this.workerStatus = workerStatus;
  }

  public HandledOccupationLinkType retired(String retired) {
    
    this.retired = retired;
    return this;
  }

  /**
   * Get retired
   * @return retired
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRetired() {
    return retired;
  }


  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRetired(String retired) {
    this.retired = retired;
  }

  public HandledOccupationLinkType apprenticeship(String apprenticeship) {
    
    this.apprenticeship = apprenticeship;
    return this;
  }

  /**
   * Get apprenticeship
   * @return apprenticeship
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApprenticeship() {
    return apprenticeship;
  }


  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApprenticeship(String apprenticeship) {
    this.apprenticeship = apprenticeship;
  }

  public HandledOccupationLinkType contractType(String contractType) {
    
    this.contractType = contractType;
    return this;
  }

  /**
   * Get contractType
   * @return contractType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContractType() {
    return contractType;
  }


  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContractType(String contractType) {
    this.contractType = contractType;
  }

  public HandledOccupationLinkType remunMethod(String remunMethod) {
    
    this.remunMethod = remunMethod;
    return this;
  }

  /**
   * Get remunMethod
   * @return remunMethod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemunMethod() {
    return remunMethod;
  }


  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemunMethod(String remunMethod) {
    this.remunMethod = remunMethod;
  }

  public HandledOccupationLinkType internalOccupationNbr(String internalOccupationNbr) {
    
    this.internalOccupationNbr = internalOccupationNbr;
    return this;
  }

  /**
   * Get internalOccupationNbr
   * @return internalOccupationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERNAL_OCCUPATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInternalOccupationNbr() {
    return internalOccupationNbr;
  }


  @JsonProperty(JSON_PROPERTY_INTERNAL_OCCUPATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInternalOccupationNbr(String internalOccupationNbr) {
    this.internalOccupationNbr = internalOccupationNbr;
  }

  public HandledOccupationLinkType occupationValidationCode(String occupationValidationCode) {
    
    this.occupationValidationCode = occupationValidationCode;
    return this;
  }

  /**
   * Get occupationValidationCode
   * @return occupationValidationCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_VALIDATION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOccupationValidationCode() {
    return occupationValidationCode;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_VALIDATION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationValidationCode(String occupationValidationCode) {
    this.occupationValidationCode = occupationValidationCode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandledOccupationLinkType handledOccupationLinkType = (HandledOccupationLinkType) o;
    return Objects.equals(this.occupationStartingDate, handledOccupationLinkType.occupationStartingDate) &&
        Objects.equals(this.occupationEndingDate, handledOccupationLinkType.occupationEndingDate) &&
        Objects.equals(this.jointCommissionNbr, handledOccupationLinkType.jointCommissionNbr) &&
        Objects.equals(this.workingDaysSystem, handledOccupationLinkType.workingDaysSystem) &&
        Objects.equals(this.meanWorkingHours, handledOccupationLinkType.meanWorkingHours) &&
        Objects.equals(this.refMeanWorkingHours, handledOccupationLinkType.refMeanWorkingHours) &&
        Objects.equals(this.workerStatus, handledOccupationLinkType.workerStatus) &&
        Objects.equals(this.retired, handledOccupationLinkType.retired) &&
        Objects.equals(this.apprenticeship, handledOccupationLinkType.apprenticeship) &&
        Objects.equals(this.contractType, handledOccupationLinkType.contractType) &&
        Objects.equals(this.remunMethod, handledOccupationLinkType.remunMethod) &&
        Objects.equals(this.internalOccupationNbr, handledOccupationLinkType.internalOccupationNbr) &&
        Objects.equals(this.occupationValidationCode, handledOccupationLinkType.occupationValidationCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationStartingDate, occupationEndingDate, jointCommissionNbr, workingDaysSystem, meanWorkingHours, refMeanWorkingHours, workerStatus, retired, apprenticeship, contractType, remunMethod, internalOccupationNbr, occupationValidationCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandledOccupationLinkType {\n");
    sb.append("    occupationStartingDate: ").append(toIndentedString(occupationStartingDate)).append("\n");
    sb.append("    occupationEndingDate: ").append(toIndentedString(occupationEndingDate)).append("\n");
    sb.append("    jointCommissionNbr: ").append(toIndentedString(jointCommissionNbr)).append("\n");
    sb.append("    workingDaysSystem: ").append(toIndentedString(workingDaysSystem)).append("\n");
    sb.append("    meanWorkingHours: ").append(toIndentedString(meanWorkingHours)).append("\n");
    sb.append("    refMeanWorkingHours: ").append(toIndentedString(refMeanWorkingHours)).append("\n");
    sb.append("    workerStatus: ").append(toIndentedString(workerStatus)).append("\n");
    sb.append("    retired: ").append(toIndentedString(retired)).append("\n");
    sb.append("    apprenticeship: ").append(toIndentedString(apprenticeship)).append("\n");
    sb.append("    contractType: ").append(toIndentedString(contractType)).append("\n");
    sb.append("    remunMethod: ").append(toIndentedString(remunMethod)).append("\n");
    sb.append("    internalOccupationNbr: ").append(toIndentedString(internalOccupationNbr)).append("\n");
    sb.append("    occupationValidationCode: ").append(toIndentedString(occupationValidationCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

