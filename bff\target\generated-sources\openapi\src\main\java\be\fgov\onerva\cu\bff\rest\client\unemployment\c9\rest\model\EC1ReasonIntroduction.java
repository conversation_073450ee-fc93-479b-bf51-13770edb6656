/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1ReasonIntroduction
 */
@JsonPropertyOrder({
  EC1ReasonIntroduction.JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE,
  EC1ReasonIntroduction.JSON_PROPERTY_HAS_ALTERNATE_TRAINING_AND_TEMPORARY_UNEMPLOYMENT,
  EC1ReasonIntroduction.JSON_PROPERTY_IS_ASKING_ALLOWANCE_FIRST_TIME,
  EC1ReasonIntroduction.JSON_PROPERTY_CHANGE_OF_PAYMENT_OFFICE_FROM_DATE,
  EC1ReasonIntroduction.JSON_PROPERTY_HAS_DECLARE_MODIFICATION_ABOUT,
  EC1ReasonIntroduction.JSON_PROPERTY_CHANGE_MY_ADDRESS_FROM_DATE,
  EC1ReasonIntroduction.JSON_PROPERTY_HAS_CHANGE_DEDUCTION_TRADE_UNION_CONTRIBUTION,
  EC1ReasonIntroduction.JSON_PROPERTY_HAS_CHANGE_RESIDENCE_OR_WORK_PERMIT,
  EC1ReasonIntroduction.JSON_PROPERTY_CHANGE_SITUATION_PERSONAL_OR_FAMILY_FROM_DATE,
  EC1ReasonIntroduction.JSON_PROPERTY_CHANGE_MODE_OF_PAYMENT_OR_ACCOUNT_NR_FROM_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1ReasonIntroduction {
  public static final String JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE = "askAllowanceFromDate";
  private LocalDate askAllowanceFromDate;

  public static final String JSON_PROPERTY_HAS_ALTERNATE_TRAINING_AND_TEMPORARY_UNEMPLOYMENT = "hasAlternateTrainingAndTemporaryUnemployment";
  private Boolean hasAlternateTrainingAndTemporaryUnemployment;

  public static final String JSON_PROPERTY_IS_ASKING_ALLOWANCE_FIRST_TIME = "isAskingAllowanceFirstTime";
  private Boolean isAskingAllowanceFirstTime;

  public static final String JSON_PROPERTY_CHANGE_OF_PAYMENT_OFFICE_FROM_DATE = "changeOfPaymentOfficeFromDate";
  private LocalDate changeOfPaymentOfficeFromDate;

  public static final String JSON_PROPERTY_HAS_DECLARE_MODIFICATION_ABOUT = "hasDeclareModificationAbout";
  private Boolean hasDeclareModificationAbout;

  public static final String JSON_PROPERTY_CHANGE_MY_ADDRESS_FROM_DATE = "changeMyAddressFromDate";
  private LocalDate changeMyAddressFromDate;

  public static final String JSON_PROPERTY_HAS_CHANGE_DEDUCTION_TRADE_UNION_CONTRIBUTION = "hasChangeDeductionTradeUnionContribution";
  private Boolean hasChangeDeductionTradeUnionContribution;

  public static final String JSON_PROPERTY_HAS_CHANGE_RESIDENCE_OR_WORK_PERMIT = "hasChangeResidenceOrWorkPermit";
  private Boolean hasChangeResidenceOrWorkPermit;

  public static final String JSON_PROPERTY_CHANGE_SITUATION_PERSONAL_OR_FAMILY_FROM_DATE = "changeSituationPersonalOrFamilyFromDate";
  private LocalDate changeSituationPersonalOrFamilyFromDate;

  public static final String JSON_PROPERTY_CHANGE_MODE_OF_PAYMENT_OR_ACCOUNT_NR_FROM_DATE = "changeModeOfPaymentOrAccountNrFromDate";
  private LocalDate changeModeOfPaymentOrAccountNrFromDate;

  public EC1ReasonIntroduction() {
  }

  public EC1ReasonIntroduction askAllowanceFromDate(LocalDate askAllowanceFromDate) {
    
    this.askAllowanceFromDate = askAllowanceFromDate;
    return this;
  }

  /**
   * Get askAllowanceFromDate
   * @return askAllowanceFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getAskAllowanceFromDate() {
    return askAllowanceFromDate;
  }


  @JsonProperty(JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAskAllowanceFromDate(LocalDate askAllowanceFromDate) {
    this.askAllowanceFromDate = askAllowanceFromDate;
  }

  public EC1ReasonIntroduction hasAlternateTrainingAndTemporaryUnemployment(Boolean hasAlternateTrainingAndTemporaryUnemployment) {
    
    this.hasAlternateTrainingAndTemporaryUnemployment = hasAlternateTrainingAndTemporaryUnemployment;
    return this;
  }

  /**
   * Get hasAlternateTrainingAndTemporaryUnemployment
   * @return hasAlternateTrainingAndTemporaryUnemployment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_ALTERNATE_TRAINING_AND_TEMPORARY_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasAlternateTrainingAndTemporaryUnemployment() {
    return hasAlternateTrainingAndTemporaryUnemployment;
  }


  @JsonProperty(JSON_PROPERTY_HAS_ALTERNATE_TRAINING_AND_TEMPORARY_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasAlternateTrainingAndTemporaryUnemployment(Boolean hasAlternateTrainingAndTemporaryUnemployment) {
    this.hasAlternateTrainingAndTemporaryUnemployment = hasAlternateTrainingAndTemporaryUnemployment;
  }

  public EC1ReasonIntroduction isAskingAllowanceFirstTime(Boolean isAskingAllowanceFirstTime) {
    
    this.isAskingAllowanceFirstTime = isAskingAllowanceFirstTime;
    return this;
  }

  /**
   * Get isAskingAllowanceFirstTime
   * @return isAskingAllowanceFirstTime
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_ASKING_ALLOWANCE_FIRST_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsAskingAllowanceFirstTime() {
    return isAskingAllowanceFirstTime;
  }


  @JsonProperty(JSON_PROPERTY_IS_ASKING_ALLOWANCE_FIRST_TIME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsAskingAllowanceFirstTime(Boolean isAskingAllowanceFirstTime) {
    this.isAskingAllowanceFirstTime = isAskingAllowanceFirstTime;
  }

  public EC1ReasonIntroduction changeOfPaymentOfficeFromDate(LocalDate changeOfPaymentOfficeFromDate) {
    
    this.changeOfPaymentOfficeFromDate = changeOfPaymentOfficeFromDate;
    return this;
  }

  /**
   * Get changeOfPaymentOfficeFromDate
   * @return changeOfPaymentOfficeFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHANGE_OF_PAYMENT_OFFICE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getChangeOfPaymentOfficeFromDate() {
    return changeOfPaymentOfficeFromDate;
  }


  @JsonProperty(JSON_PROPERTY_CHANGE_OF_PAYMENT_OFFICE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChangeOfPaymentOfficeFromDate(LocalDate changeOfPaymentOfficeFromDate) {
    this.changeOfPaymentOfficeFromDate = changeOfPaymentOfficeFromDate;
  }

  public EC1ReasonIntroduction hasDeclareModificationAbout(Boolean hasDeclareModificationAbout) {
    
    this.hasDeclareModificationAbout = hasDeclareModificationAbout;
    return this;
  }

  /**
   * Get hasDeclareModificationAbout
   * @return hasDeclareModificationAbout
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_DECLARE_MODIFICATION_ABOUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasDeclareModificationAbout() {
    return hasDeclareModificationAbout;
  }


  @JsonProperty(JSON_PROPERTY_HAS_DECLARE_MODIFICATION_ABOUT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasDeclareModificationAbout(Boolean hasDeclareModificationAbout) {
    this.hasDeclareModificationAbout = hasDeclareModificationAbout;
  }

  public EC1ReasonIntroduction changeMyAddressFromDate(LocalDate changeMyAddressFromDate) {
    
    this.changeMyAddressFromDate = changeMyAddressFromDate;
    return this;
  }

  /**
   * Get changeMyAddressFromDate
   * @return changeMyAddressFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHANGE_MY_ADDRESS_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getChangeMyAddressFromDate() {
    return changeMyAddressFromDate;
  }


  @JsonProperty(JSON_PROPERTY_CHANGE_MY_ADDRESS_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChangeMyAddressFromDate(LocalDate changeMyAddressFromDate) {
    this.changeMyAddressFromDate = changeMyAddressFromDate;
  }

  public EC1ReasonIntroduction hasChangeDeductionTradeUnionContribution(Boolean hasChangeDeductionTradeUnionContribution) {
    
    this.hasChangeDeductionTradeUnionContribution = hasChangeDeductionTradeUnionContribution;
    return this;
  }

  /**
   * Get hasChangeDeductionTradeUnionContribution
   * @return hasChangeDeductionTradeUnionContribution
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_CHANGE_DEDUCTION_TRADE_UNION_CONTRIBUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasChangeDeductionTradeUnionContribution() {
    return hasChangeDeductionTradeUnionContribution;
  }


  @JsonProperty(JSON_PROPERTY_HAS_CHANGE_DEDUCTION_TRADE_UNION_CONTRIBUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasChangeDeductionTradeUnionContribution(Boolean hasChangeDeductionTradeUnionContribution) {
    this.hasChangeDeductionTradeUnionContribution = hasChangeDeductionTradeUnionContribution;
  }

  public EC1ReasonIntroduction hasChangeResidenceOrWorkPermit(Boolean hasChangeResidenceOrWorkPermit) {
    
    this.hasChangeResidenceOrWorkPermit = hasChangeResidenceOrWorkPermit;
    return this;
  }

  /**
   * Get hasChangeResidenceOrWorkPermit
   * @return hasChangeResidenceOrWorkPermit
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_CHANGE_RESIDENCE_OR_WORK_PERMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasChangeResidenceOrWorkPermit() {
    return hasChangeResidenceOrWorkPermit;
  }


  @JsonProperty(JSON_PROPERTY_HAS_CHANGE_RESIDENCE_OR_WORK_PERMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasChangeResidenceOrWorkPermit(Boolean hasChangeResidenceOrWorkPermit) {
    this.hasChangeResidenceOrWorkPermit = hasChangeResidenceOrWorkPermit;
  }

  public EC1ReasonIntroduction changeSituationPersonalOrFamilyFromDate(LocalDate changeSituationPersonalOrFamilyFromDate) {
    
    this.changeSituationPersonalOrFamilyFromDate = changeSituationPersonalOrFamilyFromDate;
    return this;
  }

  /**
   * Get changeSituationPersonalOrFamilyFromDate
   * @return changeSituationPersonalOrFamilyFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHANGE_SITUATION_PERSONAL_OR_FAMILY_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getChangeSituationPersonalOrFamilyFromDate() {
    return changeSituationPersonalOrFamilyFromDate;
  }


  @JsonProperty(JSON_PROPERTY_CHANGE_SITUATION_PERSONAL_OR_FAMILY_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChangeSituationPersonalOrFamilyFromDate(LocalDate changeSituationPersonalOrFamilyFromDate) {
    this.changeSituationPersonalOrFamilyFromDate = changeSituationPersonalOrFamilyFromDate;
  }

  public EC1ReasonIntroduction changeModeOfPaymentOrAccountNrFromDate(LocalDate changeModeOfPaymentOrAccountNrFromDate) {
    
    this.changeModeOfPaymentOrAccountNrFromDate = changeModeOfPaymentOrAccountNrFromDate;
    return this;
  }

  /**
   * Get changeModeOfPaymentOrAccountNrFromDate
   * @return changeModeOfPaymentOrAccountNrFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CHANGE_MODE_OF_PAYMENT_OR_ACCOUNT_NR_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getChangeModeOfPaymentOrAccountNrFromDate() {
    return changeModeOfPaymentOrAccountNrFromDate;
  }


  @JsonProperty(JSON_PROPERTY_CHANGE_MODE_OF_PAYMENT_OR_ACCOUNT_NR_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setChangeModeOfPaymentOrAccountNrFromDate(LocalDate changeModeOfPaymentOrAccountNrFromDate) {
    this.changeModeOfPaymentOrAccountNrFromDate = changeModeOfPaymentOrAccountNrFromDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1ReasonIntroduction ec1ReasonIntroduction = (EC1ReasonIntroduction) o;
    return Objects.equals(this.askAllowanceFromDate, ec1ReasonIntroduction.askAllowanceFromDate) &&
        Objects.equals(this.hasAlternateTrainingAndTemporaryUnemployment, ec1ReasonIntroduction.hasAlternateTrainingAndTemporaryUnemployment) &&
        Objects.equals(this.isAskingAllowanceFirstTime, ec1ReasonIntroduction.isAskingAllowanceFirstTime) &&
        Objects.equals(this.changeOfPaymentOfficeFromDate, ec1ReasonIntroduction.changeOfPaymentOfficeFromDate) &&
        Objects.equals(this.hasDeclareModificationAbout, ec1ReasonIntroduction.hasDeclareModificationAbout) &&
        Objects.equals(this.changeMyAddressFromDate, ec1ReasonIntroduction.changeMyAddressFromDate) &&
        Objects.equals(this.hasChangeDeductionTradeUnionContribution, ec1ReasonIntroduction.hasChangeDeductionTradeUnionContribution) &&
        Objects.equals(this.hasChangeResidenceOrWorkPermit, ec1ReasonIntroduction.hasChangeResidenceOrWorkPermit) &&
        Objects.equals(this.changeSituationPersonalOrFamilyFromDate, ec1ReasonIntroduction.changeSituationPersonalOrFamilyFromDate) &&
        Objects.equals(this.changeModeOfPaymentOrAccountNrFromDate, ec1ReasonIntroduction.changeModeOfPaymentOrAccountNrFromDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(askAllowanceFromDate, hasAlternateTrainingAndTemporaryUnemployment, isAskingAllowanceFirstTime, changeOfPaymentOfficeFromDate, hasDeclareModificationAbout, changeMyAddressFromDate, hasChangeDeductionTradeUnionContribution, hasChangeResidenceOrWorkPermit, changeSituationPersonalOrFamilyFromDate, changeModeOfPaymentOrAccountNrFromDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1ReasonIntroduction {\n");
    sb.append("    askAllowanceFromDate: ").append(toIndentedString(askAllowanceFromDate)).append("\n");
    sb.append("    hasAlternateTrainingAndTemporaryUnemployment: ").append(toIndentedString(hasAlternateTrainingAndTemporaryUnemployment)).append("\n");
    sb.append("    isAskingAllowanceFirstTime: ").append(toIndentedString(isAskingAllowanceFirstTime)).append("\n");
    sb.append("    changeOfPaymentOfficeFromDate: ").append(toIndentedString(changeOfPaymentOfficeFromDate)).append("\n");
    sb.append("    hasDeclareModificationAbout: ").append(toIndentedString(hasDeclareModificationAbout)).append("\n");
    sb.append("    changeMyAddressFromDate: ").append(toIndentedString(changeMyAddressFromDate)).append("\n");
    sb.append("    hasChangeDeductionTradeUnionContribution: ").append(toIndentedString(hasChangeDeductionTradeUnionContribution)).append("\n");
    sb.append("    hasChangeResidenceOrWorkPermit: ").append(toIndentedString(hasChangeResidenceOrWorkPermit)).append("\n");
    sb.append("    changeSituationPersonalOrFamilyFromDate: ").append(toIndentedString(changeSituationPersonalOrFamilyFromDate)).append("\n");
    sb.append("    changeModeOfPaymentOrAccountNrFromDate: ").append(toIndentedString(changeModeOfPaymentOrAccountNrFromDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

