/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.Period;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RegisterAddress
 */
@JsonPropertyOrder({
  RegisterAddress.JSON_PROPERTY_AT_TYPE,
  RegisterAddress.JSON_PROPERTY_RADIATED,
  RegisterAddress.JSON_PROPERTY_VALIDITY_PERIOD
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@JsonIgnoreProperties(
  value = "@type", // ignore manually set @type, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the @type to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type", visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(value = ContactAddress.class, name = "ContactAddress"),
  @JsonSubTypes.Type(value = DiplomaticAddress.class, name = "DiplomaticAddress"),
  @JsonSubTypes.Type(value = ResidentialAddress.class, name = "ResidentialAddress"),
})

public class RegisterAddress {
  /**
   * Gets or Sets atType
   */
  public enum AtTypeEnum {
    RESIDENTIAL_ADDRESS("ResidentialAddress"),
    
    DIPLOMATIC_ADDRESS("DiplomaticAddress"),
    
    CONTACT_ADDRESS("ContactAddress");

    private String value;

    AtTypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static AtTypeEnum fromValue(String value) {
      for (AtTypeEnum b : AtTypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_AT_TYPE = "@type";
  protected AtTypeEnum atType;

  public static final String JSON_PROPERTY_RADIATED = "radiated";
  protected Boolean radiated;

  public static final String JSON_PROPERTY_VALIDITY_PERIOD = "validityPeriod";
  protected Period validityPeriod;

  public RegisterAddress() {
  }

  public RegisterAddress atType(AtTypeEnum atType) {
    
    this.atType = atType;
    return this;
  }

  /**
   * Get atType
   * @return atType
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_AT_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public AtTypeEnum getAtType() {
    return atType;
  }


  @JsonProperty(JSON_PROPERTY_AT_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAtType(AtTypeEnum atType) {
    this.atType = atType;
  }

  public RegisterAddress radiated(Boolean radiated) {
    
    this.radiated = radiated;
    return this;
  }

  /**
   * Get radiated
   * @return radiated
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RADIATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRadiated() {
    return radiated;
  }


  @JsonProperty(JSON_PROPERTY_RADIATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRadiated(Boolean radiated) {
    this.radiated = radiated;
  }

  public RegisterAddress validityPeriod(Period validityPeriod) {
    
    this.validityPeriod = validityPeriod;
    return this;
  }

  /**
   * Get validityPeriod
   * @return validityPeriod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALIDITY_PERIOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Period getValidityPeriod() {
    return validityPeriod;
  }


  @JsonProperty(JSON_PROPERTY_VALIDITY_PERIOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValidityPeriod(Period validityPeriod) {
    this.validityPeriod = validityPeriod;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegisterAddress registerAddress = (RegisterAddress) o;
    return Objects.equals(this.atType, registerAddress.atType) &&
        Objects.equals(this.radiated, registerAddress.radiated) &&
        Objects.equals(this.validityPeriod, registerAddress.validityPeriod);
  }

  @Override
  public int hashCode() {
    return Objects.hash(atType, radiated, validityPeriod);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegisterAddress {\n");
    sb.append("    atType: ").append(toIndentedString(atType)).append("\n");
    sb.append("    radiated: ").append(toIndentedString(radiated)).append("\n");
    sb.append("    validityPeriod: ").append(toIndentedString(validityPeriod)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

