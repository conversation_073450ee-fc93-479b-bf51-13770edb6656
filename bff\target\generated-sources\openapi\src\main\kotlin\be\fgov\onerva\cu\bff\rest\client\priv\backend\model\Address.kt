/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param country The country of residence
 * @param street The street name
 * @param houseNumber The house number
 * @param zipCode The postal/zip code
 * @param city The city name
 * @param boxNumber The box number (optional)
 */


data class Address (

    /* The country of residence */
    @get:JsonProperty("country")
    val country: kotlin.String,

    /* The street name */
    @get:JsonProperty("street")
    val street: kotlin.String,

    /* The house number */
    @get:JsonProperty("houseNumber")
    val houseNumber: kotlin.String,

    /* The postal/zip code */
    @get:JsonProperty("zipCode")
    val zipCode: kotlin.String,

    /* The city name */
    @get:JsonProperty("city")
    val city: kotlin.String,

    /* The box number (optional) */
    @get:JsonProperty("boxNumber")
    val boxNumber: kotlin.String? = null

) {


}

