/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.Address

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param address 
 * @param nationality The nationality of the employee
 */


data class UpdateCitizenInformationRequest (

    /* The birth date of the employee (format YYYY-MM-DD) */
    @get:JsonProperty("birthDate")
    val birthDate: java.time.LocalDate,

    @get:JsonProperty("address")
    val address: Address,

    /* The nationality of the employee */
    @get:J<PERSON><PERSON>roper<PERSON>("nationality")
    val nationality: kotlin.String? = null

) {


}

