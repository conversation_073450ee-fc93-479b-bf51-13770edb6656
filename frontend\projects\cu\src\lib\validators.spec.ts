import {FormControl} from "@angular/forms";
import CustomValidators, {validateNiss} from "./validators";
import {DateUtils} from "./date.utils";
import {DateTime} from "luxon";

jest.mock("luxon", () => {
    const actualLuxon = jest.requireActual("luxon");
    return {
        ...actualLuxon,
        DateTime: {
            ...actualLuxon.DateTime,
            now: () => actualLuxon.DateTime.fromISO("2023-01-15T10:00:00"),
        },
    };
});

describe("CustomValidators", () => {
    describe("dateInPastOrPresent", () => {
        const validator = CustomValidators.dateInPastOrPresent();

        beforeEach(() => {
            jest.spyOn(DateUtils, "convertToDateTime").mockImplementation((date) => {
                if (!date) {
                    return null;
                }

                if (typeof date === "string") {
                    try {
                        return DateTime.fromISO(date);
                    } catch (e) {
                        console.error("Failed to parse date string:", e);
                        return null;
                    }
                }

                if (date instanceof Date) {
                    return DateTime.fromJSDate(date);
                }

                return date;
            });
        });

        afterEach(() => {
            jest.restoreAllMocks();
        });

        it("should return null if the control already has a different error", () => {
            const control = new FormControl("2024-01-15T10:00:00");
            control.setErrors({anotherError: true});

            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return null if the control value is null or undefined", () => {
            const control = new FormControl(null);
            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return null for a date in the past", () => {
            const control = new FormControl("2022-01-01T10:00:00");
            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return null for the current date", () => {
            const control = new FormControl("2023-01-15T10:00:00");
            const result = validator(control);
            expect(result).toBeNull();
        });
    });

    describe("countryLookupValidator", () => {
        const validator = CustomValidators.countryLookupValidator();

        it("should return null if control already has a different error", () => {
            const control = new FormControl({code: "BE"});
            control.setErrors({anotherError: true});

            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return null if control.value is null or undefined", () => {
            const control = new FormControl(null);
            const result = validator(control);
            expect(result).toBeNull();

            const emptyControl = new FormControl("");
            const emptyResult = validator(emptyControl);
            expect(emptyResult).toBeNull();
        });

        it("should return null if control.value has a \"code\" property", () => {
            const control = new FormControl({code: "BE"});
            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return an error if control.value does not have a \"code\" property", () => {
            const control = new FormControl({label: "Belgium"});
            const result = validator(control);
            expect(result).toEqual({countryLookupNotSelected: true});
        });

        it("should return an error if control.value.code is empty string", () => {
            const control = new FormControl({code: ""});
            const result = validator(control);
            expect(result).toEqual({countryLookupNotSelected: true});
        });

        it("should handle different input types correctly", () => {
            const stringControl = new FormControl("Belgium");
            expect(validator(stringControl)).toEqual({countryLookupNotSelected: true});

            const arrayControl = new FormControl(["BE", "FR"]);
            expect(validator(arrayControl)).toEqual({countryLookupNotSelected: true});

            const nullCodeControl = new FormControl({code: null});
            expect(validator(nullCodeControl)).toEqual({countryLookupNotSelected: true});
        });
    });
    describe("bicValidator", () => {
        const validator = CustomValidators.bicValidator();

        it("should return null if control already has a different error", () => {
            const control = new FormControl("DEUTDEFFXXX");
            control.setErrors({anotherError: true});

            const result = validator(control);
            expect(result).toBeNull();
        });

        it("should return null if control.value is null or empty", () => {
            const nullControl = new FormControl(null);
            expect(validator(nullControl)).toBeNull();

            const emptyControl = new FormControl("");
            expect(validator(emptyControl)).toBeNull();
        });

        it("should return null for valid BIC codes with 8 characters", () => {
            const validBICs = [
                "DEUTDEFF",  // Deutsche Bank, Germany
                "BNPAFRPP",  // BNP Paribas, France
                "MIDLGB22",  // HSBC, UK
                "INGBNL2A",   // ING, Netherlands
            ];

            validBICs.forEach(bic => {
                const control = new FormControl(bic);
                expect(validator(control)).toBeNull();
            });
        });

        it("should return null for valid BIC codes with 11 characters", () => {
            const validBICs = [
                "DEUTDEFFXXX",  // Deutsche Bank, Germany with branch code
                "BNPAFRPPXXX",  // BNP Paribas, France with branch code
                "MIDLGB22123",  // HSBC, UK with numeric branch code
                "INGBNL2A123",   // ING, Netherlands with numeric branch code
            ];

            validBICs.forEach(bic => {
                const control = new FormControl(bic);
                expect(validator(control)).toBeNull();
            });
        });

        it("should return error for BIC codes with incorrect length", () => {
            const invalidBICs = [
                "DEUT",        // Too short
                "DEUTDEF",     // 7 characters (too short)
                "DEUTDEFFXX",  // 10 characters (invalid length)
                "DEUTDEFFXXXX", // 12 characters (too long)
            ];

            invalidBICs.forEach(bic => {
                const control = new FormControl(bic);
                expect(validator(control)).toEqual({invalidBic: true});
            });
        });

        it("should return error for BIC codes with invalid character format", () => {
            const invalidBICs = [
                "deut1234",     // Lowercase letters
                "DEUT123D",     // Numbers in bank code (first 4 chars)
                "DEUT12PP",     // Numbers in country code (chars 5-6)
                "DEUTDE1$",     // Special characters
                "DEUTDE-1",     // Special characters
                "DEUTDE 1",      // Spaces
            ];

            invalidBICs.forEach(bic => {
                const control = new FormControl(bic);
                expect(validator(control)).toEqual({invalidBic: true});
            });
        });

        it("should handle edge cases and mixed input types", () => {
            const testCases = [
                {niss: "***********", expected: null},
                {niss: "***********", expected: {checkSum: true}},
                {niss: null, expected: null},
                {niss: undefined, expected: null},
            ];

            testCases.forEach(({niss, expected}) => {
                const control = new FormControl(niss);
                expect(validateNiss(control)).toEqual(expected);
            });
        });
    });
});

describe("validateNiss", () => {
    it("should return null if value is empty", () => {
        const control = new FormControl("");
        expect(validateNiss(control)).toBeNull();
    });

    it("should return null if value length is not 11", () => {
        const control = new FormControl("1234567890");
        expect(validateNiss(control)).toBeNull();

        const tooLongControl = new FormControl("123456789012");
        expect(validateNiss(tooLongControl)).toBeNull();
    });

    it("should return null if the NISS is correct (checksum valid)", () => {
        const control = new FormControl("93051822361");
        expect(validateNiss(control)).toBeNull();
    });

    it("should return { checkSum: true } for invalid NISS for person born after 2000", () => {
        const control = new FormControl("01010199976");
        expect(validateNiss(control)).toEqual({checkSum: true});
    });

    it("should return { checkSum: true } if the checksum is invalid", () => {
        const control = new FormControl("93051822362");
        expect(validateNiss(control)).toEqual({checkSum: true});
    });

    it("should return { checkSum: true } for this NISS with leading zeros", () => {
        const control = new FormControl("01010133737");
        expect(validateNiss(control)).toEqual({checkSum: true});
    });

    it("should validate various edge cases for NISS", () => {
        const testCases = [
            {niss: "***********", expected: null},
            {niss: "***********", expected: {checkSum: true}},
            {niss: null, expected: null},
            {niss: undefined, expected: null},
        ];

        testCases.forEach(({niss, expected}) => {
            const control = new FormControl(niss);
            expect(validateNiss(control)).toEqual(expected);
        });
    });

    it("should handle non-string NISS values", () => {
        const numberControl = new FormControl(93051822361);
        expect(validateNiss(numberControl)).toBeNull();

        const objectControl = new FormControl({niss: "93051822361"});
        expect(validateNiss(objectControl)).toBeNull();
    });
});