package be.fgov.onerva.cu.backend.adapter.out.external.featureflag

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.port.out.FeatureFlagPort
import dev.openfeature.sdk.Client

@Service
class FeatureFlagAdapter(private val client: Client) : FeatureFlagPort {

    override fun isFeatureEnabled(flagName: String, defaultValue: Boolean): Boolean {
        return client.getBooleanValue(flagName, defaultValue)
    }

    override fun getFeatureListValue(flagName: String, defaultValue: List<String>): List<String> {
        val enabled = this.isFeatureEnabled(flagName, false)
        if (!enabled) {
            return defaultValue
        }
        val value = client.getStringValue(flagName, "")
        return value.split(",").toList()
    }
}
