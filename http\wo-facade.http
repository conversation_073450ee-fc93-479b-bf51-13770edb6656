### Opened tasks - Run this to get the latest task in Wave so the lastRequestId can be used in the other calls
POST {{wo-facade-url}}/api/tasks/search
Content-Type: application/json

{
  "taskStatus": "OPEN"
}
> {%
    let tasks = response.body.content
    tasks.sort(function(a, b) {
        return a.creationDate.localeCompare(b.creationDate)
    })
    let latestTask = tasks[tasks.length- 1]
    let latestTaskId = latestTask.taskId

    console.log("Latest task: ", latestTaskId)
    client.global.set('latestTaskId', latestTaskId)
    latestTask.metadata.forEach(function(metadata) {
        if(metadata.code === "CU_REQUEST_ID") {
            console.log("Setting latestRequestId to " + metadata.value)
            client.global.set('latestRequestId', metadata.value)
        }
    })

%}



### Tasks for requestId
POST {{wo-facade-url}}/api/tasks/search
Content-Type: application/json

{
  "taskStatus": "OPEN",
  "taskCodes": ["CHANGE_PERSONAL_DATA_CAPTURE"],
  "metadata": [
      [
        {
          "code": "CU_REQUEST_ID",
          "value":[ "{{latestRequestId}}"]
        }
      ]
  ]
}

### Openend tasks
POST {{wo-facade-url}}/api/tasks/search
Content-Type: application/json

{
  "taskStatus": "OPEN"
}


### Closed tasks
POST {{wo-facade-url}}/api/tasks/search
Content-Type: application/json

{
  "taskStatus": "CLOSED"
}

### Get metadata from latestTask
< {%
    let latestTaskId = client.global.get('latestTaskId');
    console.log('latestTaskId: ' + latestTaskId);
    request.variables.set('latestTaskId', latestTaskId);
%}
GET {{wo-facade-url}}/api/tasks/{{latestTaskId}}

> {%
    console.log("Task metadata:")
    response.body.metadata.forEach(function(metadata) {
        console.log(metadata.code + " = " + metadata.value)
    })
    console.log("\nProcess metadata:")
    response.body.processMetadata.forEach(function(metadata) {
        console.log(metadata.code + " = " + metadata.value)
        if (metadata.code === "CU_REQUEST_ID") {
            console.log("Setting latestRequestId to " + metadata.value)
            client.global.set('latestRequestId', metadata.value)
        }
    })
%}

### GET users
GET {{wo-facade-url}}/api/users?inss=sfsdf
Content-Type: application/json
