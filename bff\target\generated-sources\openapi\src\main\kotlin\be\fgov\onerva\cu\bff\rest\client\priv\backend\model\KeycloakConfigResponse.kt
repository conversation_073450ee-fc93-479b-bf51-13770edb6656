/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.KeycloakConfigResponseConfig
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.KeycloakConfigResponseInitOptions

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param config 
 * @param initOptions 
 */


data class KeycloakConfigResponse (

    @get:JsonProperty("config")
    val config: KeycloakConfigResponseConfig,

    @get:JsonProperty("initOptions")
    val initOptions: KeycloakConfigResponseInitOptions? = null

) {


}

