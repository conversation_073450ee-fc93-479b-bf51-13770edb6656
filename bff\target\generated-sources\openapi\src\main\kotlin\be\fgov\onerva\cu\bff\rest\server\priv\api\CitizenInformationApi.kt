/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface CitizenInformationApi {

    @Operation(
        tags = ["Citizen Information",],
        summary = "",
        operationId = "getCitizenInformation",
        description = """Retrieve employee information for a specific request""",
        responses = [
            ApiResponse(responseCode = "200", description = "Employee information successfully retrieved", content = [Content(schema = Schema(implementation = CitizenInformationDetailResponse::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/requests/{requestId}/citizen-information"],
            produces = ["application/json"]
    )
    fun getCitizenInformation(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<CitizenInformationDetailResponse>

    @Operation(
        tags = ["Citizen Information",],
        summary = "",
        operationId = "selectCitizenInformationSources",
        description = """Select sources for individual fields in citizen information""",
        responses = [
            ApiResponse(responseCode = "204", description = "Field sources successfully selected"),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/citizen-information/select"],
            consumes = ["application/json"]
    )
    fun selectCitizenInformationSources(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "", required = true) @Valid @RequestBody selectFieldSourcesRequest: SelectFieldSourcesRequest): ResponseEntity<Unit>

    @Operation(
        tags = ["Citizen Information",],
        summary = "",
        operationId = "updateCitizenInformation",
        description = """Update mutable employee information for a specific request""",
        responses = [
            ApiResponse(responseCode = "204", description = "Employee information successfully updated"),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/citizen-information"],
            consumes = ["application/json"]
    )
    fun updateCitizenInformation(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "", required = true) @Valid @RequestBody updateCitizenInformationRequest: UpdateCitizenInformationRequest): ResponseEntity<Unit>
}
