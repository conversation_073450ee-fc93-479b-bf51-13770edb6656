import {Pipe, PipeTransform} from '@angular/core';

@Pipe({
    name: 'formatBarema',
    standalone: true
})
export class FormatBaremaPipe implements PipeTransform {
    transform(barema: string | undefined): string {
        if(!barema){
            return '';
        }
        if (barema && barema.length >= 2 && /^\d\d/.test(barema)) {
            return barema.slice(0, 2) + '/' + barema.slice(2);
        }
        return barema;
    }
}
