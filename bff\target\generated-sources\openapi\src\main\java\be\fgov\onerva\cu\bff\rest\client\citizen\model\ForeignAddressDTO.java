/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ForeignAddressDTO
 */
@JsonPropertyOrder({
  ForeignAddressDTO.JSON_PROPERTY_STREET,
  ForeignAddressDTO.JSON_PROPERTY_NUMBER,
  ForeignAddressDTO.JSON_PROPERTY_BOX,
  ForeignAddressDTO.JSON_PROPERTY_ZIP,
  ForeignAddressDTO.JSON_PROPERTY_CITY,
  ForeignAddressDTO.JSON_PROPERTY_COUNTRY_CODE,
  ForeignAddressDTO.JSON_PROPERTY_VALID_FROM
})
@JsonTypeName("ForeignAddress")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ForeignAddressDTO {
  public static final String JSON_PROPERTY_STREET = "street";
  private String street;

  public static final String JSON_PROPERTY_NUMBER = "number";
  private String number;

  public static final String JSON_PROPERTY_BOX = "box";
  private String box;

  public static final String JSON_PROPERTY_ZIP = "zip";
  private String zip;

  public static final String JSON_PROPERTY_CITY = "city";
  private String city;

  public static final String JSON_PROPERTY_COUNTRY_CODE = "countryCode";
  private Integer countryCode;

  public static final String JSON_PROPERTY_VALID_FROM = "validFrom";
  private LocalDate validFrom;

  public ForeignAddressDTO() {
  }

  public ForeignAddressDTO street(String street) {
    
    this.street = street;
    return this;
  }

  /**
   * Get street
   * @return street
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStreet() {
    return street;
  }


  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStreet(String street) {
    this.street = street;
  }

  public ForeignAddressDTO number(String number) {
    
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNumber() {
    return number;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumber(String number) {
    this.number = number;
  }

  public ForeignAddressDTO box(String box) {
    
    this.box = box;
    return this;
  }

  /**
   * Get box
   * @return box
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBox() {
    return box;
  }


  @JsonProperty(JSON_PROPERTY_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBox(String box) {
    this.box = box;
  }

  public ForeignAddressDTO zip(String zip) {
    
    this.zip = zip;
    return this;
  }

  /**
   * Get zip
   * @return zip
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getZip() {
    return zip;
  }


  @JsonProperty(JSON_PROPERTY_ZIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZip(String zip) {
    this.zip = zip;
  }

  public ForeignAddressDTO city(String city) {
    
    this.city = city;
    return this;
  }

  /**
   * Get city
   * @return city
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCity(String city) {
    this.city = city;
  }

  public ForeignAddressDTO countryCode(Integer countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

  /**
   * NEO country code. List of possible values http://services/lookupwppt/lookups/common/CountryList.seam
   * @return countryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCountryCode() {
    return countryCode;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCode(Integer countryCode) {
    this.countryCode = countryCode;
  }

  public ForeignAddressDTO validFrom(LocalDate validFrom) {
    
    this.validFrom = validFrom;
    return this;
  }

  /**
   * Get validFrom
   * @return validFrom
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getValidFrom() {
    return validFrom;
  }


  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValidFrom(LocalDate validFrom) {
    this.validFrom = validFrom;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ForeignAddressDTO foreignAddress = (ForeignAddressDTO) o;
    return Objects.equals(this.street, foreignAddress.street) &&
        Objects.equals(this.number, foreignAddress.number) &&
        Objects.equals(this.box, foreignAddress.box) &&
        Objects.equals(this.zip, foreignAddress.zip) &&
        Objects.equals(this.city, foreignAddress.city) &&
        Objects.equals(this.countryCode, foreignAddress.countryCode) &&
        Objects.equals(this.validFrom, foreignAddress.validFrom);
  }

  @Override
  public int hashCode() {
    return Objects.hash(street, number, box, zip, city, countryCode, validFrom);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ForeignAddressDTO {\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    box: ").append(toIndentedString(box)).append("\n");
    sb.append("    zip: ").append(toIndentedString(zip)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("    validFrom: ").append(toIndentedString(validFrom)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

