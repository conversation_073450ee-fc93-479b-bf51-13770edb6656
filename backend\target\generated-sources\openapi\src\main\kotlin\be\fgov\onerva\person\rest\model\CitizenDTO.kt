/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Citizen information
 *
 * @param niss 
 * @param firstname 
 * @param lastname 
 * @param numbox 
 * @param zipCode 
 * @param pensionNumber 
 * @param agent 
 */


data class CitizenDTO (

    @get:JsonProperty("niss")
    val niss: kotlin.String? = null,

    @get:JsonProperty("firstname")
    val firstname: kotlin.String? = null,

    @get:JsonProperty("lastname")
    val lastname: kotlin.String? = null,

    @get:JsonProperty("numbox")
    val numbox: kotlin.Int? = null,

    @get:JsonProperty("zipCode")
    val zipCode: kotlin.Int? = null,

    @get:JsonProperty("pensionNumber")
    val pensionNumber: kotlin.Int? = null,

    @get:Json<PERSON>roperty("agent")
    val agent: kotlin.<PERSON>olean? = null

) {


}

