/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.ProfessionalActivity;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.ReplacementIncome;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * FamilyPersonInfo
 */
@JsonPropertyOrder({
  FamilyPersonInfo.JSON_PROPERTY_LAST_NAME,
  FamilyPersonInfo.JSON_PROPERTY_FIRST_NAME,
  FamilyPersonInfo.JSON_PROPERTY_AFFINITY,
  FamilyPersonInfo.JSON_PROPERTY_DATE_OF_BIRTH,
  FamilyPersonInfo.JSON_PROPERTY_HAS_CHILD_SUPPORT,
  FamilyPersonInfo.JSON_PROPERTY_PROFESSIONAL_ACTIVITIES,
  FamilyPersonInfo.JSON_PROPERTY_REPLACEMENT_INCOMES,
  FamilyPersonInfo.JSON_PROPERTY_FINANCIALLY_DEPENDENT,
  FamilyPersonInfo.JSON_PROPERTY_IS_DECLARING_C1_PARTNER
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class FamilyPersonInfo {
  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_AFFINITY = "affinity";
  private String affinity;

  public static final String JSON_PROPERTY_DATE_OF_BIRTH = "dateOfBirth";
  private LocalDate dateOfBirth;

  public static final String JSON_PROPERTY_HAS_CHILD_SUPPORT = "hasChildSupport";
  private Boolean hasChildSupport;

  public static final String JSON_PROPERTY_PROFESSIONAL_ACTIVITIES = "professionalActivities";
  private List<ProfessionalActivity> professionalActivities;

  public static final String JSON_PROPERTY_REPLACEMENT_INCOMES = "replacementIncomes";
  private List<ReplacementIncome> replacementIncomes;

  public static final String JSON_PROPERTY_FINANCIALLY_DEPENDENT = "financiallyDependent";
  private Boolean financiallyDependent;

  public static final String JSON_PROPERTY_IS_DECLARING_C1_PARTNER = "isDeclaringC1Partner";
  private Boolean isDeclaringC1Partner;

  public FamilyPersonInfo() {
  }

  public FamilyPersonInfo lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public FamilyPersonInfo firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public FamilyPersonInfo affinity(String affinity) {
    
    this.affinity = affinity;
    return this;
  }

  /**
   * Get affinity
   * @return affinity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AFFINITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAffinity() {
    return affinity;
  }


  @JsonProperty(JSON_PROPERTY_AFFINITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAffinity(String affinity) {
    this.affinity = affinity;
  }

  public FamilyPersonInfo dateOfBirth(LocalDate dateOfBirth) {
    
    this.dateOfBirth = dateOfBirth;
    return this;
  }

  /**
   * Get dateOfBirth
   * @return dateOfBirth
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_OF_BIRTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateOfBirth() {
    return dateOfBirth;
  }


  @JsonProperty(JSON_PROPERTY_DATE_OF_BIRTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateOfBirth(LocalDate dateOfBirth) {
    this.dateOfBirth = dateOfBirth;
  }

  public FamilyPersonInfo hasChildSupport(Boolean hasChildSupport) {
    
    this.hasChildSupport = hasChildSupport;
    return this;
  }

  /**
   * Get hasChildSupport
   * @return hasChildSupport
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_CHILD_SUPPORT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasChildSupport() {
    return hasChildSupport;
  }


  @JsonProperty(JSON_PROPERTY_HAS_CHILD_SUPPORT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasChildSupport(Boolean hasChildSupport) {
    this.hasChildSupport = hasChildSupport;
  }

  public FamilyPersonInfo professionalActivities(List<ProfessionalActivity> professionalActivities) {
    
    this.professionalActivities = professionalActivities;
    return this;
  }

  public FamilyPersonInfo addProfessionalActivitiesItem(ProfessionalActivity professionalActivitiesItem) {
    if (this.professionalActivities == null) {
      this.professionalActivities = new ArrayList<>();
    }
    this.professionalActivities.add(professionalActivitiesItem);
    return this;
  }

  /**
   * Get professionalActivities
   * @return professionalActivities
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROFESSIONAL_ACTIVITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ProfessionalActivity> getProfessionalActivities() {
    return professionalActivities;
  }


  @JsonProperty(JSON_PROPERTY_PROFESSIONAL_ACTIVITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProfessionalActivities(List<ProfessionalActivity> professionalActivities) {
    this.professionalActivities = professionalActivities;
  }

  public FamilyPersonInfo replacementIncomes(List<ReplacementIncome> replacementIncomes) {
    
    this.replacementIncomes = replacementIncomes;
    return this;
  }

  public FamilyPersonInfo addReplacementIncomesItem(ReplacementIncome replacementIncomesItem) {
    if (this.replacementIncomes == null) {
      this.replacementIncomes = new ArrayList<>();
    }
    this.replacementIncomes.add(replacementIncomesItem);
    return this;
  }

  /**
   * Get replacementIncomes
   * @return replacementIncomes
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REPLACEMENT_INCOMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ReplacementIncome> getReplacementIncomes() {
    return replacementIncomes;
  }


  @JsonProperty(JSON_PROPERTY_REPLACEMENT_INCOMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReplacementIncomes(List<ReplacementIncome> replacementIncomes) {
    this.replacementIncomes = replacementIncomes;
  }

  public FamilyPersonInfo financiallyDependent(Boolean financiallyDependent) {
    
    this.financiallyDependent = financiallyDependent;
    return this;
  }

  /**
   * Get financiallyDependent
   * @return financiallyDependent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FINANCIALLY_DEPENDENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFinanciallyDependent() {
    return financiallyDependent;
  }


  @JsonProperty(JSON_PROPERTY_FINANCIALLY_DEPENDENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFinanciallyDependent(Boolean financiallyDependent) {
    this.financiallyDependent = financiallyDependent;
  }

  public FamilyPersonInfo isDeclaringC1Partner(Boolean isDeclaringC1Partner) {
    
    this.isDeclaringC1Partner = isDeclaringC1Partner;
    return this;
  }

  /**
   * Get isDeclaringC1Partner
   * @return isDeclaringC1Partner
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_DECLARING_C1_PARTNER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsDeclaringC1Partner() {
    return isDeclaringC1Partner;
  }


  @JsonProperty(JSON_PROPERTY_IS_DECLARING_C1_PARTNER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsDeclaringC1Partner(Boolean isDeclaringC1Partner) {
    this.isDeclaringC1Partner = isDeclaringC1Partner;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FamilyPersonInfo familyPersonInfo = (FamilyPersonInfo) o;
    return Objects.equals(this.lastName, familyPersonInfo.lastName) &&
        Objects.equals(this.firstName, familyPersonInfo.firstName) &&
        Objects.equals(this.affinity, familyPersonInfo.affinity) &&
        Objects.equals(this.dateOfBirth, familyPersonInfo.dateOfBirth) &&
        Objects.equals(this.hasChildSupport, familyPersonInfo.hasChildSupport) &&
        Objects.equals(this.professionalActivities, familyPersonInfo.professionalActivities) &&
        Objects.equals(this.replacementIncomes, familyPersonInfo.replacementIncomes) &&
        Objects.equals(this.financiallyDependent, familyPersonInfo.financiallyDependent) &&
        Objects.equals(this.isDeclaringC1Partner, familyPersonInfo.isDeclaringC1Partner);
  }

  @Override
  public int hashCode() {
    return Objects.hash(lastName, firstName, affinity, dateOfBirth, hasChildSupport, professionalActivities, replacementIncomes, financiallyDependent, isDeclaringC1Partner);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FamilyPersonInfo {\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    affinity: ").append(toIndentedString(affinity)).append("\n");
    sb.append("    dateOfBirth: ").append(toIndentedString(dateOfBirth)).append("\n");
    sb.append("    hasChildSupport: ").append(toIndentedString(hasChildSupport)).append("\n");
    sb.append("    professionalActivities: ").append(toIndentedString(professionalActivities)).append("\n");
    sb.append("    replacementIncomes: ").append(toIndentedString(replacementIncomes)).append("\n");
    sb.append("    financiallyDependent: ").append(toIndentedString(financiallyDependent)).append("\n");
    sb.append("    isDeclaringC1Partner: ").append(toIndentedString(isDeclaringC1Partner)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

