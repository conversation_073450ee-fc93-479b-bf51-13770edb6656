package be.fgov.onerva.cu.bff.adapter.out

import java.time.LocalDate
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import be.fgov.onerva.cu.bff.exceptions.S24Exception
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.NavigateMainframeToS24Api
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.InlineResponse2001
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.NavigateS24Body
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
@Execution(ExecutionMode.SAME_THREAD)
class C9NavigateMainframeToS24ServiceTest {

    @MockK
    lateinit var c9NavigateMainframeToS24Api: NavigateMainframeToS24Api

    @InjectMockKs
    lateinit var c9NavigateMainframeToS24Service: C9NavigateMainframeToS24Service

    @Test
    fun `openS24Session should open S24 session when valid input is provided`() {
        // Given
        val ssin = "85050599890"
        val dateValid = LocalDate.now()
        val navigateBody = NavigateS24Body().apply {
            this.ssin = ssin
            this.dateValid = dateValid
        }

        every { c9NavigateMainframeToS24Api.navigateMainframeToS24(navigateBody) } returns InlineResponse2001().apply {
            status = InlineResponse2001.StatusEnum.SUCCESS
        }

        // When
        c9NavigateMainframeToS24Service.openS24Session(ssin, dateValid)

        // Then
        // No exception should be thrown
        verify(exactly = 1) { c9NavigateMainframeToS24Api.navigateMainframeToS24(navigateBody) }
    }

    @Test
    fun `openS24Session should fail when invalid input is provided`() {
        // Given
        val ssin = "85050599890"
        val dateValid = LocalDate.now()
        val navigateBody = NavigateS24Body().apply {
            this.ssin = ssin
            this.dateValid = dateValid
        }

        every { c9NavigateMainframeToS24Api.navigateMainframeToS24(navigateBody) } returns InlineResponse2001().apply {
            status = InlineResponse2001.StatusEnum.FAILURE
        }

        // When & Then
        assertThrows<S24Exception> {
            c9NavigateMainframeToS24Service.openS24Session(ssin, dateValid)
        }
    }
}