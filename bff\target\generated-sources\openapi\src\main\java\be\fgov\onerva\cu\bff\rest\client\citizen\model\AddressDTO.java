/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AddressDTO
 */
@JsonPropertyOrder({
  AddressDTO.JSON_PROPERTY_STREET,
  AddressDTO.JSON_PROPERTY_NUMBER,
  AddressDTO.JSON_PROPERTY_BOX,
  AddressDTO.JSON_PROPERTY_ZIP,
  AddressDTO.JSON_PROPERTY_CITY
})
@JsonTypeName("Address")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class AddressDTO {
  public static final String JSON_PROPERTY_STREET = "street";
  private String street;

  public static final String JSON_PROPERTY_NUMBER = "number";
  private String number;

  public static final String JSON_PROPERTY_BOX = "box";
  private String box;

  public static final String JSON_PROPERTY_ZIP = "zip";
  private Integer zip;

  public static final String JSON_PROPERTY_CITY = "city";
  private String city;

  public AddressDTO() {
  }

  public AddressDTO street(String street) {
    
    this.street = street;
    return this;
  }

  /**
   * Get street
   * @return street
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getStreet() {
    return street;
  }


  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setStreet(String street) {
    this.street = street;
  }

  public AddressDTO number(String number) {
    
    this.number = number;
    return this;
  }

  /**
   * Get number
   * @return number
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNumber() {
    return number;
  }


  @JsonProperty(JSON_PROPERTY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumber(String number) {
    this.number = number;
  }

  public AddressDTO box(String box) {
    
    this.box = box;
    return this;
  }

  /**
   * Get box
   * @return box
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBox() {
    return box;
  }


  @JsonProperty(JSON_PROPERTY_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBox(String box) {
    this.box = box;
  }

  public AddressDTO zip(Integer zip) {
    
    this.zip = zip;
    return this;
  }

  /**
   * Get zip
   * minimum: 1000
   * maximum: 9999
   * @return zip
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ZIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getZip() {
    return zip;
  }


  @JsonProperty(JSON_PROPERTY_ZIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setZip(Integer zip) {
    this.zip = zip;
  }

  public AddressDTO city(String city) {
    
    this.city = city;
    return this;
  }

  /**
   * Get city
   * @return city
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCity(String city) {
    this.city = city;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AddressDTO address = (AddressDTO) o;
    return Objects.equals(this.street, address.street) &&
        Objects.equals(this.number, address.number) &&
        Objects.equals(this.box, address.box) &&
        Objects.equals(this.zip, address.zip) &&
        Objects.equals(this.city, address.city);
  }

  @Override
  public int hashCode() {
    return Objects.hash(street, number, box, zip, city);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AddressDTO {\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    number: ").append(toIndentedString(number)).append("\n");
    sb.append("    box: ").append(toIndentedString(box)).append("\n");
    sb.append("    zip: ").append(toIndentedString(zip)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

