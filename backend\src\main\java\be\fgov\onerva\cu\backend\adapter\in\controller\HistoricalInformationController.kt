package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenC1Response
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenOnemResponse
import be.fgov.onerva.cu.backend.application.exception.BaremaNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.HistoricalInformationUseCase
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.rest.priv.api.HistoricalInformationApi
import be.fgov.onerva.cu.rest.priv.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.rest.priv.model.HistoricalCitizenOnemResponse

@RestController
class HistoricalInformationController(val historicalInformationUseCase: HistoricalInformationUseCase) :
    HistoricalInformationApi {
    /**
     * GET /api/requests/{requestId}/historical/barema
     * Retrieve barema information from a historical source (ONEM, Authentic Sources, or Internal)
     *
     * @param requestId The UUID of the request (required)
     * @return Barema information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    @LogWithRequestId
    override fun getBarema(requestId: UUID?): HistoricalBaremaResponse {
        return historicalInformationUseCase.getBarema(requireNotNull(requestId))?.let {
            HistoricalBaremaResponse().apply {
                barema = it.barema
                article = it.article
            }
        } ?: throw BaremaNotFoundException("Barema not found for request $requestId")
    }

    @LogWithRequestId
    override fun getHistoricalCitizenAuthenticSources(requestId: UUID?): HistoricalCitizenAuthenticSourcesResponse =
        historicalInformationUseCase
            .getHistoricalCitizenAuthenticSources(requireNotNull(requestId))
            .toHistoricalCitizenAuthenticSourcesResponse()

    @LogWithRequestId
    override fun getHistoricalCitizenC1(requestId: UUID?): HistoricalCitizenC1Response =
        historicalInformationUseCase
            .getHistoricalCitizenC1(requireNotNull(requestId))
            .toHistoricalCitizenC1Response()

    @LogWithRequestId
    override fun getHistoricalCitizenOnem(requestId: UUID?): HistoricalCitizenOnemResponse =
        historicalInformationUseCase
            .getHistoricalCitizenOnem(requireNotNull(requestId))
            .toHistoricalCitizenOnemResponse()
}