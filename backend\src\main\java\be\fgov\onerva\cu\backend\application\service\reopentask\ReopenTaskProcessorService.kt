package be.fgov.onerva.cu.backend.application.service.reopentask

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.port.`in`.ReopenTaskUseCase
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.common.utils.logger

@Service
class ReopenTaskProcessorService(
    reopenTaskDataCaptureService: ReopenTaskDataCaptureService,
    reopenTaskDataValidationService: ReopenTaskDataValidationService
) : ReopenTaskUseCase {
    private val log = logger

    private val reopenTaskServiceMap: Map<String, ReopenTaskAbstractService> = mapOf(
        WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE to reopenTaskDataCaptureService,
        WaveTaskPort.VALIDATION_DATA to reopenTaskDataValidationService
    )


    override fun reopenTask(requestId: UUID, taskType: String) {
        log.info("Reopening task for request ID: $requestId in ReopenTaskProcessorService")
        val reopenService = reopenTaskServiceMap[taskType] ?: throw IllegalArgumentException("Invalid task type")
        reopenService.reopenTask(requestId)
    }
}