package be.fgov.onerva.cu.backend.adapter.out.mapper

import java.time.LocalDate
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.Address
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.BaremaSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.BasicRequest
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskStatusException

fun RequestEntity.toDomainRequest() =
    when (this) {
        is ChangePersonalDataRequestEntity -> this.toDomainChangePersonalData()
        else -> BasicRequest(
            id = this.id,
            c9id = this.c9Id,
            c9Type = this.c9Type,
            ssin = this.ssin,
            requestDate = this.requestDate,
            opKey = this.opKey,
            sectOp = this.sectOp,
            decisionType = this.decisionType,
            decisionBarema = this.decisionBarema,
        )
    }

fun CitizenInformationEntity.toDomainCitizenInformation() =
    CitizenInformation(
        firstName = this.firstName,
        lastName = this.lastName,
        birthDate = this.birthDate,
        nationality = this.nationality,
        address = be.fgov.onerva.cu.backend.application.domain.Address(
            street = this.address.street,
            houseNumber = this.address.houseNumber,
            zipCode = this.address.zipCode,
            city = this.address.city,
            country = this.address.country,
            boxNumber = this.address.boxNumber
        )
    )

fun ModeOfPaymentEntity.toDomainModeOfPayment() =
    ModeOfPayment(
        otherPersonName = this.otherPersonName,
        iban = this.iban,
        bic = this.bic,
    )

fun CitizenInformation.toCitizenInformationEntity(
    request: ChangePersonalDataRequestEntity,
    updateStatus: UpdateStatus,
) =
    CitizenInformationEntity(
        request = request,
        firstName = this.firstName,
        lastName = this.lastName,
        birthDate = this.birthDate,
        nationality = this.nationality,
        updateStatus = updateStatus,
        address = Address(
            street = this.address.street,
            houseNumber = this.address.houseNumber,
            zipCode = this.address.zipCode,
            city = this.address.city,
            country = this.address.country,
            boxNumber = this.address.boxNumber
        )
    )

fun ModeOfPayment.toModeOfPaymentEntity(request: ChangePersonalDataRequestEntity, updateStatus: UpdateStatus) =
    ModeOfPaymentEntity(
        otherPersonName = this.otherPersonName,
        iban = this.iban,
        bic = this.bic,
        validFrom = LocalDate.now(),
        request = request,
        updateStatus = updateStatus,
    )

fun WaveTaskEntity.toDomainWaveTask() = WaveTask(
    processId = this.processId,
    taskId = this.taskId,
    status = this.status,
)

fun WaveTask.toChangePersonalDataCaptureWaveTaskEntity(requestEntity: RequestEntity) =
    ChangePersonalDataCaptureWaveTaskEntity(
        citizenInformationRevisionNumber = null,
        modeOfPaymentRevisionNumber = null,
        unionContributionRevisionNumber = null,
        requestInformationRevisionNumber = null,
        processId = this.processId,
        taskId = this.taskId,
        status = WaveTaskStatus.OPEN,
        request = requestEntity
    )

fun WaveTask.toChangePersonalDataValidateWaveTaskEntity(requestEntity: RequestEntity) =
    ChangePersonalDataValidateWaveTaskEntity(
        citizenInformationRevisionNumber = null,
        modeOfPaymentRevisionNumber = null,
        unionContributionRevisionNumber = null,
        requestInformationRevisionNumber = null,
        processId = this.processId,
        taskId = this.taskId,
        status = WaveTaskStatus.OPEN,
        request = requestEntity
    )

fun UnionContributionEntity.toDomainUnionContribution() =
    UnionContribution(
        authorized = this.authorized, effectiveDate = this.effectiveDate,
    )

fun UnionContribution.toUnionContributionEntity(
    requestEntity: ChangePersonalDataRequestEntity,
    updateStatus: UpdateStatus,
) = UnionContributionEntity(
    authorized = this.authorized,
    effectiveDate = this.effectiveDate,
    request = requestEntity,
    updateStatus = updateStatus,
)

fun ChangePersonalDataRequestEntity.toDomainChangePersonalData() =
    ChangePersonalDataRequest(
        opKey = this.opKey,
        sectOp = this.sectOp,
        id = this.id,
        c9id = this.c9Id,
        c9Type = this.c9Type,
        requestDate = this.requestDate,
        ssin = this.ssin,
        documentType = this.documentType,
        citizenInformation = this.citizenInformation?.toDomainCitizenInformation(),
        modeOfPayment = this.modeOfPayment?.toDomainModeOfPayment(),
        unionContribution = this.unionContribution?.toDomainUnionContribution(),
        requestInformation = this.requestInformation?.toDomainRequestInformation(),
        changePersonalDataCaptureWaveTask = null,
        changePersonalDataValidateWaveTask = null,
        decisionType = this.decisionType,
        decisionBarema = this.decisionBarema,
    )

fun RequestInformationEntity.toDomainRequestInformation() =
    RequestInformation(
        requestDate = this.requestDate,
    )

fun RequestInformation.toRequestInformationEntity(requestEntity: RequestEntity) =
    RequestInformationEntity(
        request = requestEntity,
        requestDate = this.requestDate,
    )

fun CitizenInformationSnapshotEntity.toDomainSnapshot() = Snapshot.Found(
    value = HistoricalCitizenSnapshot(
        firstName = this.firstName,
        lastName = this.lastName,
        numbox = this.numbox,
        nationality = this.nationality,
        birthDate = this.birthDate,
        address = AddressNullable(
            street = this.address.street,
            houseNumber = this.address.houseNumber,
            boxNumber = this.address.boxNumber,
            zipCode = this.address.zipCode,
            country = this.address.country,
            city = this.address.city,
            valueDate = this.address.valueDate,
        ),
        iban = this.modeOfPayment.iban,
        bic = this.modeOfPayment.bic,
        otherPersonName = this.modeOfPayment.otherPersonName,
        bankAccountValueDate = this.modeOfPayment.valueDate,
        paymentMode = this.modeOfPayment.paymentMode,
        authorized = this.unionContribution?.authorized,
        effectiveDate = this.unionContribution?.effectiveDate,
    ),
    readonly = this.readonly
)

fun BaremaSnapshotEntity?.toDomainSnapshot() = when (this) {
    null -> null
    else ->
        if (this.found) Snapshot.Found(
            value = Barema(
                barema = this.barema ?: throw RequestInvalidStateException("Barema found but not specified"),
                article = this.article
            ),
            readonly = this.readonly
        ) else Snapshot.NotFound
}

@Suppress("kotlin:S1871") // Duplicate implementation intentional for type safety
fun WaveTaskEntity.toWaveTaskRevisionNumbers() = when (this) {
    is ChangePersonalDataCaptureWaveTaskEntity -> WaveTaskRevisionNumbers(
        citizenInformationRevisionNumber = this.citizenInformationRevisionNumber,
        modeOfPaymentRevisionNumber = this.modeOfPaymentRevisionNumber,
        unionContributionRevisionNumber = this.unionContributionRevisionNumber,
        requestInformationRevisionNumber = this.requestInformationRevisionNumber,
    )

    is ChangePersonalDataValidateWaveTaskEntity -> WaveTaskRevisionNumbers(
        citizenInformationRevisionNumber = this.citizenInformationRevisionNumber,
        modeOfPaymentRevisionNumber = this.modeOfPaymentRevisionNumber,
        unionContributionRevisionNumber = this.unionContributionRevisionNumber,
        requestInformationRevisionNumber = this.requestInformationRevisionNumber,
    )
    else -> throw WaveTaskStatusException("Unknown wave task type: ${this.javaClass.simpleName}")
}
