package be.fgov.onerva.cu.bff.adapter.out

import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoApi
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient

@Component
class CitizenInfoApiFactory(private val defaultCitizenInfoApi: CitizenInfoApi) {
    operator fun invoke(authToken: String): CitizenInfoApi {
        val restTemplate = RestTemplate().apply {
            interceptors.add { request, body, execution ->
                request.headers["Authorization"] = authToken
                execution.execute(request, body)
            }
        }

        return CitizenInfoApi(
            ApiClient(restTemplate).apply {
                basePath = defaultCitizenInfoApi.apiClient.basePath
            }
        )
    }
}
