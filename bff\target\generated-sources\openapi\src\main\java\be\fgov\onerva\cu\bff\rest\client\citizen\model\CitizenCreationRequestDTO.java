/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Describe how to request
 */
@JsonPropertyOrder({
  CitizenCreationRequestDTO.JSON_PROPERTY_NISS,
  CitizenCreationRequestDTO.JSON_PROPERTY_FIRSTNAME,
  CitizenCreationRequestDTO.JSON_PROPERTY_LASTNAME,
  CitizenCreationRequestDTO.JSON_PROPERTY_FALLBACK_URL,
  CitizenCreationRequestDTO.JSON_PROPERTY_CORRELATION_ID
})
@JsonTypeName("CitizenCreationRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenCreationRequestDTO {
  public static final String JSON_PROPERTY_NISS = "niss";
  private String niss;

  public static final String JSON_PROPERTY_FIRSTNAME = "firstname";
  private String firstname;

  public static final String JSON_PROPERTY_LASTNAME = "lastname";
  private String lastname;

  public static final String JSON_PROPERTY_FALLBACK_URL = "fallbackUrl";
  private String fallbackUrl;

  public static final String JSON_PROPERTY_CORRELATION_ID = "correlationId";
  private String correlationId;

  public CitizenCreationRequestDTO() {
  }

  public CitizenCreationRequestDTO niss(String niss) {
    
    this.niss = niss;
    return this;
  }

  /**
   * Get niss
   * @return niss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNiss() {
    return niss;
  }


  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNiss(String niss) {
    this.niss = niss;
  }

  public CitizenCreationRequestDTO firstname(String firstname) {
    
    this.firstname = firstname;
    return this;
  }

  /**
   * Get firstname
   * @return firstname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstname() {
    return firstname;
  }


  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstname(String firstname) {
    this.firstname = firstname;
  }

  public CitizenCreationRequestDTO lastname(String lastname) {
    
    this.lastname = lastname;
    return this;
  }

  /**
   * Get lastname
   * @return lastname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastname() {
    return lastname;
  }


  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastname(String lastname) {
    this.lastname = lastname;
  }

  public CitizenCreationRequestDTO fallbackUrl(String fallbackUrl) {
    
    this.fallbackUrl = fallbackUrl;
    return this;
  }

  /**
   * Deprecated. A POST will be done on this endpoint with the following request body {\&quot;status \&quot;:  \&quot;SUCCESS  FAILED\&quot;}
   * @return fallbackUrl
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FALLBACK_URL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFallbackUrl() {
    return fallbackUrl;
  }


  @JsonProperty(JSON_PROPERTY_FALLBACK_URL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFallbackUrl(String fallbackUrl) {
    this.fallbackUrl = fallbackUrl;
  }

  public CitizenCreationRequestDTO correlationId(String correlationId) {
    
    this.correlationId = correlationId;
    return this;
  }

  /**
   * An optional ID of your choice to correlate with.
   * @return correlationId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCorrelationId() {
    return correlationId;
  }


  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenCreationRequestDTO citizenCreationRequest = (CitizenCreationRequestDTO) o;
    return Objects.equals(this.niss, citizenCreationRequest.niss) &&
        Objects.equals(this.firstname, citizenCreationRequest.firstname) &&
        Objects.equals(this.lastname, citizenCreationRequest.lastname) &&
        Objects.equals(this.fallbackUrl, citizenCreationRequest.fallbackUrl) &&
        Objects.equals(this.correlationId, citizenCreationRequest.correlationId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(niss, firstname, lastname, fallbackUrl, correlationId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenCreationRequestDTO {\n");
    sb.append("    niss: ").append(toIndentedString(niss)).append("\n");
    sb.append("    firstname: ").append(toIndentedString(firstname)).append("\n");
    sb.append("    lastname: ").append(toIndentedString(lastname)).append("\n");
    sb.append("    fallbackUrl: ").append(toIndentedString(fallbackUrl)).append("\n");
    sb.append("    correlationId: ").append(toIndentedString(correlationId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

