package be.fgov.onerva.cu.backend.adapter.out.persistence.model

import java.time.Instant
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.FetchType
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.annotations.NaturalId
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus

@Entity
@Table(name = "sync_follow_up")
class SyncFollowUpEntity(
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "request_id")
    val request: RequestEntity,

    @Column(name = "correlation_id") @NaturalId
    var correlationId: String? = null,

    @Column(name = "date_message_sent")
    var dateMessageSent: Instant? = null,

    @Column(name = "date_response_received")
    var dateResponseReceived: Instant? = null,

    @Column(name = "error")
    var error: String? = null,

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    var status: SyncFollowUpStatus,
) : BaseEntity()
