/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * InvalidParam
 */
@JsonPropertyOrder({
  InvalidParam.JSON_PROPERTY_IN,
  InvalidParam.JSON_PROPERTY_NAME,
  InvalidParam.JSON_PROPERTY_REASON,
  InvalidParam.JSON_PROPERTY_VALUE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class InvalidParam {
  /**
   * The location of the invalid parameter (cfr Swagger parameters)
   */
  public enum InEnum {
    BODY("body"),
    
    PATH("path"),
    
    QUERY("query"),
    
    HEADER("header");

    private String value;

    InEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static InEnum fromValue(String value) {
      for (InEnum b : InEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_IN = "in";
  private InEnum in;

  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_REASON = "reason";
  private String reason;

  public static final String JSON_PROPERTY_VALUE = "value";
  private Object value;

  public InvalidParam() {
  }

  public InvalidParam in(InEnum in) {
    
    this.in = in;
    return this;
  }

  /**
   * The location of the invalid parameter (cfr Swagger parameters)
   * @return in
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public InEnum getIn() {
    return in;
  }


  @JsonProperty(JSON_PROPERTY_IN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIn(InEnum in) {
    this.in = in;
  }

  public InvalidParam name(String name) {
    
    this.name = name;
    return this;
  }

  /**
   * The name of the invalid parameter
   * @return name
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getName() {
    return name;
  }


  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setName(String name) {
    this.name = name;
  }

  public InvalidParam reason(String reason) {
    
    this.reason = reason;
    return this;
  }

  /**
   * A message explaining the violation
   * @return reason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReason() {
    return reason;
  }


  @JsonProperty(JSON_PROPERTY_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReason(String reason) {
    this.reason = reason;
  }

  public InvalidParam value(Object value) {
    
    this.value = value;
    return this;
  }

  /**
   * The value of the erroneous parameter
   * @return value
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getValue() {
    return value;
  }


  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValue(Object value) {
    this.value = value;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvalidParam invalidParam = (InvalidParam) o;
    return Objects.equals(this.in, invalidParam.in) &&
        Objects.equals(this.name, invalidParam.name) &&
        Objects.equals(this.reason, invalidParam.reason) &&
        Objects.equals(this.value, invalidParam.value);
  }

  @Override
  public int hashCode() {
    return Objects.hash(in, name, reason, value);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvalidParam {\n");
    sb.append("    in: ").append(toIndentedString(in)).append("\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

