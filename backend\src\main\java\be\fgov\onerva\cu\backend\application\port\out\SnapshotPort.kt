package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.domain.Snapshot

interface SnapshotPort {
    fun getCitizenInformationSnapshot(requestId: UUID, source: ExternalSource): Snapshot<HistoricalCitizenSnapshot>

    fun saveCitizenInformationSnapshot(
        requestId: UUID,
        source: ExternalSource,
        historicalCitizenSnapshot: HistoricalCitizenSnapshot,
    )

    fun makeCitizenInformationSnapshotReadonly(
        requestId: UUID,
        source: ExternalSource,
    )

    fun getBaremaSnapshot(requestId: UUID): Snapshot<Barema>

    fun saveBaremaSnapshot(requestId: UUID, barema: Barema?)

    fun makeBaremaSnapshotReadonly(requestId: UUID)
}