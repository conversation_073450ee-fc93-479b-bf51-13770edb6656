/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.CalculationBaseAllowanceType;
import be.fgov.onerva.unemployment.c9.rest.model.CommentDeclarationType;
import be.fgov.onerva.unemployment.c9.rest.model.WorkGrid;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502AnnualDclTempUnemployment
 */
@JsonPropertyOrder({
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE,
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_MAJOR_FORCE_REASON,
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D,
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE,
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_WORK_GRID,
  Wech502AnnualDclTempUnemployment.JSON_PROPERTY_COMMENT_DECLARATION
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502AnnualDclTempUnemployment {
  public static final String JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE = "tempUnemploymentStartingDate";
  private LocalDate tempUnemploymentStartingDate;

  public static final String JSON_PROPERTY_MAJOR_FORCE_REASON = "majorForceReason";
  private String majorForceReason;

  public static final String JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D = "usingEmployerCompanyID";
  private String usingEmployerCompanyID;

  public static final String JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE = "calculationBaseAllowance";
  private CalculationBaseAllowanceType calculationBaseAllowance;

  public static final String JSON_PROPERTY_WORK_GRID = "workGrid";
  private WorkGrid workGrid;

  public static final String JSON_PROPERTY_COMMENT_DECLARATION = "commentDeclaration";
  private CommentDeclarationType commentDeclaration;

  public Wech502AnnualDclTempUnemployment() {
  }

  public Wech502AnnualDclTempUnemployment tempUnemploymentStartingDate(LocalDate tempUnemploymentStartingDate) {
    
    this.tempUnemploymentStartingDate = tempUnemploymentStartingDate;
    return this;
  }

  /**
   * Get tempUnemploymentStartingDate
   * @return tempUnemploymentStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getTempUnemploymentStartingDate() {
    return tempUnemploymentStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTempUnemploymentStartingDate(LocalDate tempUnemploymentStartingDate) {
    this.tempUnemploymentStartingDate = tempUnemploymentStartingDate;
  }

  public Wech502AnnualDclTempUnemployment majorForceReason(String majorForceReason) {
    
    this.majorForceReason = majorForceReason;
    return this;
  }

  /**
   * Get majorForceReason
   * @return majorForceReason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMajorForceReason() {
    return majorForceReason;
  }


  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMajorForceReason(String majorForceReason) {
    this.majorForceReason = majorForceReason;
  }

  public Wech502AnnualDclTempUnemployment usingEmployerCompanyID(String usingEmployerCompanyID) {
    
    this.usingEmployerCompanyID = usingEmployerCompanyID;
    return this;
  }

  /**
   * Get usingEmployerCompanyID
   * @return usingEmployerCompanyID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsingEmployerCompanyID() {
    return usingEmployerCompanyID;
  }


  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsingEmployerCompanyID(String usingEmployerCompanyID) {
    this.usingEmployerCompanyID = usingEmployerCompanyID;
  }

  public Wech502AnnualDclTempUnemployment calculationBaseAllowance(CalculationBaseAllowanceType calculationBaseAllowance) {
    
    this.calculationBaseAllowance = calculationBaseAllowance;
    return this;
  }

  /**
   * Get calculationBaseAllowance
   * @return calculationBaseAllowance
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public CalculationBaseAllowanceType getCalculationBaseAllowance() {
    return calculationBaseAllowance;
  }


  @JsonProperty(JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCalculationBaseAllowance(CalculationBaseAllowanceType calculationBaseAllowance) {
    this.calculationBaseAllowance = calculationBaseAllowance;
  }

  public Wech502AnnualDclTempUnemployment workGrid(WorkGrid workGrid) {
    
    this.workGrid = workGrid;
    return this;
  }

  /**
   * Get workGrid
   * @return workGrid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public WorkGrid getWorkGrid() {
    return workGrid;
  }


  @JsonProperty(JSON_PROPERTY_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkGrid(WorkGrid workGrid) {
    this.workGrid = workGrid;
  }

  public Wech502AnnualDclTempUnemployment commentDeclaration(CommentDeclarationType commentDeclaration) {
    
    this.commentDeclaration = commentDeclaration;
    return this;
  }

  /**
   * Get commentDeclaration
   * @return commentDeclaration
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENT_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CommentDeclarationType getCommentDeclaration() {
    return commentDeclaration;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommentDeclaration(CommentDeclarationType commentDeclaration) {
    this.commentDeclaration = commentDeclaration;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502AnnualDclTempUnemployment wech502AnnualDclTempUnemployment = (Wech502AnnualDclTempUnemployment) o;
    return Objects.equals(this.tempUnemploymentStartingDate, wech502AnnualDclTempUnemployment.tempUnemploymentStartingDate) &&
        Objects.equals(this.majorForceReason, wech502AnnualDclTempUnemployment.majorForceReason) &&
        Objects.equals(this.usingEmployerCompanyID, wech502AnnualDclTempUnemployment.usingEmployerCompanyID) &&
        Objects.equals(this.calculationBaseAllowance, wech502AnnualDclTempUnemployment.calculationBaseAllowance) &&
        Objects.equals(this.workGrid, wech502AnnualDclTempUnemployment.workGrid) &&
        Objects.equals(this.commentDeclaration, wech502AnnualDclTempUnemployment.commentDeclaration);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tempUnemploymentStartingDate, majorForceReason, usingEmployerCompanyID, calculationBaseAllowance, workGrid, commentDeclaration);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502AnnualDclTempUnemployment {\n");
    sb.append("    tempUnemploymentStartingDate: ").append(toIndentedString(tempUnemploymentStartingDate)).append("\n");
    sb.append("    majorForceReason: ").append(toIndentedString(majorForceReason)).append("\n");
    sb.append("    usingEmployerCompanyID: ").append(toIndentedString(usingEmployerCompanyID)).append("\n");
    sb.append("    calculationBaseAllowance: ").append(toIndentedString(calculationBaseAllowance)).append("\n");
    sb.append("    workGrid: ").append(toIndentedString(workGrid)).append("\n");
    sb.append("    commentDeclaration: ").append(toIndentedString(commentDeclaration)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

