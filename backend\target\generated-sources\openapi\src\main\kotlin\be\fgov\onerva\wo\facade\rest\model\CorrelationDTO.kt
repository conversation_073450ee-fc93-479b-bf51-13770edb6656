/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param source 
 * @param correlationId 
 */


data class CorrelationDTO (

    @get:JsonProperty("source")
    val source: kotlin.String,

    @get:JsonProperty("correlationId")
    val correlationId: kotlin.String

) {


}

