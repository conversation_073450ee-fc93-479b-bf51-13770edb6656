package be.fgov.onerva.cu.backend.integration
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.context.jdbc.Sql
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.CuBaseIntegration
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.cu.backend.security.Roles
import be.fgov.onerva.cu.backend.security.WithMockedJwtToken
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.FieldSource
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import com.fasterxml.jackson.databind.ObjectMapper
import io.mockk.every

@Sql(
    scripts = [
        "classpath:/cleanup.sql",
        "classpath:/data-change-personal-data.sql"
    ]
)
class SelectModeOfPaymentEndpointIT() : CuBaseIntegration() {
    @Autowired
    lateinit var jdbcTemplate: JdbcTemplate

    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    private val requestId = UUID.fromString("F1DA3F30-10A5-4124-AA5E-2D4E46A09B16")
    private val c9Id = 12346L
    private val ssin = "***********"

    @Test
    @WithMockedJwtToken(username = "test_user", roles = [Roles.CU_ROLE_USER])
    fun `test select source for mode of payment`() {
        // Given
        every {
            citizenInfoApi?.searchCitizenInfo(
                listOf("***********"),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns CitizenInfoPageDTO(
            pageNumber = 0,
            pageSize = 10,
            totalElements = 1,
            isFirst = true,
            isLast = true,
            content = listOf(
                CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(151),
                    iban = "****************",
                    address = "Main Street 123 Box A",
                    postalCode = "1000",
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = "CMRPL",
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 150,
                        zip = "1000",
                        number = "123",
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = true,
                        validFrom = LocalDate.of(2022, 1, 1),
                    )
                )
            )
        )

        // When
        mockMvc.perform(
            put("/api/requests/$requestId/mode-of-payment/select")
                .contentType(MediaType.APPLICATION_JSON)
                .content(
                    objectMapper.writeValueAsString(
                        SelectFieldSourcesRequest(

                        ).apply {
                            fieldSources = listOf(
                                FieldSource().apply {
                                    fieldName = "account"
                                    source = ExternalSource.ONEM
                                },
                                FieldSource().apply {
                                    fieldName = "otherPersonName"
                                    source = ExternalSource.C1
                                },
                            )
                        }
                    )
                )
        )
            .andExpect(status().`is`(204))
            .andReturn()

        val modeOfPayment = jdbcHelper.getModeOfPayment(requestId)

        assertThat(modeOfPayment).containsEntry("iban", "****************").containsEntry("bic", "CMRPL")
            .containsEntry("other_person_name", "The other person name")
    }
}