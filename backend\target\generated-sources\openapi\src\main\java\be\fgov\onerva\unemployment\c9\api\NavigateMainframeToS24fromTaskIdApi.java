package be.fgov.onerva.unemployment.c9.api;

import be.fgov.onerva.unemployment.c9.invoker.ApiClient;
import be.fgov.onerva.unemployment.c9.invoker.BaseApi;

import be.fgov.onerva.unemployment.c9.rest.model.InlineResponse2002;
import be.fgov.onerva.unemployment.c9.rest.model.NavigateS24fromtaskidBody;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NavigateMainframeToS24fromTaskIdApi extends BaseApi {

    public NavigateMainframeToS24fromTaskIdApi() {
        super(new ApiClient());
    }

    public NavigateMainframeToS24fromTaskIdApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Endpoint to display a specific S24 in the mainframe, prerequirement is that the use making the call have an open T27 and is connected with his operator code
     * <p><b>200</b> - OK
     * @param navigateS24fromtaskidBody  (optional)
     * @return InlineResponse2002
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public InlineResponse2002 navigateMainframeToS24fromTaskId(NavigateS24fromtaskidBody navigateS24fromtaskidBody) throws RestClientException {
        return navigateMainframeToS24fromTaskIdWithHttpInfo(navigateS24fromtaskidBody).getBody();
    }

    /**
     * 
     * Endpoint to display a specific S24 in the mainframe, prerequirement is that the use making the call have an open T27 and is connected with his operator code
     * <p><b>200</b> - OK
     * @param navigateS24fromtaskidBody  (optional)
     * @return ResponseEntity&lt;InlineResponse2002&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<InlineResponse2002> navigateMainframeToS24fromTaskIdWithHttpInfo(NavigateS24fromtaskidBody navigateS24fromtaskidBody) throws RestClientException {
        Object localVarPostBody = navigateS24fromtaskidBody;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<InlineResponse2002> localReturnType = new ParameterizedTypeReference<InlineResponse2002>() {};
        return apiClient.invokeAPI("/mainframe/navigate/S24fromtaskid", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
