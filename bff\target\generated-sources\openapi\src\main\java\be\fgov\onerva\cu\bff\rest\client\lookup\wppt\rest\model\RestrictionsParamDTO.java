/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RestrictionsParamDTO
 */
@JsonPropertyOrder({
  RestrictionsParamDTO.JSON_PROPERTY_COLLECTION,
  RestrictionsParamDTO.JSON_PROPERTY_CRITERION1,
  RestrictionsParamDTO.JSON_PROPERTY_CRITERION2,
  RestrictionsParamDTO.JSON_PROPERTY_EXPRESSION,
  RestrictionsParamDTO.JSON_PROPERTY_MATCH_MODE,
  RestrictionsParamDTO.JSON_PROPERTY_PROPERTY_NAME,
  RestrictionsParamDTO.JSON_PROPERTY_VALUE,
  RestrictionsParamDTO.JSON_PROPERTY_VALUE_CLASS_NAME
})
@JsonTypeName("RestrictionsParam")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RestrictionsParamDTO {
  public static final String JSON_PROPERTY_COLLECTION = "collection";
  private List<Object> collection = new ArrayList<>();

  public static final String JSON_PROPERTY_CRITERION1 = "criterion1";
  private RestrictionsParamDTO criterion1;

  public static final String JSON_PROPERTY_CRITERION2 = "criterion2";
  private RestrictionsParamDTO criterion2;

  public static final String JSON_PROPERTY_EXPRESSION = "expression";
  private String expression;

  /**
   * Gets or Sets matchMode
   */
  public enum MatchModeEnum {
    ANYWHERE("ANYWHERE"),
    
    END("END"),
    
    EXACT("EXACT"),
    
    START("START");

    private String value;

    MatchModeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static MatchModeEnum fromValue(String value) {
      for (MatchModeEnum b : MatchModeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_MATCH_MODE = "matchMode";
  private MatchModeEnum matchMode;

  public static final String JSON_PROPERTY_PROPERTY_NAME = "propertyName";
  private String propertyName;

  public static final String JSON_PROPERTY_VALUE = "value";
  private String value;

  public static final String JSON_PROPERTY_VALUE_CLASS_NAME = "valueClassName";
  private String valueClassName;

  public RestrictionsParamDTO() {
  }

  public RestrictionsParamDTO collection(List<Object> collection) {
    
    this.collection = collection;
    return this;
  }

  public RestrictionsParamDTO addCollectionItem(Object collectionItem) {
    if (this.collection == null) {
      this.collection = new ArrayList<>();
    }
    this.collection.add(collectionItem);
    return this;
  }

  /**
   * Get collection
   * @return collection
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COLLECTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Object> getCollection() {
    return collection;
  }


  @JsonProperty(JSON_PROPERTY_COLLECTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCollection(List<Object> collection) {
    this.collection = collection;
  }

  public RestrictionsParamDTO criterion1(RestrictionsParamDTO criterion1) {
    
    this.criterion1 = criterion1;
    return this;
  }

  /**
   * Get criterion1
   * @return criterion1
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRITERION1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public RestrictionsParamDTO getCriterion1() {
    return criterion1;
  }


  @JsonProperty(JSON_PROPERTY_CRITERION1)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCriterion1(RestrictionsParamDTO criterion1) {
    this.criterion1 = criterion1;
  }

  public RestrictionsParamDTO criterion2(RestrictionsParamDTO criterion2) {
    
    this.criterion2 = criterion2;
    return this;
  }

  /**
   * Get criterion2
   * @return criterion2
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CRITERION2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public RestrictionsParamDTO getCriterion2() {
    return criterion2;
  }


  @JsonProperty(JSON_PROPERTY_CRITERION2)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCriterion2(RestrictionsParamDTO criterion2) {
    this.criterion2 = criterion2;
  }

  public RestrictionsParamDTO expression(String expression) {
    
    this.expression = expression;
    return this;
  }

  /**
   * Get expression
   * @return expression
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXPRESSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getExpression() {
    return expression;
  }


  @JsonProperty(JSON_PROPERTY_EXPRESSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExpression(String expression) {
    this.expression = expression;
  }

  public RestrictionsParamDTO matchMode(MatchModeEnum matchMode) {
    
    this.matchMode = matchMode;
    return this;
  }

  /**
   * Get matchMode
   * @return matchMode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MATCH_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MatchModeEnum getMatchMode() {
    return matchMode;
  }


  @JsonProperty(JSON_PROPERTY_MATCH_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMatchMode(MatchModeEnum matchMode) {
    this.matchMode = matchMode;
  }

  public RestrictionsParamDTO propertyName(String propertyName) {
    
    this.propertyName = propertyName;
    return this;
  }

  /**
   * Get propertyName
   * @return propertyName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPropertyName() {
    return propertyName;
  }


  @JsonProperty(JSON_PROPERTY_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPropertyName(String propertyName) {
    this.propertyName = propertyName;
  }

  public RestrictionsParamDTO value(String value) {
    
    this.value = value;
    return this;
  }

  /**
   * Get value
   * @return value
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getValue() {
    return value;
  }


  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValue(String value) {
    this.value = value;
  }

  public RestrictionsParamDTO valueClassName(String valueClassName) {
    
    this.valueClassName = valueClassName;
    return this;
  }

  /**
   * Get valueClassName
   * @return valueClassName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getValueClassName() {
    return valueClassName;
  }


  @JsonProperty(JSON_PROPERTY_VALUE_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValueClassName(String valueClassName) {
    this.valueClassName = valueClassName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RestrictionsParamDTO restrictionsParam = (RestrictionsParamDTO) o;
    return Objects.equals(this.collection, restrictionsParam.collection) &&
        Objects.equals(this.criterion1, restrictionsParam.criterion1) &&
        Objects.equals(this.criterion2, restrictionsParam.criterion2) &&
        Objects.equals(this.expression, restrictionsParam.expression) &&
        Objects.equals(this.matchMode, restrictionsParam.matchMode) &&
        Objects.equals(this.propertyName, restrictionsParam.propertyName) &&
        Objects.equals(this.value, restrictionsParam.value) &&
        Objects.equals(this.valueClassName, restrictionsParam.valueClassName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(collection, criterion1, criterion2, expression, matchMode, propertyName, value, valueClassName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RestrictionsParamDTO {\n");
    sb.append("    collection: ").append(toIndentedString(collection)).append("\n");
    sb.append("    criterion1: ").append(toIndentedString(criterion1)).append("\n");
    sb.append("    criterion2: ").append(toIndentedString(criterion2)).append("\n");
    sb.append("    expression: ").append(toIndentedString(expression)).append("\n");
    sb.append("    matchMode: ").append(toIndentedString(matchMode)).append("\n");
    sb.append("    propertyName: ").append(toIndentedString(propertyName)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("    valueClassName: ").append(toIndentedString(valueClassName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

