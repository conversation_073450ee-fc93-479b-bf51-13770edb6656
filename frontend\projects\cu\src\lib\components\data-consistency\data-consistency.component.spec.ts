import {ComponentFixture, TestBed} from "@angular/core/testing";
import {HistoricalBaremaResponse} from "@rest-client/cu-bff";
import {DataConsistencyComponent} from "./data-consistency.component";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {HttpClientTestingModule} from "@angular/common/http/testing";

class MockGeoLookupService {
}

describe("DataConsistencyComponent", () => {
    let component: DataConsistencyComponent;
    let fixture: ComponentFixture<DataConsistencyComponent>;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                NoopAnimationsModule,
                TranslateModule.forRoot(),
                CommonModule,
                HttpClientTestingModule,
            ],
            providers: [
                TranslateService,
                {provide: "GeoLookupService", useClass: MockGeoLookupService},
            ],
        }).compileComponents();
    });

    beforeEach(() => {
        fixture = TestBed.createComponent(DataConsistencyComponent);
        component = fixture.componentInstance;
    });

    it("should create", () => {
        fixture.detectChanges();
        expect(component).toBeTruthy();
    });

    it("should initialize with default values", () => {
        expect(component.language).toEqual("NL");
        expect(component.taskStatus).toEqual("");
        expect(component.dataConsistencyData).toEqual(undefined);
        expect(component.displayedColumns)
            .toEqual(["label", "encodedValue", "dbValue", "sourceValue", "valueToKeep", "icon"]);
    });

    it("should call initializeData on ngOnInit", () => {
        const initializeDataSpy = jest.spyOn(component as any, "initializeData");
        component.ngOnInit();
        expect(initializeDataSpy).toHaveBeenCalled();
    });

    it("should call initializeData on ngOnChanges", () => {
        const initializeDataSpy = jest.spyOn(component as any, "initializeData");
        component.ngOnChanges();
        expect(initializeDataSpy).toHaveBeenCalled();
    });

    it("should initialize citizen information data when available", () => {
        component.dataConsistencyData = {
            c1CitizenInformation: {
                address: {
                    street: "Test Street",
                    zipCode: "1000",
                    city: "Brussels",
                },
            },
        };

        component.ngOnInit();

        expect(component.citizenInformation).toEqual(component.dataConsistencyData.c1CitizenInformation);
        expect(component.panelDataLoadedStatus["citizenInformation"]).toBeTruthy();
    });

    it("should check if panel is edited", () => {
        component.panelEditStatus["testPanel"] = true;
        expect(component.isPanelEdited("testPanel")).toBeTruthy();
        expect(component.isPanelEdited("nonexistentPanel")).toBeFalsy();
    });

    it("should check if panel data is loaded", () => {
        component.panelDataLoadedStatus["testPanel"] = true;
        expect(component.isPanelDataLoaded("testPanel")).toBeTruthy();
        expect(component.isPanelDataLoaded("nonexistentPanel")).toBeFalsy();
    });

    it("should handle value change for address field", () => {
        const event = {id: "address", value: "Test Address"};
        const consoleSpy = jest.spyOn(console, "log");

        component.onValueChange(event);

        expect(component.panelEditStatus["citizenInformation"]).toBeTruthy();
    });

    it("should handle value change for address component fields", () => {
        component.citizenInformation = {address: {}};

        component.onValueChange({id: "street", value: "Test Street"});
        component.onValueChange({id: "zipCode", value: "1000"});
        component.onValueChange({id: "city", value: "Brussels"});

        expect(component.citizenInformation.address.street).toEqual("Test Street");
        expect(component.citizenInformation.address.zipCode).toEqual("1000");
        expect(component.citizenInformation.address.city).toEqual("Brussels");
    });

    it("should handle value change for full name", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "fullName", value: "John Doe"});

        expect(component.citizenInformation.firstName).toEqual("John");
        expect(component.citizenInformation.name).toEqual("Doe");
    });

    it("should handle value change for birth date", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "birthDate", value: "1990-01-01"});

        expect(component.citizenInformation.dateOfBirth).toEqual("1990-01-01");
    });

    it("should handle value change for nationality in Dutch", () => {
        component.language = "NL";
        component.citizenInformation = {nationality: {descNl: "", descFr: ""}};

        component.onValueChange({id: "nationality", value: "Belgisch"});

        expect(component.citizenInformation.nationality.descNl).toEqual("Belgisch");
    });

    it("should handle value change for nationality in French", () => {
        component.language = "fr";
        component.citizenInformation = {nationality: {descNl: "", descFr: ""}};

        component.onValueChange({id: "nationality", value: "Belge"});

        expect(component.citizenInformation.nationality.descFr).toEqual("Belge");
    });

    it("should handle value change for nationality as string", () => {
        component.citizenInformation = {nationality: "Unknown"};

        component.onValueChange({id: "nationality", value: "Belgian"});

        expect(component.citizenInformation.nationality).toEqual("Belgian");
    });

    it("should handle value change for Belgian bank account", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "bankAccount", value: "BE123456789012"});

        expect(component.citizenInformation.iban).toEqual("BE123456789012");
    });

    it("should handle value change for foreign bank account", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "bankAccount", value: "FR123456789012"});

        expect(component.citizenInformation.iban).toEqual("FR123456789012");
    });

    it("should handle value change for bank account owner name", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "otherPersonName", value: "John Doe"});

        expect(component.citizenInformation.otherPersonName).toEqual("John Doe");
    });

    it("should correctly identify panel ID for different fields", () => {
        const addressFields = ["address", "street", "houseNumber", "boxNumber", "zipCode", "city", "country"];
        const citizenFields = ["fullName", "birthDate", "nationality", "bankAccount", "otherPersonName"];

        [...addressFields, ...citizenFields].forEach(field => {
            expect((component as any).getPanelIdForField(field)).toEqual("citizenInformation");
        });

        expect((component as any).getPanelIdForField("unknownField")).toEqual("");
    });

    it("should do nothing if event is invalid in onValueChange", () => {
        const updateFieldValueSpy = jest.spyOn(component as any, "updateFieldValue");

        component.onValueChange(null as any);
        component.onValueChange({} as any);

        expect(updateFieldValueSpy).not.toHaveBeenCalled();
    });

    it("should handle Belgian bank account with BIC", () => {
        component.citizenInformation = {
            address: {
                street: "Test Street",
                zipCode: "1000",
                city: "Brussels",
            },
        };

        component.onValueChange({id: "bankAccount", value: "BE123456789012"});
        component.onValueChange({id: "bic", value: "GEBABEBB"});

        expect(component.citizenInformation.iban).toEqual("BE123456789012");
        expect(component.citizenInformation.bic).toEqual("GEBABEBB");
    });

    it("should handle foreign bank account with BIC", () => {
        component.citizenInformation = {
            address: {
                street: "Test Street",
                zipCode: "1000",
                city: "Brussels",
            },
        };

        component.onValueChange({id: "bankAccount", value: "FR123456789012"});
        component.onValueChange({id: "bic", value: "BNPAFRPP"});

        expect(component.citizenInformation.iban).toEqual("FR123456789012");
        expect(component.citizenInformation.bic).toEqual("BNPAFRPP");
    });

    it("should handle table selection change for bank account", () => {
        const emitSpy = jest.spyOn(component.fieldSourcesChange, "emit");

        component.onTableSelectionChange({
            id: "bankAccount",
            selectedValue: {
                origin: "ONEM",
                value: {
                    iban: "BE987654321098",
                    bic: "GEBABEBB",
                },
            },
        });

        expect(emitSpy).toHaveBeenCalledWith(expect.arrayContaining([
            expect.objectContaining({
                fieldName: "bankAccount",
                source: "ONEM",
            }),
        ]));
    });

    it("should convert BIC to uppercase", () => {
        component.citizenInformation = {};

        component.onValueChange({id: "bic", value: "gebabebb"});

        expect(component.citizenInformation.bic).toEqual("GEBABEBB");
    });

});