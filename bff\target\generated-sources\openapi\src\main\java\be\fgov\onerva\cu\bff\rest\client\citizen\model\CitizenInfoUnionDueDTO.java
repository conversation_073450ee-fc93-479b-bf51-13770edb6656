/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * The citizen mandates or not the union to pay the union dues with his allowance money. If no mandate was ever found this will be null.
 */
@JsonPropertyOrder({
  CitizenInfoUnionDueDTO.JSON_PROPERTY_MANDATE_ACTIVE,
  CitizenInfoUnionDueDTO.JSON_PROPERTY_VALID_FROM
})
@JsonTypeName("CitizenInfo_unionDue")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenInfoUnionDueDTO {
  public static final String JSON_PROPERTY_MANDATE_ACTIVE = "mandateActive";
  private Boolean mandateActive;

  public static final String JSON_PROPERTY_VALID_FROM = "validFrom";
  private LocalDate validFrom;

  public CitizenInfoUnionDueDTO() {
  }

  public CitizenInfoUnionDueDTO mandateActive(Boolean mandateActive) {
    
    this.mandateActive = mandateActive;
    return this;
  }

  /**
   * Is the union dues mandate active or not.
   * @return mandateActive
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MANDATE_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Boolean getMandateActive() {
    return mandateActive;
  }


  @JsonProperty(JSON_PROPERTY_MANDATE_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMandateActive(Boolean mandateActive) {
    this.mandateActive = mandateActive;
  }

  public CitizenInfoUnionDueDTO validFrom(LocalDate validFrom) {
    
    this.validFrom = validFrom;
    return this;
  }

  /**
   * The date on which the mandate was activated or deactivated.
   * @return validFrom
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getValidFrom() {
    return validFrom;
  }


  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setValidFrom(LocalDate validFrom) {
    this.validFrom = validFrom;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenInfoUnionDueDTO citizenInfoUnionDue = (CitizenInfoUnionDueDTO) o;
    return Objects.equals(this.mandateActive, citizenInfoUnionDue.mandateActive) &&
        Objects.equals(this.validFrom, citizenInfoUnionDue.validFrom);
  }

  @Override
  public int hashCode() {
    return Objects.hash(mandateActive, validFrom);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenInfoUnionDueDTO {\n");
    sb.append("    mandateActive: ").append(toIndentedString(mandateActive)).append("\n");
    sb.append("    validFrom: ").append(toIndentedString(validFrom)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

