/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UnionContributionFields

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param otherPersonName 
 * @param iban 
 * @param bic 
 * @param firstName The first name of the person
 * @param lastName The last name of the person
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param nationality The nationality of the employee
 * @param address 
 * @param addressValueDate The value date for the bank account
 * @param bankAccountValueDate The value date for the bank account
 * @param unionContributionValueDate The value date for the bank account
 * @param unionDue 
 */


data class HistoricalCitizenC1Response (

    @get:JsonProperty("otherPersonName")
    val otherPersonName: kotlin.String? = null,

    @get:JsonProperty("iban")
    val iban: kotlin.String? = null,

    @get:JsonProperty("bic")
    val bic: kotlin.String? = null,

    /* The first name of the person */
    @get:JsonProperty("firstName")
    val firstName: kotlin.String? = null,

    /* The last name of the person */
    @get:JsonProperty("lastName")
    val lastName: kotlin.String? = null,

    /* The birth date of the employee (format YYYY-MM-DD) */
    @get:JsonProperty("birthDate")
    val birthDate: java.time.LocalDate? = null,

    /* The nationality of the employee */
    @get:JsonProperty("nationality")
    val nationality: kotlin.String? = null,

    @get:JsonProperty("address")
    val address: AddressNullable? = null,

    /* The value date for the bank account */
    @get:JsonProperty("addressValueDate")
    val addressValueDate: java.time.LocalDate? = null,

    /* The value date for the bank account */
    @get:JsonProperty("bankAccountValueDate")
    val bankAccountValueDate: java.time.LocalDate? = null,

    /* The value date for the bank account */
    @get:JsonProperty("unionContributionValueDate")
    val unionContributionValueDate: java.time.LocalDate? = null,

    @get:JsonProperty("unionDue")
    val unionDue: UnionContributionFields? = null

) {


}

