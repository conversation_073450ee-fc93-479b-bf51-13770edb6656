/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ClosingPeriodType
 */
@JsonPropertyOrder({
  ClosingPeriodType.JSON_PROPERTY_CLOSING_PERIOD_STARTING_DATE,
  ClosingPeriodType.JSON_PROPERTY_CLOSING_PERIOD_ENDING_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ClosingPeriodType {
  public static final String JSON_PROPERTY_CLOSING_PERIOD_STARTING_DATE = "closingPeriodStartingDate";
  private LocalDate closingPeriodStartingDate;

  public static final String JSON_PROPERTY_CLOSING_PERIOD_ENDING_DATE = "closingPeriodEndingDate";
  private LocalDate closingPeriodEndingDate;

  public ClosingPeriodType() {
  }

  public ClosingPeriodType closingPeriodStartingDate(LocalDate closingPeriodStartingDate) {
    
    this.closingPeriodStartingDate = closingPeriodStartingDate;
    return this;
  }

  /**
   * Get closingPeriodStartingDate
   * @return closingPeriodStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getClosingPeriodStartingDate() {
    return closingPeriodStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setClosingPeriodStartingDate(LocalDate closingPeriodStartingDate) {
    this.closingPeriodStartingDate = closingPeriodStartingDate;
  }

  public ClosingPeriodType closingPeriodEndingDate(LocalDate closingPeriodEndingDate) {
    
    this.closingPeriodEndingDate = closingPeriodEndingDate;
    return this;
  }

  /**
   * Get closingPeriodEndingDate
   * @return closingPeriodEndingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getClosingPeriodEndingDate() {
    return closingPeriodEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setClosingPeriodEndingDate(LocalDate closingPeriodEndingDate) {
    this.closingPeriodEndingDate = closingPeriodEndingDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ClosingPeriodType closingPeriodType = (ClosingPeriodType) o;
    return Objects.equals(this.closingPeriodStartingDate, closingPeriodType.closingPeriodStartingDate) &&
        Objects.equals(this.closingPeriodEndingDate, closingPeriodType.closingPeriodEndingDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(closingPeriodStartingDate, closingPeriodEndingDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ClosingPeriodType {\n");
    sb.append("    closingPeriodStartingDate: ").append(toIndentedString(closingPeriodStartingDate)).append("\n");
    sb.append("    closingPeriodEndingDate: ").append(toIndentedString(closingPeriodEndingDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

