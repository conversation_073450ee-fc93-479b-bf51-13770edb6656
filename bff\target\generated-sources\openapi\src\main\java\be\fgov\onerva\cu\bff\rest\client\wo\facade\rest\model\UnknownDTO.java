/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyQualityDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyTypeDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * When the type is not present in the smals api this type will be returned; this type was introduced for the search api which attow does not have the capacity to return a proper third party type(&#39;nature&#39;) due to limitations of WO(smals)
 */
@JsonPropertyOrder({
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@JsonIgnoreProperties(
  value = "type", // ignore manually set type, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the type to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type", visible = true)

public class UnknownDTO extends ThirdPartyDTO {
  public UnknownDTO() {

  }

  @Override
  public UnknownDTO id(String id) {
    this.setId(id);
    return this;
  }

  @Override
  public UnknownDTO type(ThirdPartyTypeDTO type) {
    this.setType(type);
    return this;
  }

  @Override
  public UnknownDTO quality(ThirdPartyQualityDTO quality) {
    this.setQuality(quality);
    return this;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    return super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UnknownDTO {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

