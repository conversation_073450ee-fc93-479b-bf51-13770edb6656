package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param requestDate The date when the request was submitted
 */
data class RequestInformationResponse(

    @field:Valid
    @Schema(example = "null", description = "The date when the request was submitted")
    @get:JsonProperty("requestDate") val requestDate: java.time.LocalDate? = null
    ) {

}

