/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HandledNaturalPersonType
 */
@JsonPropertyOrder({
  HandledNaturalPersonType.JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR,
  HandledNaturalPersonType.JSON_PROPERTY_INSS,
  HandledNaturalPersonType.JSON_PROPERTY_SIS,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_NAME,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_FIRST_NAME,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_INITIAL,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_BIRTHDATE,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_BIRTHPLACE,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_BIRTHPLACE_COUNTRY,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_SEX,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_STREET,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_HOUSE_NBR,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_POST_BOX,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_Z_I_P_CODE,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_CITY,
  HandledNaturalPersonType.JSON_PROPERTY_WORKER_COUNTRY,
  HandledNaturalPersonType.JSON_PROPERTY_NATIONALITY,
  HandledNaturalPersonType.JSON_PROPERTY_ORIOLUS_SUCCESS_CODE,
  HandledNaturalPersonType.JSON_PROPERTY_ORIOLUS_SUB_STATUS_RIP,
  HandledNaturalPersonType.JSON_PROPERTY_BIS_FLAG_INDICATION,
  HandledNaturalPersonType.JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE,
  HandledNaturalPersonType.JSON_PROPERTY_PREVIOUS_I_N_S_S
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HandledNaturalPersonType {
  public static final String JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR = "naturalPersonSequenceNbr";
  private String naturalPersonSequenceNbr;

  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public static final String JSON_PROPERTY_SIS = "sis";
  private String sis;

  public static final String JSON_PROPERTY_WORKER_NAME = "workerName";
  private String workerName;

  public static final String JSON_PROPERTY_WORKER_FIRST_NAME = "workerFirstName";
  private String workerFirstName;

  public static final String JSON_PROPERTY_WORKER_INITIAL = "workerInitial";
  private String workerInitial;

  public static final String JSON_PROPERTY_WORKER_BIRTHDATE = "workerBirthdate";
  private String workerBirthdate;

  public static final String JSON_PROPERTY_WORKER_BIRTHPLACE = "workerBirthplace";
  private String workerBirthplace;

  public static final String JSON_PROPERTY_WORKER_BIRTHPLACE_COUNTRY = "workerBirthplaceCountry";
  private String workerBirthplaceCountry;

  public static final String JSON_PROPERTY_WORKER_SEX = "workerSex";
  private Integer workerSex;

  public static final String JSON_PROPERTY_WORKER_STREET = "workerStreet";
  private String workerStreet;

  public static final String JSON_PROPERTY_WORKER_HOUSE_NBR = "workerHouseNbr";
  private String workerHouseNbr;

  public static final String JSON_PROPERTY_WORKER_POST_BOX = "workerPostBox";
  private String workerPostBox;

  public static final String JSON_PROPERTY_WORKER_Z_I_P_CODE = "workerZIPCode";
  private String workerZIPCode;

  public static final String JSON_PROPERTY_WORKER_CITY = "workerCity";
  private String workerCity;

  public static final String JSON_PROPERTY_WORKER_COUNTRY = "workerCountry";
  private String workerCountry;

  public static final String JSON_PROPERTY_NATIONALITY = "nationality";
  private String nationality;

  public static final String JSON_PROPERTY_ORIOLUS_SUCCESS_CODE = "oriolusSuccessCode";
  private String oriolusSuccessCode;

  public static final String JSON_PROPERTY_ORIOLUS_SUB_STATUS_RIP = "oriolusSubStatusRip";
  private String oriolusSubStatusRip;

  public static final String JSON_PROPERTY_BIS_FLAG_INDICATION = "bisFlagIndication";
  private String bisFlagIndication;

  public static final String JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE = "naturalPersonUserReference";
  private String naturalPersonUserReference;

  public static final String JSON_PROPERTY_PREVIOUS_I_N_S_S = "previousINSS";
  private String previousINSS;

  public HandledNaturalPersonType() {
  }

  public HandledNaturalPersonType naturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
    return this;
  }

  /**
   * Get naturalPersonSequenceNbr
   * @return naturalPersonSequenceNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNaturalPersonSequenceNbr() {
    return naturalPersonSequenceNbr;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
  }

  public HandledNaturalPersonType inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  public HandledNaturalPersonType sis(String sis) {
    
    this.sis = sis;
    return this;
  }

  /**
   * Get sis
   * @return sis
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSis() {
    return sis;
  }


  @JsonProperty(JSON_PROPERTY_SIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSis(String sis) {
    this.sis = sis;
  }

  public HandledNaturalPersonType workerName(String workerName) {
    
    this.workerName = workerName;
    return this;
  }

  /**
   * Get workerName
   * @return workerName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerName() {
    return workerName;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerName(String workerName) {
    this.workerName = workerName;
  }

  public HandledNaturalPersonType workerFirstName(String workerFirstName) {
    
    this.workerFirstName = workerFirstName;
    return this;
  }

  /**
   * Get workerFirstName
   * @return workerFirstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerFirstName() {
    return workerFirstName;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerFirstName(String workerFirstName) {
    this.workerFirstName = workerFirstName;
  }

  public HandledNaturalPersonType workerInitial(String workerInitial) {
    
    this.workerInitial = workerInitial;
    return this;
  }

  /**
   * Get workerInitial
   * @return workerInitial
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_INITIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerInitial() {
    return workerInitial;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_INITIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerInitial(String workerInitial) {
    this.workerInitial = workerInitial;
  }

  public HandledNaturalPersonType workerBirthdate(String workerBirthdate) {
    
    this.workerBirthdate = workerBirthdate;
    return this;
  }

  /**
   * Get workerBirthdate
   * @return workerBirthdate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerBirthdate() {
    return workerBirthdate;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerBirthdate(String workerBirthdate) {
    this.workerBirthdate = workerBirthdate;
  }

  public HandledNaturalPersonType workerBirthplace(String workerBirthplace) {
    
    this.workerBirthplace = workerBirthplace;
    return this;
  }

  /**
   * Get workerBirthplace
   * @return workerBirthplace
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHPLACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerBirthplace() {
    return workerBirthplace;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHPLACE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerBirthplace(String workerBirthplace) {
    this.workerBirthplace = workerBirthplace;
  }

  public HandledNaturalPersonType workerBirthplaceCountry(String workerBirthplaceCountry) {
    
    this.workerBirthplaceCountry = workerBirthplaceCountry;
    return this;
  }

  /**
   * Get workerBirthplaceCountry
   * @return workerBirthplaceCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHPLACE_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerBirthplaceCountry() {
    return workerBirthplaceCountry;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_BIRTHPLACE_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerBirthplaceCountry(String workerBirthplaceCountry) {
    this.workerBirthplaceCountry = workerBirthplaceCountry;
  }

  public HandledNaturalPersonType workerSex(Integer workerSex) {
    
    this.workerSex = workerSex;
    return this;
  }

  /**
   * Get workerSex
   * @return workerSex
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getWorkerSex() {
    return workerSex;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerSex(Integer workerSex) {
    this.workerSex = workerSex;
  }

  public HandledNaturalPersonType workerStreet(String workerStreet) {
    
    this.workerStreet = workerStreet;
    return this;
  }

  /**
   * Get workerStreet
   * @return workerStreet
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStreet() {
    return workerStreet;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStreet(String workerStreet) {
    this.workerStreet = workerStreet;
  }

  public HandledNaturalPersonType workerHouseNbr(String workerHouseNbr) {
    
    this.workerHouseNbr = workerHouseNbr;
    return this;
  }

  /**
   * Get workerHouseNbr
   * @return workerHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerHouseNbr() {
    return workerHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerHouseNbr(String workerHouseNbr) {
    this.workerHouseNbr = workerHouseNbr;
  }

  public HandledNaturalPersonType workerPostBox(String workerPostBox) {
    
    this.workerPostBox = workerPostBox;
    return this;
  }

  /**
   * Get workerPostBox
   * @return workerPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerPostBox() {
    return workerPostBox;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerPostBox(String workerPostBox) {
    this.workerPostBox = workerPostBox;
  }

  public HandledNaturalPersonType workerZIPCode(String workerZIPCode) {
    
    this.workerZIPCode = workerZIPCode;
    return this;
  }

  /**
   * Get workerZIPCode
   * @return workerZIPCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerZIPCode() {
    return workerZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerZIPCode(String workerZIPCode) {
    this.workerZIPCode = workerZIPCode;
  }

  public HandledNaturalPersonType workerCity(String workerCity) {
    
    this.workerCity = workerCity;
    return this;
  }

  /**
   * Get workerCity
   * @return workerCity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCity() {
    return workerCity;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCity(String workerCity) {
    this.workerCity = workerCity;
  }

  public HandledNaturalPersonType workerCountry(String workerCountry) {
    
    this.workerCountry = workerCountry;
    return this;
  }

  /**
   * Get workerCountry
   * @return workerCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCountry() {
    return workerCountry;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCountry(String workerCountry) {
    this.workerCountry = workerCountry;
  }

  public HandledNaturalPersonType nationality(String nationality) {
    
    this.nationality = nationality;
    return this;
  }

  /**
   * Get nationality
   * @return nationality
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNationality() {
    return nationality;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationality(String nationality) {
    this.nationality = nationality;
  }

  public HandledNaturalPersonType oriolusSuccessCode(String oriolusSuccessCode) {
    
    this.oriolusSuccessCode = oriolusSuccessCode;
    return this;
  }

  /**
   * Get oriolusSuccessCode
   * @return oriolusSuccessCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ORIOLUS_SUCCESS_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOriolusSuccessCode() {
    return oriolusSuccessCode;
  }


  @JsonProperty(JSON_PROPERTY_ORIOLUS_SUCCESS_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOriolusSuccessCode(String oriolusSuccessCode) {
    this.oriolusSuccessCode = oriolusSuccessCode;
  }

  public HandledNaturalPersonType oriolusSubStatusRip(String oriolusSubStatusRip) {
    
    this.oriolusSubStatusRip = oriolusSubStatusRip;
    return this;
  }

  /**
   * Get oriolusSubStatusRip
   * @return oriolusSubStatusRip
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ORIOLUS_SUB_STATUS_RIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOriolusSubStatusRip() {
    return oriolusSubStatusRip;
  }


  @JsonProperty(JSON_PROPERTY_ORIOLUS_SUB_STATUS_RIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOriolusSubStatusRip(String oriolusSubStatusRip) {
    this.oriolusSubStatusRip = oriolusSubStatusRip;
  }

  public HandledNaturalPersonType bisFlagIndication(String bisFlagIndication) {
    
    this.bisFlagIndication = bisFlagIndication;
    return this;
  }

  /**
   * Get bisFlagIndication
   * @return bisFlagIndication
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_BIS_FLAG_INDICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getBisFlagIndication() {
    return bisFlagIndication;
  }


  @JsonProperty(JSON_PROPERTY_BIS_FLAG_INDICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setBisFlagIndication(String bisFlagIndication) {
    this.bisFlagIndication = bisFlagIndication;
  }

  public HandledNaturalPersonType naturalPersonUserReference(String naturalPersonUserReference) {
    
    this.naturalPersonUserReference = naturalPersonUserReference;
    return this;
  }

  /**
   * Get naturalPersonUserReference
   * @return naturalPersonUserReference
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNaturalPersonUserReference() {
    return naturalPersonUserReference;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNaturalPersonUserReference(String naturalPersonUserReference) {
    this.naturalPersonUserReference = naturalPersonUserReference;
  }

  public HandledNaturalPersonType previousINSS(String previousINSS) {
    
    this.previousINSS = previousINSS;
    return this;
  }

  /**
   * Get previousINSS
   * @return previousINSS
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PREVIOUS_I_N_S_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPreviousINSS() {
    return previousINSS;
  }


  @JsonProperty(JSON_PROPERTY_PREVIOUS_I_N_S_S)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPreviousINSS(String previousINSS) {
    this.previousINSS = previousINSS;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandledNaturalPersonType handledNaturalPersonType = (HandledNaturalPersonType) o;
    return Objects.equals(this.naturalPersonSequenceNbr, handledNaturalPersonType.naturalPersonSequenceNbr) &&
        Objects.equals(this.inss, handledNaturalPersonType.inss) &&
        Objects.equals(this.sis, handledNaturalPersonType.sis) &&
        Objects.equals(this.workerName, handledNaturalPersonType.workerName) &&
        Objects.equals(this.workerFirstName, handledNaturalPersonType.workerFirstName) &&
        Objects.equals(this.workerInitial, handledNaturalPersonType.workerInitial) &&
        Objects.equals(this.workerBirthdate, handledNaturalPersonType.workerBirthdate) &&
        Objects.equals(this.workerBirthplace, handledNaturalPersonType.workerBirthplace) &&
        Objects.equals(this.workerBirthplaceCountry, handledNaturalPersonType.workerBirthplaceCountry) &&
        Objects.equals(this.workerSex, handledNaturalPersonType.workerSex) &&
        Objects.equals(this.workerStreet, handledNaturalPersonType.workerStreet) &&
        Objects.equals(this.workerHouseNbr, handledNaturalPersonType.workerHouseNbr) &&
        Objects.equals(this.workerPostBox, handledNaturalPersonType.workerPostBox) &&
        Objects.equals(this.workerZIPCode, handledNaturalPersonType.workerZIPCode) &&
        Objects.equals(this.workerCity, handledNaturalPersonType.workerCity) &&
        Objects.equals(this.workerCountry, handledNaturalPersonType.workerCountry) &&
        Objects.equals(this.nationality, handledNaturalPersonType.nationality) &&
        Objects.equals(this.oriolusSuccessCode, handledNaturalPersonType.oriolusSuccessCode) &&
        Objects.equals(this.oriolusSubStatusRip, handledNaturalPersonType.oriolusSubStatusRip) &&
        Objects.equals(this.bisFlagIndication, handledNaturalPersonType.bisFlagIndication) &&
        Objects.equals(this.naturalPersonUserReference, handledNaturalPersonType.naturalPersonUserReference) &&
        Objects.equals(this.previousINSS, handledNaturalPersonType.previousINSS);
  }

  @Override
  public int hashCode() {
    return Objects.hash(naturalPersonSequenceNbr, inss, sis, workerName, workerFirstName, workerInitial, workerBirthdate, workerBirthplace, workerBirthplaceCountry, workerSex, workerStreet, workerHouseNbr, workerPostBox, workerZIPCode, workerCity, workerCountry, nationality, oriolusSuccessCode, oriolusSubStatusRip, bisFlagIndication, naturalPersonUserReference, previousINSS);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandledNaturalPersonType {\n");
    sb.append("    naturalPersonSequenceNbr: ").append(toIndentedString(naturalPersonSequenceNbr)).append("\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("    sis: ").append(toIndentedString(sis)).append("\n");
    sb.append("    workerName: ").append(toIndentedString(workerName)).append("\n");
    sb.append("    workerFirstName: ").append(toIndentedString(workerFirstName)).append("\n");
    sb.append("    workerInitial: ").append(toIndentedString(workerInitial)).append("\n");
    sb.append("    workerBirthdate: ").append(toIndentedString(workerBirthdate)).append("\n");
    sb.append("    workerBirthplace: ").append(toIndentedString(workerBirthplace)).append("\n");
    sb.append("    workerBirthplaceCountry: ").append(toIndentedString(workerBirthplaceCountry)).append("\n");
    sb.append("    workerSex: ").append(toIndentedString(workerSex)).append("\n");
    sb.append("    workerStreet: ").append(toIndentedString(workerStreet)).append("\n");
    sb.append("    workerHouseNbr: ").append(toIndentedString(workerHouseNbr)).append("\n");
    sb.append("    workerPostBox: ").append(toIndentedString(workerPostBox)).append("\n");
    sb.append("    workerZIPCode: ").append(toIndentedString(workerZIPCode)).append("\n");
    sb.append("    workerCity: ").append(toIndentedString(workerCity)).append("\n");
    sb.append("    workerCountry: ").append(toIndentedString(workerCountry)).append("\n");
    sb.append("    nationality: ").append(toIndentedString(nationality)).append("\n");
    sb.append("    oriolusSuccessCode: ").append(toIndentedString(oriolusSuccessCode)).append("\n");
    sb.append("    oriolusSubStatusRip: ").append(toIndentedString(oriolusSubStatusRip)).append("\n");
    sb.append("    bisFlagIndication: ").append(toIndentedString(bisFlagIndication)).append("\n");
    sb.append("    naturalPersonUserReference: ").append(toIndentedString(naturalPersonUserReference)).append("\n");
    sb.append("    previousINSS: ").append(toIndentedString(previousINSS)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

