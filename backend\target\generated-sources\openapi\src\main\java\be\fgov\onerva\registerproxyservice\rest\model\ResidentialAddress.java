/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.ForeignAddressMap;
import be.fgov.onerva.registerproxyservice.rest.model.Period;
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ResidentialAddress
 */
@JsonPropertyOrder({
  ResidentialAddress.JSON_PROPERTY_COUNTRY_CODE,
  ResidentialAddress.JSON_PROPERTY_COUNTRY_CODE_I_S_O,
  ResidentialAddress.JSON_PROPERTY_CITY_CODE,
  ResidentialAddress.JSON_PROPERTY_STREET_CODE,
  ResidentialAddress.JSON_PROPERTY_POSTAL_CODE,
  ResidentialAddress.JSON_PROPERTY_HOUSE_NUMBER,
  ResidentialAddress.JSON_PROPERTY_BOX_NUMBER,
  ResidentialAddress.JSON_PROPERTY_REGIONAL_STREET_CODE,
  ResidentialAddress.JSON_PROPERTY_REGION_CODE,
  ResidentialAddress.JSON_PROPERTY_FOREIGN_ADDRESSES
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@JsonIgnoreProperties(
  value = "@type", // ignore manually set @type, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the @type to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type", visible = true)

public class ResidentialAddress extends RegisterAddress {
  public static final String JSON_PROPERTY_COUNTRY_CODE = "countryCode";
  private Integer countryCode;

  public static final String JSON_PROPERTY_COUNTRY_CODE_I_S_O = "countryCodeISO";
  private String countryCodeISO;

  public static final String JSON_PROPERTY_CITY_CODE = "cityCode";
  private Integer cityCode;

  public static final String JSON_PROPERTY_STREET_CODE = "streetCode";
  private Integer streetCode;

  public static final String JSON_PROPERTY_POSTAL_CODE = "postalCode";
  private String postalCode;

  public static final String JSON_PROPERTY_HOUSE_NUMBER = "houseNumber";
  private String houseNumber;

  public static final String JSON_PROPERTY_BOX_NUMBER = "boxNumber";
  private String boxNumber;

  public static final String JSON_PROPERTY_REGIONAL_STREET_CODE = "regionalStreetCode";
  private String regionalStreetCode;

  public static final String JSON_PROPERTY_REGION_CODE = "regionCode";
  private String regionCode;

  public static final String JSON_PROPERTY_FOREIGN_ADDRESSES = "foreignAddresses";
  private ForeignAddressMap foreignAddresses;

  public ResidentialAddress() {

  }

  public ResidentialAddress countryCode(Integer countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

  /**
   * Get countryCode
   * @return countryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCountryCode() {
    return countryCode;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCode(Integer countryCode) {
    this.countryCode = countryCode;
  }

  public ResidentialAddress countryCodeISO(String countryCodeISO) {
    
    this.countryCodeISO = countryCodeISO;
    return this;
  }

  /**
   * Get countryCodeISO
   * @return countryCodeISO
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountryCodeISO() {
    return countryCodeISO;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCodeISO(String countryCodeISO) {
    this.countryCodeISO = countryCodeISO;
  }

  public ResidentialAddress cityCode(Integer cityCode) {
    
    this.cityCode = cityCode;
    return this;
  }

  /**
   * Get cityCode
   * @return cityCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCityCode() {
    return cityCode;
  }


  @JsonProperty(JSON_PROPERTY_CITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCityCode(Integer cityCode) {
    this.cityCode = cityCode;
  }

  public ResidentialAddress streetCode(Integer streetCode) {
    
    this.streetCode = streetCode;
    return this;
  }

  /**
   * Get streetCode
   * @return streetCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STREET_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getStreetCode() {
    return streetCode;
  }


  @JsonProperty(JSON_PROPERTY_STREET_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStreetCode(Integer streetCode) {
    this.streetCode = streetCode;
  }

  public ResidentialAddress postalCode(String postalCode) {
    
    this.postalCode = postalCode;
    return this;
  }

  /**
   * Get postalCode
   * @return postalCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostalCode() {
    return postalCode;
  }


  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }

  public ResidentialAddress houseNumber(String houseNumber) {
    
    this.houseNumber = houseNumber;
    return this;
  }

  /**
   * Get houseNumber
   * @return houseNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOUSE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHouseNumber() {
    return houseNumber;
  }


  @JsonProperty(JSON_PROPERTY_HOUSE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHouseNumber(String houseNumber) {
    this.houseNumber = houseNumber;
  }

  public ResidentialAddress boxNumber(String boxNumber) {
    
    this.boxNumber = boxNumber;
    return this;
  }

  /**
   * Get boxNumber
   * @return boxNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BOX_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBoxNumber() {
    return boxNumber;
  }


  @JsonProperty(JSON_PROPERTY_BOX_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBoxNumber(String boxNumber) {
    this.boxNumber = boxNumber;
  }

  public ResidentialAddress regionalStreetCode(String regionalStreetCode) {
    
    this.regionalStreetCode = regionalStreetCode;
    return this;
  }

  /**
   * Get regionalStreetCode
   * @return regionalStreetCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REGIONAL_STREET_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRegionalStreetCode() {
    return regionalStreetCode;
  }


  @JsonProperty(JSON_PROPERTY_REGIONAL_STREET_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRegionalStreetCode(String regionalStreetCode) {
    this.regionalStreetCode = regionalStreetCode;
  }

  public ResidentialAddress regionCode(String regionCode) {
    
    this.regionCode = regionCode;
    return this;
  }

  /**
   * Get regionCode
   * @return regionCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REGION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRegionCode() {
    return regionCode;
  }


  @JsonProperty(JSON_PROPERTY_REGION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRegionCode(String regionCode) {
    this.regionCode = regionCode;
  }

  public ResidentialAddress foreignAddresses(ForeignAddressMap foreignAddresses) {
    
    this.foreignAddresses = foreignAddresses;
    return this;
  }

  /**
   * Get foreignAddresses
   * @return foreignAddresses
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOREIGN_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ForeignAddressMap getForeignAddresses() {
    return foreignAddresses;
  }


  @JsonProperty(JSON_PROPERTY_FOREIGN_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForeignAddresses(ForeignAddressMap foreignAddresses) {
    this.foreignAddresses = foreignAddresses;
  }

  @Override
  public ResidentialAddress atType(AtTypeEnum atType) {
    this.setAtType(atType);
    return this;
  }

  @Override
  public ResidentialAddress radiated(Boolean radiated) {
    this.setRadiated(radiated);
    return this;
  }

  @Override
  public ResidentialAddress validityPeriod(Period validityPeriod) {
    this.setValidityPeriod(validityPeriod);
    return this;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResidentialAddress residentialAddress = (ResidentialAddress) o;
    return Objects.equals(this.countryCode, residentialAddress.countryCode) &&
        Objects.equals(this.countryCodeISO, residentialAddress.countryCodeISO) &&
        Objects.equals(this.cityCode, residentialAddress.cityCode) &&
        Objects.equals(this.streetCode, residentialAddress.streetCode) &&
        Objects.equals(this.postalCode, residentialAddress.postalCode) &&
        Objects.equals(this.houseNumber, residentialAddress.houseNumber) &&
        Objects.equals(this.boxNumber, residentialAddress.boxNumber) &&
        Objects.equals(this.regionalStreetCode, residentialAddress.regionalStreetCode) &&
        Objects.equals(this.regionCode, residentialAddress.regionCode) &&
        Objects.equals(this.foreignAddresses, residentialAddress.foreignAddresses) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(countryCode, countryCodeISO, cityCode, streetCode, postalCode, houseNumber, boxNumber, regionalStreetCode, regionCode, foreignAddresses, super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResidentialAddress {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("    countryCodeISO: ").append(toIndentedString(countryCodeISO)).append("\n");
    sb.append("    cityCode: ").append(toIndentedString(cityCode)).append("\n");
    sb.append("    streetCode: ").append(toIndentedString(streetCode)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    houseNumber: ").append(toIndentedString(houseNumber)).append("\n");
    sb.append("    boxNumber: ").append(toIndentedString(boxNumber)).append("\n");
    sb.append("    regionalStreetCode: ").append(toIndentedString(regionalStreetCode)).append("\n");
    sb.append("    regionCode: ").append(toIndentedString(regionCode)).append("\n");
    sb.append("    foreignAddresses: ").append(toIndentedString(foreignAddresses)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

