/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1TradeUnionContribution
 */
@JsonPropertyOrder({
  EC1TradeUnionContribution.JSON_PROPERTY_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH,
  EC1TradeUnionContribution.JSON_PROPERTY_STOP_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1TradeUnionContribution {
  public static final String JSON_PROPERTY_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH = "contributionDeductionFromTheMonth";
  private LocalDate contributionDeductionFromTheMonth;

  public static final String JSON_PROPERTY_STOP_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH = "stopContributionDeductionFromTheMonth";
  private LocalDate stopContributionDeductionFromTheMonth;

  public EC1TradeUnionContribution() {
  }

  public EC1TradeUnionContribution contributionDeductionFromTheMonth(LocalDate contributionDeductionFromTheMonth) {
    
    this.contributionDeductionFromTheMonth = contributionDeductionFromTheMonth;
    return this;
  }

  /**
   * Get contributionDeductionFromTheMonth
   * @return contributionDeductionFromTheMonth
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getContributionDeductionFromTheMonth() {
    return contributionDeductionFromTheMonth;
  }


  @JsonProperty(JSON_PROPERTY_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContributionDeductionFromTheMonth(LocalDate contributionDeductionFromTheMonth) {
    this.contributionDeductionFromTheMonth = contributionDeductionFromTheMonth;
  }

  public EC1TradeUnionContribution stopContributionDeductionFromTheMonth(LocalDate stopContributionDeductionFromTheMonth) {
    
    this.stopContributionDeductionFromTheMonth = stopContributionDeductionFromTheMonth;
    return this;
  }

  /**
   * Get stopContributionDeductionFromTheMonth
   * @return stopContributionDeductionFromTheMonth
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STOP_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getStopContributionDeductionFromTheMonth() {
    return stopContributionDeductionFromTheMonth;
  }


  @JsonProperty(JSON_PROPERTY_STOP_CONTRIBUTION_DEDUCTION_FROM_THE_MONTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStopContributionDeductionFromTheMonth(LocalDate stopContributionDeductionFromTheMonth) {
    this.stopContributionDeductionFromTheMonth = stopContributionDeductionFromTheMonth;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1TradeUnionContribution ec1TradeUnionContribution = (EC1TradeUnionContribution) o;
    return Objects.equals(this.contributionDeductionFromTheMonth, ec1TradeUnionContribution.contributionDeductionFromTheMonth) &&
        Objects.equals(this.stopContributionDeductionFromTheMonth, ec1TradeUnionContribution.stopContributionDeductionFromTheMonth);
  }

  @Override
  public int hashCode() {
    return Objects.hash(contributionDeductionFromTheMonth, stopContributionDeductionFromTheMonth);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1TradeUnionContribution {\n");
    sb.append("    contributionDeductionFromTheMonth: ").append(toIndentedString(contributionDeductionFromTheMonth)).append("\n");
    sb.append("    stopContributionDeductionFromTheMonth: ").append(toIndentedString(stopContributionDeductionFromTheMonth)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

