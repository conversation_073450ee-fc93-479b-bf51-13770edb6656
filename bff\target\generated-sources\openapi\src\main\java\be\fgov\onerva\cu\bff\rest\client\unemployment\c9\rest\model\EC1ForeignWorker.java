/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1ForeignWorker
 */
@JsonPropertyOrder({
  EC1ForeignWorker.JSON_PROPERTY_HAS_STATUTE_OF_REFUGEE_PERSON,
  EC1ForeignWorker.JSON_PROPERTY_IS_RECOGNIZED_STATELESS_PERSON,
  EC1ForeignWorker.JSON_PROPERTY_HAS_HAVE_RESIDENCE_PERMIT,
  EC1ForeignWorker.JSON_PROPERTY_HAS_HAVE_UNLIMITED_ACCESS_TO_WORK_MARKET,
  EC1ForeignWorker.JSON_PROPERTY_HAS_HAVE_LIMITED_ACCESS_TO_WORK_MARKET,
  EC1ForeignWorker.JSON_PROPERTY_DESCRIPTION,
  EC1ForeignWorker.JSON_PROPERTY_HAS_NOT_ACCESS_TO_WORK_MARKET
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1ForeignWorker {
  public static final String JSON_PROPERTY_HAS_STATUTE_OF_REFUGEE_PERSON = "hasStatuteOfRefugeePerson";
  private Boolean hasStatuteOfRefugeePerson;

  public static final String JSON_PROPERTY_IS_RECOGNIZED_STATELESS_PERSON = "isRecognizedStatelessPerson";
  private Boolean isRecognizedStatelessPerson;

  public static final String JSON_PROPERTY_HAS_HAVE_RESIDENCE_PERMIT = "hasHaveResidencePermit";
  private Boolean hasHaveResidencePermit;

  public static final String JSON_PROPERTY_HAS_HAVE_UNLIMITED_ACCESS_TO_WORK_MARKET = "hasHaveUnlimitedAccessToWorkMarket";
  private Boolean hasHaveUnlimitedAccessToWorkMarket;

  public static final String JSON_PROPERTY_HAS_HAVE_LIMITED_ACCESS_TO_WORK_MARKET = "hasHaveLimitedAccessToWorkMarket";
  private Boolean hasHaveLimitedAccessToWorkMarket;

  public static final String JSON_PROPERTY_DESCRIPTION = "description";
  private String description;

  public static final String JSON_PROPERTY_HAS_NOT_ACCESS_TO_WORK_MARKET = "hasNotAccessToWorkMarket";
  private Boolean hasNotAccessToWorkMarket;

  public EC1ForeignWorker() {
  }

  public EC1ForeignWorker hasStatuteOfRefugeePerson(Boolean hasStatuteOfRefugeePerson) {
    
    this.hasStatuteOfRefugeePerson = hasStatuteOfRefugeePerson;
    return this;
  }

  /**
   * Get hasStatuteOfRefugeePerson
   * @return hasStatuteOfRefugeePerson
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_STATUTE_OF_REFUGEE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasStatuteOfRefugeePerson() {
    return hasStatuteOfRefugeePerson;
  }


  @JsonProperty(JSON_PROPERTY_HAS_STATUTE_OF_REFUGEE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasStatuteOfRefugeePerson(Boolean hasStatuteOfRefugeePerson) {
    this.hasStatuteOfRefugeePerson = hasStatuteOfRefugeePerson;
  }

  public EC1ForeignWorker isRecognizedStatelessPerson(Boolean isRecognizedStatelessPerson) {
    
    this.isRecognizedStatelessPerson = isRecognizedStatelessPerson;
    return this;
  }

  /**
   * Get isRecognizedStatelessPerson
   * @return isRecognizedStatelessPerson
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_RECOGNIZED_STATELESS_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsRecognizedStatelessPerson() {
    return isRecognizedStatelessPerson;
  }


  @JsonProperty(JSON_PROPERTY_IS_RECOGNIZED_STATELESS_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsRecognizedStatelessPerson(Boolean isRecognizedStatelessPerson) {
    this.isRecognizedStatelessPerson = isRecognizedStatelessPerson;
  }

  public EC1ForeignWorker hasHaveResidencePermit(Boolean hasHaveResidencePermit) {
    
    this.hasHaveResidencePermit = hasHaveResidencePermit;
    return this;
  }

  /**
   * Get hasHaveResidencePermit
   * @return hasHaveResidencePermit
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_HAVE_RESIDENCE_PERMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasHaveResidencePermit() {
    return hasHaveResidencePermit;
  }


  @JsonProperty(JSON_PROPERTY_HAS_HAVE_RESIDENCE_PERMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasHaveResidencePermit(Boolean hasHaveResidencePermit) {
    this.hasHaveResidencePermit = hasHaveResidencePermit;
  }

  public EC1ForeignWorker hasHaveUnlimitedAccessToWorkMarket(Boolean hasHaveUnlimitedAccessToWorkMarket) {
    
    this.hasHaveUnlimitedAccessToWorkMarket = hasHaveUnlimitedAccessToWorkMarket;
    return this;
  }

  /**
   * Get hasHaveUnlimitedAccessToWorkMarket
   * @return hasHaveUnlimitedAccessToWorkMarket
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_HAVE_UNLIMITED_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasHaveUnlimitedAccessToWorkMarket() {
    return hasHaveUnlimitedAccessToWorkMarket;
  }


  @JsonProperty(JSON_PROPERTY_HAS_HAVE_UNLIMITED_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasHaveUnlimitedAccessToWorkMarket(Boolean hasHaveUnlimitedAccessToWorkMarket) {
    this.hasHaveUnlimitedAccessToWorkMarket = hasHaveUnlimitedAccessToWorkMarket;
  }

  public EC1ForeignWorker hasHaveLimitedAccessToWorkMarket(Boolean hasHaveLimitedAccessToWorkMarket) {
    
    this.hasHaveLimitedAccessToWorkMarket = hasHaveLimitedAccessToWorkMarket;
    return this;
  }

  /**
   * Get hasHaveLimitedAccessToWorkMarket
   * @return hasHaveLimitedAccessToWorkMarket
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_HAVE_LIMITED_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasHaveLimitedAccessToWorkMarket() {
    return hasHaveLimitedAccessToWorkMarket;
  }


  @JsonProperty(JSON_PROPERTY_HAS_HAVE_LIMITED_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasHaveLimitedAccessToWorkMarket(Boolean hasHaveLimitedAccessToWorkMarket) {
    this.hasHaveLimitedAccessToWorkMarket = hasHaveLimitedAccessToWorkMarket;
  }

  public EC1ForeignWorker description(String description) {
    
    this.description = description;
    return this;
  }

  /**
   * Get description
   * @return description
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESCRIPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescription() {
    return description;
  }


  @JsonProperty(JSON_PROPERTY_DESCRIPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescription(String description) {
    this.description = description;
  }

  public EC1ForeignWorker hasNotAccessToWorkMarket(Boolean hasNotAccessToWorkMarket) {
    
    this.hasNotAccessToWorkMarket = hasNotAccessToWorkMarket;
    return this;
  }

  /**
   * Get hasNotAccessToWorkMarket
   * @return hasNotAccessToWorkMarket
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_NOT_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasNotAccessToWorkMarket() {
    return hasNotAccessToWorkMarket;
  }


  @JsonProperty(JSON_PROPERTY_HAS_NOT_ACCESS_TO_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasNotAccessToWorkMarket(Boolean hasNotAccessToWorkMarket) {
    this.hasNotAccessToWorkMarket = hasNotAccessToWorkMarket;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1ForeignWorker ec1ForeignWorker = (EC1ForeignWorker) o;
    return Objects.equals(this.hasStatuteOfRefugeePerson, ec1ForeignWorker.hasStatuteOfRefugeePerson) &&
        Objects.equals(this.isRecognizedStatelessPerson, ec1ForeignWorker.isRecognizedStatelessPerson) &&
        Objects.equals(this.hasHaveResidencePermit, ec1ForeignWorker.hasHaveResidencePermit) &&
        Objects.equals(this.hasHaveUnlimitedAccessToWorkMarket, ec1ForeignWorker.hasHaveUnlimitedAccessToWorkMarket) &&
        Objects.equals(this.hasHaveLimitedAccessToWorkMarket, ec1ForeignWorker.hasHaveLimitedAccessToWorkMarket) &&
        Objects.equals(this.description, ec1ForeignWorker.description) &&
        Objects.equals(this.hasNotAccessToWorkMarket, ec1ForeignWorker.hasNotAccessToWorkMarket);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hasStatuteOfRefugeePerson, isRecognizedStatelessPerson, hasHaveResidencePermit, hasHaveUnlimitedAccessToWorkMarket, hasHaveLimitedAccessToWorkMarket, description, hasNotAccessToWorkMarket);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1ForeignWorker {\n");
    sb.append("    hasStatuteOfRefugeePerson: ").append(toIndentedString(hasStatuteOfRefugeePerson)).append("\n");
    sb.append("    isRecognizedStatelessPerson: ").append(toIndentedString(isRecognizedStatelessPerson)).append("\n");
    sb.append("    hasHaveResidencePermit: ").append(toIndentedString(hasHaveResidencePermit)).append("\n");
    sb.append("    hasHaveUnlimitedAccessToWorkMarket: ").append(toIndentedString(hasHaveUnlimitedAccessToWorkMarket)).append("\n");
    sb.append("    hasHaveLimitedAccessToWorkMarket: ").append(toIndentedString(hasHaveLimitedAccessToWorkMarket)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    hasNotAccessToWorkMarket: ").append(toIndentedString(hasNotAccessToWorkMarket)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

