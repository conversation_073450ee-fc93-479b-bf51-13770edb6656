package be.fgov.onerva.cu.backend.application.mapper

import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException

fun HistoricalCitizenSnapshot.toHistoricalCitizenAuthenticSources() = HistoricalCitizenAuthenticSources(
    firstName = this.firstName,
    lastName = this.lastName,

    nationality = this.nationality,
    address = this.address,
    birthDate = this.birthDate
        ?: throw InvalidExternalDataException("No birth date found in snapshot for authentic sources"),
)

fun HistoricalCitizenSnapshot.toHistoricalCitizenOnem() = HistoricalCitizenOnem(
    firstName = this.firstName,
    lastName = this.lastName,
    numbox = this.numbox
        ?: throw InvalidExternalDataException("No numbox found in snapshot for ONEM"),
    nationality = this.nationality,
    address = this.address,
    iban = this.iban,
    bic = this.bic,
    otherPersonName = this.otherPersonName,
    birthDate = this.birthDate,
    bankAccountValueDate = this.bankAccountValueDate,
    paymentMode = this.paymentMode,
    authorized = this.authorized,
    effectiveDate = this.effectiveDate,
)

fun HistoricalCitizenAuthenticSources.toHistoricalCitizenSnapshot() = HistoricalCitizenSnapshot(
    firstName = this.firstName,
    lastName = this.lastName,
    numbox = null,
    nationality = this.nationality,
    address = this.address,
    iban = null,
    bic = null,
    otherPersonName = null,
    birthDate = this.birthDate,
    bankAccountValueDate = null,
    paymentMode = null,
    authorized = null,
    effectiveDate = null,
)

fun HistoricalCitizenOnem.toHistoricalCitizenSnapshot() = HistoricalCitizenSnapshot(
    firstName = this.firstName,
    lastName = this.lastName,
    numbox = this.numbox,
    nationality = this.nationality,
    address = this.address,
    iban = this.iban,
    bic = this.bic,
    otherPersonName = this.otherPersonName,
    birthDate = this.birthDate,
    bankAccountValueDate = this.bankAccountValueDate,
    paymentMode = this.paymentMode,
    authorized = this.authorized,
    effectiveDate = this.effectiveDate,
)

fun AddressNullable.toDomainAddress() = Address(
    street = this.street,
    houseNumber = this.houseNumber ?: "",
    boxNumber = this.boxNumber,
    country = this.country ?: "",
    city = this.city ?: "",
    zipCode = this.zipCode,
)
