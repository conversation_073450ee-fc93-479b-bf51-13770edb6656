package be.fgov.onerva.cu.backend.adapter.out.persistence.model

import java.time.LocalDate
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import org.hibernate.envers.NotAudited
import be.fgov.onerva.cu.backend.application.domain.ExternalSource

@Embeddable
data class SnapshotAddress(
    @Column(name = "street", nullable = false)
    var street: String,

    @Column(name = "house_number", nullable = false)
    var houseNumber: String? = null,

    @Column(name = "box_number", nullable = true)
    var boxNumber: String? = null,

    @Column(name = "zip_code", nullable = false)
    var zipCode: String,

    @Column(name = "city", nullable = true)
    var city: String?,

    @Column(name = "country", nullable = false)
    var country: String?,

    @Column(name = "address_value_date", nullable = true)
    var valueDate: LocalDate?,
)

@Embeddable
data class SnapshotModeOfPayment(
    @Column(name = "other_person_name", nullable = true)
    var otherPersonName: String?,

    @Column(name = "own_account", nullable = true)
    var ownBankAccount: Boolean,

    @Column(name = "foreign_account")
    var foreignAccount: Boolean,

    @Column(name = "iban", nullable = true)
    var iban: String?,

    @Column(name = "bic", nullable = true)
    var bic: String?,

    @Column(name = "bank_account_value_date", nullable = true)
    var valueDate: LocalDate?,

    @Column(name = "payment_mode", nullable = false)
    var paymentMode: Int,
)

@Embeddable
data class SnapshotUnionContribution(
    @Column(name = "union_contribution_authorized", nullable = true)
    var authorized: Boolean?,

    @Column(name = "union_contribution_effective_date", nullable = true)
    var effectiveDate: LocalDate?,
)

@Entity
@Table(name = "citizen_information_snapshot")
class CitizenInformationSnapshotEntity
    (
    @OneToOne
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "numbox", nullable = true)
    var numbox: Int?,

    @Column(name = "last_name", nullable = false)
    var lastName: String,

    @Column(name = "first_name", nullable = false)
    var firstName: String,

    @Column(name = "nationality", nullable = false)
    var nationality: String,

    @Column(name = "birth_date", nullable = true)
    var birthDate: LocalDate?,

    @Embedded
    var address: SnapshotAddress,

    @Embedded
    var modeOfPayment: SnapshotModeOfPayment,

    @Embedded
    var unionContribution: SnapshotUnionContribution?,

    @Column(name = "external_source")
    @Enumerated(EnumType.STRING)
    var externalSource: ExternalSource,

    @Column(name = "readonly")
    var readonly: Boolean,

    ) : BaseEntity()

@Entity
@Table(name = "barema_snapshot")
class BaremaSnapshotEntity
    (
    @OneToOne
    @JoinColumn(name = "request_id", nullable = false)
    @NotAudited
    val request: RequestEntity,

    @Column(name = "found", nullable = false)
    var found: Boolean,

    @Column(name = "barema", nullable = true)
    var barema: String?,

    @Column(name = "article", nullable = true)
    var article: String?,

    @Column(name = "readonly")
    var readonly: Boolean,

    ) : BaseEntity()