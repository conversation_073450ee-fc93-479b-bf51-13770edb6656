package be.fgov.onerva.registerproxyservice.api;

import be.fgov.onerva.registerproxyservice.invoker.ApiClient;
import be.fgov.onerva.registerproxyservice.invoker.BaseApi;

import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenApi extends BaseApi {

    public CitizenApi() {
        super(new ApiClient());
    }

    public CitizenApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Retrieves a citizen&#39;s addresses by their unique identifier (deprecated)
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param uniqueid  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public RegisterPerson getCitizenAddresses(Long uniqueid, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenAddressesWithHttpInfo(uniqueid, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s addresses by their unique identifier (deprecated)
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param uniqueid  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public ResponseEntity<RegisterPerson> getCitizenAddressesWithHttpInfo(Long uniqueid, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'uniqueid' is set
        if (uniqueid == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'uniqueid' when calling getCitizenAddresses");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "uniqueid", uniqueid));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenAddresses", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves a citizen&#39;s addresses by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenAddressesBySsin(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenAddressesBySsinWithHttpInfo(ssin, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s addresses by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenAddressesBySsinWithHttpInfo(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenAddressesBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenAddressesBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves a citizen&#39;s contact addresses by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenContactAddressesBySsin(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenContactAddressesBySsinWithHttpInfo(ssin, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s contact addresses by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenContactAddressesBySsinWithHttpInfo(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenContactAddressesBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenContactAddressesBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves a citizen&#39;s date of death by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenDeceaseDateBySsin(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenDeceaseDateBySsinWithHttpInfo(ssin, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s date of death by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenDeceaseDateBySsinWithHttpInfo(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenDeceaseDateBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenDeceaseDateBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Searches for citizens by name (deprecated)
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param name  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @return List&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public List<RegisterPerson> getCitizenInfosByName(String name, String xRemoteIdentity, String legalContext) throws RestClientException {
        return getCitizenInfosByNameWithHttpInfo(name, xRemoteIdentity, legalContext).getBody();
    }

    /**
     * 
     * Searches for citizens by name (deprecated)
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param name  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @return ResponseEntity&lt;List&lt;RegisterPerson&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public ResponseEntity<List<RegisterPerson>> getCitizenInfosByNameWithHttpInfo(String name, String xRemoteIdentity, String legalContext) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'name' is set
        if (name == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'name' when calling getCitizenInfosByName");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "name", name));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<RegisterPerson>> localReturnType = new ParameterizedTypeReference<List<RegisterPerson>>() {};
        return apiClient.invokeAPI("/getCitizenInfosByName", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves comprehensive information about a citizen by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param adresses  (optional, default to false)
     * @param contactaddress  (optional, default to false)
     * @param decease  (optional, default to false)
     * @param legalContext  (optional, default to )
     * @param names  (optional, default to false)
     * @param nationalities  (optional, default to false)
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenInfosBySsin(String ssin, String xRemoteIdentity, Boolean adresses, Boolean contactaddress, Boolean decease, String legalContext, Boolean names, Boolean nationalities, Boolean replacedNiss) throws RestClientException {
        return getCitizenInfosBySsinWithHttpInfo(ssin, xRemoteIdentity, adresses, contactaddress, decease, legalContext, names, nationalities, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves comprehensive information about a citizen by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param adresses  (optional, default to false)
     * @param contactaddress  (optional, default to false)
     * @param decease  (optional, default to false)
     * @param legalContext  (optional, default to )
     * @param names  (optional, default to false)
     * @param nationalities  (optional, default to false)
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenInfosBySsinWithHttpInfo(String ssin, String xRemoteIdentity, Boolean adresses, Boolean contactaddress, Boolean decease, String legalContext, Boolean names, Boolean nationalities, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenInfosBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "adresses", adresses));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "contactaddress", contactaddress));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "decease", decease));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "names", names));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "nationalities", nationalities));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenInfosBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves a citizen&#39;s names by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenNamesBySsin(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenNamesBySsinWithHttpInfo(ssin, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s names by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenNamesBySsinWithHttpInfo(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenNamesBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenNamesBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves a citizen&#39;s nationalities by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return RegisterPerson
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RegisterPerson getCitizenNationalitiesBySsin(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        return getCitizenNationalitiesBySsinWithHttpInfo(ssin, xRemoteIdentity, legalContext, replacedNiss).getBody();
    }

    /**
     * 
     * Retrieves a citizen&#39;s nationalities by their SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @param replacedNiss  (optional, default to false)
     * @return ResponseEntity&lt;RegisterPerson&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RegisterPerson> getCitizenNationalitiesBySsinWithHttpInfo(String ssin, String xRemoteIdentity, String legalContext, Boolean replacedNiss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getCitizenNationalitiesBySsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "replaced_niss", replacedNiss));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<RegisterPerson> localReturnType = new ParameterizedTypeReference<RegisterPerson>() {};
        return apiClient.invokeAPI("/getCitizenNationalitiesBySsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
