/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.IndicationDaysAndHoursType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505WorkGridRestInnerAnyOf1
 */
@JsonPropertyOrder({
  Wech505WorkGridRestInnerAnyOf1.JSON_PROPERTY_INDICATION_DAYS_AND_HOURS
})
@JsonTypeName("Wech505WorkGrid_rest_inner_anyOf_1")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505WorkGridRestInnerAnyOf1 {
  public static final String JSON_PROPERTY_INDICATION_DAYS_AND_HOURS = "IndicationDaysAndHours";
  private IndicationDaysAndHoursType indicationDaysAndHours;

  public Wech505WorkGridRestInnerAnyOf1() {
  }

  public Wech505WorkGridRestInnerAnyOf1 indicationDaysAndHours(IndicationDaysAndHoursType indicationDaysAndHours) {
    
    this.indicationDaysAndHours = indicationDaysAndHours;
    return this;
  }

  /**
   * Get indicationDaysAndHours
   * @return indicationDaysAndHours
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INDICATION_DAYS_AND_HOURS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public IndicationDaysAndHoursType getIndicationDaysAndHours() {
    return indicationDaysAndHours;
  }


  @JsonProperty(JSON_PROPERTY_INDICATION_DAYS_AND_HOURS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIndicationDaysAndHours(IndicationDaysAndHoursType indicationDaysAndHours) {
    this.indicationDaysAndHours = indicationDaysAndHours;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505WorkGridRestInnerAnyOf1 wech505WorkGridRestInnerAnyOf1 = (Wech505WorkGridRestInnerAnyOf1) o;
    return Objects.equals(this.indicationDaysAndHours, wech505WorkGridRestInnerAnyOf1.indicationDaysAndHours);
  }

  @Override
  public int hashCode() {
    return Objects.hash(indicationDaysAndHours);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505WorkGridRestInnerAnyOf1 {\n");
    sb.append("    indicationDaysAndHours: ").append(toIndentedString(indicationDaysAndHours)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

