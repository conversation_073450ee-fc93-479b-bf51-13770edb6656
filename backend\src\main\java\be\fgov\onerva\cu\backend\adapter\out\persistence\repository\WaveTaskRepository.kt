package be.fgov.onerva.cu.backend.adapter.out.persistence.repository

import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus

interface WaveTaskRepository : JpaRepository<WaveTaskEntity, UUID> {
    fun findAllByRequestId(requestId: UUID): List<WaveTaskEntity>

    fun findAllByRequestIdAndStatusIn(requestId: UUID, statuses: Collection<WaveTaskStatus>): List<WaveTaskEntity>

    fun findByTaskId(taskId: String): WaveTaskEntity?
}