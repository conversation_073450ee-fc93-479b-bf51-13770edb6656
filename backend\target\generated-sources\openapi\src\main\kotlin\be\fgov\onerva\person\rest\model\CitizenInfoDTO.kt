/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model

import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param id Was never implemented.
 * @param ssin 
 * @param numPens 
 * @param lastName 
 * @param firstName 
 * @param address the address line, see addressObj for more info
 * @param postalCode use the addressObj instead
 * @param addressObj 
 * @param numBox 
 * @param OP 
 * @param unemploymentOffice 
 * @param iban use the bankAccount instead
 * @param bankAccount 
 * @param paymentMode List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam
 * @param language 
 * @param sex 
 * @param flagNation 
 * @param flagVCpte Was never implemented.
 * @param email 
 * @param emailReg 
 * @param flagToPurge 
 * @param lastModifDate 
 * @param telephoneOnem 
 * @param gsmOnem 
 * @param telephoneReg 
 * @param gsmReg 
 * @param birthDate 
 * @param deceasedDate 
 * @param bisNumber 
 * @param employmentContract Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam
 * @param unionDue 
 */


data class CitizenInfoDTO (

    /* Was never implemented. */
    @get:JsonProperty("id")
    @Deprecated(message = "This property is deprecated.")
    val id: java.math.BigDecimal? = null,

    @get:JsonProperty("ssin")
    val ssin: kotlin.String? = null,

    @get:JsonProperty("numPens")
    val numPens: java.math.BigDecimal? = null,

    @get:JsonProperty("lastName")
    val lastName: kotlin.String? = null,

    @get:JsonProperty("firstName")
    val firstName: kotlin.String? = null,

    /* the address line, see addressObj for more info */
    @get:JsonProperty("address")
    val address: kotlin.String? = null,

    /* use the addressObj instead */
    @get:JsonProperty("postalCode")
    @Deprecated(message = "This property is deprecated.")
    val postalCode: kotlin.String? = null,

    @get:JsonProperty("addressObj")
    val addressObj: ForeignAddressDTO? = null,

    @get:JsonProperty("numBox")
    val numBox: java.math.BigDecimal? = null,

    @get:JsonProperty("OP")
    val OP: java.math.BigDecimal? = null,

    @get:JsonProperty("unemploymentOffice")
    val unemploymentOffice: java.math.BigDecimal? = null,

    /* use the bankAccount instead */
    @get:JsonProperty("iban")
    @Deprecated(message = "This property is deprecated.")
    val iban: kotlin.String? = null,

    @get:JsonProperty("bankAccount")
    val bankAccount: BankAccountDTO? = null,

    /* List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam */
    @get:JsonProperty("paymentMode")
    val paymentMode: kotlin.Int? = null,

    @get:JsonProperty("language")
    val language: kotlin.String? = null,

    @get:JsonProperty("sex")
    val sex: kotlin.String? = null,

    @get:JsonProperty("flagNation")
    val flagNation: java.math.BigDecimal? = null,

    /* Was never implemented. */
    @get:JsonProperty("flagVCpte")
    @Deprecated(message = "This property is deprecated.")
    val flagVCpte: java.math.BigDecimal? = null,

    @get:JsonProperty("email")
    val email: kotlin.String? = null,

    @get:JsonProperty("emailReg")
    val emailReg: kotlin.String? = null,

    @get:JsonProperty("flagToPurge")
    val flagToPurge: kotlin.String? = null,

    @get:JsonProperty("lastModifDate")
    val lastModifDate: kotlin.Int? = null,

    @get:JsonProperty("telephoneOnem")
    val telephoneOnem: kotlin.String? = null,

    @get:JsonProperty("gsmOnem")
    val gsmOnem: kotlin.String? = null,

    @get:JsonProperty("telephoneReg")
    val telephoneReg: kotlin.String? = null,

    @get:JsonProperty("gsmReg")
    val gsmReg: kotlin.String? = null,

    @get:JsonProperty("birthDate")
    val birthDate: java.time.LocalDate? = null,

    @get:JsonProperty("deceasedDate")
    val deceasedDate: java.time.LocalDate? = null,

    @get:JsonProperty("bisNumber")
    val bisNumber: kotlin.collections.List<kotlin.String>? = null,

    /* Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam */
    @get:JsonProperty("employmentContract")
    val employmentContract: kotlin.Int? = null,

    @get:JsonProperty("unionDue")
    val unionDue: CitizenInfoUnionDueDTO? = null

) {


}

