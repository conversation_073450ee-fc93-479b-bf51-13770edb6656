/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param iban 
 * @param bic 
 * @param holder The name of the bank account holder
 * @param validFrom 
 */


data class BankAccountDTO (

    @get:JsonProperty("iban")
    val iban: kotlin.String? = null,

    @get:JsonProperty("bic")
    val bic: kotlin.String? = null,

    /* The name of the bank account holder */
    @get:JsonProperty("holder")
    val holder: kotlin.String? = null,

    @get:JsonProperty("validFrom")
    val validFrom: java.time.LocalDate? = null

) {


}

