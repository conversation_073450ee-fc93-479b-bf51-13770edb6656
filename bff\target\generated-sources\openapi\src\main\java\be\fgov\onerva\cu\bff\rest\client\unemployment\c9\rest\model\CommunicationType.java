/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CommunicationType
 */
@JsonPropertyOrder({
  CommunicationType.JSON_PROPERTY_PHONE_NBR,
  CommunicationType.JSON_PROPERTY_GSM_NBR,
  CommunicationType.JSON_PROPERTY_FAX_NBR,
  CommunicationType.JSON_PROPERTY_EMAIL_ADDRESS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CommunicationType {
  public static final String JSON_PROPERTY_PHONE_NBR = "phoneNbr";
  private String phoneNbr;

  public static final String JSON_PROPERTY_GSM_NBR = "gsmNbr";
  private String gsmNbr;

  public static final String JSON_PROPERTY_FAX_NBR = "faxNbr";
  private String faxNbr;

  public static final String JSON_PROPERTY_EMAIL_ADDRESS = "emailAddress";
  private String emailAddress;

  public CommunicationType() {
  }

  public CommunicationType phoneNbr(String phoneNbr) {
    
    this.phoneNbr = phoneNbr;
    return this;
  }

  /**
   * Get phoneNbr
   * @return phoneNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhoneNbr() {
    return phoneNbr;
  }


  @JsonProperty(JSON_PROPERTY_PHONE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhoneNbr(String phoneNbr) {
    this.phoneNbr = phoneNbr;
  }

  public CommunicationType gsmNbr(String gsmNbr) {
    
    this.gsmNbr = gsmNbr;
    return this;
  }

  /**
   * Get gsmNbr
   * @return gsmNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GSM_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGsmNbr() {
    return gsmNbr;
  }


  @JsonProperty(JSON_PROPERTY_GSM_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGsmNbr(String gsmNbr) {
    this.gsmNbr = gsmNbr;
  }

  public CommunicationType faxNbr(String faxNbr) {
    
    this.faxNbr = faxNbr;
    return this;
  }

  /**
   * Get faxNbr
   * @return faxNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FAX_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFaxNbr() {
    return faxNbr;
  }


  @JsonProperty(JSON_PROPERTY_FAX_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFaxNbr(String faxNbr) {
    this.faxNbr = faxNbr;
  }

  public CommunicationType emailAddress(String emailAddress) {
    
    this.emailAddress = emailAddress;
    return this;
  }

  /**
   * Get emailAddress
   * @return emailAddress
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmailAddress() {
    return emailAddress;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmailAddress(String emailAddress) {
    this.emailAddress = emailAddress;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CommunicationType communicationType = (CommunicationType) o;
    return Objects.equals(this.phoneNbr, communicationType.phoneNbr) &&
        Objects.equals(this.gsmNbr, communicationType.gsmNbr) &&
        Objects.equals(this.faxNbr, communicationType.faxNbr) &&
        Objects.equals(this.emailAddress, communicationType.emailAddress);
  }

  @Override
  public int hashCode() {
    return Objects.hash(phoneNbr, gsmNbr, faxNbr, emailAddress);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CommunicationType {\n");
    sb.append("    phoneNbr: ").append(toIndentedString(phoneNbr)).append("\n");
    sb.append("    gsmNbr: ").append(toIndentedString(gsmNbr)).append("\n");
    sb.append("    faxNbr: ").append(toIndentedString(faxNbr)).append("\n");
    sb.append("    emailAddress: ").append(toIndentedString(emailAddress)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

