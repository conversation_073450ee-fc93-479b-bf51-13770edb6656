/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.ProblemErrorDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * A JSON object representing a problem as defined by the  RFC 7807 specification, used to convey errors encountered in a REST API. 
 *
 * @param type A URI reference that identifies the problem type.
 * @param title A short, human-readable summary of the problem.
 * @param status 
 * @param detail A human-readable explanation specific to this occurrence of the problem.
 * @param instance A URI reference that identifies the specific occurrence of the problem.
 * @param timestamp The timestamp indicating when the problem was generated.
 * @param errors 
 */


data class ProblemDTO (

    /* A URI reference that identifies the problem type. */
    @get:JsonProperty("type")
    val type: java.net.URI,

    /* A short, human-readable summary of the problem. */
    @get:JsonProperty("title")
    val title: kotlin.String,

    @get:JsonProperty("status")
    val status: kotlin.Int,

    /* A human-readable explanation specific to this occurrence of the problem. */
    @get:JsonProperty("detail")
    val detail: kotlin.String? = null,

    /* A URI reference that identifies the specific occurrence of the problem. */
    @get:JsonProperty("instance")
    val instance: java.net.URI? = null,

    /* The timestamp indicating when the problem was generated. */
    @get:JsonProperty("timestamp")
    val timestamp: java.time.OffsetDateTime? = null,

    @get:JsonProperty("errors")
    val errors: kotlin.collections.List<ProblemErrorDTO>? = null

) {


}

