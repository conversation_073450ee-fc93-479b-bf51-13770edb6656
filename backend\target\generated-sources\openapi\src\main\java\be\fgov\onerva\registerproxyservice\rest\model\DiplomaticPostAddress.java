/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * DiplomaticPostAddress
 */
@JsonPropertyOrder({
  DiplomaticPostAddress.JSON_PROPERTY_COUNTRY_CODE,
  DiplomaticPostAddress.JSON_PROPERTY_COUNTRY_CODE_I_S_O,
  DiplomaticPostAddress.JSON_PROPERTY_ADDRESS,
  DiplomaticPostAddress.JSON_PROPERTY_BEGIN_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class DiplomaticPostAddress {
  public static final String JSON_PROPERTY_COUNTRY_CODE = "countryCode";
  private Integer countryCode;

  public static final String JSON_PROPERTY_COUNTRY_CODE_I_S_O = "countryCodeISO";
  private String countryCodeISO;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private String address;

  public static final String JSON_PROPERTY_BEGIN_DATE = "beginDate";
  private Long beginDate;

  public DiplomaticPostAddress() {
  }

  public DiplomaticPostAddress countryCode(Integer countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

  /**
   * Get countryCode
   * @return countryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCountryCode() {
    return countryCode;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCode(Integer countryCode) {
    this.countryCode = countryCode;
  }

  public DiplomaticPostAddress countryCodeISO(String countryCodeISO) {
    
    this.countryCodeISO = countryCodeISO;
    return this;
  }

  /**
   * Get countryCodeISO
   * @return countryCodeISO
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountryCodeISO() {
    return countryCodeISO;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCodeISO(String countryCodeISO) {
    this.countryCodeISO = countryCodeISO;
  }

  public DiplomaticPostAddress address(String address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(String address) {
    this.address = address;
  }

  public DiplomaticPostAddress beginDate(Long beginDate) {
    
    this.beginDate = beginDate;
    return this;
  }

  /**
   * Begin date represented as Unix timestamp in milliseconds (UTC-based)
   * @return beginDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getBeginDate() {
    return beginDate;
  }


  @JsonProperty(JSON_PROPERTY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBeginDate(Long beginDate) {
    this.beginDate = beginDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DiplomaticPostAddress diplomaticPostAddress = (DiplomaticPostAddress) o;
    return Objects.equals(this.countryCode, diplomaticPostAddress.countryCode) &&
        Objects.equals(this.countryCodeISO, diplomaticPostAddress.countryCodeISO) &&
        Objects.equals(this.address, diplomaticPostAddress.address) &&
        Objects.equals(this.beginDate, diplomaticPostAddress.beginDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(countryCode, countryCodeISO, address, beginDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DiplomaticPostAddress {\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("    countryCodeISO: ").append(toIndentedString(countryCodeISO)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    beginDate: ").append(toIndentedString(beginDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

