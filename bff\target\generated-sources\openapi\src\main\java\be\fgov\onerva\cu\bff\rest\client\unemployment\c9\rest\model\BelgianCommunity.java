/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * BelgianCommunity
 */
@JsonPropertyOrder({
  BelgianCommunity.JSON_PROPERTY_ZIP_CODE,
  BelgianCommunity.JSON_PROPERTY_DESC_FR,
  BelgianCommunity.JSON_PROPERTY_DESC_NL
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class BelgianCommunity {
  public static final String JSON_PROPERTY_ZIP_CODE = "zipCode";
  private String zipCode;

  public static final String JSON_PROPERTY_DESC_FR = "descFr";
  private String descFr;

  public static final String JSON_PROPERTY_DESC_NL = "descNl";
  private String descNl;

  public BelgianCommunity() {
  }

  public BelgianCommunity zipCode(String zipCode) {
    
    this.zipCode = zipCode;
    return this;
  }

  /**
   * Get zipCode
   * @return zipCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getZipCode() {
    return zipCode;
  }


  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZipCode(String zipCode) {
    this.zipCode = zipCode;
  }

  public BelgianCommunity descFr(String descFr) {
    
    this.descFr = descFr;
    return this;
  }

  /**
   * Get descFr
   * @return descFr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescFr() {
    return descFr;
  }


  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescFr(String descFr) {
    this.descFr = descFr;
  }

  public BelgianCommunity descNl(String descNl) {
    
    this.descNl = descNl;
    return this;
  }

  /**
   * Get descNl
   * @return descNl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescNl() {
    return descNl;
  }


  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescNl(String descNl) {
    this.descNl = descNl;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BelgianCommunity belgianCommunity = (BelgianCommunity) o;
    return Objects.equals(this.zipCode, belgianCommunity.zipCode) &&
        Objects.equals(this.descFr, belgianCommunity.descFr) &&
        Objects.equals(this.descNl, belgianCommunity.descNl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(zipCode, descFr, descNl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BelgianCommunity {\n");
    sb.append("    zipCode: ").append(toIndentedString(zipCode)).append("\n");
    sb.append("    descFr: ").append(toIndentedString(descFr)).append("\n");
    sb.append("    descNl: ").append(toIndentedString(descNl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

