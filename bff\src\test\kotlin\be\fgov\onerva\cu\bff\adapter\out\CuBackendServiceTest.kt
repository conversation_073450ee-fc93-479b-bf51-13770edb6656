package be.fgov.onerva.cu.bff.adapter.out

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.HttpMethod
import org.springframework.test.util.ReflectionTestUtils
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestInformationResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateBasicInfoRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateRequestInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateUnionContributionRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify
import kotlinx.coroutines.runBlocking

@ExtendWith(MockKExtension::class)
class CuBackendServiceTest {

    @MockK
    private lateinit var restTemplateUtil: RestTemplateUtil

    private val backendBaseUrl = "http://localhost:9091"

    @InjectMockKs
    private lateinit var cuBackendService: CuBackendService

    companion object {
        private val REQUEST_ID = UUID.fromString("123e4567-e89b-12d3-a456-************")
        private const val AUTH_HEADER = "Bearer test-token"
        private const val BACKEND_BASE_URL = "http://localhost:9091"
    }

    @BeforeEach
    fun setup() {
        ReflectionTestUtils.setField(cuBackendService, "backendBaseUrl", backendBaseUrl)
    }

    @Nested
    inner class GetRequestInformation {

        @Test
        fun `should return request information when found`() = runBlocking {
            // Given
            val expectedResponse = RequestInformationResponse()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/request-information"

            every {
                restTemplateUtil.getForObject(expectedUrl, RequestInformationResponse::class.java)
            } returns expectedResponse

            // When
            val result = cuBackendService.getRequestInformation(REQUEST_ID)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.getForObject(expectedUrl, RequestInformationResponse::class.java)
            }
        }
    }

    @Nested
    inner class GetBasicInfo {

        @Test
        fun `should return basic info when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<RequestBasicInfoResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, RequestBasicInfoResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getBasicInfo(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, RequestBasicInfoResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetCitizenInfo {

        @Test
        fun `should return citizen info when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<CitizenInformationDetailResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/citizen-information"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, CitizenInformationDetailResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getCitizenInfo(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, CitizenInformationDetailResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetPaymentInfo {

        @Test
        fun `should return payment info when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<ModeOfPaymentDetailResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/mode-of-payment"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, ModeOfPaymentDetailResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getPaymentInfo(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, ModeOfPaymentDetailResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetUnionInfo {

        @Test
        fun `should return union info when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<UnionContributionDetailResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/union-contribution"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, UnionContributionDetailResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getUnionInfo(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, UnionContributionDetailResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetHistoricalBarema {

        @Test
        fun `should return historical barema when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<HistoricalBaremaResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/historical/barema"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalBaremaResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getHistoricalBarema(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalBaremaResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetCitizenInfoFromC1 {

        @Test
        fun `should return citizen info from C1 when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<HistoricalCitizenC1Response>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/historical/citizen-information/C1"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalCitizenC1Response::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getCitizenInfoFromC1(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalCitizenC1Response::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetCitizenInfoFromOnem {

        @Test
        fun `should return citizen info from ONEM when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<HistoricalCitizenOnemResponse>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/historical/citizen-information/ONEM"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalCitizenOnemResponse::class.java, null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getCitizenInfoFromOnem(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl, HttpMethod.GET, AUTH_HEADER, HistoricalCitizenOnemResponse::class.java, null
                )
            }
        }
    }

    @Nested
    inner class GetCitizenInfoAuthentic {

        @Test
        fun `should return citizen info from authentic sources when found`() = runBlocking {
            // Given
            val expectedResponse = mockk<HistoricalCitizenAuthenticSourcesResponse>()
            val expectedUrl =
                "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/historical/citizen-information/AUTHENTIC_SOURCES"

            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl,
                    HttpMethod.GET,
                    AUTH_HEADER,
                    HistoricalCitizenAuthenticSourcesResponse::class.java,
                    null
                )
            } returns expectedResponse

            // When
            val result = cuBackendService.getCitizenInfoAuthentic(REQUEST_ID, AUTH_HEADER)

            // Then
            assertThat(result).isEqualTo(expectedResponse)
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl,
                    HttpMethod.GET,
                    AUTH_HEADER,
                    HistoricalCitizenAuthenticSourcesResponse::class.java,
                    null
                )
            }
        }
    }

    @Nested
    inner class UpdateCitizenInformation {

        @Test
        fun `should update citizen information when request is not null`() = runBlocking {
            // Given
            val updateRequest = mockk<UpdateCitizenInformationRequest>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/citizen-information"

            every {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            } returns Unit

            // When
            cuBackendService.updateCitizenInformation(REQUEST_ID, updateRequest, AUTH_HEADER)

            // Then
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            }
        }
    }

    @Nested
    inner class UpdateModeOfPayment {

        @Test
        fun `should update mode of payment when request is not null`() = runBlocking {
            // Given
            val updateRequest = mockk<UpdateModeOfPaymentRequest>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/mode-of-payment"

            every {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            } returns Unit

            // When
            cuBackendService.updateModeOfPayment(REQUEST_ID, updateRequest, AUTH_HEADER)

            // Then
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            }
        }
    }

    @Nested
    inner class UpdateUnionContribution {

        @Test
        fun `should update union contribution when request is not null`() = runBlocking {
            // Given
            val updateRequest = mockk<UpdateUnionContributionRequest>()
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/union-contribution"

            every {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            } returns Unit

            // When
            cuBackendService.updateUnionContribution(REQUEST_ID, updateRequest, AUTH_HEADER)

            // Then
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, updateRequest)
            }
        }
    }

    @Nested
    inner class UpdateBasicInfo {

        @Test
        fun `should update basic info when request has request date`() = runBlocking {
            // Given
            val requestDate = LocalDate.now()
            val updateRequest = mockk<UpdateBasicInfoRequest>()
            val updateRequestInformationRequest = UpdateRequestInformationRequest(requestDate = requestDate)
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/request-information"

            every {
                updateRequest.requestDate
            } returns requestDate
            every {
                restTemplateUtil.executeWithAuth(
                    expectedUrl,
                    HttpMethod.PUT,
                    AUTH_HEADER,
                    updateRequestInformationRequest
                )
            } returns Unit

            // When
            cuBackendService.updateBasicInfo(REQUEST_ID, updateRequest, AUTH_HEADER)

            // Then
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(
                    expectedUrl,
                    HttpMethod.PUT,
                    AUTH_HEADER,
                    updateRequestInformationRequest
                )
            }
        }

        @Test
        fun `should not call rest template when request is null`() = runBlocking {
            // When
            cuBackendService.updateBasicInfo(REQUEST_ID, null, AUTH_HEADER)

            // Then
            verify(exactly = 0) {
                restTemplateUtil.executeWithAuth(
                    any<String>(),
                    any<HttpMethod>(),
                    any<String>(),
                    any<UpdateRequestInformationRequest>()
                )
            }
        }

        @Test
        fun `should not call rest template when request date is null`() = runBlocking {
            // Given
            val updateRequest = UpdateBasicInfoRequest(requestDate = null)

            // When
            cuBackendService.updateBasicInfo(REQUEST_ID, updateRequest, AUTH_HEADER)

            // Then
            verify(exactly = 0) {
                restTemplateUtil.executeWithAuth(
                    any<String>(),
                    any<HttpMethod>(),
                    any<String>(),
                    any<UpdateRequestInformationRequest>()
                )
            }
        }
    }

    @Nested
    inner class AssignTaskToUser {

        @Test
        fun `should assign task to user`() = runBlocking {
            // Given
            val expectedUrl = "$BACKEND_BASE_URL/api/requests/$REQUEST_ID/assign-user"

            every {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, null)
            } returns Unit

            // When
            cuBackendService.assignTaskToUser(REQUEST_ID, AUTH_HEADER)

            // Then
            verify(exactly = 1) {
                restTemplateUtil.executeWithAuth(expectedUrl, HttpMethod.PUT, AUTH_HEADER, null)
            }
        }
    }
}