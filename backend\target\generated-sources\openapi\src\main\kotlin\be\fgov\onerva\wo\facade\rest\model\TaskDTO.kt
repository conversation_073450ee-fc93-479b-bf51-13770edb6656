/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO
import be.fgov.onerva.wo.facade.rest.model.CommentDTO
import be.fgov.onerva.wo.facade.rest.model.CorrelationDTO
import be.fgov.onerva.wo.facade.rest.model.HistoryDTO
import be.fgov.onerva.wo.facade.rest.model.MetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.StatusDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param concernedEntities 
 * @param assignee It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \"E\" + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup
 * @param taskTypeCode 
 * @param processId 
 * @param taskId 
 * @param assigneeInfo 
 * @param metadata 
 * @param dueDate 
 * @param correlation 
 * @param processMetadata 
 * @param permissionGroupId 
 * @param businessId 
 * @param creationDate 
 * @param closureDate 
 * @param comments 
 * @param status 
 * @param history 
 */


data class TaskDTO (

    @get:JsonProperty("concernedEntities")
    val concernedEntities: kotlin.collections.List<ThirdPartyDTO>,

    /* It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \"E\" + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup */
    @get:JsonProperty("assignee")
    val assignee: kotlin.String,

    @get:JsonProperty("taskTypeCode")
    val taskTypeCode: kotlin.String,

    @get:JsonProperty("processId")
    val processId: kotlin.Long?,

    @get:JsonProperty("taskId")
    val taskId: kotlin.Long,

    @get:JsonProperty("assigneeInfo")
    val assigneeInfo: AssigneeInfoDTO,

    @get:JsonProperty("metadata")
    val metadata: kotlin.collections.List<MetaDataDTO>? = null,

    @get:JsonProperty("dueDate")
    val dueDate: java.time.LocalDate? = null,

    @get:JsonProperty("correlation")
    val correlation: CorrelationDTO? = null,

    @get:JsonProperty("processMetadata")
    val processMetadata: kotlin.collections.List<MetaDataDTO>? = null,

    @get:JsonProperty("permissionGroupId")
    val permissionGroupId: kotlin.String? = null,

    @get:JsonProperty("businessId")
    val businessId: kotlin.String? = null,

    @get:JsonProperty("creationDate")
    val creationDate: java.time.OffsetDateTime? = null,

    @get:JsonProperty("closureDate")
    val closureDate: java.time.OffsetDateTime? = null,

    @get:JsonProperty("comments")
    val comments: kotlin.collections.List<CommentDTO>? = null,

    @get:JsonProperty("status")
    val status: StatusDTO? = null,

    @get:JsonProperty("history")
    val history: kotlin.collections.List<HistoryDTO>? = null

) {


}

