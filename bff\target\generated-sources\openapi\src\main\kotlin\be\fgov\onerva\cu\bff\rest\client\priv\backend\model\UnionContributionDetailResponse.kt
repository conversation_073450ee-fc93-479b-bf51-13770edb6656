/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.FieldSource

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param authorized 
 * @param effectiveDate The effective date of the union contribution (format YYYY-MM-DD)
 * @param fieldSources Sources for individual fields
 */


data class UnionContributionDetailResponse (

    @get:JsonProperty("authorized")
    val authorized: kotlin.Boolean? = null,

    /* The effective date of the union contribution (format YYYY-MM-DD) */
    @get:JsonProperty("effectiveDate")
    val effectiveDate: java.time.LocalDate? = null,

    /* Sources for individual fields */
    @get:JsonProperty("fieldSources")
    val fieldSources: kotlin.collections.List<FieldSource>? = null

) {


}

