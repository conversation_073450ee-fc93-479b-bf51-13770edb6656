/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.HandledNaturalPersonType;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502WorkerRecordLink;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502NaturalPerson
 */
@JsonPropertyOrder({
  Wech502NaturalPerson.JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR,
  Wech502NaturalPerson.JSON_PROPERTY_INSS,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_STREET,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_HOUSE_NBR,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_POST_BOX,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_Z_I_P_CODE,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_CITY,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_COUNTRY,
  Wech502NaturalPerson.JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE,
  Wech502NaturalPerson.JSON_PROPERTY_HANDLED_NATURAL_PERSON,
  Wech502NaturalPerson.JSON_PROPERTY_WORKER_RECORD_LINK
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502NaturalPerson {
  public static final String JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR = "naturalPersonSequenceNbr";
  private String naturalPersonSequenceNbr;

  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public static final String JSON_PROPERTY_WORKER_STREET = "workerStreet";
  private String workerStreet;

  public static final String JSON_PROPERTY_WORKER_HOUSE_NBR = "workerHouseNbr";
  private String workerHouseNbr;

  public static final String JSON_PROPERTY_WORKER_POST_BOX = "workerPostBox";
  private String workerPostBox;

  public static final String JSON_PROPERTY_WORKER_Z_I_P_CODE = "workerZIPCode";
  private String workerZIPCode;

  public static final String JSON_PROPERTY_WORKER_CITY = "workerCity";
  private String workerCity;

  public static final String JSON_PROPERTY_WORKER_COUNTRY = "workerCountry";
  private String workerCountry;

  public static final String JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE = "naturalPersonUserReference";
  private String naturalPersonUserReference;

  public static final String JSON_PROPERTY_HANDLED_NATURAL_PERSON = "handledNaturalPerson";
  private HandledNaturalPersonType handledNaturalPerson;

  public static final String JSON_PROPERTY_WORKER_RECORD_LINK = "workerRecordLink";
  private Wech502WorkerRecordLink workerRecordLink;

  public Wech502NaturalPerson() {
  }

  public Wech502NaturalPerson naturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
    return this;
  }

  /**
   * Get naturalPersonSequenceNbr
   * @return naturalPersonSequenceNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNaturalPersonSequenceNbr() {
    return naturalPersonSequenceNbr;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
  }

  public Wech502NaturalPerson inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  public Wech502NaturalPerson workerStreet(String workerStreet) {
    
    this.workerStreet = workerStreet;
    return this;
  }

  /**
   * Get workerStreet
   * @return workerStreet
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStreet() {
    return workerStreet;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStreet(String workerStreet) {
    this.workerStreet = workerStreet;
  }

  public Wech502NaturalPerson workerHouseNbr(String workerHouseNbr) {
    
    this.workerHouseNbr = workerHouseNbr;
    return this;
  }

  /**
   * Get workerHouseNbr
   * @return workerHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerHouseNbr() {
    return workerHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerHouseNbr(String workerHouseNbr) {
    this.workerHouseNbr = workerHouseNbr;
  }

  public Wech502NaturalPerson workerPostBox(String workerPostBox) {
    
    this.workerPostBox = workerPostBox;
    return this;
  }

  /**
   * Get workerPostBox
   * @return workerPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerPostBox() {
    return workerPostBox;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerPostBox(String workerPostBox) {
    this.workerPostBox = workerPostBox;
  }

  public Wech502NaturalPerson workerZIPCode(String workerZIPCode) {
    
    this.workerZIPCode = workerZIPCode;
    return this;
  }

  /**
   * Get workerZIPCode
   * @return workerZIPCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerZIPCode() {
    return workerZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerZIPCode(String workerZIPCode) {
    this.workerZIPCode = workerZIPCode;
  }

  public Wech502NaturalPerson workerCity(String workerCity) {
    
    this.workerCity = workerCity;
    return this;
  }

  /**
   * Get workerCity
   * @return workerCity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCity() {
    return workerCity;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCity(String workerCity) {
    this.workerCity = workerCity;
  }

  public Wech502NaturalPerson workerCountry(String workerCountry) {
    
    this.workerCountry = workerCountry;
    return this;
  }

  /**
   * Get workerCountry
   * @return workerCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCountry() {
    return workerCountry;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCountry(String workerCountry) {
    this.workerCountry = workerCountry;
  }

  public Wech502NaturalPerson naturalPersonUserReference(String naturalPersonUserReference) {
    
    this.naturalPersonUserReference = naturalPersonUserReference;
    return this;
  }

  /**
   * Get naturalPersonUserReference
   * @return naturalPersonUserReference
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNaturalPersonUserReference() {
    return naturalPersonUserReference;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNaturalPersonUserReference(String naturalPersonUserReference) {
    this.naturalPersonUserReference = naturalPersonUserReference;
  }

  public Wech502NaturalPerson handledNaturalPerson(HandledNaturalPersonType handledNaturalPerson) {
    
    this.handledNaturalPerson = handledNaturalPerson;
    return this;
  }

  /**
   * Get handledNaturalPerson
   * @return handledNaturalPerson
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledNaturalPersonType getHandledNaturalPerson() {
    return handledNaturalPerson;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledNaturalPerson(HandledNaturalPersonType handledNaturalPerson) {
    this.handledNaturalPerson = handledNaturalPerson;
  }

  public Wech502NaturalPerson workerRecordLink(Wech502WorkerRecordLink workerRecordLink) {
    
    this.workerRecordLink = workerRecordLink;
    return this;
  }

  /**
   * Get workerRecordLink
   * @return workerRecordLink
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_RECORD_LINK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Wech502WorkerRecordLink getWorkerRecordLink() {
    return workerRecordLink;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_RECORD_LINK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerRecordLink(Wech502WorkerRecordLink workerRecordLink) {
    this.workerRecordLink = workerRecordLink;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502NaturalPerson wech502NaturalPerson = (Wech502NaturalPerson) o;
    return Objects.equals(this.naturalPersonSequenceNbr, wech502NaturalPerson.naturalPersonSequenceNbr) &&
        Objects.equals(this.inss, wech502NaturalPerson.inss) &&
        Objects.equals(this.workerStreet, wech502NaturalPerson.workerStreet) &&
        Objects.equals(this.workerHouseNbr, wech502NaturalPerson.workerHouseNbr) &&
        Objects.equals(this.workerPostBox, wech502NaturalPerson.workerPostBox) &&
        Objects.equals(this.workerZIPCode, wech502NaturalPerson.workerZIPCode) &&
        Objects.equals(this.workerCity, wech502NaturalPerson.workerCity) &&
        Objects.equals(this.workerCountry, wech502NaturalPerson.workerCountry) &&
        Objects.equals(this.naturalPersonUserReference, wech502NaturalPerson.naturalPersonUserReference) &&
        Objects.equals(this.handledNaturalPerson, wech502NaturalPerson.handledNaturalPerson) &&
        Objects.equals(this.workerRecordLink, wech502NaturalPerson.workerRecordLink);
  }

  @Override
  public int hashCode() {
    return Objects.hash(naturalPersonSequenceNbr, inss, workerStreet, workerHouseNbr, workerPostBox, workerZIPCode, workerCity, workerCountry, naturalPersonUserReference, handledNaturalPerson, workerRecordLink);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502NaturalPerson {\n");
    sb.append("    naturalPersonSequenceNbr: ").append(toIndentedString(naturalPersonSequenceNbr)).append("\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("    workerStreet: ").append(toIndentedString(workerStreet)).append("\n");
    sb.append("    workerHouseNbr: ").append(toIndentedString(workerHouseNbr)).append("\n");
    sb.append("    workerPostBox: ").append(toIndentedString(workerPostBox)).append("\n");
    sb.append("    workerZIPCode: ").append(toIndentedString(workerZIPCode)).append("\n");
    sb.append("    workerCity: ").append(toIndentedString(workerCity)).append("\n");
    sb.append("    workerCountry: ").append(toIndentedString(workerCountry)).append("\n");
    sb.append("    naturalPersonUserReference: ").append(toIndentedString(naturalPersonUserReference)).append("\n");
    sb.append("    handledNaturalPerson: ").append(toIndentedString(handledNaturalPerson)).append("\n");
    sb.append("    workerRecordLink: ").append(toIndentedString(workerRecordLink)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

