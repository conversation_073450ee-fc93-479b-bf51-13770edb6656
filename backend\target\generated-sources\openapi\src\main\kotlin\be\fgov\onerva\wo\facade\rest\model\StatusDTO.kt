/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.StateDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param task 
 * @param process 
 * @param taskStep 
 */


data class StatusDTO (

    @get:JsonProperty("task")
    val task: StateDTO,

    @get:JsonProperty("process")
    val process: StateDTO,

    @get:JsonProperty("taskStep")
    val taskStep: kotlin.String? = null

) {


}

