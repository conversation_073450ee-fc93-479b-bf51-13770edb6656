import {
  MAT_SNACK_BAR_DATA,
  MatSnackBarModule
} from "./chunk-WCD4BJ6A.js";
import "./chunk-2LUJNGSZ.js";
import "./chunk-VP7OCDXQ.js";
import "./chunk-W456NIV3.js";
import "./chunk-SY6O6U7L.js";
import "./chunk-C3EWM6A7.js";
import "./chunk-UPOW3STX.js";
import "./chunk-IE44L42K.js";
import {
  NgClass
} from "./chunk-QNFKXUK7.js";
import {
  Component,
  Inject,
  NgModule,
  setClassMetadata,
  ɵɵadvance,
  ɵɵattribute,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵproperty,
  ɵɵtext,
  ɵɵtextInterpolate1
} from "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-toast.mjs
var ToastType;
(function(ToastType2) {
  ToastType2["info"] = "info";
  ToastType2["success"] = "success";
  ToastType2["warn"] = "warn";
  ToastType2["error"] = "error";
  ToastType2["accent"] = "accent";
  ToastType2["primary"] = "primary";
})(ToastType || (ToastType = {}));
var OnemrvaMatToastComponent = class _OnemrvaMatToastComponent {
  constructor(data) {
    this.data = data;
    this.dataCy = data.dataCy ? data.dataCy : "onemrva-mat-toast";
  }
  static {
    this.ɵfac = function OnemrvaMatToastComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatToastComponent)(ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatToastComponent,
      selectors: [["onemrva-mat-toast"]],
      decls: 2,
      vars: 3,
      consts: [["role", "alert", "aria-atomic", "true", 1, "onemrva-mat-toast", 3, "ngClass"]],
      template: function OnemrvaMatToastComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "div", 0);
          ɵɵtext(1);
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("ngClass", ctx.data.type);
          ɵɵattribute("data-cy", ctx.dataCy);
          ɵɵadvance();
          ɵɵtextInterpolate1(" ", ctx.data.message, " ");
        }
      },
      dependencies: [NgClass],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatToastComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-toast",
      template: `
    <div
      class="onemrva-mat-toast"
      [ngClass]="data.type"
      [attr.data-cy]="dataCy"
      role="alert"
      aria-atomic="true"
    >
      {{ data.message }}
    </div>
  `,
      standalone: true,
      imports: [NgClass]
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Inject,
      args: [MAT_SNACK_BAR_DATA]
    }]
  }], null);
})();
var OnemrvaMatToastModule = class _OnemrvaMatToastModule {
  static {
    this.ɵfac = function OnemrvaMatToastModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatToastModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatToastModule,
      imports: [OnemrvaMatToastComponent],
      exports: [OnemrvaMatToastComponent, MatSnackBarModule]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [MatSnackBarModule]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatToastModule, [{
    type: NgModule,
    args: [{
      imports: [OnemrvaMatToastComponent],
      exports: [OnemrvaMatToastComponent, MatSnackBarModule]
    }]
  }], null, null);
})();
export {
  OnemrvaMatToastComponent,
  OnemrvaMatToastModule,
  ToastType
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-toast.js.map
