{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system-theme/fesm2022/onemrvapublic-design-system-theme.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { NgModule } from '@angular/core';\nimport { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';\nclass OnemrvaThemeModule {\n  static {\n    this.ɵfac = function OnemrvaThemeModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaThemeModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaThemeModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [{\n        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,\n        useValue: {\n          appearance: 'outline',\n          floatLabel: 'always'\n        }\n      }]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaThemeModule, [{\n    type: NgModule,\n    args: [{\n      providers: [{\n        provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,\n        useValue: {\n          appearance: 'outline',\n          floatLabel: 'always'\n        }\n      }],\n      imports: []\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of theme\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaThemeModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAGA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,YAAY;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,WAAW,CAAC;AAAA,QACV,SAAS;AAAA,QACT,UAAU;AAAA,UACR,YAAY;AAAA,UACZ,YAAY;AAAA,QACd;AAAA,MACF,CAAC;AAAA,MACD,SAAS,CAAC;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}