/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.AnnexType

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Represents an attachment of the C9 and its display url
 *
 * @param type 
 * @param url 
 */


data class Annex (

    @get:JsonProperty("type")
    val type: AnnexType? = null,

    @get:JsonProperty("url")
    val url: kotlin.String? = null

) {


}

