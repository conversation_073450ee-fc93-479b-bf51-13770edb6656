package be.fgov.onerva.cu.common.aop

@Target(AnnotationTarget.FUNCTION, AnnotationTarget.PROPERTY_GETTER, AnnotationTarget.PROPERTY_SETTER)
@Retention(
    AnnotationRetention.RUNTIME
)
annotation class LogMethodCall

@Target(AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
annotation class SensitiveParam

/**
 * Annotation to mark methods that should log the request ID.
 */
@Target(
    AnnotationTarget.FUNCTION,
    AnnotationTarget.PROPERTY_GETTER,
    AnnotationTarget.PROPERTY_SETTER
) // Applicable to methods only
@Retention(AnnotationRetention.RUNTIME) // Available at runtime
annotation class LogWithRequestId
