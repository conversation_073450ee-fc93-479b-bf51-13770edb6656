import {
  OnemrvaMatColor
} from "./chunk-X7GZ3B26.js";
import {
  Component,
  HostBinding,
  Input,
  NgModule,
  setClassMetadata,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵprojection,
  ɵɵprojectionDef
} from "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-sticker.mjs
var _c0 = ["*"];
var OnemrvaMatStickerComponent = class _OnemrvaMatStickerComponent {
  constructor() {
    this.color = OnemrvaMatColor.PRIMARY;
  }
  /** @hidden @internal */
  get _isPrimary() {
    return this.color === OnemrvaMatColor.PRIMARY;
  }
  /** @hidden @internal */
  get _isAccent() {
    return this.color === OnemrvaMatColor.ACCENT;
  }
  /** @hidden @internal */
  get _isError() {
    return this.color === OnemrvaMatColor.ERROR;
  }
  /** @hidden @internal */
  get _isWarn() {
    return this.color === OnemrvaMatColor.WARN;
  }
  /** @hidden @internal */
  get _isSuccess() {
    return this.color === OnemrvaMatColor.SUCCESS;
  }
  /** @hidden @internal */
  get _isInfo() {
    return this.color === OnemrvaMatColor.INFO;
  }
  /** @hidden @internal */
  get _isGrayscale() {
    return this.color === OnemrvaMatColor.GRAYSCALE;
  }
  static {
    this.ɵfac = function OnemrvaMatStickerComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatStickerComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatStickerComponent,
      selectors: [["onemrva-mat-sticker"]],
      hostVars: 14,
      hostBindings: function OnemrvaMatStickerComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("mat-primary", ctx._isPrimary)("mat-accent", ctx._isAccent)("mat-error", ctx._isError)("mat-warn", ctx._isWarn)("mat-success", ctx._isSuccess)("mat-info", ctx._isInfo)("mat-grayscale", ctx._isGrayscale);
        }
      },
      inputs: {
        color: "color"
      },
      ngContentSelectors: _c0,
      decls: 1,
      vars: 0,
      template: function OnemrvaMatStickerComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵprojection(0);
        }
      },
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatStickerComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-sticker",
      template: ` <ng-content></ng-content>`,
      standalone: true
    }]
  }], null, {
    color: [{
      type: Input
    }],
    _isPrimary: [{
      type: HostBinding,
      args: ["class.mat-primary"]
    }],
    _isAccent: [{
      type: HostBinding,
      args: ["class.mat-accent"]
    }],
    _isError: [{
      type: HostBinding,
      args: ["class.mat-error"]
    }],
    _isWarn: [{
      type: HostBinding,
      args: ["class.mat-warn"]
    }],
    _isSuccess: [{
      type: HostBinding,
      args: ["class.mat-success"]
    }],
    _isInfo: [{
      type: HostBinding,
      args: ["class.mat-info"]
    }],
    _isGrayscale: [{
      type: HostBinding,
      args: ["class.mat-grayscale"]
    }]
  });
})();
var OnemrvaMatStickerModule = class _OnemrvaMatStickerModule {
  static {
    this.ɵfac = function OnemrvaMatStickerModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatStickerModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatStickerModule,
      imports: [OnemrvaMatStickerComponent],
      exports: [OnemrvaMatStickerComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatStickerModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OnemrvaMatStickerComponent],
      exports: [OnemrvaMatStickerComponent]
    }]
  }], null, null);
})();
export {
  OnemrvaMatStickerComponent,
  OnemrvaMatStickerModule
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-sticker.js.map
