/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.BelgianCommunity;
import be.fgov.onerva.unemployment.c9.rest.model.Language;
import be.fgov.onerva.unemployment.c9.rest.model.NationalityBCSS;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1Identity
 */
@JsonPropertyOrder({
  EC1Identity.JSON_PROPERTY_INSS,
  EC1Identity.JSON_PROPERTY_LAST_NAME,
  EC1Identity.JSON_PROPERTY_FIRST_NAME,
  EC1Identity.JSON_PROPERTY_STREET,
  EC1Identity.JSON_PROPERTY_HOUSE_NUMBER,
  EC1Identity.JSON_PROPERTY_BOX_NUMBER,
  EC1Identity.JSON_PROPERTY_ZIP_CODE,
  EC1Identity.JSON_PROPERTY_CITY,
  EC1Identity.JSON_PROPERTY_COUNTRY,
  EC1Identity.JSON_PROPERTY_DATE_OF_BIRTH,
  EC1Identity.JSON_PROPERTY_NATIONALITY,
  EC1Identity.JSON_PROPERTY_LANGUAGE,
  EC1Identity.JSON_PROPERTY_PHONE_NUMBER,
  EC1Identity.JSON_PROPERTY_EMAIL
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1Identity {
  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_STREET = "street";
  private String street;

  public static final String JSON_PROPERTY_HOUSE_NUMBER = "houseNumber";
  private String houseNumber;

  public static final String JSON_PROPERTY_BOX_NUMBER = "boxNumber";
  private String boxNumber;

  public static final String JSON_PROPERTY_ZIP_CODE = "zipCode";
  private BelgianCommunity zipCode;

  public static final String JSON_PROPERTY_CITY = "city";
  private String city;

  public static final String JSON_PROPERTY_COUNTRY = "country";
  private NationalityBCSS country;

  public static final String JSON_PROPERTY_DATE_OF_BIRTH = "dateOfBirth";
  private String dateOfBirth;

  public static final String JSON_PROPERTY_NATIONALITY = "nationality";
  private NationalityBCSS nationality;

  public static final String JSON_PROPERTY_LANGUAGE = "language";
  private Language language;

  public static final String JSON_PROPERTY_PHONE_NUMBER = "phoneNumber";
  private String phoneNumber;

  public static final String JSON_PROPERTY_EMAIL = "email";
  private String email;

  public EC1Identity() {
  }

  public EC1Identity inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  public EC1Identity lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public EC1Identity firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public EC1Identity street(String street) {
    
    this.street = street;
    return this;
  }

  /**
   * Get street
   * @return street
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStreet() {
    return street;
  }


  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStreet(String street) {
    this.street = street;
  }

  public EC1Identity houseNumber(String houseNumber) {
    
    this.houseNumber = houseNumber;
    return this;
  }

  /**
   * Get houseNumber
   * @return houseNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOUSE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHouseNumber() {
    return houseNumber;
  }


  @JsonProperty(JSON_PROPERTY_HOUSE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHouseNumber(String houseNumber) {
    this.houseNumber = houseNumber;
  }

  public EC1Identity boxNumber(String boxNumber) {
    
    this.boxNumber = boxNumber;
    return this;
  }

  /**
   * Get boxNumber
   * @return boxNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BOX_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBoxNumber() {
    return boxNumber;
  }


  @JsonProperty(JSON_PROPERTY_BOX_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBoxNumber(String boxNumber) {
    this.boxNumber = boxNumber;
  }

  public EC1Identity zipCode(BelgianCommunity zipCode) {
    
    this.zipCode = zipCode;
    return this;
  }

  /**
   * Get zipCode
   * @return zipCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BelgianCommunity getZipCode() {
    return zipCode;
  }


  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZipCode(BelgianCommunity zipCode) {
    this.zipCode = zipCode;
  }

  public EC1Identity city(String city) {
    
    this.city = city;
    return this;
  }

  /**
   * Get city
   * @return city
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCity() {
    return city;
  }


  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCity(String city) {
    this.city = city;
  }

  public EC1Identity country(NationalityBCSS country) {
    
    this.country = country;
    return this;
  }

  /**
   * Get country
   * @return country
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public NationalityBCSS getCountry() {
    return country;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountry(NationalityBCSS country) {
    this.country = country;
  }

  public EC1Identity dateOfBirth(String dateOfBirth) {
    
    this.dateOfBirth = dateOfBirth;
    return this;
  }

  /**
   * Get dateOfBirth
   * @return dateOfBirth
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_OF_BIRTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDateOfBirth() {
    return dateOfBirth;
  }


  @JsonProperty(JSON_PROPERTY_DATE_OF_BIRTH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateOfBirth(String dateOfBirth) {
    this.dateOfBirth = dateOfBirth;
  }

  public EC1Identity nationality(NationalityBCSS nationality) {
    
    this.nationality = nationality;
    return this;
  }

  /**
   * Get nationality
   * @return nationality
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public NationalityBCSS getNationality() {
    return nationality;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationality(NationalityBCSS nationality) {
    this.nationality = nationality;
  }

  public EC1Identity language(Language language) {
    
    this.language = language;
    return this;
  }

  /**
   * Get language
   * @return language
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Language getLanguage() {
    return language;
  }


  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLanguage(Language language) {
    this.language = language;
  }

  public EC1Identity phoneNumber(String phoneNumber) {
    
    this.phoneNumber = phoneNumber;
    return this;
  }

  /**
   * Get phoneNumber
   * @return phoneNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhoneNumber() {
    return phoneNumber;
  }


  @JsonProperty(JSON_PROPERTY_PHONE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhoneNumber(String phoneNumber) {
    this.phoneNumber = phoneNumber;
  }

  public EC1Identity email(String email) {
    
    this.email = email;
    return this;
  }

  /**
   * Get email
   * @return email
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmail() {
    return email;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmail(String email) {
    this.email = email;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1Identity ec1Identity = (EC1Identity) o;
    return Objects.equals(this.inss, ec1Identity.inss) &&
        Objects.equals(this.lastName, ec1Identity.lastName) &&
        Objects.equals(this.firstName, ec1Identity.firstName) &&
        Objects.equals(this.street, ec1Identity.street) &&
        Objects.equals(this.houseNumber, ec1Identity.houseNumber) &&
        Objects.equals(this.boxNumber, ec1Identity.boxNumber) &&
        Objects.equals(this.zipCode, ec1Identity.zipCode) &&
        Objects.equals(this.city, ec1Identity.city) &&
        Objects.equals(this.country, ec1Identity.country) &&
        Objects.equals(this.dateOfBirth, ec1Identity.dateOfBirth) &&
        Objects.equals(this.nationality, ec1Identity.nationality) &&
        Objects.equals(this.language, ec1Identity.language) &&
        Objects.equals(this.phoneNumber, ec1Identity.phoneNumber) &&
        Objects.equals(this.email, ec1Identity.email);
  }

  @Override
  public int hashCode() {
    return Objects.hash(inss, lastName, firstName, street, houseNumber, boxNumber, zipCode, city, country, dateOfBirth, nationality, language, phoneNumber, email);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1Identity {\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    houseNumber: ").append(toIndentedString(houseNumber)).append("\n");
    sb.append("    boxNumber: ").append(toIndentedString(boxNumber)).append("\n");
    sb.append("    zipCode: ").append(toIndentedString(zipCode)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    country: ").append(toIndentedString(country)).append("\n");
    sb.append("    dateOfBirth: ").append(toIndentedString(dateOfBirth)).append("\n");
    sb.append("    nationality: ").append(toIndentedString(nationality)).append("\n");
    sb.append("    language: ").append(toIndentedString(language)).append("\n");
    sb.append("    phoneNumber: ").append(toIndentedString(phoneNumber)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

