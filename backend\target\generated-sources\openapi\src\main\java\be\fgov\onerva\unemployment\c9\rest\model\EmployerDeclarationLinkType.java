/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EmployerDeclarationLinkType
 */
@JsonPropertyOrder({
  EmployerDeclarationLinkType.JSON_PROPERTY_NOSS_REGISTRATION_NBR,
  EmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP,
  EmployerDeclarationLinkType.JSON_PROPERTY_COMPANY_I_D
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EmployerDeclarationLinkType {
  public static final String JSON_PROPERTY_NOSS_REGISTRATION_NBR = "nossRegistrationNbr";
  private Integer nossRegistrationNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP = "trusteeship";
  private String trusteeship;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public EmployerDeclarationLinkType() {
  }

  public EmployerDeclarationLinkType nossRegistrationNbr(Integer nossRegistrationNbr) {
    
    this.nossRegistrationNbr = nossRegistrationNbr;
    return this;
  }

  /**
   * Get nossRegistrationNbr
   * @return nossRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNossRegistrationNbr() {
    return nossRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNossRegistrationNbr(Integer nossRegistrationNbr) {
    this.nossRegistrationNbr = nossRegistrationNbr;
  }

  public EmployerDeclarationLinkType trusteeship(String trusteeship) {
    
    this.trusteeship = trusteeship;
    return this;
  }

  /**
   * Get trusteeship
   * @return trusteeship
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTrusteeship() {
    return trusteeship;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTrusteeship(String trusteeship) {
    this.trusteeship = trusteeship;
  }

  public EmployerDeclarationLinkType companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EmployerDeclarationLinkType employerDeclarationLinkType = (EmployerDeclarationLinkType) o;
    return Objects.equals(this.nossRegistrationNbr, employerDeclarationLinkType.nossRegistrationNbr) &&
        Objects.equals(this.trusteeship, employerDeclarationLinkType.trusteeship) &&
        Objects.equals(this.companyID, employerDeclarationLinkType.companyID);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nossRegistrationNbr, trusteeship, companyID);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EmployerDeclarationLinkType {\n");
    sb.append("    nossRegistrationNbr: ").append(toIndentedString(nossRegistrationNbr)).append("\n");
    sb.append("    trusteeship: ").append(toIndentedString(trusteeship)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

