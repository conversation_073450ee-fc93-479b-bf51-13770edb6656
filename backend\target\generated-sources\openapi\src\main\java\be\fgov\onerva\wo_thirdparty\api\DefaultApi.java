package be.fgov.onerva.wo_thirdparty.api;

import be.fgov.onerva.wo_thirdparty.invoker.ApiClient;
import be.fgov.onerva.wo_thirdparty.invoker.BaseApi;

import be.fgov.onerva.wo_thirdparty.rest.model.BusinessKey;
import be.fgov.onerva.wo_thirdparty.rest.model.BusinessKeyTypes;
import be.fgov.onerva.wo_thirdparty.rest.model.Party;
import be.fgov.onerva.wo_thirdparty.rest.model.PatchEntry;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class DefaultApi extends BaseApi {

    public DefaultApi() {
        super(new ApiClient());
    }

    public DefaultApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * Add a new business key to a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to add a key (required)
     * @param businessKey The key information (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void addBusinessKey(String tpmsId, BusinessKey businessKey, String authorization) throws RestClientException {
        addBusinessKeyWithHttpInfo(tpmsId, businessKey, authorization);
    }

    /**
     * Add a new business key to a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to add a key (required)
     * @param businessKey The key information (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> addBusinessKeyWithHttpInfo(String tpmsId, BusinessKey businessKey, String authorization) throws RestClientException {
        Object localVarPostBody = businessKey;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling addBusinessKey");
        }
        
        // verify the required parameter 'businessKey' is set
        if (businessKey == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'businessKey' when calling addBusinessKey");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "oauth_accessCode", "oauth_implicit" };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/businessKeys", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Create a new party
     * 
     * <p><b>201</b> - Entity created successfully
     * <p><b>400</b> - Malformed message
     * <p><b>409</b> - The entity already exists for the given business key/denomination or some fields are missing in order to create the entity
     * @param party The party to create (required)
     * @param authorization  (optional)
     * @return Party
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Party createParty(Party party, String authorization) throws RestClientException {
        return createPartyWithHttpInfo(party, authorization).getBody();
    }

    /**
     * Create a new party
     * 
     * <p><b>201</b> - Entity created successfully
     * <p><b>400</b> - Malformed message
     * <p><b>409</b> - The entity already exists for the given business key/denomination or some fields are missing in order to create the entity
     * @param party The party to create (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Party&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Party> createPartyWithHttpInfo(Party party, String authorization) throws RestClientException {
        Object localVarPostBody = party;
        
        // verify the required parameter 'party' is set
        if (party == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'party' when calling createParty");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Party> localReturnType = new ParameterizedTypeReference<Party>() {};
        return apiClient.invokeAPI("/parties", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Delete a business key of a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to delete a key (required)
     * @param type The type of the key we want to delete (required)
     * @param value The value of the key we want to delete (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void deleteBusinessKey(String tpmsId, String type, String value, String authorization) throws RestClientException {
        deleteBusinessKeyWithHttpInfo(tpmsId, type, value, authorization);
    }

    /**
     * Delete a business key of a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to delete a key (required)
     * @param type The type of the key we want to delete (required)
     * @param value The value of the key we want to delete (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> deleteBusinessKeyWithHttpInfo(String tpmsId, String type, String value, String authorization) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling deleteBusinessKey");
        }
        
        // verify the required parameter 'type' is set
        if (type == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'type' when calling deleteBusinessKey");
        }
        
        // verify the required parameter 'value' is set
        if (value == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'value' when calling deleteBusinessKey");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);
        uriVariables.put("type", type);
        uriVariables.put("value", value);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "oauth_accessCode", "oauth_implicit" };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/businessKeys/{type}:{value}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Getting the key types that can be handled with the businessKeys method
     * 
     * <p><b>200</b> - OK
     * @return BusinessKeyTypes
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public BusinessKeyTypes getBusinessKeyTypes() throws RestClientException {
        return getBusinessKeyTypesWithHttpInfo().getBody();
    }

    /**
     * Getting the key types that can be handled with the businessKeys method
     * 
     * <p><b>200</b> - OK
     * @return ResponseEntity&lt;BusinessKeyTypes&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<BusinessKeyTypes> getBusinessKeyTypesWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "oauth_accessCode", "oauth_implicit" };

        ParameterizedTypeReference<BusinessKeyTypes> localReturnType = new ParameterizedTypeReference<BusinessKeyTypes>() {};
        return apiClient.invokeAPI("/refData/businessKeyTypes", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Get all business keys of the entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to get the keys (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void getBusinessKeys(String tpmsId, String authorization) throws RestClientException {
        getBusinessKeysWithHttpInfo(tpmsId, authorization);
    }

    /**
     * Get all business keys of the entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to get the keys (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> getBusinessKeysWithHttpInfo(String tpmsId, String authorization) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling getBusinessKeys");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "oauth_accessCode", "oauth_implicit" };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/businessKeys", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Get the history of one tpms id
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param tpmsId ID of the party to merge (required)
     * @param relatedParties Indicate that the history has to be retrieved (required)
     * @param lang Lang (optional)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void getHistoryTpmsId(String tpmsId, String relatedParties, String lang, String authorization) throws RestClientException {
        getHistoryTpmsIdWithHttpInfo(tpmsId, relatedParties, lang, authorization);
    }

    /**
     * Get the history of one tpms id
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param tpmsId ID of the party to merge (required)
     * @param relatedParties Indicate that the history has to be retrieved (required)
     * @param lang Lang (optional)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> getHistoryTpmsIdWithHttpInfo(String tpmsId, String relatedParties, String lang, String authorization) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling getHistoryTpmsId");
        }
        
        // verify the required parameter 'relatedParties' is set
        if (relatedParties == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'relatedParties' when calling getHistoryTpmsId");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);
        uriVariables.put("relatedParties", relatedParties);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "lang", lang));
        

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/{relatedParties}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Merge a party with an other one
     * 
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * @param tpmsId ID of the party to merge (required)
     * @param body ID of the party to replace (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void mergeEntity(String tpmsId, String body, String authorization) throws RestClientException {
        mergeEntityWithHttpInfo(tpmsId, body, authorization);
    }

    /**
     * Merge a party with an other one
     * 
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * @param tpmsId ID of the party to merge (required)
     * @param body ID of the party to replace (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> mergeEntityWithHttpInfo(String tpmsId, String body, String authorization) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling mergeEntity");
        }
        
        // verify the required parameter 'body' is set
        if (body == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'body' when calling mergeEntity");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/mergeParties", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Edit a business key of a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to modify a key (required)
     * @param type The type of the key we want to modify (required)
     * @param value The value of the key we want to modify (required)
     * @param businessKey The key information (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void modifyBusinessKey(String tpmsId, String type, String value, BusinessKey businessKey, String authorization) throws RestClientException {
        modifyBusinessKeyWithHttpInfo(tpmsId, type, value, businessKey, authorization);
    }

    /**
     * Edit a business key of a given entity
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - No party found with the given identifier
     * <p><b>409</b> - Conflict
     * <p><b>412</b> - Precondition Failed
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param tpmsId The party identifier for which one wants to modify a key (required)
     * @param type The type of the key we want to modify (required)
     * @param value The value of the key we want to modify (required)
     * @param businessKey The key information (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> modifyBusinessKeyWithHttpInfo(String tpmsId, String type, String value, BusinessKey businessKey, String authorization) throws RestClientException {
        Object localVarPostBody = businessKey;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling modifyBusinessKey");
        }
        
        // verify the required parameter 'type' is set
        if (type == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'type' when calling modifyBusinessKey");
        }
        
        // verify the required parameter 'value' is set
        if (value == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'value' when calling modifyBusinessKey");
        }
        
        // verify the required parameter 'businessKey' is set
        if (businessKey == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'businessKey' when calling modifyBusinessKey");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);
        uriVariables.put("type", type);
        uriVariables.put("value", value);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "oauth_accessCode", "oauth_implicit" };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/businessKeys/{type}:{value}", HttpMethod.PATCH, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Move a party keys to an other party
     * 
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * @param tpmsId ID of the party to update (required)
     * @param body ID of the party to delete (required)
     * @param authorization  (optional)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void moveParty(String tpmsId, String body, String authorization) throws RestClientException {
        movePartyWithHttpInfo(tpmsId, body, authorization);
    }

    /**
     * Move a party keys to an other party
     * 
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * @param tpmsId ID of the party to update (required)
     * @param body ID of the party to delete (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> movePartyWithHttpInfo(String tpmsId, String body, String authorization) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling moveParty");
        }
        
        // verify the required parameter 'body' is set
        if (body == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'body' when calling moveParty");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}/movePartyKeys", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Update an entity property
     * 
     * <p><b>200</b> - Entity successfully updated
     * <p><b>204</b> - Nsso entity not found
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Internal server error
     * @param tpmsId ID of the entity to retrieve (required)
     * @param patchEntry The property to update (required)
     * @param authorization  (optional)
     * @return Party
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Party updateEntityProperty(String tpmsId, PatchEntry patchEntry, String authorization) throws RestClientException {
        return updateEntityPropertyWithHttpInfo(tpmsId, patchEntry, authorization).getBody();
    }

    /**
     * Update an entity property
     * 
     * <p><b>200</b> - Entity successfully updated
     * <p><b>204</b> - Nsso entity not found
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Internal server error
     * @param tpmsId ID of the entity to retrieve (required)
     * @param patchEntry The property to update (required)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Party&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Party> updateEntityPropertyWithHttpInfo(String tpmsId, PatchEntry patchEntry, String authorization) throws RestClientException {
        Object localVarPostBody = patchEntry;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling updateEntityProperty");
        }
        
        // verify the required parameter 'patchEntry' is set
        if (patchEntry == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'patchEntry' when calling updateEntityProperty");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Party> localReturnType = new ParameterizedTypeReference<Party>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}", HttpMethod.PATCH, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
