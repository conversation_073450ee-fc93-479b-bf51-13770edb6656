/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1Various
 */
@JsonPropertyOrder({
  EC1Various.JSON_PROPERTY_VACATION_WITHOUT_SALARY,
  EC1Various.JSON_PROPERTY_VACATION_START_DATE,
  EC1Various.JSON_PROPERTY_VACATION_END_DATE,
  EC1Various.JSON_PROPERTY_UNABLE_TO_WORK33_PERCENT
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1Various {
  public static final String JSON_PROPERTY_VACATION_WITHOUT_SALARY = "vacationWithoutSalary";
  private Boolean vacationWithoutSalary;

  public static final String JSON_PROPERTY_VACATION_START_DATE = "vacationStartDate";
  private LocalDate vacationStartDate;

  public static final String JSON_PROPERTY_VACATION_END_DATE = "vacationEndDate";
  private LocalDate vacationEndDate;

  public static final String JSON_PROPERTY_UNABLE_TO_WORK33_PERCENT = "unableToWork33Percent";
  private Boolean unableToWork33Percent;

  public EC1Various() {
  }

  public EC1Various vacationWithoutSalary(Boolean vacationWithoutSalary) {
    
    this.vacationWithoutSalary = vacationWithoutSalary;
    return this;
  }

  /**
   * Get vacationWithoutSalary
   * @return vacationWithoutSalary
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VACATION_WITHOUT_SALARY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVacationWithoutSalary() {
    return vacationWithoutSalary;
  }


  @JsonProperty(JSON_PROPERTY_VACATION_WITHOUT_SALARY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVacationWithoutSalary(Boolean vacationWithoutSalary) {
    this.vacationWithoutSalary = vacationWithoutSalary;
  }

  public EC1Various vacationStartDate(LocalDate vacationStartDate) {
    
    this.vacationStartDate = vacationStartDate;
    return this;
  }

  /**
   * Get vacationStartDate
   * @return vacationStartDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VACATION_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getVacationStartDate() {
    return vacationStartDate;
  }


  @JsonProperty(JSON_PROPERTY_VACATION_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVacationStartDate(LocalDate vacationStartDate) {
    this.vacationStartDate = vacationStartDate;
  }

  public EC1Various vacationEndDate(LocalDate vacationEndDate) {
    
    this.vacationEndDate = vacationEndDate;
    return this;
  }

  /**
   * Get vacationEndDate
   * @return vacationEndDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VACATION_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getVacationEndDate() {
    return vacationEndDate;
  }


  @JsonProperty(JSON_PROPERTY_VACATION_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVacationEndDate(LocalDate vacationEndDate) {
    this.vacationEndDate = vacationEndDate;
  }

  public EC1Various unableToWork33Percent(Boolean unableToWork33Percent) {
    
    this.unableToWork33Percent = unableToWork33Percent;
    return this;
  }

  /**
   * Get unableToWork33Percent
   * @return unableToWork33Percent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNABLE_TO_WORK33_PERCENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUnableToWork33Percent() {
    return unableToWork33Percent;
  }


  @JsonProperty(JSON_PROPERTY_UNABLE_TO_WORK33_PERCENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnableToWork33Percent(Boolean unableToWork33Percent) {
    this.unableToWork33Percent = unableToWork33Percent;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1Various ec1Various = (EC1Various) o;
    return Objects.equals(this.vacationWithoutSalary, ec1Various.vacationWithoutSalary) &&
        Objects.equals(this.vacationStartDate, ec1Various.vacationStartDate) &&
        Objects.equals(this.vacationEndDate, ec1Various.vacationEndDate) &&
        Objects.equals(this.unableToWork33Percent, ec1Various.unableToWork33Percent);
  }

  @Override
  public int hashCode() {
    return Objects.hash(vacationWithoutSalary, vacationStartDate, vacationEndDate, unableToWork33Percent);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1Various {\n");
    sb.append("    vacationWithoutSalary: ").append(toIndentedString(vacationWithoutSalary)).append("\n");
    sb.append("    vacationStartDate: ").append(toIndentedString(vacationStartDate)).append("\n");
    sb.append("    vacationEndDate: ").append(toIndentedString(vacationEndDate)).append("\n");
    sb.append("    unableToWork33Percent: ").append(toIndentedString(unableToWork33Percent)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

