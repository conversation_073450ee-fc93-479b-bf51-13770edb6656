/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo.organizational.chart.rest.model.ComponentStatus;
import be.fgov.onerva.wo.organizational.chart.rest.model.HealthLevel;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Response message for the API health
 */
@JsonPropertyOrder({
  HealthStatus.JSON_PROPERTY_STATUS,
  HealthStatus.JSON_PROPERTY_DETAILS,
  HealthStatus.JSON_PROPERTY_ADDITIONAL_PROPERTIES
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HealthStatus {
  public static final String JSON_PROPERTY_STATUS = "status";
  private HealthLevel status;

  public static final String JSON_PROPERTY_DETAILS = "details";
  private Object details;

  public static final String JSON_PROPERTY_ADDITIONAL_PROPERTIES = "additionalProperties";
  private ComponentStatus additionalProperties;

  public HealthStatus() {
  }

  public HealthStatus status(HealthLevel status) {
    
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HealthLevel getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setStatus(HealthLevel status) {
    this.status = status;
  }

  public HealthStatus details(Object details) {
    
    this.details = details;
    return this;
  }

  /**
   * Get details
   * @return details
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getDetails() {
    return details;
  }


  @JsonProperty(JSON_PROPERTY_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDetails(Object details) {
    this.details = details;
  }

  public HealthStatus additionalProperties(ComponentStatus additionalProperties) {
    
    this.additionalProperties = additionalProperties;
    return this;
  }

  /**
   * Get additionalProperties
   * @return additionalProperties
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDITIONAL_PROPERTIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ComponentStatus getAdditionalProperties() {
    return additionalProperties;
  }


  @JsonProperty(JSON_PROPERTY_ADDITIONAL_PROPERTIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAdditionalProperties(ComponentStatus additionalProperties) {
    this.additionalProperties = additionalProperties;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HealthStatus healthStatus = (HealthStatus) o;
    return Objects.equals(this.status, healthStatus.status) &&
        Objects.equals(this.details, healthStatus.details) &&
        Objects.equals(this.additionalProperties, healthStatus.additionalProperties);
  }

  @Override
  public int hashCode() {
    return Objects.hash(status, details, additionalProperties);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HealthStatus {\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    details: ").append(toIndentedString(details)).append("\n");
    sb.append("    additionalProperties: ").append(toIndentedString(additionalProperties)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

