/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { CitizenInformationDetailNullableResponse } from './citizenInformationDetailNullableResponse';
import { HistoricalBaremaResponse } from './historicalBaremaResponse';
import { HistoricalCitizenC1Response } from './historicalCitizenC1Response';
import { HistoricalCitizenOnemResponse } from './historicalCitizenOnemResponse';
import { UnionContributionDetailResponse } from './unionContributionDetailResponse';
import { HistoricalCitizenAuthenticSourcesResponse } from './historicalCitizenAuthenticSourcesResponse';
import { ModeOfPaymentDetailResponse } from './modeOfPaymentDetailResponse';
import { RequestBasicInfoResponse } from './requestBasicInfoResponse';


export interface AggregatedChangePersonalDataValidateResponse { 
    requestId?: string;
    basicInfo?: RequestBasicInfoResponse;
    citizenInformation?: CitizenInformationDetailNullableResponse;
    modeOfPayment?: ModeOfPaymentDetailResponse;
    unionContribution?: UnionContributionDetailResponse;
    onemCitizenInformation?: HistoricalCitizenOnemResponse;
    c1CitizenInformation?: HistoricalCitizenC1Response;
    authenticCitizenInformation?: HistoricalCitizenAuthenticSourcesResponse;
    barema?: HistoricalBaremaResponse;
}

