import {
  DEFAULT_LANGUAGE,
  FakeMissingTranslationHandler,
  ISOLATE_TRANSLATE_SERVICE,
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateDefaultParser,
  TranslateDirective,
  TranslateFakeCompiler,
  TranslateFakeLoader,
  TranslateLoader,
  TranslateModule,
  TranslateParser,
  TranslatePipe,
  TranslateService,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND,
  _,
  equals,
  getValue,
  isArray,
  isDefined,
  isDict,
  isFunction,
  isObject,
  isString,
  mergeDeep,
  provideTranslateService,
  setValue
} from "./chunk-VKFZTD5H.js";
import "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";
export {
  DEFAULT_LANGUAGE,
  FakeMissingTranslationHand<PERSON>,
  ISOLATE_TRANSLATE_SERVICE,
  MissingTranslationHandler,
  TranslateCompiler,
  TranslateDefaultParser,
  TranslateDirective,
  TranslateFakeCompiler,
  TranslateFakeLoader,
  TranslateLoader,
  TranslateModule,
  TranslateParser,
  TranslatePipe,
  TranslateService,
  TranslateStore,
  USE_DEFAULT_LANG,
  USE_EXTEND,
  _,
  equals,
  getValue,
  isArray,
  isDefined,
  isDict,
  isFunction,
  isObject,
  isString,
  mergeDeep,
  provideTranslateService,
  setValue
};
