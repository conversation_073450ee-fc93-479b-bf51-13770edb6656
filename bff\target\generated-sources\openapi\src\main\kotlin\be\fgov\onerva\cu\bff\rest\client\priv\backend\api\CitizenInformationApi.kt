/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class CitizenInformationApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun getCitizenInformation(requestId: java.util.UUID): CitizenInformationDetailResponse {
        val result = getCitizenInformationWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getCitizenInformationWithHttpInfo(requestId: java.util.UUID): ResponseEntity<CitizenInformationDetailResponse> {
        val localVariableConfig = getCitizenInformationRequestConfig(requestId = requestId)
        return request<Unit, CitizenInformationDetailResponse>(
            localVariableConfig
        )
    }

    fun getCitizenInformationRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/citizen-information",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun selectCitizenInformationSources(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): Unit {
        selectCitizenInformationSourcesWithHttpInfo(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
    }

    @Throws(RestClientResponseException::class)
    fun selectCitizenInformationSourcesWithHttpInfo(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): ResponseEntity<Unit> {
        val localVariableConfig = selectCitizenInformationSourcesRequestConfig(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
        return request<SelectFieldSourcesRequest, Unit>(
            localVariableConfig
        )
    }

    fun selectCitizenInformationSourcesRequestConfig(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest) : RequestConfig<SelectFieldSourcesRequest> {
        val localVariableBody = selectFieldSourcesRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/citizen-information/select",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateCitizenInformation(requestId: java.util.UUID, updateCitizenInformationRequest: UpdateCitizenInformationRequest): Unit {
        updateCitizenInformationWithHttpInfo(requestId = requestId, updateCitizenInformationRequest = updateCitizenInformationRequest)
    }

    @Throws(RestClientResponseException::class)
    fun updateCitizenInformationWithHttpInfo(requestId: java.util.UUID, updateCitizenInformationRequest: UpdateCitizenInformationRequest): ResponseEntity<Unit> {
        val localVariableConfig = updateCitizenInformationRequestConfig(requestId = requestId, updateCitizenInformationRequest = updateCitizenInformationRequest)
        return request<UpdateCitizenInformationRequest, Unit>(
            localVariableConfig
        )
    }

    fun updateCitizenInformationRequestConfig(requestId: java.util.UUID, updateCitizenInformationRequest: UpdateCitizenInformationRequest) : RequestConfig<UpdateCitizenInformationRequest> {
        val localVariableBody = updateCitizenInformationRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/citizen-information",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
