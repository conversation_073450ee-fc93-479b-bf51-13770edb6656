package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainUnionContribution
import be.fgov.onerva.cu.backend.adapter.out.mapper.toUnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.UnionContributionRepository
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
@Transactional
class UnionContributionPersistenceAdapter(
    val changePersonalDataRepository: ChangePersonalDataRepository,
    val unionContributionRepository: UnionContributionRepository,
) : UnionContributionPort {
    val log = logger

    @LogMethodCall
    override fun getUnionContribution(requestId: UUID): UnionContribution? {
        changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        return unionContributionRepository.findByRequestId(requestId)?.toDomainUnionContribution()
    }

    @LogMethodCall
    override fun persistUnionContribution(requestId: UUID, @SensitiveParam unionContribution: UnionContribution) {
        val changePersonalData = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")

        var unionContributionEntity = changePersonalData.unionContribution
        if (unionContributionEntity == null) {
            unionContributionEntity =
                unionContribution.toUnionContributionEntity(changePersonalData, UpdateStatus.EDITED)
            unionContributionRepository.save(unionContributionEntity)
        } else {
            unionContributionEntity.apply {
                authorized = unionContribution.authorized
                effectiveDate = unionContribution.effectiveDate
                updateStatus = UpdateStatus.EDITED
            }
        }
    }

    override fun getLatestRevision(requestId: UUID): Int {
        val request = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        return request.unionContribution?.id?.let {
            unionContributionRepository
                .findLastChangeRevision(it)
                .getOrNull()?.revisionNumber?.getOrNull()
        } ?: 0
    }

    override fun getUnionContributionForRevision(requestId: UUID, revision: Int): UnionContribution? {
        val id = getEntityId(requestId)
        return unionContributionRepository.findRevision(id, revision).orElseThrow {
            InvalidRequestIdException("Revision not found for request $requestId and revision $revision")
        }.entity.toDomainUnionContribution()
    }

    @LogMethodCall
    override fun deleteUnionContribution(requestId: UUID) {
        val unionContributionEntity = unionContributionRepository.findByRequestId(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        // First, find and update the parent entity to remove the reference
        val changePersonalDataRequest = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")

        // Clear the bidirectional relationship
        changePersonalDataRequest.unionContribution = null
        changePersonalDataRepository.save(changePersonalDataRequest)

        // Now we can safely delete the union contribution
        unionContributionRepository.delete(unionContributionEntity)
    }

    override fun getEntityId(requestId: UUID): UUID {
        return unionContributionRepository.findByRequestId(requestId)?.id
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
    }

    override fun patchCurrentDataWithRevision(requestId: UUID, revision: Int) {
        log.info("Patching current data for request ID: $requestId with the latest revision data")

        val currentData = unionContributionRepository.findByRequestId(requestId)

        if(currentData == null){
            throw InvalidRequestIdException("Request ID not found: $requestId")
        }

        val latestRevision = unionContributionRepository.findRevision(currentData.id, revision)

        currentData.apply {
            this.authorized = latestRevision.get().entity.authorized
            this.effectiveDate = latestRevision.get().entity.effectiveDate
            this.updateStatus = UpdateStatus.EDITED
        }

        log.info("Patching complete. Updated Union Contribution data")

        unionContributionRepository.save(currentData)

    }
}