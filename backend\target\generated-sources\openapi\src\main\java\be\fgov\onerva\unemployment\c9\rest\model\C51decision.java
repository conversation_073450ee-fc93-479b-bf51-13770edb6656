/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * C51decision
 */
@JsonPropertyOrder({
  C51decision.JSON_PROPERTY_C51A,
  C51decision.JSON_PROPERTY_TYPE_C9,
  C51decision.JSON_PROPERTY_FLAG_VALID
})
@JsonTypeName("c51decision")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class C51decision {
  public static final String JSON_PROPERTY_C51A = "c51a";
  private String c51a;

  public static final String JSON_PROPERTY_TYPE_C9 = "typeC9";
  private String typeC9;

  public static final String JSON_PROPERTY_FLAG_VALID = "flagValid";
  private String flagValid;

  public C51decision() {
  }

  public C51decision c51a(String c51a) {
    
    this.c51a = c51a;
    return this;
  }

  /**
   * Get c51a
   * @return c51a
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_C51A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getC51a() {
    return c51a;
  }


  @JsonProperty(JSON_PROPERTY_C51A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setC51a(String c51a) {
    this.c51a = c51a;
  }

  public C51decision typeC9(String typeC9) {
    
    this.typeC9 = typeC9;
    return this;
  }

  /**
   * Get typeC9
   * @return typeC9
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TYPE_C9)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTypeC9() {
    return typeC9;
  }


  @JsonProperty(JSON_PROPERTY_TYPE_C9)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTypeC9(String typeC9) {
    this.typeC9 = typeC9;
  }

  public C51decision flagValid(String flagValid) {
    
    this.flagValid = flagValid;
    return this;
  }

  /**
   * Get flagValid
   * @return flagValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFlagValid() {
    return flagValid;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagValid(String flagValid) {
    this.flagValid = flagValid;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    C51decision c51decision = (C51decision) o;
    return Objects.equals(this.c51a, c51decision.c51a) &&
        Objects.equals(this.typeC9, c51decision.typeC9) &&
        Objects.equals(this.flagValid, c51decision.flagValid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(c51a, typeC9, flagValid);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class C51decision {\n");
    sb.append("    c51a: ").append(toIndentedString(c51a)).append("\n");
    sb.append("    typeC9: ").append(toIndentedString(typeC9)).append("\n");
    sb.append("    flagValid: ").append(toIndentedString(flagValid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

