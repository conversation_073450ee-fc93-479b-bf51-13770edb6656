package be.fgov.onerva.cu.backend.controller;

import be.fgov.onerva.cu.rest.priv.api.ConfigApi;
import be.fgov.onerva.cu.rest.priv.model.FlagsmithConfigResponse;
import be.fgov.onerva.cu.rest.priv.model.KeycloakConfigResponse;
import be.fgov.onerva.cu.rest.priv.model.KeycloakConfigResponseConfig;
import be.fgov.onerva.cu.rest.priv.model.KeycloakConfigResponseInitOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Slf4j
public class FrontendConfigurationController implements ConfigApi {

    @Value("${keycloak.auth-server-url:'http://localhost:8082'}")
    private String keycloakUrl;

    @Value("${keycloak.realm:onemrva-agents}")
    private String realm;

    @Value("${keycloak.redirect:'http://localhost:4300'}")
    private String redirectUri;

    @Value("${flagsmith.api.url}")
    private String flagsmithApiUrl;

    @Value("${flagsmith.api.key}")
    private String flagsmithApiKey;

    @Override
    public FlagsmithConfigResponse getFlagsmithConfig() {
        var config = new FlagsmithConfigResponse();
        config.api(flagsmithApiUrl);
        config.environmentID(flagsmithApiKey);
        return config;
    }

    @Override
    public KeycloakConfigResponse getKeycloakConfig() {

        var config = new KeycloakConfigResponseConfig();
        config.setUrl(keycloakUrl);
        config.setRealm(realm);
        config.setClientId("cu-frontend");

        var initOptions = new KeycloakConfigResponseInitOptions();
        initOptions.setRedirectUri(redirectUri);
        initOptions.setCheckLoginIframe(false);
        initOptions.setOnLoad("login-required");

        var keycloakConfigResponse = new KeycloakConfigResponse();
        keycloakConfigResponse.setConfig(config);
        keycloakConfigResponse.setInitOptions(initOptions);

        return keycloakConfigResponse;
    }


}
