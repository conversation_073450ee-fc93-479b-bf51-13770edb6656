/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param operation 
 * @param oldValue 
 * @param newValue 
 * @param comment This field will contain the comment added when doing the action (for example an assign).
 * @param updater The person who did the change
 * @param updateDate The person who did the change
 */


data class HistoryDTO (

    @get:JsonProperty("operation")
    val operation: HistoryDTO.Operation? = null,

    @get:JsonProperty("oldValue")
    val oldValue: kotlin.String? = null,

    @get:JsonProperty("newValue")
    val newValue: kotlin.String? = null,

    /* This field will contain the comment added when doing the action (for example an assign). */
    @get:JsonProperty("comment")
    val comment: kotlin.String? = null,

    /* The person who did the change */
    @get:Json<PERSON>roperty("updater")
    val updater: kotlin.String? = null,

    /* The person who did the change */
    @get:JsonProperty("updateDate")
    val updateDate: java.time.OffsetDateTime? = null

) {

    /**
     * 
     *
     * Values: ASSIGN,DISTRIBUTE,CREATE_TASK,ADD_CONCERNED_ENTITIES,REMOVE_CONCERNED_ENTITIES,UPDATE_DUE_DATE,UPDATE_PRIORITY,CLOSE,PRECLOSE,REOPEN,WAKEUP,WAIT,IN_REVIEW,END_REVIEW,OTHER
     */
    enum class Operation(val value: kotlin.String) {
        @JsonProperty(value = "assign") ASSIGN("assign"),
        @JsonProperty(value = "distribute") DISTRIBUTE("distribute"),
        @JsonProperty(value = "createTask") CREATE_TASK("createTask"),
        @JsonProperty(value = "addConcernedEntities") ADD_CONCERNED_ENTITIES("addConcernedEntities"),
        @JsonProperty(value = "removeConcernedEntities") REMOVE_CONCERNED_ENTITIES("removeConcernedEntities"),
        @JsonProperty(value = "updateDueDate") UPDATE_DUE_DATE("updateDueDate"),
        @JsonProperty(value = "updatePriority") UPDATE_PRIORITY("updatePriority"),
        @JsonProperty(value = "close") CLOSE("close"),
        @JsonProperty(value = "preclose") PRECLOSE("preclose"),
        @JsonProperty(value = "reopen") REOPEN("reopen"),
        @JsonProperty(value = "wakeup") WAKEUP("wakeup"),
        @JsonProperty(value = "wait") WAIT("wait"),
        @JsonProperty(value = "inReview") IN_REVIEW("inReview"),
        @JsonProperty(value = "endReview") END_REVIEW("endReview"),
        @JsonProperty(value = "other") OTHER("other");
    }

}

