import {Injectable} from "@angular/core";
import {Address} from "@rest-client/cu-bff";

@Injectable({
  providedIn: 'root',
})
export class StringUtils {
  static trimIfString(originalString: string | null) {
        if (typeof originalString === 'string') {
            return originalString.trim();
        }
        return originalString;
    }
    static formatAddress(address: Address): string {
        if (!address) {
            return "-";
        }

        const street = (address.street || "").trim();
        const houseNumber = (address.houseNumber || "").trim();
        const boxNumber = (address.boxNumber || "").trim();
        const zipCode = (address.zipCode || "").trim();
        const city = (address.city || "").trim();
        const boxNumberPart = boxNumber ? `/${boxNumber}` : "";

        const streetPart = [street, houseNumber + boxNumberPart]
            .filter(part => part.trim())
            .join(' ')
            .trim();

        const cityPart = [zipCode, city]
            .filter(part => part.trim())
            .join(' ')
            .trim();

        let result = '';
        if (streetPart) {
            result = streetPart;
        }
        if (cityPart) {
            result += (result ? ', ' : '') + '<br>' + cityPart;
        } else if (result) {
            result += ', <br>';
        }

        return result
                .replace(/\s+/g, ' ')
                .trim()
            || "-";
    }
}
