/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo.organizational.chart.rest.model.InvalidParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.net.URI;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Problem details for invalid input parameter(s)
 */
@JsonPropertyOrder({
  InvalidParamProblem.JSON_PROPERTY_TYPE,
  InvalidParamProblem.JSON_PROPERTY_TITLE,
  InvalidParamProblem.JSON_PROPERTY_STATUS,
  InvalidParamProblem.JSON_PROPERTY_DETAIL,
  InvalidParamProblem.JSON_PROPERTY_INSTANCE,
  InvalidParamProblem.JSON_PROPERTY_INVALID_PARAMS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class InvalidParamProblem {
  public static final String JSON_PROPERTY_TYPE = "type";
  private URI type;

  public static final String JSON_PROPERTY_TITLE = "title";
  private String title;

  public static final String JSON_PROPERTY_STATUS = "status";
  private Integer status;

  public static final String JSON_PROPERTY_DETAIL = "detail";
  private String detail;

  public static final String JSON_PROPERTY_INSTANCE = "instance";
  private URI instance;

  public static final String JSON_PROPERTY_INVALID_PARAMS = "invalidParams";
  private List<InvalidParam> invalidParams = new ArrayList<>();

  public InvalidParamProblem() {
  }

  public InvalidParamProblem type(URI type) {
    
    this.type = type;
    return this;
  }

  /**
   * An URI reference that identifies the problem type. When dereferenced, it SHOULD provide human-readable documentation for the problem type (e.g. using HTML).
   * @return type
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setType(URI type) {
    this.type = type;
  }

  public InvalidParamProblem title(String title) {
    
    this.title = title;
    return this;
  }

  /**
   * A short, summary of the problem type. Written in english and readable for engineers (usually not suited for non technical stakeholders and not localized)
   * @return title
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTitle() {
    return title;
  }


  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTitle(String title) {
    this.title = title;
  }

  public InvalidParamProblem status(Integer status) {
    
    this.status = status;
    return this;
  }

  /**
   * The HTTP status code generated by the origin server for this occurrence of the problem.
   * minimum: 400
   * maximum: 600
   * @return status
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(Integer status) {
    this.status = status;
  }

  public InvalidParamProblem detail(String detail) {
    
    this.detail = detail;
    return this;
  }

  /**
   * A human-readable explanation specific to this occurrence of the problem
   * @return detail
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DETAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDetail() {
    return detail;
  }


  @JsonProperty(JSON_PROPERTY_DETAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDetail(String detail) {
    this.detail = detail;
  }

  public InvalidParamProblem instance(URI instance) {
    
    this.instance = instance;
    return this;
  }

  /**
   * A URI reference that identifies the specific occurrence of the problem. It may or may not yield further information if dereferenced.
   * @return instance
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSTANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getInstance() {
    return instance;
  }


  @JsonProperty(JSON_PROPERTY_INSTANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInstance(URI instance) {
    this.instance = instance;
  }

  public InvalidParamProblem invalidParams(List<InvalidParam> invalidParams) {
    
    this.invalidParams = invalidParams;
    return this;
  }

  public InvalidParamProblem addInvalidParamsItem(InvalidParam invalidParamsItem) {
    if (this.invalidParams == null) {
      this.invalidParams = new ArrayList<>();
    }
    this.invalidParams.add(invalidParamsItem);
    return this;
  }

  /**
   * An array of parameter OpenAPI violations
   * @return invalidParams
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INVALID_PARAMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<InvalidParam> getInvalidParams() {
    return invalidParams;
  }


  @JsonProperty(JSON_PROPERTY_INVALID_PARAMS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInvalidParams(List<InvalidParam> invalidParams) {
    this.invalidParams = invalidParams;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InvalidParamProblem invalidParamProblem = (InvalidParamProblem) o;
    return Objects.equals(this.type, invalidParamProblem.type) &&
        Objects.equals(this.title, invalidParamProblem.title) &&
        Objects.equals(this.status, invalidParamProblem.status) &&
        Objects.equals(this.detail, invalidParamProblem.detail) &&
        Objects.equals(this.instance, invalidParamProblem.instance) &&
        Objects.equals(this.invalidParams, invalidParamProblem.invalidParams);
  }

  @Override
  public int hashCode() {
    return Objects.hash(type, title, status, detail, instance, invalidParams);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InvalidParamProblem {\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    detail: ").append(toIndentedString(detail)).append("\n");
    sb.append("    instance: ").append(toIndentedString(instance)).append("\n");
    sb.append("    invalidParams: ").append(toIndentedString(invalidParams)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

