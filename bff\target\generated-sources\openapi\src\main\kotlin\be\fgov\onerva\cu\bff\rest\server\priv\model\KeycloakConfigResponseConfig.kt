package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param url Base uri of the realm
 * @param clientId The id of the frontend client
 * @param realm The keycloak realm
 */
data class KeycloakConfigResponseConfig(

    @Schema(example = "http://host.docker.internal:8082/auth/", required = true, description = "Base uri of the realm")
    @get:JsonProperty("url", required = true) val url: kotlin.String,

    @Schema(example = "null", required = true, description = "The id of the frontend client")
    @get:JsonProperty("clientId", required = true) val clientId: kotlin.String,

    @Schema(example = "null", required = true, description = "The keycloak realm")
    @get:JsonProperty("realm", required = true) val realm: kotlin.String
    ) {

}

