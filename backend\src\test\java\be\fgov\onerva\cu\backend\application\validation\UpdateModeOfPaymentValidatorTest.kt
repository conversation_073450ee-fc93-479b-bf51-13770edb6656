package be.fgov.onerva.cu.backend.application.validation

import java.util.stream.Stream
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.ConstraintValidatorContext.ConstraintViolationBuilder
import jakarta.validation.ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderCustomizableContext
import jakarta.validation.ConstraintValidatorContext.ConstraintViolationBuilder.NodeBuilderDefinedContext
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import be.fgov.onerva.cu.backend.application.domain.UpdateModeOfPayment
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
@Execution(ExecutionMode.SAME_THREAD)
class UpdateModeOfPaymentValidatorTest {

    @MockK
    private lateinit var context: ConstraintValidatorContext

    @MockK
    private lateinit var violationBuilder: ConstraintViolationBuilder

    @MockK
    private lateinit var nodeBuilderCustomizable: NodeBuilderCustomizableContext

    @MockK
    private lateinit var nodeBuilderDefined: NodeBuilderDefinedContext

    @InjectMockKs
    private lateinit var validator: UpdateModeOfPaymentValidator

    @BeforeEach
    fun setUp() {
        every { context.disableDefaultConstraintViolation() } returns Unit
        every { context.buildConstraintViolationWithTemplate(any()) } returns violationBuilder
        every { violationBuilder.addPropertyNode(any()) } returns nodeBuilderCustomizable
        every { nodeBuilderCustomizable.addConstraintViolation() } returns context
    }


    @Test
    fun `isValid should return false when value is null`() {
        // When
        val result = validator.isValid(null, context)

        // Then
        assertThat(result).isFalse()
    }

    @Nested
    inner class BelgianAccountTests {

        @Test
        fun `isValid should return true for valid Belgian account without BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "****************",
                bic = null,
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValid should return false for invalid Belgian account`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "BE1234567890",
                bic = null,
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("Invalid Belgian IBAN format")
                violationBuilder.addPropertyNode("iban")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }

        @Test
        fun `isValid should return true for valid Belgian account with valid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "****************",
                bic = "BBRUBEBB",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValid should return false for valid Belgian account with invalid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "****************",
                bic = "INVALID",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("Invalid BIC format")
                violationBuilder.addPropertyNode("bic")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }
    }

    @Nested
    inner class ForeignAccountTests {

        @Test
        fun `isValid should return true for valid foreign account with valid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "***************************",
                bic = "BNPAFRPP",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValid should return false for valid foreign account without BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "***************************",
                bic = null,
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("BIC is required and must be valid for foreign accounts")
                violationBuilder.addPropertyNode("bic")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }

        @Test
        fun `isValid should return false for invalid foreign account with valid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "FR123456789",
                bic = "BNPAFRPP",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("Invalid foreign IBAN format")
                violationBuilder.addPropertyNode("iban")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }

        @Test
        fun `isValid should return false for valid foreign account with invalid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "***************************",
                bic = "INVALID",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("BIC is required and must be valid for foreign accounts")
                violationBuilder.addPropertyNode("bic")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }

        @Test
        fun `isValid should return false for invalid foreign account with invalid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = null,
                iban = "FR123456789",
                bic = "INVALID",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isFalse()
            verify {
                context.buildConstraintViolationWithTemplate("Invalid foreign IBAN format")
                violationBuilder.addPropertyNode("iban")
                nodeBuilderCustomizable.addConstraintViolation()
            }
            verify {
                context.buildConstraintViolationWithTemplate("BIC is required and must be valid for foreign accounts")
                violationBuilder.addPropertyNode("bic")
                nodeBuilderCustomizable.addConstraintViolation()
            }
        }
    }

    @Nested
    inner class OtherPersonAccountTests {

        @Test
        fun `isValid should return true for other person's valid Belgian account`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = "John Doe",
                iban = "****************",
                bic = null,
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `isValid should return true for other person's valid foreign account with valid BIC`() {
            // Given
            val update = UpdateModeOfPayment(
                otherPersonName = "John Doe",
                iban = "***************************",
                bic = "BNPAFRPP",
            )

            // When
            val result = validator.isValid(update, context)

            // Then
            assertThat(result).isTrue()
        }
    }

    @ParameterizedTest
    @MethodSource("provideTestScenarios")
    fun `isValid should handle various scenarios correctly`(
        update: UpdateModeOfPayment,
        isBelgian: Boolean,
        isValidBelgianIBAN: Boolean,
        isValidIBAN: Boolean,
        isValidBIC: Boolean,
        expected: Boolean,
    ) {
        // Given

        // When
        val result = validator.isValid(update, context)

        // Then
        assertThat(result).isEqualTo(expected)
    }

    companion object {
        @JvmStatic
        fun provideTestScenarios(): Stream<Arguments> = Stream.of(
            // Valid Belgian account without BIC
            Arguments.of(
                UpdateModeOfPayment(null, "****************", null),
                true,  // isBelgian
                true,  // isValidBelgianIBAN
                false, // isValidIBAN (not used for Belgian)
                false, // isValidBIC (not used as BIC is null)
                true   // expected result
            ),
            // Valid Belgian account with valid BIC
            Arguments.of(
                UpdateModeOfPayment(null, "****************", "BBRUBEBB"),
                true,  // isBelgian
                true,  // isValidBelgianIBAN
                false, // isValidIBAN (not used for Belgian)
                true,  // isValidBIC
                true   // expected result
            ),
            // Invalid Belgian IBAN
            Arguments.of(
                UpdateModeOfPayment(null, "BE1234", null),
                true,  // isBelgian
                false, // isValidBelgianIBAN
                false, // isValidIBAN (not used for Belgian)
                false, // isValidBIC (not used as BIC is null)
                false  // expected result
            ),
            // Belgian account with invalid BIC
            Arguments.of(
                UpdateModeOfPayment(null, "****************", "INVALID"),
                true,  // isBelgian
                true,  // isValidBelgianIBAN
                false, // isValidIBAN (not used for Belgian)
                false, // isValidBIC
                false  // expected result
            ),
            // Valid foreign account with valid BIC
            Arguments.of(
                UpdateModeOfPayment(null, "***************************", "BNPAFRPP"),
                false, // isBelgian
                false, // isValidBelgianIBAN (not used for foreign)
                true,  // isValidIBAN
                true,  // isValidBIC
                true   // expected result
            ),
            // Valid foreign account without BIC
            Arguments.of(
                UpdateModeOfPayment(null, "***************************", null),
                false, // isBelgian
                false, // isValidBelgianIBAN (not used for foreign)
                true,  // isValidIBAN
                false, // isValidBIC (BIC is null)
                false  // expected result
            ),
            // Invalid foreign IBAN with valid BIC
            Arguments.of(
                UpdateModeOfPayment(null, "FR1234", "BNPAFRPP"),
                false, // isBelgian
                false, // isValidBelgianIBAN (not used for foreign)
                false, // isValidIBAN
                true,  // isValidBIC
                false  // expected result
            ),
            // Valid foreign IBAN with invalid BIC
            Arguments.of(
                UpdateModeOfPayment(null, "***************************", "INVALID"),
                false, // isBelgian
                false, // isValidBelgianIBAN (not used for foreign)
                true,  // isValidIBAN
                false, // isValidBIC
                false  // expected result
            ),
            // Other person account - valid Belgian
            Arguments.of(
                UpdateModeOfPayment("John Doe", "****************", null),
                true,  // isBelgian
                true,  // isValidBelgianIBAN
                false, // isValidIBAN (not used for Belgian)
                false, // isValidBIC (not used as BIC is null)
                true   // expected result
            ),
            // Other person account - valid foreign with valid BIC
            Arguments.of(
                UpdateModeOfPayment("John Doe", "***************************", "BNPAFRPP"),
                false, // isBelgian
                false, // isValidBelgianIBAN (not used for foreign)
                true,  // isValidIBAN
                true,  // isValidBIC
                true   // expected result
            )
        )
    }
}