/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * TranslatedString
 */
@JsonPropertyOrder({
  TranslatedString.JSON_PROPERTY_FR,
  TranslatedString.JSON_PROPERTY_NL
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class TranslatedString {
  public static final String JSON_PROPERTY_FR = "fr";
  private String fr;

  public static final String JSON_PROPERTY_NL = "nl";
  private String nl;

  public TranslatedString() {
  }

  public TranslatedString fr(String fr) {
    
    this.fr = fr;
    return this;
  }

  /**
   * The french translation of the element
   * @return fr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFr() {
    return fr;
  }


  @JsonProperty(JSON_PROPERTY_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFr(String fr) {
    this.fr = fr;
  }

  public TranslatedString nl(String nl) {
    
    this.nl = nl;
    return this;
  }

  /**
   * The dutch translation of the element
   * @return nl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNl() {
    return nl;
  }


  @JsonProperty(JSON_PROPERTY_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNl(String nl) {
    this.nl = nl;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TranslatedString translatedString = (TranslatedString) o;
    return Objects.equals(this.fr, translatedString.fr) &&
        Objects.equals(this.nl, translatedString.nl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fr, nl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TranslatedString {\n");
    sb.append("    fr: ").append(toIndentedString(fr)).append("\n");
    sb.append("    nl: ").append(toIndentedString(nl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

