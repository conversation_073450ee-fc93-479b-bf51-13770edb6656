ARG ANGULAR_IMAGE_TAG
FROM docker-proxy.onemrva.priv/openapitools/openapi-generator-cli:v7.9.0 AS openapigenerator

COPY api /usr/src/api
COPY scripts/generate_apis.sh /usr/src/api
WORKDIR /usr/src/api

RUN chmod +x generate_apis.sh
RUN ./generate_apis.sh


FROM docker-release.onemrva.priv/onemrva/node:${ANGULAR_IMAGE_TAG} AS nodecompiler
WORKDIR /usr/src/app

COPY frontend/package.json frontend/package-lock.json ./
RUN npm i --no-fund --no-audit
COPY frontend/ .

RUN ls  -R projects/
COPY --from=openapigenerator /usr/src/api/rest-client/public ./projects/cu/src/rest-client
COPY --from=openapigenerator /usr/src/api/rest-client/private ./projects/cu/src/rest-client


RUN ls  -R projects/
RUN ls  -R projects/cu/src
RUN npm run build:web-components

FROM docker-proxy.onemrva.priv/nginxinc/nginx-unprivileged:alpine3.21
ARG APPLICATION_NAME
COPY --from=nodecompiler --chown=101:0 /usr/src/app/dist/$APPLICATION_NAME/browser /usr/share/nginx/html/app-elements
COPY --from=nodecompiler --chown=101:0 /usr/src/app/projects/cu/src/assets/i18n /usr/share/nginx/html/app-elements/assets/i18n
RUN mv /usr/share/nginx/html/app-elements/main.js /usr/share/nginx/html/app-elements/elements.js

EXPOSE 8080
