/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Signature
 */
@JsonPropertyOrder({
  Signature.JSON_PROPERTY_SIGNATURE_XML,
  Signature.JSON_PROPERTY_IS_VALID,
  Signature.JSON_PROPERTY_SIGNATURE_DATE,
  Signature.JSON_PROPERTY_ERROR_MESSAGE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Signature {
  public static final String JSON_PROPERTY_SIGNATURE_XML = "signatureXml";
  private String signatureXml;

  public static final String JSON_PROPERTY_IS_VALID = "isValid";
  private Boolean isValid;

  public static final String JSON_PROPERTY_SIGNATURE_DATE = "signatureDate";
  private String signatureDate;

  public static final String JSON_PROPERTY_ERROR_MESSAGE = "errorMessage";
  private String errorMessage;

  public Signature() {
  }

  public Signature signatureXml(String signatureXml) {
    
    this.signatureXml = signatureXml;
    return this;
  }

  /**
   * Get signatureXml
   * @return signatureXml
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_XML)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignatureXml() {
    return signatureXml;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_XML)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignatureXml(String signatureXml) {
    this.signatureXml = signatureXml;
  }

  public Signature isValid(Boolean isValid) {
    
    this.isValid = isValid;
    return this;
  }

  /**
   * Get isValid
   * @return isValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsValid() {
    return isValid;
  }


  @JsonProperty(JSON_PROPERTY_IS_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsValid(Boolean isValid) {
    this.isValid = isValid;
  }

  public Signature signatureDate(String signatureDate) {
    
    this.signatureDate = signatureDate;
    return this;
  }

  /**
   * Get signatureDate
   * @return signatureDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSignatureDate() {
    return signatureDate;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignatureDate(String signatureDate) {
    this.signatureDate = signatureDate;
  }

  public Signature errorMessage(String errorMessage) {
    
    this.errorMessage = errorMessage;
    return this;
  }

  /**
   * Get errorMessage
   * @return errorMessage
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getErrorMessage() {
    return errorMessage;
  }


  @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Signature signature = (Signature) o;
    return Objects.equals(this.signatureXml, signature.signatureXml) &&
        Objects.equals(this.isValid, signature.isValid) &&
        Objects.equals(this.signatureDate, signature.signatureDate) &&
        Objects.equals(this.errorMessage, signature.errorMessage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(signatureXml, isValid, signatureDate, errorMessage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Signature {\n");
    sb.append("    signatureXml: ").append(toIndentedString(signatureXml)).append("\n");
    sb.append("    isValid: ").append(toIndentedString(isValid)).append("\n");
    sb.append("    signatureDate: ").append(toIndentedString(signatureDate)).append("\n");
    sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

