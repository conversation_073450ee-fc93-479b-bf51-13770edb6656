package be.fgov.onerva.cu.bff.controller

import java.util.UUID
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.bff.adapter.out.RedirectService
import be.fgov.onerva.cu.bff.rest.server.priv.api.RedirectApi
import be.fgov.onerva.cu.common.aop.LogMethodCall

@RestController
@RequestMapping("/bff")
class RedirectController(
    private val redirectService: RedirectService,
) : RedirectApi {

    @LogMethodCall
    override fun getC51Redirect(requestId: UUID): ResponseEntity<String> {
        return ResponseEntity.ok()
            .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
            .body(redirectService.getC51Link(requestId))
    }

    @LogMethodCall
    override fun openS24Session(requestId: UUID): ResponseEntity<Unit> {
        redirectService.openS24Session(requestId)
        return ResponseEntity.ok().build()
    }

    @LogMethodCall
    override fun getRegisRedirect(requestId: UUID, languageCode: String): ResponseEntity<String> {
        return ResponseEntity.ok()
            .contentType(org.springframework.http.MediaType.TEXT_PLAIN)
            .body(redirectService.getRegisRedirectUrl(requestId, languageCode))
    }
}