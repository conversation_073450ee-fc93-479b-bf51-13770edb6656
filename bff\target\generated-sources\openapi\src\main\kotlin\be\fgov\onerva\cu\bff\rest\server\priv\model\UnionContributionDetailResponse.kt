package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.FieldSource
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param authorized 
 * @param effectiveDate The effective date of the union contribution (format YYYY-MM-DD)
 * @param fieldSources Sources for individual fields
 */
data class UnionContributionDetailResponse(

    @Schema(example = "null", description = "")
    @get:JsonProperty("authorized") val authorized: kotlin.Boolean? = null,

    @field:Valid
    @Schema(example = "null", description = "The effective date of the union contribution (format YYYY-MM-DD)")
    @get:JsonProperty("effectiveDate") val effectiveDate: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "Sources for individual fields")
    @get:JsonProperty("fieldSources") val fieldSources: kotlin.collections.List<FieldSource>? = null
    ) {

}

