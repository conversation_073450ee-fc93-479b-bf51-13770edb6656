import { ComponentFixture, TestBed } from '@angular/core/testing';
import { RegisCheckComponent } from './regis-check.component';
import { RedirectHandlerService } from '../../../../http/redirect-handler.service';
import {Observable, of, throwError} from "rxjs";
import {TranslateLoader, TranslateModule, TranslatePipe} from "@ngx-translate/core";
import { NO_ERRORS_SCHEMA } from '@angular/core';

describe('RegisCheckComponent', () => {
    let component: RegisCheckComponent;
    let fixture: ComponentFixture<RegisCheckComponent>;
    let redirectHandlerServiceMock: { getRegisRedirectUrl: jest.Mock };

    class MockTranslateLoader implements TranslateLoader {
        getTranslation(lang: string): Observable<any> {
            return of({
                'CU_DATA_CONSISTENCY.DC.FAMILY.FAMILY_SITUATION': 'Family Situation',
                'CU_DATA_CONSISTENCY.DC.FAMILY.LAST_DECISION': 'Last Decision',
                'CU_CALCULATION.CU_BAREMA.BAREMA': 'Barema',
                'CU_CALCULATION.CU_BAREMA.ARTICLE': 'Article'
            });
        }
    }

    beforeEach(async () => {
        redirectHandlerServiceMock = {
            getRegisRedirectUrl: jest.fn()
        };

        await TestBed.configureTestingModule({
            imports: [RegisCheckComponent, TranslateModule.forRoot({
                loader: { provide: TranslateLoader, useClass: MockTranslateLoader }
            })],
            providers: [
                { provide: RedirectHandlerService, useValue: redirectHandlerServiceMock },
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(RegisCheckComponent);
        component = fixture.componentInstance;
        component.language = 'en';

        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize with isVerified set to false', () => {
        expect(component.isVerified).toBeFalsy();
    });

    describe('openRegis', () => {
        it('should log an error if requestId is undefined', () => {
            component.requestId = undefined;
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            component.openRegis();

            expect(consoleSpy).toHaveBeenCalledWith('Request ID is missing');
            expect(redirectHandlerServiceMock.getRegisRedirectUrl).not.toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it('should call getRegisRedirectUrl with requestId and language', () => {
            component.requestId = '123';
            component.language = 'en';
            redirectHandlerServiceMock.getRegisRedirectUrl.mockReturnValue(of('https://example.com'));
            const windowSpy = jest.spyOn(window, 'open').mockImplementation();
            component.openRegis();

            expect(redirectHandlerServiceMock.getRegisRedirectUrl).toHaveBeenCalledWith('123', 'en');
            expect(windowSpy).toHaveBeenCalledWith('https://example.com', '_blank');

            windowSpy.mockRestore();
        });

        it('should handle error when getRegisRedirectUrl fails', () => {
            component.requestId = '123';
            component.language = 'en';
            redirectHandlerServiceMock.getRegisRedirectUrl.mockReturnValue(throwError(() => new Error('Failed')));
            const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
            component.openRegis();

            expect(redirectHandlerServiceMock.getRegisRedirectUrl).toHaveBeenCalledWith('123', 'en');
            expect(consoleSpy).toHaveBeenCalled();

            consoleSpy.mockRestore();
        });

        it('should not open a window if url is falsy', () => {
            component.requestId = '123';
            component.language = 'en';
            redirectHandlerServiceMock.getRegisRedirectUrl.mockReturnValue(of(null));
            const windowSpy = jest.spyOn(window, 'open').mockImplementation();
            component.openRegis();

            expect(redirectHandlerServiceMock.getRegisRedirectUrl).toHaveBeenCalledWith('123', 'en');
            expect(windowSpy).not.toHaveBeenCalled();

            windowSpy.mockRestore();
        });
    });

    describe('onVerificationChange', () => {
        it('should update isVerified and emit the value', () => {
            const emitSpy = jest.spyOn(component.verificationChange, 'emit');
            const event = { target: { checked: true } };
            component.onVerificationChange(event);

            expect(component.isVerified).toBeTruthy();
            expect(emitSpy).toHaveBeenCalledWith(true);
        });

        it('should emit false when checkbox is unchecked', () => {
            const emitSpy = jest.spyOn(component.verificationChange, 'emit');
            const event = { target: { checked: false } };
            component.onVerificationChange(event);

            expect(component.isVerified).toBeFalsy();
            expect(emitSpy).toHaveBeenCalledWith(false);
        });
    });

    describe('Input/Output properties', () => {
        it('should accept requestId input', () => {
            component.requestId = '456';

            expect(component.requestId).toBe('456');
        });

        it('should accept language input', () => {
            component.language = 'fr';

            expect(component.language).toBe('fr');
        });

        it('should have a verificationChange output', () => {
            expect(component.verificationChange).toBeTruthy();
        });
    });
});