/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.EC1Activity;
import be.fgov.onerva.unemployment.c9.rest.model.EC1DeclarationAndSignature;
import be.fgov.onerva.unemployment.c9.rest.model.EC1FamilySituation;
import be.fgov.onerva.unemployment.c9.rest.model.EC1FinancialSituation;
import be.fgov.onerva.unemployment.c9.rest.model.EC1ForeignWorker;
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity;
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment;
import be.fgov.onerva.unemployment.c9.rest.model.EC1ReasonIntroduction;
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution;
import be.fgov.onerva.unemployment.c9.rest.model.EC1Various;
import be.fgov.onerva.unemployment.c9.rest.model.Signature;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1
 */
@JsonPropertyOrder({
  EC1.JSON_PROPERTY_VERSION,
  EC1.JSON_PROPERTY_COMPLETE_UNEMPLOYMENT,
  EC1.JSON_PROPERTY_TEMPORARY_UNEMPLOYMENT,
  EC1.JSON_PROPERTY_IDENTITY,
  EC1.JSON_PROPERTY_REASON_INTRODUCTION,
  EC1.JSON_PROPERTY_FAMILY_SITUATION,
  EC1.JSON_PROPERTY_ACTIVITY,
  EC1.JSON_PROPERTY_FINANCIAL_SITUATION,
  EC1.JSON_PROPERTY_MODE_OF_PAYMENT,
  EC1.JSON_PROPERTY_TRADE_UNION_CONTRIBUTION,
  EC1.JSON_PROPERTY_FOREIGN_WORKER,
  EC1.JSON_PROPERTY_VARIOUS,
  EC1.JSON_PROPERTY_DECLARATION_AND_SIGNATURE,
  EC1.JSON_PROPERTY_SIGNATURE,
  EC1.JSON_PROPERTY_ERROR
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1 {
  public static final String JSON_PROPERTY_VERSION = "version";
  private String version;

  public static final String JSON_PROPERTY_COMPLETE_UNEMPLOYMENT = "completeUnemployment";
  private Boolean completeUnemployment;

  public static final String JSON_PROPERTY_TEMPORARY_UNEMPLOYMENT = "temporaryUnemployment";
  private Boolean temporaryUnemployment;

  public static final String JSON_PROPERTY_IDENTITY = "identity";
  private EC1Identity identity;

  public static final String JSON_PROPERTY_REASON_INTRODUCTION = "reasonIntroduction";
  private EC1ReasonIntroduction reasonIntroduction;

  public static final String JSON_PROPERTY_FAMILY_SITUATION = "familySituation";
  private EC1FamilySituation familySituation;

  public static final String JSON_PROPERTY_ACTIVITY = "activity";
  private EC1Activity activity;

  public static final String JSON_PROPERTY_FINANCIAL_SITUATION = "financialSituation";
  private EC1FinancialSituation financialSituation;

  public static final String JSON_PROPERTY_MODE_OF_PAYMENT = "modeOfPayment";
  private EC1ModeOfPayment modeOfPayment;

  public static final String JSON_PROPERTY_TRADE_UNION_CONTRIBUTION = "tradeUnionContribution";
  private EC1TradeUnionContribution tradeUnionContribution;

  public static final String JSON_PROPERTY_FOREIGN_WORKER = "foreignWorker";
  private EC1ForeignWorker foreignWorker;

  public static final String JSON_PROPERTY_VARIOUS = "various";
  private EC1Various various;

  public static final String JSON_PROPERTY_DECLARATION_AND_SIGNATURE = "declarationAndSignature";
  private EC1DeclarationAndSignature declarationAndSignature;

  public static final String JSON_PROPERTY_SIGNATURE = "signature";
  private Signature signature;

  public static final String JSON_PROPERTY_ERROR = "error";
  private String error;

  public EC1() {
  }

  public EC1 version(String version) {
    
    this.version = version;
    return this;
  }

  /**
   * Get version
   * @return version
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVersion() {
    return version;
  }


  @JsonProperty(JSON_PROPERTY_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVersion(String version) {
    this.version = version;
  }

  public EC1 completeUnemployment(Boolean completeUnemployment) {
    
    this.completeUnemployment = completeUnemployment;
    return this;
  }

  /**
   * Get completeUnemployment
   * @return completeUnemployment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLETE_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCompleteUnemployment() {
    return completeUnemployment;
  }


  @JsonProperty(JSON_PROPERTY_COMPLETE_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompleteUnemployment(Boolean completeUnemployment) {
    this.completeUnemployment = completeUnemployment;
  }

  public EC1 temporaryUnemployment(Boolean temporaryUnemployment) {
    
    this.temporaryUnemployment = temporaryUnemployment;
    return this;
  }

  /**
   * Get temporaryUnemployment
   * @return temporaryUnemployment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TEMPORARY_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTemporaryUnemployment() {
    return temporaryUnemployment;
  }


  @JsonProperty(JSON_PROPERTY_TEMPORARY_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTemporaryUnemployment(Boolean temporaryUnemployment) {
    this.temporaryUnemployment = temporaryUnemployment;
  }

  public EC1 identity(EC1Identity identity) {
    
    this.identity = identity;
    return this;
  }

  /**
   * Get identity
   * @return identity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IDENTITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1Identity getIdentity() {
    return identity;
  }


  @JsonProperty(JSON_PROPERTY_IDENTITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIdentity(EC1Identity identity) {
    this.identity = identity;
  }

  public EC1 reasonIntroduction(EC1ReasonIntroduction reasonIntroduction) {
    
    this.reasonIntroduction = reasonIntroduction;
    return this;
  }

  /**
   * Get reasonIntroduction
   * @return reasonIntroduction
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REASON_INTRODUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1ReasonIntroduction getReasonIntroduction() {
    return reasonIntroduction;
  }


  @JsonProperty(JSON_PROPERTY_REASON_INTRODUCTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReasonIntroduction(EC1ReasonIntroduction reasonIntroduction) {
    this.reasonIntroduction = reasonIntroduction;
  }

  public EC1 familySituation(EC1FamilySituation familySituation) {
    
    this.familySituation = familySituation;
    return this;
  }

  /**
   * Get familySituation
   * @return familySituation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FAMILY_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1FamilySituation getFamilySituation() {
    return familySituation;
  }


  @JsonProperty(JSON_PROPERTY_FAMILY_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFamilySituation(EC1FamilySituation familySituation) {
    this.familySituation = familySituation;
  }

  public EC1 activity(EC1Activity activity) {
    
    this.activity = activity;
    return this;
  }

  /**
   * Get activity
   * @return activity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1Activity getActivity() {
    return activity;
  }


  @JsonProperty(JSON_PROPERTY_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setActivity(EC1Activity activity) {
    this.activity = activity;
  }

  public EC1 financialSituation(EC1FinancialSituation financialSituation) {
    
    this.financialSituation = financialSituation;
    return this;
  }

  /**
   * Get financialSituation
   * @return financialSituation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FINANCIAL_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1FinancialSituation getFinancialSituation() {
    return financialSituation;
  }


  @JsonProperty(JSON_PROPERTY_FINANCIAL_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFinancialSituation(EC1FinancialSituation financialSituation) {
    this.financialSituation = financialSituation;
  }

  public EC1 modeOfPayment(EC1ModeOfPayment modeOfPayment) {
    
    this.modeOfPayment = modeOfPayment;
    return this;
  }

  /**
   * Get modeOfPayment
   * @return modeOfPayment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MODE_OF_PAYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1ModeOfPayment getModeOfPayment() {
    return modeOfPayment;
  }


  @JsonProperty(JSON_PROPERTY_MODE_OF_PAYMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setModeOfPayment(EC1ModeOfPayment modeOfPayment) {
    this.modeOfPayment = modeOfPayment;
  }

  public EC1 tradeUnionContribution(EC1TradeUnionContribution tradeUnionContribution) {
    
    this.tradeUnionContribution = tradeUnionContribution;
    return this;
  }

  /**
   * Get tradeUnionContribution
   * @return tradeUnionContribution
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRADE_UNION_CONTRIBUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1TradeUnionContribution getTradeUnionContribution() {
    return tradeUnionContribution;
  }


  @JsonProperty(JSON_PROPERTY_TRADE_UNION_CONTRIBUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTradeUnionContribution(EC1TradeUnionContribution tradeUnionContribution) {
    this.tradeUnionContribution = tradeUnionContribution;
  }

  public EC1 foreignWorker(EC1ForeignWorker foreignWorker) {
    
    this.foreignWorker = foreignWorker;
    return this;
  }

  /**
   * Get foreignWorker
   * @return foreignWorker
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOREIGN_WORKER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1ForeignWorker getForeignWorker() {
    return foreignWorker;
  }


  @JsonProperty(JSON_PROPERTY_FOREIGN_WORKER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setForeignWorker(EC1ForeignWorker foreignWorker) {
    this.foreignWorker = foreignWorker;
  }

  public EC1 various(EC1Various various) {
    
    this.various = various;
    return this;
  }

  /**
   * Get various
   * @return various
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VARIOUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1Various getVarious() {
    return various;
  }


  @JsonProperty(JSON_PROPERTY_VARIOUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVarious(EC1Various various) {
    this.various = various;
  }

  public EC1 declarationAndSignature(EC1DeclarationAndSignature declarationAndSignature) {
    
    this.declarationAndSignature = declarationAndSignature;
    return this;
  }

  /**
   * Get declarationAndSignature
   * @return declarationAndSignature
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECLARATION_AND_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EC1DeclarationAndSignature getDeclarationAndSignature() {
    return declarationAndSignature;
  }


  @JsonProperty(JSON_PROPERTY_DECLARATION_AND_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeclarationAndSignature(EC1DeclarationAndSignature declarationAndSignature) {
    this.declarationAndSignature = declarationAndSignature;
  }

  public EC1 signature(Signature signature) {
    
    this.signature = signature;
    return this;
  }

  /**
   * Get signature
   * @return signature
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Signature getSignature() {
    return signature;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignature(Signature signature) {
    this.signature = signature;
  }

  public EC1 error(String error) {
    
    this.error = error;
    return this;
  }

  /**
   * Get error
   * @return error
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ERROR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getError() {
    return error;
  }


  @JsonProperty(JSON_PROPERTY_ERROR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setError(String error) {
    this.error = error;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1 EC1 = (EC1) o;
    return Objects.equals(this.version, EC1.version) &&
        Objects.equals(this.completeUnemployment, EC1.completeUnemployment) &&
        Objects.equals(this.temporaryUnemployment, EC1.temporaryUnemployment) &&
        Objects.equals(this.identity, EC1.identity) &&
        Objects.equals(this.reasonIntroduction, EC1.reasonIntroduction) &&
        Objects.equals(this.familySituation, EC1.familySituation) &&
        Objects.equals(this.activity, EC1.activity) &&
        Objects.equals(this.financialSituation, EC1.financialSituation) &&
        Objects.equals(this.modeOfPayment, EC1.modeOfPayment) &&
        Objects.equals(this.tradeUnionContribution, EC1.tradeUnionContribution) &&
        Objects.equals(this.foreignWorker, EC1.foreignWorker) &&
        Objects.equals(this.various, EC1.various) &&
        Objects.equals(this.declarationAndSignature, EC1.declarationAndSignature) &&
        Objects.equals(this.signature, EC1.signature) &&
        Objects.equals(this.error, EC1.error);
  }

  @Override
  public int hashCode() {
    return Objects.hash(version, completeUnemployment, temporaryUnemployment, identity, reasonIntroduction, familySituation, activity, financialSituation, modeOfPayment, tradeUnionContribution, foreignWorker, various, declarationAndSignature, signature, error);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1 {\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    completeUnemployment: ").append(toIndentedString(completeUnemployment)).append("\n");
    sb.append("    temporaryUnemployment: ").append(toIndentedString(temporaryUnemployment)).append("\n");
    sb.append("    identity: ").append(toIndentedString(identity)).append("\n");
    sb.append("    reasonIntroduction: ").append(toIndentedString(reasonIntroduction)).append("\n");
    sb.append("    familySituation: ").append(toIndentedString(familySituation)).append("\n");
    sb.append("    activity: ").append(toIndentedString(activity)).append("\n");
    sb.append("    financialSituation: ").append(toIndentedString(financialSituation)).append("\n");
    sb.append("    modeOfPayment: ").append(toIndentedString(modeOfPayment)).append("\n");
    sb.append("    tradeUnionContribution: ").append(toIndentedString(tradeUnionContribution)).append("\n");
    sb.append("    foreignWorker: ").append(toIndentedString(foreignWorker)).append("\n");
    sb.append("    various: ").append(toIndentedString(various)).append("\n");
    sb.append("    declarationAndSignature: ").append(toIndentedString(declarationAndSignature)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("    error: ").append(toIndentedString(error)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

