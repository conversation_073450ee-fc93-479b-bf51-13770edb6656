package be.fgov.onerva.cu.bff.model

import java.time.LocalDate

/**
 * Represents citizen information including their physical address in the system.
 *
 * This data class extends the basic citizen information by including a complete physical address.
 * It combines identification and contact information with detailed address data.
 *
 * @property firstName The citizen's first name
 * @property lastName The citizen's last name
 * @property numbox The unique numeric identifier used for citizen
 * @property nationality The nationality of the citizen
 * @property address The complete physical address details of the citizen
 * @property iban The IBAN number of the citizen
 * @property bic The BIC number of the citizen
 * @property otherPersonName The name of the holder if it is another name
 *
 * @see Citizen
 * @see Address
 */
data class CitizenInfoWithAddress(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String?,
    val address: Address,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val paymentMode: Int?,
    val unionDue: UnionContribution?,
)

/**
 * Represents a physical address.
 *
 * @property street Street name
 * @property houseNumber House number
 * @property boxNumber Optional box or apartment number
 * @property country Country name
 * @property city City name
 * @property zipCode Postal code
 */
data class Address(
    val street: String?,
    val houseNumber: String?,
    val boxNumber: String? = null,
    var country: String?,
    var city: String?,
    var zipCode: String?,
)

data class UnionContribution(
    val mandateActive: Boolean?,
    val validFrom: LocalDate?,
)
