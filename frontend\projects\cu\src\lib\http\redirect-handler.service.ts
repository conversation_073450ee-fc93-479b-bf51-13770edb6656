import {Injectable} from "@angular/core";
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {Configuration as ConfigurationBff, RedirectService} from "@rest-client/cu-bff";
import {isValidLanguageCode, Language, LanguageCode} from "../model/Language.model";
import {ConfigService} from "./../config/config.service";
import {Observable} from "rxjs";

@Injectable({
    providedIn: "root",
})
export class RedirectHandlerService {
    private redirectService!: RedirectService;

    constructor(
        readonly http: HttpClient,
        readonly configService: ConfigService,
    ) {
        this.initializeServices();
    }

    initializeServices(token?: string) {
        const configBff = new ConfigurationBff({
            basePath: this.configService.getEnvironmentVariable("bffBaseUrl", true),
            credentials: token ? {"Bearer": token} : undefined,
        });

        const defaultHeaders = token ?
            new HttpHeaders()
                .set("Authorization", `Bearer ${token}`)
                .set("Content-Type", "application/json") :
            new HttpHeaders().set("Content-Type", "application/json");

        this.redirectService = new RedirectService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );

        if (token) {
            this.redirectService.defaultHeaders = defaultHeaders;
        }
    }

    getC51RedirectUrl(requestId: string): Observable<string> {
        return this.redirectService.getC51Redirect(requestId);
    }

    openS24Session(requestId: string): Observable<string> {
        return this.redirectService.openS24Session(requestId);
    }

    getRegisRedirectUrl(requestId: string, languageCode: string): Observable<string> {
        if (!isValidLanguageCode(languageCode)) {
            console.warn(`Invalid language code: ${languageCode}. Defaulting to 'nl'.`);
            languageCode = Language.DUTCH;
        }
        return this.redirectService.getRegisRedirect(requestId, languageCode as LanguageCode);
    }

}
