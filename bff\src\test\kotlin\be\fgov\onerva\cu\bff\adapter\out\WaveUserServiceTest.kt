package be.fgov.onerva.cu.bff.adapter.out

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import be.fgov.onerva.cu.bff.exceptions.WaveUserNotFoundException
import be.fgov.onerva.cu.bff.rest.client.wo.facade.api.UserApi
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.UserDTO
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension

@ExtendWith(MockKExtension::class)
@Execution(ExecutionMode.SAME_THREAD)
class WaveUserServiceTest {

    @MockK
    lateinit var waveUserApi: UserApi

    @InjectMockKs
    lateinit var waveUserService: WaveUserService

    @Test
    fun `getOperatorCode should return operator code when user exists`() {
        // Given
        val ssin = "85050599890"
        val expectedOperatorCode = "OPERATOR_CODE"
        val user = UserDTO().apply {
            operatorCodes = listOf(expectedOperatorCode)
        }

        every { waveUserApi.searchUsers(any()) } returns user

        // When
        val result = waveUserService.getOperatorCode(ssin)

        // Then
        assertThat(result).isEqualTo(expectedOperatorCode)
    }

    @Test
    fun `getOperatorCode should throw WaveUserNotFoundException when user does not exist`() {
        // Given
        val ssin = "85050599890"

        every { waveUserApi.searchUsers(any()) } throws Exception("User not found")

        // When & Then
        assertThatThrownBy { waveUserService.getOperatorCode(ssin) }
            .isInstanceOf(WaveUserNotFoundException::class.java)
            .hasMessageContaining("Wave user not found for SSIN $ssin")
    }
}