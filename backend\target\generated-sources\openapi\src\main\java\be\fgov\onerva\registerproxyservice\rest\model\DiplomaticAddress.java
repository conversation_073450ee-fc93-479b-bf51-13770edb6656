/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.DiplomaticPostAddress;
import be.fgov.onerva.registerproxyservice.rest.model.DiplomaticPostNamesMap;
import be.fgov.onerva.registerproxyservice.rest.model.Period;
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * DiplomaticAddress
 */
@JsonPropertyOrder({
  DiplomaticAddress.JSON_PROPERTY_COUNTRY_CODE,
  DiplomaticAddress.JSON_PROPERTY_COUNTRY_CODE_I_S_O,
  DiplomaticAddress.JSON_PROPERTY_ADDRESS,
  DiplomaticAddress.JSON_PROPERTY_POST_CODE,
  DiplomaticAddress.JSON_PROPERTY_POST_COUNTRY_CODE,
  DiplomaticAddress.JSON_PROPERTY_DIPLOMATIC_POST_ADDRESSES,
  DiplomaticAddress.JSON_PROPERTY_DIPLOMATIC_POST_NAMES
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@JsonIgnoreProperties(
  value = "@type", // ignore manually set @type, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the @type to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "@type", visible = true)

public class DiplomaticAddress extends RegisterAddress {
  public static final String JSON_PROPERTY_COUNTRY_CODE = "countryCode";
  private Integer countryCode;

  public static final String JSON_PROPERTY_COUNTRY_CODE_I_S_O = "countryCodeISO";
  private String countryCodeISO;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private String address;

  public static final String JSON_PROPERTY_POST_CODE = "postCode";
  private Integer postCode;

  public static final String JSON_PROPERTY_POST_COUNTRY_CODE = "postCountryCode";
  private Integer postCountryCode;

  public static final String JSON_PROPERTY_DIPLOMATIC_POST_ADDRESSES = "diplomaticPostAddresses";
  private List<DiplomaticPostAddress> diplomaticPostAddresses = new ArrayList<>();

  public static final String JSON_PROPERTY_DIPLOMATIC_POST_NAMES = "diplomaticPostNames";
  private DiplomaticPostNamesMap diplomaticPostNames;

  public DiplomaticAddress() {

  }

  public DiplomaticAddress countryCode(Integer countryCode) {
    
    this.countryCode = countryCode;
    return this;
  }

  /**
   * Get countryCode
   * @return countryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCountryCode() {
    return countryCode;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCode(Integer countryCode) {
    this.countryCode = countryCode;
  }

  public DiplomaticAddress countryCodeISO(String countryCodeISO) {
    
    this.countryCodeISO = countryCodeISO;
    return this;
  }

  /**
   * Get countryCodeISO
   * @return countryCodeISO
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountryCodeISO() {
    return countryCodeISO;
  }


  @JsonProperty(JSON_PROPERTY_COUNTRY_CODE_I_S_O)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountryCodeISO(String countryCodeISO) {
    this.countryCodeISO = countryCodeISO;
  }

  public DiplomaticAddress address(String address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(String address) {
    this.address = address;
  }

  public DiplomaticAddress postCode(Integer postCode) {
    
    this.postCode = postCode;
    return this;
  }

  /**
   * Get postCode
   * @return postCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POST_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPostCode() {
    return postCode;
  }


  @JsonProperty(JSON_PROPERTY_POST_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostCode(Integer postCode) {
    this.postCode = postCode;
  }

  public DiplomaticAddress postCountryCode(Integer postCountryCode) {
    
    this.postCountryCode = postCountryCode;
    return this;
  }

  /**
   * Get postCountryCode
   * @return postCountryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POST_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPostCountryCode() {
    return postCountryCode;
  }


  @JsonProperty(JSON_PROPERTY_POST_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostCountryCode(Integer postCountryCode) {
    this.postCountryCode = postCountryCode;
  }

  public DiplomaticAddress diplomaticPostAddresses(List<DiplomaticPostAddress> diplomaticPostAddresses) {
    
    this.diplomaticPostAddresses = diplomaticPostAddresses;
    return this;
  }

  public DiplomaticAddress addDiplomaticPostAddressesItem(DiplomaticPostAddress diplomaticPostAddressesItem) {
    if (this.diplomaticPostAddresses == null) {
      this.diplomaticPostAddresses = new ArrayList<>();
    }
    this.diplomaticPostAddresses.add(diplomaticPostAddressesItem);
    return this;
  }

  /**
   * Get diplomaticPostAddresses
   * @return diplomaticPostAddresses
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DIPLOMATIC_POST_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<DiplomaticPostAddress> getDiplomaticPostAddresses() {
    return diplomaticPostAddresses;
  }


  @JsonProperty(JSON_PROPERTY_DIPLOMATIC_POST_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDiplomaticPostAddresses(List<DiplomaticPostAddress> diplomaticPostAddresses) {
    this.diplomaticPostAddresses = diplomaticPostAddresses;
  }

  public DiplomaticAddress diplomaticPostNames(DiplomaticPostNamesMap diplomaticPostNames) {
    
    this.diplomaticPostNames = diplomaticPostNames;
    return this;
  }

  /**
   * Get diplomaticPostNames
   * @return diplomaticPostNames
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DIPLOMATIC_POST_NAMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public DiplomaticPostNamesMap getDiplomaticPostNames() {
    return diplomaticPostNames;
  }


  @JsonProperty(JSON_PROPERTY_DIPLOMATIC_POST_NAMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDiplomaticPostNames(DiplomaticPostNamesMap diplomaticPostNames) {
    this.diplomaticPostNames = diplomaticPostNames;
  }

  @Override
  public DiplomaticAddress atType(AtTypeEnum atType) {
    this.setAtType(atType);
    return this;
  }

  @Override
  public DiplomaticAddress radiated(Boolean radiated) {
    this.setRadiated(radiated);
    return this;
  }

  @Override
  public DiplomaticAddress validityPeriod(Period validityPeriod) {
    this.setValidityPeriod(validityPeriod);
    return this;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DiplomaticAddress diplomaticAddress = (DiplomaticAddress) o;
    return Objects.equals(this.countryCode, diplomaticAddress.countryCode) &&
        Objects.equals(this.countryCodeISO, diplomaticAddress.countryCodeISO) &&
        Objects.equals(this.address, diplomaticAddress.address) &&
        Objects.equals(this.postCode, diplomaticAddress.postCode) &&
        Objects.equals(this.postCountryCode, diplomaticAddress.postCountryCode) &&
        Objects.equals(this.diplomaticPostAddresses, diplomaticAddress.diplomaticPostAddresses) &&
        Objects.equals(this.diplomaticPostNames, diplomaticAddress.diplomaticPostNames) &&
        super.equals(o);
  }

  @Override
  public int hashCode() {
    return Objects.hash(countryCode, countryCodeISO, address, postCode, postCountryCode, diplomaticPostAddresses, diplomaticPostNames, super.hashCode());
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DiplomaticAddress {\n");
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    sb.append("    countryCode: ").append(toIndentedString(countryCode)).append("\n");
    sb.append("    countryCodeISO: ").append(toIndentedString(countryCodeISO)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    postCode: ").append(toIndentedString(postCode)).append("\n");
    sb.append("    postCountryCode: ").append(toIndentedString(postCountryCode)).append("\n");
    sb.append("    diplomaticPostAddresses: ").append(toIndentedString(diplomaticPostAddresses)).append("\n");
    sb.append("    diplomaticPostNames: ").append(toIndentedString(diplomaticPostNames)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

