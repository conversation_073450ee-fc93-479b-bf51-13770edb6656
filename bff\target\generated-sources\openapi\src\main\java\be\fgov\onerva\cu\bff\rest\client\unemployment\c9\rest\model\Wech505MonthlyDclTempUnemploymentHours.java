/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.CalculationBaseAllowanceType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.ClosingPeriodType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.CommentDeclarationType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.DayNotEntitledToCompensationType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.DepartmentType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech505ReferencePeriod;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech505WorkGrid;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.WinterFormationType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505MonthlyDclTempUnemploymentHours
 */
@JsonPropertyOrder({
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_MAJOR_FORCE_REASON,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_DAY_NOT_ENTITLED_TO_COMPENSATION,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_WORK_GRID,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_REFERENCE_PERIOD,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_CLOSING_PERIOD,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_WINTER_FORMATION,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_DEPARTMENT,
  Wech505MonthlyDclTempUnemploymentHours.JSON_PROPERTY_COMMENT_DECLARATION
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505MonthlyDclTempUnemploymentHours {
  public static final String JSON_PROPERTY_MAJOR_FORCE_REASON = "majorForceReason";
  private String majorForceReason;

  public static final String JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D = "usingEmployerCompanyID";
  private String usingEmployerCompanyID;

  public static final String JSON_PROPERTY_DAY_NOT_ENTITLED_TO_COMPENSATION = "dayNotEntitledToCompensation";
  private List<DayNotEntitledToCompensationType> dayNotEntitledToCompensation = new ArrayList<>();

  public static final String JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE = "calculationBaseAllowance";
  private CalculationBaseAllowanceType calculationBaseAllowance;

  public static final String JSON_PROPERTY_WORK_GRID = "workGrid";
  private Wech505WorkGrid workGrid;

  public static final String JSON_PROPERTY_REFERENCE_PERIOD = "referencePeriod";
  private Wech505ReferencePeriod referencePeriod;

  public static final String JSON_PROPERTY_CLOSING_PERIOD = "closingPeriod";
  private List<ClosingPeriodType> closingPeriod;

  public static final String JSON_PROPERTY_WINTER_FORMATION = "winterFormation";
  private List<WinterFormationType> winterFormation = new ArrayList<>();

  public static final String JSON_PROPERTY_DEPARTMENT = "department";
  private List<DepartmentType> department;

  public static final String JSON_PROPERTY_COMMENT_DECLARATION = "commentDeclaration";
  private CommentDeclarationType commentDeclaration;

  public Wech505MonthlyDclTempUnemploymentHours() {
  }

  public Wech505MonthlyDclTempUnemploymentHours majorForceReason(String majorForceReason) {
    
    this.majorForceReason = majorForceReason;
    return this;
  }

  /**
   * Get majorForceReason
   * @return majorForceReason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMajorForceReason() {
    return majorForceReason;
  }


  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMajorForceReason(String majorForceReason) {
    this.majorForceReason = majorForceReason;
  }

  public Wech505MonthlyDclTempUnemploymentHours usingEmployerCompanyID(String usingEmployerCompanyID) {
    
    this.usingEmployerCompanyID = usingEmployerCompanyID;
    return this;
  }

  /**
   * Get usingEmployerCompanyID
   * @return usingEmployerCompanyID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsingEmployerCompanyID() {
    return usingEmployerCompanyID;
  }


  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsingEmployerCompanyID(String usingEmployerCompanyID) {
    this.usingEmployerCompanyID = usingEmployerCompanyID;
  }

  public Wech505MonthlyDclTempUnemploymentHours dayNotEntitledToCompensation(List<DayNotEntitledToCompensationType> dayNotEntitledToCompensation) {
    
    this.dayNotEntitledToCompensation = dayNotEntitledToCompensation;
    return this;
  }

  public Wech505MonthlyDclTempUnemploymentHours addDayNotEntitledToCompensationItem(DayNotEntitledToCompensationType dayNotEntitledToCompensationItem) {
    if (this.dayNotEntitledToCompensation == null) {
      this.dayNotEntitledToCompensation = new ArrayList<>();
    }
    this.dayNotEntitledToCompensation.add(dayNotEntitledToCompensationItem);
    return this;
  }

  /**
   * Get dayNotEntitledToCompensation
   * @return dayNotEntitledToCompensation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DAY_NOT_ENTITLED_TO_COMPENSATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<DayNotEntitledToCompensationType> getDayNotEntitledToCompensation() {
    return dayNotEntitledToCompensation;
  }


  @JsonProperty(JSON_PROPERTY_DAY_NOT_ENTITLED_TO_COMPENSATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDayNotEntitledToCompensation(List<DayNotEntitledToCompensationType> dayNotEntitledToCompensation) {
    this.dayNotEntitledToCompensation = dayNotEntitledToCompensation;
  }

  public Wech505MonthlyDclTempUnemploymentHours calculationBaseAllowance(CalculationBaseAllowanceType calculationBaseAllowance) {
    
    this.calculationBaseAllowance = calculationBaseAllowance;
    return this;
  }

  /**
   * Get calculationBaseAllowance
   * @return calculationBaseAllowance
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public CalculationBaseAllowanceType getCalculationBaseAllowance() {
    return calculationBaseAllowance;
  }


  @JsonProperty(JSON_PROPERTY_CALCULATION_BASE_ALLOWANCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCalculationBaseAllowance(CalculationBaseAllowanceType calculationBaseAllowance) {
    this.calculationBaseAllowance = calculationBaseAllowance;
  }

  public Wech505MonthlyDclTempUnemploymentHours workGrid(Wech505WorkGrid workGrid) {
    
    this.workGrid = workGrid;
    return this;
  }

  /**
   * Get workGrid
   * @return workGrid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Wech505WorkGrid getWorkGrid() {
    return workGrid;
  }


  @JsonProperty(JSON_PROPERTY_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkGrid(Wech505WorkGrid workGrid) {
    this.workGrid = workGrid;
  }

  public Wech505MonthlyDclTempUnemploymentHours referencePeriod(Wech505ReferencePeriod referencePeriod) {
    
    this.referencePeriod = referencePeriod;
    return this;
  }

  /**
   * Get referencePeriod
   * @return referencePeriod
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REFERENCE_PERIOD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505ReferencePeriod getReferencePeriod() {
    return referencePeriod;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE_PERIOD)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setReferencePeriod(Wech505ReferencePeriod referencePeriod) {
    this.referencePeriod = referencePeriod;
  }

  public Wech505MonthlyDclTempUnemploymentHours closingPeriod(List<ClosingPeriodType> closingPeriod) {
    
    this.closingPeriod = closingPeriod;
    return this;
  }

  public Wech505MonthlyDclTempUnemploymentHours addClosingPeriodItem(ClosingPeriodType closingPeriodItem) {
    if (this.closingPeriod == null) {
      this.closingPeriod = new ArrayList<>();
    }
    this.closingPeriod.add(closingPeriodItem);
    return this;
  }

  /**
   * Get closingPeriod
   * @return closingPeriod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ClosingPeriodType> getClosingPeriod() {
    return closingPeriod;
  }


  @JsonProperty(JSON_PROPERTY_CLOSING_PERIOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClosingPeriod(List<ClosingPeriodType> closingPeriod) {
    this.closingPeriod = closingPeriod;
  }

  public Wech505MonthlyDclTempUnemploymentHours winterFormation(List<WinterFormationType> winterFormation) {
    
    this.winterFormation = winterFormation;
    return this;
  }

  public Wech505MonthlyDclTempUnemploymentHours addWinterFormationItem(WinterFormationType winterFormationItem) {
    if (this.winterFormation == null) {
      this.winterFormation = new ArrayList<>();
    }
    this.winterFormation.add(winterFormationItem);
    return this;
  }

  /**
   * Get winterFormation
   * @return winterFormation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WINTER_FORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<WinterFormationType> getWinterFormation() {
    return winterFormation;
  }


  @JsonProperty(JSON_PROPERTY_WINTER_FORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWinterFormation(List<WinterFormationType> winterFormation) {
    this.winterFormation = winterFormation;
  }

  public Wech505MonthlyDclTempUnemploymentHours department(List<DepartmentType> department) {
    
    this.department = department;
    return this;
  }

  public Wech505MonthlyDclTempUnemploymentHours addDepartmentItem(DepartmentType departmentItem) {
    if (this.department == null) {
      this.department = new ArrayList<>();
    }
    this.department.add(departmentItem);
    return this;
  }

  /**
   * Get department
   * @return department
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DEPARTMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<DepartmentType> getDepartment() {
    return department;
  }


  @JsonProperty(JSON_PROPERTY_DEPARTMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDepartment(List<DepartmentType> department) {
    this.department = department;
  }

  public Wech505MonthlyDclTempUnemploymentHours commentDeclaration(CommentDeclarationType commentDeclaration) {
    
    this.commentDeclaration = commentDeclaration;
    return this;
  }

  /**
   * Get commentDeclaration
   * @return commentDeclaration
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENT_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CommentDeclarationType getCommentDeclaration() {
    return commentDeclaration;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommentDeclaration(CommentDeclarationType commentDeclaration) {
    this.commentDeclaration = commentDeclaration;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505MonthlyDclTempUnemploymentHours wech505MonthlyDclTempUnemploymentHours = (Wech505MonthlyDclTempUnemploymentHours) o;
    return Objects.equals(this.majorForceReason, wech505MonthlyDclTempUnemploymentHours.majorForceReason) &&
        Objects.equals(this.usingEmployerCompanyID, wech505MonthlyDclTempUnemploymentHours.usingEmployerCompanyID) &&
        Objects.equals(this.dayNotEntitledToCompensation, wech505MonthlyDclTempUnemploymentHours.dayNotEntitledToCompensation) &&
        Objects.equals(this.calculationBaseAllowance, wech505MonthlyDclTempUnemploymentHours.calculationBaseAllowance) &&
        Objects.equals(this.workGrid, wech505MonthlyDclTempUnemploymentHours.workGrid) &&
        Objects.equals(this.referencePeriod, wech505MonthlyDclTempUnemploymentHours.referencePeriod) &&
        Objects.equals(this.closingPeriod, wech505MonthlyDclTempUnemploymentHours.closingPeriod) &&
        Objects.equals(this.winterFormation, wech505MonthlyDclTempUnemploymentHours.winterFormation) &&
        Objects.equals(this.department, wech505MonthlyDclTempUnemploymentHours.department) &&
        Objects.equals(this.commentDeclaration, wech505MonthlyDclTempUnemploymentHours.commentDeclaration);
  }

  @Override
  public int hashCode() {
    return Objects.hash(majorForceReason, usingEmployerCompanyID, dayNotEntitledToCompensation, calculationBaseAllowance, workGrid, referencePeriod, closingPeriod, winterFormation, department, commentDeclaration);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505MonthlyDclTempUnemploymentHours {\n");
    sb.append("    majorForceReason: ").append(toIndentedString(majorForceReason)).append("\n");
    sb.append("    usingEmployerCompanyID: ").append(toIndentedString(usingEmployerCompanyID)).append("\n");
    sb.append("    dayNotEntitledToCompensation: ").append(toIndentedString(dayNotEntitledToCompensation)).append("\n");
    sb.append("    calculationBaseAllowance: ").append(toIndentedString(calculationBaseAllowance)).append("\n");
    sb.append("    workGrid: ").append(toIndentedString(workGrid)).append("\n");
    sb.append("    referencePeriod: ").append(toIndentedString(referencePeriod)).append("\n");
    sb.append("    closingPeriod: ").append(toIndentedString(closingPeriod)).append("\n");
    sb.append("    winterFormation: ").append(toIndentedString(winterFormation)).append("\n");
    sb.append("    department: ").append(toIndentedString(department)).append("\n");
    sb.append("    commentDeclaration: ").append(toIndentedString(commentDeclaration)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

