/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * NOSSLPAEmployerDeclarationLinkType
 */
@JsonPropertyOrder({
  NOSSLPAEmployerDeclarationLinkType.JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR,
  NOSSLPAEmployerDeclarationLinkType.JSON_PROPERTY_COMPANY_I_D
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NOSSLPAEmployerDeclarationLinkType {
  public static final String JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR = "nosslpaRegistrationNbr";
  private Integer nosslpaRegistrationNbr;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public NOSSLPAEmployerDeclarationLinkType() {
  }

  public NOSSLPAEmployerDeclarationLinkType nosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
    return this;
  }

  /**
   * Get nosslpaRegistrationNbr
   * @return nosslpaRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNosslpaRegistrationNbr() {
    return nosslpaRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
  }

  public NOSSLPAEmployerDeclarationLinkType companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NOSSLPAEmployerDeclarationLinkType noSSLPAEmployerDeclarationLinkType = (NOSSLPAEmployerDeclarationLinkType) o;
    return Objects.equals(this.nosslpaRegistrationNbr, noSSLPAEmployerDeclarationLinkType.nosslpaRegistrationNbr) &&
        Objects.equals(this.companyID, noSSLPAEmployerDeclarationLinkType.companyID);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nosslpaRegistrationNbr, companyID);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NOSSLPAEmployerDeclarationLinkType {\n");
    sb.append("    nosslpaRegistrationNbr: ").append(toIndentedString(nosslpaRegistrationNbr)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

