/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param propertySize 
 * @param page 
 */


data class PageableDTO (

    @get:JsonProperty("size")
    val propertySize: kotlin.Int? = null,

    @get:JsonProperty("page")
    val page: kotlin.Int? = null

) {


}

