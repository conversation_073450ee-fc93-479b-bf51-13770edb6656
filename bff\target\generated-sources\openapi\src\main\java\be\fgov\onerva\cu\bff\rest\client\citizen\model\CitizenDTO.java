/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Citizen information
 */
@JsonPropertyOrder({
  CitizenDTO.JSON_PROPERTY_NISS,
  CitizenDTO.JSON_PROPERTY_FIRSTNAME,
  CitizenDTO.JSON_PROPERTY_LASTNAME,
  CitizenDTO.JSON_PROPERTY_NUMBOX,
  CitizenDTO.JSON_PROPERTY_ZIP_CODE,
  CitizenDTO.JSON_PROPERTY_PENSION_NUMBER,
  CitizenDTO.JSON_PROPERTY_AGENT
})
@JsonTypeName("Citizen")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenDTO {
  public static final String JSON_PROPERTY_NISS = "niss";
  private String niss;

  public static final String JSON_PROPERTY_FIRSTNAME = "firstname";
  private String firstname;

  public static final String JSON_PROPERTY_LASTNAME = "lastname";
  private String lastname;

  public static final String JSON_PROPERTY_NUMBOX = "numbox";
  private Integer numbox;

  public static final String JSON_PROPERTY_ZIP_CODE = "zipCode";
  private Integer zipCode;

  public static final String JSON_PROPERTY_PENSION_NUMBER = "pensionNumber";
  private Integer pensionNumber;

  public static final String JSON_PROPERTY_AGENT = "agent";
  private Boolean agent;

  public CitizenDTO() {
  }

  public CitizenDTO niss(String niss) {
    
    this.niss = niss;
    return this;
  }

  /**
   * Get niss
   * @return niss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNiss() {
    return niss;
  }


  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNiss(String niss) {
    this.niss = niss;
  }

  public CitizenDTO firstname(String firstname) {
    
    this.firstname = firstname;
    return this;
  }

  /**
   * Get firstname
   * @return firstname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstname() {
    return firstname;
  }


  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstname(String firstname) {
    this.firstname = firstname;
  }

  public CitizenDTO lastname(String lastname) {
    
    this.lastname = lastname;
    return this;
  }

  /**
   * Get lastname
   * @return lastname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastname() {
    return lastname;
  }


  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastname(String lastname) {
    this.lastname = lastname;
  }

  public CitizenDTO numbox(Integer numbox) {
    
    this.numbox = numbox;
    return this;
  }

  /**
   * Get numbox
   * @return numbox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUMBOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNumbox() {
    return numbox;
  }


  @JsonProperty(JSON_PROPERTY_NUMBOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumbox(Integer numbox) {
    this.numbox = numbox;
  }

  public CitizenDTO zipCode(Integer zipCode) {
    
    this.zipCode = zipCode;
    return this;
  }

  /**
   * Get zipCode
   * @return zipCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getZipCode() {
    return zipCode;
  }


  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZipCode(Integer zipCode) {
    this.zipCode = zipCode;
  }

  public CitizenDTO pensionNumber(Integer pensionNumber) {
    
    this.pensionNumber = pensionNumber;
    return this;
  }

  /**
   * Get pensionNumber
   * @return pensionNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PENSION_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPensionNumber() {
    return pensionNumber;
  }


  @JsonProperty(JSON_PROPERTY_PENSION_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPensionNumber(Integer pensionNumber) {
    this.pensionNumber = pensionNumber;
  }

  public CitizenDTO agent(Boolean agent) {
    
    this.agent = agent;
    return this;
  }

  /**
   * Get agent
   * @return agent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AGENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAgent() {
    return agent;
  }


  @JsonProperty(JSON_PROPERTY_AGENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAgent(Boolean agent) {
    this.agent = agent;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenDTO citizen = (CitizenDTO) o;
    return Objects.equals(this.niss, citizen.niss) &&
        Objects.equals(this.firstname, citizen.firstname) &&
        Objects.equals(this.lastname, citizen.lastname) &&
        Objects.equals(this.numbox, citizen.numbox) &&
        Objects.equals(this.zipCode, citizen.zipCode) &&
        Objects.equals(this.pensionNumber, citizen.pensionNumber) &&
        Objects.equals(this.agent, citizen.agent);
  }

  @Override
  public int hashCode() {
    return Objects.hash(niss, firstname, lastname, numbox, zipCode, pensionNumber, agent);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenDTO {\n");
    sb.append("    niss: ").append(toIndentedString(niss)).append("\n");
    sb.append("    firstname: ").append(toIndentedString(firstname)).append("\n");
    sb.append("    lastname: ").append(toIndentedString(lastname)).append("\n");
    sb.append("    numbox: ").append(toIndentedString(numbox)).append("\n");
    sb.append("    zipCode: ").append(toIndentedString(zipCode)).append("\n");
    sb.append("    pensionNumber: ").append(toIndentedString(pensionNumber)).append("\n");
    sb.append("    agent: ").append(toIndentedString(agent)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

