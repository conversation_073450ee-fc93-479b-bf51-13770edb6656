/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * FormType
 */
@JsonPropertyOrder({
  FormType.JSON_PROPERTY_IDENTIFICATION,
  FormType.JSON_PROPERTY_FORM_CREATION_DATE,
  FormType.JSON_PROPERTY_FORM_CREATION_HOUR,
  FormType.JSON_PROPERTY_ATTESTATION_STATUS,
  FormType.JSON_PROPERTY_TYPE_FORM
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class FormType {
  public static final String JSON_PROPERTY_IDENTIFICATION = "identification";
  private String identification;

  public static final String JSON_PROPERTY_FORM_CREATION_DATE = "formCreationDate";
  private LocalDate formCreationDate;

  public static final String JSON_PROPERTY_FORM_CREATION_HOUR = "formCreationHour";
  private String formCreationHour;

  public static final String JSON_PROPERTY_ATTESTATION_STATUS = "attestationStatus";
  private String attestationStatus;

  /**
   * Gets or Sets typeForm
   */
  public enum TypeFormEnum {
    SU("SU");

    private String value;

    TypeFormEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeFormEnum fromValue(String value) {
      for (TypeFormEnum b : TypeFormEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_TYPE_FORM = "typeForm";
  private TypeFormEnum typeForm;

  public FormType() {
  }

  public FormType identification(String identification) {
    
    this.identification = identification;
    return this;
  }

  /**
   * Get identification
   * @return identification
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getIdentification() {
    return identification;
  }


  @JsonProperty(JSON_PROPERTY_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setIdentification(String identification) {
    this.identification = identification;
  }

  public FormType formCreationDate(LocalDate formCreationDate) {
    
    this.formCreationDate = formCreationDate;
    return this;
  }

  /**
   * Get formCreationDate
   * @return formCreationDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORM_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getFormCreationDate() {
    return formCreationDate;
  }


  @JsonProperty(JSON_PROPERTY_FORM_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormCreationDate(LocalDate formCreationDate) {
    this.formCreationDate = formCreationDate;
  }

  public FormType formCreationHour(String formCreationHour) {
    
    this.formCreationHour = formCreationHour;
    return this;
  }

  /**
   * Get formCreationHour
   * @return formCreationHour
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORM_CREATION_HOUR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getFormCreationHour() {
    return formCreationHour;
  }


  @JsonProperty(JSON_PROPERTY_FORM_CREATION_HOUR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormCreationHour(String formCreationHour) {
    this.formCreationHour = formCreationHour;
  }

  public FormType attestationStatus(String attestationStatus) {
    
    this.attestationStatus = attestationStatus;
    return this;
  }

  /**
   * Get attestationStatus
   * @return attestationStatus
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ATTESTATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getAttestationStatus() {
    return attestationStatus;
  }


  @JsonProperty(JSON_PROPERTY_ATTESTATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAttestationStatus(String attestationStatus) {
    this.attestationStatus = attestationStatus;
  }

  public FormType typeForm(TypeFormEnum typeForm) {
    
    this.typeForm = typeForm;
    return this;
  }

  /**
   * Get typeForm
   * @return typeForm
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public TypeFormEnum getTypeForm() {
    return typeForm;
  }


  @JsonProperty(JSON_PROPERTY_TYPE_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTypeForm(TypeFormEnum typeForm) {
    this.typeForm = typeForm;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FormType formType = (FormType) o;
    return Objects.equals(this.identification, formType.identification) &&
        Objects.equals(this.formCreationDate, formType.formCreationDate) &&
        Objects.equals(this.formCreationHour, formType.formCreationHour) &&
        Objects.equals(this.attestationStatus, formType.attestationStatus) &&
        Objects.equals(this.typeForm, formType.typeForm);
  }

  @Override
  public int hashCode() {
    return Objects.hash(identification, formCreationDate, formCreationHour, attestationStatus, typeForm);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FormType {\n");
    sb.append("    identification: ").append(toIndentedString(identification)).append("\n");
    sb.append("    formCreationDate: ").append(toIndentedString(formCreationDate)).append("\n");
    sb.append("    formCreationHour: ").append(toIndentedString(formCreationHour)).append("\n");
    sb.append("    attestationStatus: ").append(toIndentedString(attestationStatus)).append("\n");
    sb.append("    typeForm: ").append(toIndentedString(typeForm)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

