/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HandledEmployerDeclarationLinkType
 */
@JsonPropertyOrder({
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_NOSS_REGISTRATION_NBR,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_COMPANY_I_D,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_DENOMINATION,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_STREET,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_HOUSE_NBR,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_POST_BOX,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_Z_I_P_CODE,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_CITY,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_EMPLOYER_COUNTRY,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_LEGAL_FORM,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_IMPORTANCE_CODE,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_REGISTRATION_NBR,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_STREET,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_HOUSE_NBR,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_POST_BOX,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_Z_I_P_CODE,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_CITY,
  HandledEmployerDeclarationLinkType.JSON_PROPERTY_TRUSTEESHIP_COUNTRY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HandledEmployerDeclarationLinkType {
  public static final String JSON_PROPERTY_NOSS_REGISTRATION_NBR = "nossRegistrationNbr";
  private Integer nossRegistrationNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP = "trusteeship";
  private String trusteeship;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_EMPLOYER_DENOMINATION = "employerDenomination";
  private String employerDenomination;

  public static final String JSON_PROPERTY_EMPLOYER_STREET = "employerStreet";
  private String employerStreet;

  public static final String JSON_PROPERTY_EMPLOYER_HOUSE_NBR = "employerHouseNbr";
  private String employerHouseNbr;

  public static final String JSON_PROPERTY_EMPLOYER_POST_BOX = "employerPostBox";
  private String employerPostBox;

  public static final String JSON_PROPERTY_EMPLOYER_Z_I_P_CODE = "employerZIPCode";
  private String employerZIPCode;

  public static final String JSON_PROPERTY_EMPLOYER_CITY = "employerCity";
  private String employerCity;

  public static final String JSON_PROPERTY_EMPLOYER_COUNTRY = "employerCountry";
  private String employerCountry;

  public static final String JSON_PROPERTY_LEGAL_FORM = "legalForm";
  private String legalForm;

  public static final String JSON_PROPERTY_IMPORTANCE_CODE = "importanceCode";
  private String importanceCode;

  public static final String JSON_PROPERTY_TRUSTEESHIP_REGISTRATION_NBR = "trusteeshipRegistrationNbr";
  private Integer trusteeshipRegistrationNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP_STREET = "trusteeshipStreet";
  private String trusteeshipStreet;

  public static final String JSON_PROPERTY_TRUSTEESHIP_HOUSE_NBR = "trusteeshipHouseNbr";
  private String trusteeshipHouseNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP_POST_BOX = "trusteeshipPostBox";
  private String trusteeshipPostBox;

  public static final String JSON_PROPERTY_TRUSTEESHIP_Z_I_P_CODE = "trusteeshipZIPCode";
  private String trusteeshipZIPCode;

  public static final String JSON_PROPERTY_TRUSTEESHIP_CITY = "trusteeshipCity";
  private String trusteeshipCity;

  public static final String JSON_PROPERTY_TRUSTEESHIP_COUNTRY = "trusteeshipCountry";
  private String trusteeshipCountry;

  public HandledEmployerDeclarationLinkType() {
  }

  public HandledEmployerDeclarationLinkType nossRegistrationNbr(Integer nossRegistrationNbr) {
    
    this.nossRegistrationNbr = nossRegistrationNbr;
    return this;
  }

  /**
   * Get nossRegistrationNbr
   * @return nossRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNossRegistrationNbr() {
    return nossRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNossRegistrationNbr(Integer nossRegistrationNbr) {
    this.nossRegistrationNbr = nossRegistrationNbr;
  }

  public HandledEmployerDeclarationLinkType trusteeship(String trusteeship) {
    
    this.trusteeship = trusteeship;
    return this;
  }

  /**
   * Get trusteeship
   * @return trusteeship
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTrusteeship() {
    return trusteeship;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTrusteeship(String trusteeship) {
    this.trusteeship = trusteeship;
  }

  public HandledEmployerDeclarationLinkType companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public HandledEmployerDeclarationLinkType employerDenomination(String employerDenomination) {
    
    this.employerDenomination = employerDenomination;
    return this;
  }

  /**
   * Get employerDenomination
   * @return employerDenomination
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerDenomination() {
    return employerDenomination;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerDenomination(String employerDenomination) {
    this.employerDenomination = employerDenomination;
  }

  public HandledEmployerDeclarationLinkType employerStreet(String employerStreet) {
    
    this.employerStreet = employerStreet;
    return this;
  }

  /**
   * Get employerStreet
   * @return employerStreet
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerStreet() {
    return employerStreet;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerStreet(String employerStreet) {
    this.employerStreet = employerStreet;
  }

  public HandledEmployerDeclarationLinkType employerHouseNbr(String employerHouseNbr) {
    
    this.employerHouseNbr = employerHouseNbr;
    return this;
  }

  /**
   * Get employerHouseNbr
   * @return employerHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerHouseNbr() {
    return employerHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerHouseNbr(String employerHouseNbr) {
    this.employerHouseNbr = employerHouseNbr;
  }

  public HandledEmployerDeclarationLinkType employerPostBox(String employerPostBox) {
    
    this.employerPostBox = employerPostBox;
    return this;
  }

  /**
   * Get employerPostBox
   * @return employerPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerPostBox() {
    return employerPostBox;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerPostBox(String employerPostBox) {
    this.employerPostBox = employerPostBox;
  }

  public HandledEmployerDeclarationLinkType employerZIPCode(String employerZIPCode) {
    
    this.employerZIPCode = employerZIPCode;
    return this;
  }

  /**
   * Get employerZIPCode
   * @return employerZIPCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerZIPCode() {
    return employerZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerZIPCode(String employerZIPCode) {
    this.employerZIPCode = employerZIPCode;
  }

  public HandledEmployerDeclarationLinkType employerCity(String employerCity) {
    
    this.employerCity = employerCity;
    return this;
  }

  /**
   * Get employerCity
   * @return employerCity
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerCity() {
    return employerCity;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerCity(String employerCity) {
    this.employerCity = employerCity;
  }

  public HandledEmployerDeclarationLinkType employerCountry(String employerCountry) {
    
    this.employerCountry = employerCountry;
    return this;
  }

  /**
   * Get employerCountry
   * @return employerCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerCountry() {
    return employerCountry;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerCountry(String employerCountry) {
    this.employerCountry = employerCountry;
  }

  public HandledEmployerDeclarationLinkType legalForm(String legalForm) {
    
    this.legalForm = legalForm;
    return this;
  }

  /**
   * Get legalForm
   * @return legalForm
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LEGAL_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLegalForm() {
    return legalForm;
  }


  @JsonProperty(JSON_PROPERTY_LEGAL_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLegalForm(String legalForm) {
    this.legalForm = legalForm;
  }

  public HandledEmployerDeclarationLinkType importanceCode(String importanceCode) {
    
    this.importanceCode = importanceCode;
    return this;
  }

  /**
   * Get importanceCode
   * @return importanceCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getImportanceCode() {
    return importanceCode;
  }


  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setImportanceCode(String importanceCode) {
    this.importanceCode = importanceCode;
  }

  public HandledEmployerDeclarationLinkType trusteeshipRegistrationNbr(Integer trusteeshipRegistrationNbr) {
    
    this.trusteeshipRegistrationNbr = trusteeshipRegistrationNbr;
    return this;
  }

  /**
   * Get trusteeshipRegistrationNbr
   * @return trusteeshipRegistrationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTrusteeshipRegistrationNbr() {
    return trusteeshipRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipRegistrationNbr(Integer trusteeshipRegistrationNbr) {
    this.trusteeshipRegistrationNbr = trusteeshipRegistrationNbr;
  }

  public HandledEmployerDeclarationLinkType trusteeshipStreet(String trusteeshipStreet) {
    
    this.trusteeshipStreet = trusteeshipStreet;
    return this;
  }

  /**
   * Get trusteeshipStreet
   * @return trusteeshipStreet
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipStreet() {
    return trusteeshipStreet;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipStreet(String trusteeshipStreet) {
    this.trusteeshipStreet = trusteeshipStreet;
  }

  public HandledEmployerDeclarationLinkType trusteeshipHouseNbr(String trusteeshipHouseNbr) {
    
    this.trusteeshipHouseNbr = trusteeshipHouseNbr;
    return this;
  }

  /**
   * Get trusteeshipHouseNbr
   * @return trusteeshipHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipHouseNbr() {
    return trusteeshipHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipHouseNbr(String trusteeshipHouseNbr) {
    this.trusteeshipHouseNbr = trusteeshipHouseNbr;
  }

  public HandledEmployerDeclarationLinkType trusteeshipPostBox(String trusteeshipPostBox) {
    
    this.trusteeshipPostBox = trusteeshipPostBox;
    return this;
  }

  /**
   * Get trusteeshipPostBox
   * @return trusteeshipPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipPostBox() {
    return trusteeshipPostBox;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipPostBox(String trusteeshipPostBox) {
    this.trusteeshipPostBox = trusteeshipPostBox;
  }

  public HandledEmployerDeclarationLinkType trusteeshipZIPCode(String trusteeshipZIPCode) {
    
    this.trusteeshipZIPCode = trusteeshipZIPCode;
    return this;
  }

  /**
   * Get trusteeshipZIPCode
   * @return trusteeshipZIPCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipZIPCode() {
    return trusteeshipZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipZIPCode(String trusteeshipZIPCode) {
    this.trusteeshipZIPCode = trusteeshipZIPCode;
  }

  public HandledEmployerDeclarationLinkType trusteeshipCity(String trusteeshipCity) {
    
    this.trusteeshipCity = trusteeshipCity;
    return this;
  }

  /**
   * Get trusteeshipCity
   * @return trusteeshipCity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipCity() {
    return trusteeshipCity;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipCity(String trusteeshipCity) {
    this.trusteeshipCity = trusteeshipCity;
  }

  public HandledEmployerDeclarationLinkType trusteeshipCountry(String trusteeshipCountry) {
    
    this.trusteeshipCountry = trusteeshipCountry;
    return this;
  }

  /**
   * Get trusteeshipCountry
   * @return trusteeshipCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTrusteeshipCountry() {
    return trusteeshipCountry;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTrusteeshipCountry(String trusteeshipCountry) {
    this.trusteeshipCountry = trusteeshipCountry;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandledEmployerDeclarationLinkType handledEmployerDeclarationLinkType = (HandledEmployerDeclarationLinkType) o;
    return Objects.equals(this.nossRegistrationNbr, handledEmployerDeclarationLinkType.nossRegistrationNbr) &&
        Objects.equals(this.trusteeship, handledEmployerDeclarationLinkType.trusteeship) &&
        Objects.equals(this.companyID, handledEmployerDeclarationLinkType.companyID) &&
        Objects.equals(this.employerDenomination, handledEmployerDeclarationLinkType.employerDenomination) &&
        Objects.equals(this.employerStreet, handledEmployerDeclarationLinkType.employerStreet) &&
        Objects.equals(this.employerHouseNbr, handledEmployerDeclarationLinkType.employerHouseNbr) &&
        Objects.equals(this.employerPostBox, handledEmployerDeclarationLinkType.employerPostBox) &&
        Objects.equals(this.employerZIPCode, handledEmployerDeclarationLinkType.employerZIPCode) &&
        Objects.equals(this.employerCity, handledEmployerDeclarationLinkType.employerCity) &&
        Objects.equals(this.employerCountry, handledEmployerDeclarationLinkType.employerCountry) &&
        Objects.equals(this.legalForm, handledEmployerDeclarationLinkType.legalForm) &&
        Objects.equals(this.importanceCode, handledEmployerDeclarationLinkType.importanceCode) &&
        Objects.equals(this.trusteeshipRegistrationNbr, handledEmployerDeclarationLinkType.trusteeshipRegistrationNbr) &&
        Objects.equals(this.trusteeshipStreet, handledEmployerDeclarationLinkType.trusteeshipStreet) &&
        Objects.equals(this.trusteeshipHouseNbr, handledEmployerDeclarationLinkType.trusteeshipHouseNbr) &&
        Objects.equals(this.trusteeshipPostBox, handledEmployerDeclarationLinkType.trusteeshipPostBox) &&
        Objects.equals(this.trusteeshipZIPCode, handledEmployerDeclarationLinkType.trusteeshipZIPCode) &&
        Objects.equals(this.trusteeshipCity, handledEmployerDeclarationLinkType.trusteeshipCity) &&
        Objects.equals(this.trusteeshipCountry, handledEmployerDeclarationLinkType.trusteeshipCountry);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nossRegistrationNbr, trusteeship, companyID, employerDenomination, employerStreet, employerHouseNbr, employerPostBox, employerZIPCode, employerCity, employerCountry, legalForm, importanceCode, trusteeshipRegistrationNbr, trusteeshipStreet, trusteeshipHouseNbr, trusteeshipPostBox, trusteeshipZIPCode, trusteeshipCity, trusteeshipCountry);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandledEmployerDeclarationLinkType {\n");
    sb.append("    nossRegistrationNbr: ").append(toIndentedString(nossRegistrationNbr)).append("\n");
    sb.append("    trusteeship: ").append(toIndentedString(trusteeship)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    employerDenomination: ").append(toIndentedString(employerDenomination)).append("\n");
    sb.append("    employerStreet: ").append(toIndentedString(employerStreet)).append("\n");
    sb.append("    employerHouseNbr: ").append(toIndentedString(employerHouseNbr)).append("\n");
    sb.append("    employerPostBox: ").append(toIndentedString(employerPostBox)).append("\n");
    sb.append("    employerZIPCode: ").append(toIndentedString(employerZIPCode)).append("\n");
    sb.append("    employerCity: ").append(toIndentedString(employerCity)).append("\n");
    sb.append("    employerCountry: ").append(toIndentedString(employerCountry)).append("\n");
    sb.append("    legalForm: ").append(toIndentedString(legalForm)).append("\n");
    sb.append("    importanceCode: ").append(toIndentedString(importanceCode)).append("\n");
    sb.append("    trusteeshipRegistrationNbr: ").append(toIndentedString(trusteeshipRegistrationNbr)).append("\n");
    sb.append("    trusteeshipStreet: ").append(toIndentedString(trusteeshipStreet)).append("\n");
    sb.append("    trusteeshipHouseNbr: ").append(toIndentedString(trusteeshipHouseNbr)).append("\n");
    sb.append("    trusteeshipPostBox: ").append(toIndentedString(trusteeshipPostBox)).append("\n");
    sb.append("    trusteeshipZIPCode: ").append(toIndentedString(trusteeshipZIPCode)).append("\n");
    sb.append("    trusteeshipCity: ").append(toIndentedString(trusteeshipCity)).append("\n");
    sb.append("    trusteeshipCountry: ").append(toIndentedString(trusteeshipCountry)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

