/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.AddressDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.PaymentTypeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Citizen creation request
 */
@JsonPropertyOrder({
  CitizenRequestDTO.JSON_PROPERTY_ID,
  CitizenRequestDTO.JSON_PROPERTY_CREATED,
  CitizenRequestDTO.JSON_PROPERTY_NISS,
  CitizenRequestDTO.JSON_PROPERTY_FIRSTNAME,
  CitizenRequestDTO.JSON_PROPERTY_LASTNAME,
  CitizenRequestDTO.JSON_PROPERTY_CORRELATION_ID,
  CitizenRequestDTO.JSON_PROPERTY_SENT,
  CitizenRequestDTO.JSON_PROPERTY_UPDATED,
  CitizenRequestDTO.JSON_PROPERTY_RETRY_COUNT,
  CitizenRequestDTO.JSON_PROPERTY_RETURN_CODE,
  CitizenRequestDTO.JSON_PROPERTY_ERROR,
  CitizenRequestDTO.JSON_PROPERTY_TYPE,
  CitizenRequestDTO.JSON_PROPERTY_NATIONALITY_CODE,
  CitizenRequestDTO.JSON_PROPERTY_PAYMENT_TYPE,
  CitizenRequestDTO.JSON_PROPERTY_UNION_DUE,
  CitizenRequestDTO.JSON_PROPERTY_VALUE_DATE,
  CitizenRequestDTO.JSON_PROPERTY_ADDRESS,
  CitizenRequestDTO.JSON_PROPERTY_USERNAME
})
@JsonTypeName("CitizenRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@com.fasterxml.jackson.annotation.JsonInclude(com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL)
public class CitizenRequestDTO {
  public static final String JSON_PROPERTY_ID = "id";
  private BigDecimal id;

  public static final String JSON_PROPERTY_CREATED = "created";
  private LocalDateTime created;

  public static final String JSON_PROPERTY_NISS = "niss";
  private String niss;

  public static final String JSON_PROPERTY_FIRSTNAME = "firstname";
  private String firstname;

  public static final String JSON_PROPERTY_LASTNAME = "lastname";
  private String lastname;

  public static final String JSON_PROPERTY_CORRELATION_ID = "correlationId";
  private String correlationId;

  public static final String JSON_PROPERTY_SENT = "sent";
  private Boolean sent;

  public static final String JSON_PROPERTY_UPDATED = "updated";
  private LocalDateTime updated;

  public static final String JSON_PROPERTY_RETRY_COUNT = "retryCount";
  private Integer retryCount;

  public static final String JSON_PROPERTY_RETURN_CODE = "returnCode";
  private Integer returnCode;

  public static final String JSON_PROPERTY_ERROR = "error";
  private String error;

  /**
   * Gets or Sets type
   */
  public enum TypeEnum {
    CREATE("CREATE"),
    
    UPDATE("UPDATE");

    private String value;

    TypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeEnum fromValue(String value) {
      for (TypeEnum b : TypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_TYPE = "type";
  private TypeEnum type;

  public static final String JSON_PROPERTY_NATIONALITY_CODE = "nationalityCode";
  private Integer nationalityCode;

  public static final String JSON_PROPERTY_PAYMENT_TYPE = "paymentType";
  private PaymentTypeDTO paymentType;

  public static final String JSON_PROPERTY_UNION_DUE = "unionDue";
  private Boolean unionDue;

  public static final String JSON_PROPERTY_VALUE_DATE = "valueDate";
  private LocalDate valueDate;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private AddressDTO address;

  public static final String JSON_PROPERTY_USERNAME = "username";
  private String username;

  public CitizenRequestDTO() {
  }

  public CitizenRequestDTO id(BigDecimal id) {
    
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public BigDecimal getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(BigDecimal id) {
    this.id = id;
  }

  public CitizenRequestDTO created(LocalDateTime created) {
    
    this.created = created;
    return this;
  }

  /**
   * Get created
   * @return created
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CREATED)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getCreated() {
    return created;
  }


  @JsonProperty(JSON_PROPERTY_CREATED)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCreated(LocalDateTime created) {
    this.created = created;
  }

  public CitizenRequestDTO niss(String niss) {
    
    this.niss = niss;
    return this;
  }

  /**
   * Get niss
   * @return niss
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNiss() {
    return niss;
  }


  @JsonProperty(JSON_PROPERTY_NISS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNiss(String niss) {
    this.niss = niss;
  }

  public CitizenRequestDTO firstname(String firstname) {
    
    this.firstname = firstname;
    return this;
  }

  /**
   * Get firstname
   * @return firstname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstname() {
    return firstname;
  }


  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstname(String firstname) {
    this.firstname = firstname;
  }

  public CitizenRequestDTO lastname(String lastname) {
    
    this.lastname = lastname;
    return this;
  }

  /**
   * Get lastname
   * @return lastname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastname() {
    return lastname;
  }


  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastname(String lastname) {
    this.lastname = lastname;
  }

  public CitizenRequestDTO correlationId(String correlationId) {
    
    this.correlationId = correlationId;
    return this;
  }

  /**
   * Get correlationId
   * @return correlationId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCorrelationId() {
    return correlationId;
  }


  @JsonProperty(JSON_PROPERTY_CORRELATION_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCorrelationId(String correlationId) {
    this.correlationId = correlationId;
  }

  public CitizenRequestDTO sent(Boolean sent) {
    
    this.sent = sent;
    return this;
  }

  /**
   * Get sent
   * @return sent
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SENT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Boolean getSent() {
    return sent;
  }


  @JsonProperty(JSON_PROPERTY_SENT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSent(Boolean sent) {
    this.sent = sent;
  }

  public CitizenRequestDTO updated(LocalDateTime updated) {
    
    this.updated = updated;
    return this;
  }

  /**
   * Get updated
   * @return updated
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UPDATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getUpdated() {
    return updated;
  }


  @JsonProperty(JSON_PROPERTY_UPDATED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUpdated(LocalDateTime updated) {
    this.updated = updated;
  }

  public CitizenRequestDTO retryCount(Integer retryCount) {
    
    this.retryCount = retryCount;
    return this;
  }

  /**
   * Get retryCount
   * @return retryCount
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RETRY_COUNT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getRetryCount() {
    return retryCount;
  }


  @JsonProperty(JSON_PROPERTY_RETRY_COUNT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRetryCount(Integer retryCount) {
    this.retryCount = retryCount;
  }

  public CitizenRequestDTO returnCode(Integer returnCode) {
    
    this.returnCode = returnCode;
    return this;
  }

  /**
   * Get returnCode
   * @return returnCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETURN_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getReturnCode() {
    return returnCode;
  }


  @JsonProperty(JSON_PROPERTY_RETURN_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReturnCode(Integer returnCode) {
    this.returnCode = returnCode;
  }

  public CitizenRequestDTO error(String error) {
    
    this.error = error;
    return this;
  }

  /**
   * Get error
   * @return error
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ERROR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getError() {
    return error;
  }


  @JsonProperty(JSON_PROPERTY_ERROR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setError(String error) {
    this.error = error;
  }

  public CitizenRequestDTO type(TypeEnum type) {
    
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public TypeEnum getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setType(TypeEnum type) {
    this.type = type;
  }

  public CitizenRequestDTO nationalityCode(Integer nationalityCode) {
    
    this.nationalityCode = nationalityCode;
    return this;
  }

  /**
   * Get nationalityCode
   * @return nationalityCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNationalityCode() {
    return nationalityCode;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationalityCode(Integer nationalityCode) {
    this.nationalityCode = nationalityCode;
  }

  public CitizenRequestDTO paymentType(PaymentTypeDTO paymentType) {
    
    this.paymentType = paymentType;
    return this;
  }

  /**
   * Get paymentType
   * @return paymentType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PaymentTypeDTO getPaymentType() {
    return paymentType;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentType(PaymentTypeDTO paymentType) {
    this.paymentType = paymentType;
  }

  public CitizenRequestDTO unionDue(Boolean unionDue) {
    
    this.unionDue = unionDue;
    return this;
  }

  /**
   * Get unionDue
   * @return unionDue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getUnionDue() {
    return unionDue;
  }


  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnionDue(Boolean unionDue) {
    this.unionDue = unionDue;
  }

  public CitizenRequestDTO valueDate(LocalDate valueDate) {
    
    this.valueDate = valueDate;
    return this;
  }

  /**
   * Get valueDate
   * @return valueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getValueDate() {
    return valueDate;
  }


  @JsonProperty(JSON_PROPERTY_VALUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValueDate(LocalDate valueDate) {
    this.valueDate = valueDate;
  }

  public CitizenRequestDTO address(AddressDTO address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public AddressDTO getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(AddressDTO address) {
    this.address = address;
  }

  public CitizenRequestDTO username(String username) {
    
    this.username = username;
    return this;
  }

  /**
   * Get username
   * @return username
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsername() {
    return username;
  }


  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsername(String username) {
    this.username = username;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenRequestDTO citizenRequest = (CitizenRequestDTO) o;
    return Objects.equals(this.id, citizenRequest.id) &&
        Objects.equals(this.created, citizenRequest.created) &&
        Objects.equals(this.niss, citizenRequest.niss) &&
        Objects.equals(this.firstname, citizenRequest.firstname) &&
        Objects.equals(this.lastname, citizenRequest.lastname) &&
        Objects.equals(this.correlationId, citizenRequest.correlationId) &&
        Objects.equals(this.sent, citizenRequest.sent) &&
        Objects.equals(this.updated, citizenRequest.updated) &&
        Objects.equals(this.retryCount, citizenRequest.retryCount) &&
        Objects.equals(this.returnCode, citizenRequest.returnCode) &&
        Objects.equals(this.error, citizenRequest.error) &&
        Objects.equals(this.type, citizenRequest.type) &&
        Objects.equals(this.nationalityCode, citizenRequest.nationalityCode) &&
        Objects.equals(this.paymentType, citizenRequest.paymentType) &&
        Objects.equals(this.unionDue, citizenRequest.unionDue) &&
        Objects.equals(this.valueDate, citizenRequest.valueDate) &&
        Objects.equals(this.address, citizenRequest.address) &&
        Objects.equals(this.username, citizenRequest.username);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, created, niss, firstname, lastname, correlationId, sent, updated, retryCount, returnCode, error, type, nationalityCode, paymentType, unionDue, valueDate, address, username);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenRequestDTO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    created: ").append(toIndentedString(created)).append("\n");
    sb.append("    niss: ").append(toIndentedString(niss)).append("\n");
    sb.append("    firstname: ").append(toIndentedString(firstname)).append("\n");
    sb.append("    lastname: ").append(toIndentedString(lastname)).append("\n");
    sb.append("    correlationId: ").append(toIndentedString(correlationId)).append("\n");
    sb.append("    sent: ").append(toIndentedString(sent)).append("\n");
    sb.append("    updated: ").append(toIndentedString(updated)).append("\n");
    sb.append("    retryCount: ").append(toIndentedString(retryCount)).append("\n");
    sb.append("    returnCode: ").append(toIndentedString(returnCode)).append("\n");
    sb.append("    error: ").append(toIndentedString(error)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    nationalityCode: ").append(toIndentedString(nationalityCode)).append("\n");
    sb.append("    paymentType: ").append(toIndentedString(paymentType)).append("\n");
    sb.append("    unionDue: ").append(toIndentedString(unionDue)).append("\n");
    sb.append("    valueDate: ").append(toIndentedString(valueDate)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

