
### Get historical citizen information from ONEM
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/historical/citizen-information/ONEM
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get historical citizen information from C1
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/historical/citizen-information/C1
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get historical citizen information from ONEM
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}
GET {{backend-url}}/api/requests/{{latestRequestId}}/historical/barema
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

### Get historical citizen information from Registry/Authentic sources
< {%
    let latestRequestId = client.global.get('latestRequestId');
    console.log('latestRequestId: ' + latestRequestId);
    request.variables.set('latestRequestId', latestRequestId);
%}

GET {{backend-url}}/api/requests/{{latestRequestId}}/historical/citizen-information/AUTHENTIC_SOURCES
Authorization: Bearer {{$auth.token("cu_user")}}
Content-Type: application/json

