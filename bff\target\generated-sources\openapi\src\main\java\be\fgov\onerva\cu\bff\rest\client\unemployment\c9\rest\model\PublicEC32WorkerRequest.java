/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PublicEC32WorkerRequest
 */
@JsonPropertyOrder({
  PublicEC32WorkerRequest.JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE,
  PublicEC32WorkerRequest.JSON_PROPERTY_PROFILE
})
@JsonTypeName("publicEC32WorkerRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PublicEC32WorkerRequest {
  public static final String JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE = "askAllowanceFromDate";
  private LocalDate askAllowanceFromDate;

  /**
   * Gets or Sets profile
   */
  public enum ProfileEnum {
    WORKER("WORKER"),
    
    APPRENTICE("APPRENTICE");

    private String value;

    ProfileEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static ProfileEnum fromValue(String value) {
      for (ProfileEnum b : ProfileEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_PROFILE = "profile";
  private ProfileEnum profile;

  public PublicEC32WorkerRequest() {
  }

  public PublicEC32WorkerRequest askAllowanceFromDate(LocalDate askAllowanceFromDate) {
    
    this.askAllowanceFromDate = askAllowanceFromDate;
    return this;
  }

  /**
   * Get askAllowanceFromDate
   * @return askAllowanceFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getAskAllowanceFromDate() {
    return askAllowanceFromDate;
  }


  @JsonProperty(JSON_PROPERTY_ASK_ALLOWANCE_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAskAllowanceFromDate(LocalDate askAllowanceFromDate) {
    this.askAllowanceFromDate = askAllowanceFromDate;
  }

  public PublicEC32WorkerRequest profile(ProfileEnum profile) {
    
    this.profile = profile;
    return this;
  }

  /**
   * Get profile
   * @return profile
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROFILE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ProfileEnum getProfile() {
    return profile;
  }


  @JsonProperty(JSON_PROPERTY_PROFILE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProfile(ProfileEnum profile) {
    this.profile = profile;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PublicEC32WorkerRequest publicEC32WorkerRequest = (PublicEC32WorkerRequest) o;
    return Objects.equals(this.askAllowanceFromDate, publicEC32WorkerRequest.askAllowanceFromDate) &&
        Objects.equals(this.profile, publicEC32WorkerRequest.profile);
  }

  @Override
  public int hashCode() {
    return Objects.hash(askAllowanceFromDate, profile);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PublicEC32WorkerRequest {\n");
    sb.append("    askAllowanceFromDate: ").append(toIndentedString(askAllowanceFromDate)).append("\n");
    sb.append("    profile: ").append(toIndentedString(profile)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

