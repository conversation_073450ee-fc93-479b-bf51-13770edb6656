/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param closeProcess 
 * @param forceCloseTask 
 * @param forceCloseProcess 
 */


data class CloseTaskOptionsDTO (

    @get:JsonProperty("closeProcess")
    val closeProcess: kotlin.Boolean? = false,

    @get:JsonProperty("forceCloseTask")
    val forceCloseTask: kotlin.Boolean? = false,

    @get:JsonProperty("forceCloseProcess")
    val forceCloseProcess: kotlin.Boolean? = false

) {


}

