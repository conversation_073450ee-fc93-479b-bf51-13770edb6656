/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ProfessionalActivity
 */
@JsonPropertyOrder({
  ProfessionalActivity.JSON_PROPERTY_NATURE_PROFESSIONAL_ACTIVITY,
  ProfessionalActivity.JSON_PROPERTY_GROSS_MONTHLY_PROFESSIONAL_INCOME
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ProfessionalActivity {
  public static final String JSON_PROPERTY_NATURE_PROFESSIONAL_ACTIVITY = "natureProfessionalActivity";
  private String natureProfessionalActivity;

  public static final String JSON_PROPERTY_GROSS_MONTHLY_PROFESSIONAL_INCOME = "grossMonthlyProfessionalIncome";
  private String grossMonthlyProfessionalIncome;

  public ProfessionalActivity() {
  }

  public ProfessionalActivity natureProfessionalActivity(String natureProfessionalActivity) {
    
    this.natureProfessionalActivity = natureProfessionalActivity;
    return this;
  }

  /**
   * Get natureProfessionalActivity
   * @return natureProfessionalActivity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURE_PROFESSIONAL_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNatureProfessionalActivity() {
    return natureProfessionalActivity;
  }


  @JsonProperty(JSON_PROPERTY_NATURE_PROFESSIONAL_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNatureProfessionalActivity(String natureProfessionalActivity) {
    this.natureProfessionalActivity = natureProfessionalActivity;
  }

  public ProfessionalActivity grossMonthlyProfessionalIncome(String grossMonthlyProfessionalIncome) {
    
    this.grossMonthlyProfessionalIncome = grossMonthlyProfessionalIncome;
    return this;
  }

  /**
   * Get grossMonthlyProfessionalIncome
   * @return grossMonthlyProfessionalIncome
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GROSS_MONTHLY_PROFESSIONAL_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGrossMonthlyProfessionalIncome() {
    return grossMonthlyProfessionalIncome;
  }


  @JsonProperty(JSON_PROPERTY_GROSS_MONTHLY_PROFESSIONAL_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGrossMonthlyProfessionalIncome(String grossMonthlyProfessionalIncome) {
    this.grossMonthlyProfessionalIncome = grossMonthlyProfessionalIncome;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ProfessionalActivity professionalActivity = (ProfessionalActivity) o;
    return Objects.equals(this.natureProfessionalActivity, professionalActivity.natureProfessionalActivity) &&
        Objects.equals(this.grossMonthlyProfessionalIncome, professionalActivity.grossMonthlyProfessionalIncome);
  }

  @Override
  public int hashCode() {
    return Objects.hash(natureProfessionalActivity, grossMonthlyProfessionalIncome);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ProfessionalActivity {\n");
    sb.append("    natureProfessionalActivity: ").append(toIndentedString(natureProfessionalActivity)).append("\n");
    sb.append("    grossMonthlyProfessionalIncome: ").append(toIndentedString(grossMonthlyProfessionalIncome)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

