/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RiskIdentificationType
 */
@JsonPropertyOrder({
  RiskIdentificationType.JSON_PROPERTY_IDENTIFICATION_OF_RISK
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RiskIdentificationType {
  public static final String JSON_PROPERTY_IDENTIFICATION_OF_RISK = "identificationOfRisk";
  private String identificationOfRisk;

  public RiskIdentificationType() {
  }

  public RiskIdentificationType identificationOfRisk(String identificationOfRisk) {
    
    this.identificationOfRisk = identificationOfRisk;
    return this;
  }

  /**
   * Get identificationOfRisk
   * @return identificationOfRisk
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_IDENTIFICATION_OF_RISK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getIdentificationOfRisk() {
    return identificationOfRisk;
  }


  @JsonProperty(JSON_PROPERTY_IDENTIFICATION_OF_RISK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setIdentificationOfRisk(String identificationOfRisk) {
    this.identificationOfRisk = identificationOfRisk;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RiskIdentificationType riskIdentificationType = (RiskIdentificationType) o;
    return Objects.equals(this.identificationOfRisk, riskIdentificationType.identificationOfRisk);
  }

  @Override
  public int hashCode() {
    return Objects.hash(identificationOfRisk);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RiskIdentificationType {\n");
    sb.append("    identificationOfRisk: ").append(toIndentedString(identificationOfRisk)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

