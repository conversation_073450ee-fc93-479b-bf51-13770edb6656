Feature: Data consistency test

  Background:
    Given I have test data

  Scenario: The data consistency works
    Then I go to the cdf
    And I fill in my credentials
    When I Insert 'LT 12 1000 0111 0100 1000' in the 'ibanInput' field
    When I Insert 'LIABLT2XXXX' in the 'bicInput' field
    And I click the 'validateRequestButton' button
    Then should verify data in each row of the table
    And I see that the validate and continue button is 'disabled'
    Then I check the regis verification checkbox
    When I go to row 'Houder' and click Corrigeren
    Then The dialog should be visible
    And I select the first value and click 'Valideren'
    Then The dialog should be closed successfully
    Then The value to maintain of row 'Houder' should contain 'Karim Benzema'
    When I go to row 'Bankrekeningnummer' and click Corrigeren
    Then The dialog should be visible
    And I select the first value and click '<PERSON>ideren'
    Then The dialog should be closed successfully
    When I go to row 'Geboortedatum' and click Corrigeren
    Then The dialog should be visible
    And I select the first value and click 'Valideren'
    Then The dialog should be closed successfully
    When I go to row '<PERSON>res' and click Corrigeren
    Then The dialog should be visible
    And I select the first value and click '<PERSON>ideren'
    Then The dialog should be closed successfully
    Then The value to maintain of row 'Bankrekeningnummer' should contain 'LT12 1000 0111 0100 1000'
    And The value to maintain of row 'Bankrekeningnummer' should contain 'BIC: LIABLT2XXXX'
    And I see that the validate and continue button is 'enabled'
    And The page is accessible