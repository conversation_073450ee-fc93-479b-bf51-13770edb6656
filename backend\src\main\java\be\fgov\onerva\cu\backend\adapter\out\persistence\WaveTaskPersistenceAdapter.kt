package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.WaveTaskTypeResolver
import be.fgov.onerva.cu.backend.adapter.out.mapper.toChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.mapper.toChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainWaveTask
import be.fgov.onerva.cu.backend.adapter.out.mapper.toWaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.WaveTaskRepository
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.SensitiveParam
import be.fgov.onerva.cu.common.utils.logger

@Service
@Transactional
class WaveTaskPersistenceAdapter(
    val requestRepository: RequestRepository,
    val waveTaskRepository: WaveTaskRepository,
    private val taskTypeResolver: WaveTaskTypeResolver
) : WaveTaskPersistencePort {
    private val log = logger

    @LogMethodCall
    override fun persistChangePersonalDataCaptureWaveTask(requestId: UUID, @SensitiveParam waveTask: WaveTask) {
        val requestEntity = requestRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        val waveTaskEntity = waveTask.toChangePersonalDataCaptureWaveTaskEntity(requestEntity)
        waveTaskRepository.save(waveTaskEntity)
    }

    @LogMethodCall
    override fun closeWaveTaskChangePersonalDataCapture(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        internalChangeWaveTaskChangePersonalDataCapture(
            requestId,
            WaveTaskStatus.CLOSED,
            citizenInformationLatestRevision,
            modeOfPaymentLatestRevision,
            unionContributionLatestRevision,
            requestInformationLatestRevision
        )
    }

    private fun internalChangeWaveTaskChangePersonalDataCapture(
        requestId: UUID,
        newStatus: WaveTaskStatus,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        val waveTaskEntity = waveTaskRepository.findAllByRequestId(requestId).firstOrNull {
            it is ChangePersonalDataCaptureWaveTaskEntity
        } as ChangePersonalDataCaptureWaveTaskEntity?
            ?: throw WaveTaskNotFoundException("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

        waveTaskEntity.apply {
            status = newStatus
            citizenInformationRevisionNumber = citizenInformationLatestRevision
            modeOfPaymentRevisionNumber = modeOfPaymentLatestRevision
            unionContributionRevisionNumber = unionContributionLatestRevision
            requestInformationRevisionNumber = requestInformationLatestRevision
        }
    }

    override fun sleepWaveTaskChangePersonalDataCapture(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        internalChangeWaveTaskChangePersonalDataCapture(
            requestId,
            WaveTaskStatus.WAITING,
            citizenInformationLatestRevision,
            modeOfPaymentLatestRevision,
            unionContributionLatestRevision,
            requestInformationLatestRevision
        )
    }

    @LogMethodCall
    override fun closeWaveTaskChangePersonalDataValidate(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        internalChangeWaveTaskChangePersonalDataValidate(
            requestId,
            WaveTaskStatus.CLOSED,
            citizenInformationLatestRevision,
            modeOfPaymentLatestRevision,
            unionContributionLatestRevision,
            requestInformationLatestRevision
        )
    }

    private fun internalChangeWaveTaskChangePersonalDataValidate(
        requestId: UUID,
        newStatus: WaveTaskStatus,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        val waveTaskEntity = waveTaskRepository.findAllByRequestId(requestId).firstOrNull {
            it is ChangePersonalDataValidateWaveTaskEntity
        } as ChangePersonalDataValidateWaveTaskEntity?
            ?: throw WaveTaskNotFoundException("Wave task ChangePersonalDataCaptureWaveTaskEntity not found for request $requestId")

        waveTaskEntity.apply {
            status = newStatus
            citizenInformationRevisionNumber = citizenInformationLatestRevision
            modeOfPaymentRevisionNumber = modeOfPaymentLatestRevision
            unionContributionRevisionNumber = unionContributionLatestRevision
            requestInformationRevisionNumber = requestInformationLatestRevision
        }
    }

    override fun sleepWaveTaskChangePersonalDataValidate(
        requestId: UUID,
        citizenInformationLatestRevision: Int,
        modeOfPaymentLatestRevision: Int,
        unionContributionLatestRevision: Int,
        requestInformationLatestRevision: Int,
    ) {
        internalChangeWaveTaskChangePersonalDataValidate(
            requestId,
            WaveTaskStatus.WAITING,
            citizenInformationLatestRevision,
            modeOfPaymentLatestRevision,
            unionContributionLatestRevision,
            requestInformationLatestRevision
        )
    }

    override fun persistChangePersonalDataValidateWaveTask(
        requestId: UUID,
        waveTask: WaveTask,
    ) {
        val requestEntity = requestRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        val waveTaskEntity = waveTask.toChangePersonalDataValidateWaveTaskEntity(requestEntity)
        waveTaskRepository.save(waveTaskEntity)
    }

    override fun softDeleteTaskById(taskId: String) {
        log.info("SOFT Deleting task with ID: $taskId")
        val task = waveTaskRepository.findByTaskId(taskId)

        if (task != null) {
            task.status = WaveTaskStatus.DELETED
            waveTaskRepository.save(task);
        }
    }

    override fun openTask(task: WaveTask) {
        val waveTaskEntity = waveTaskRepository.findByTaskId(task.taskId)
            ?: throw WaveTaskNotFoundException("Wave task not found for id ${task.taskId}")
        waveTaskEntity.status = WaveTaskStatus.OPEN
        //mgenique save ?
    }

    override fun getOpenWaveTaskByRequestId(requestId: UUID): WaveTask {
        val waveTaskEntity = waveTaskRepository.findAllByRequestId(requestId).firstOrNull {
            it.status == WaveTaskStatus.OPEN
        } ?: throw WaveTaskNotFoundException("Wave task not found for request $requestId")
        return waveTaskEntity.toDomainWaveTask()
    }

    override fun getCitizenInformationRevision(requestId: UUID, taskCode: String): WaveTaskRevisionNumbers {
        val waveTaskEntity: ChangePersonalDataCaptureWaveTaskEntity =
            waveTaskRepository.findAllByRequestIdAndStatusIn(
                requestId,
                listOf(WaveTaskStatus.CLOSED, WaveTaskStatus.WAITING)
            ).firstOrNull {
                it is ChangePersonalDataCaptureWaveTaskEntity && taskCode == WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            } as ChangePersonalDataCaptureWaveTaskEntity?
                ?: throw WaveTaskNotFoundException("Wave task not found (CLOSED or WAITING) for request $requestId")
        return waveTaskEntity.toWaveTaskRevisionNumbers()
    }

    override fun getWaitingWaveTaskByRequestId(requestId: UUID, taskCode: String): WaveTask {
        log.info("Retrieving closed wave task with ID: $requestId")
        val waveTaskEntity = waveTaskRepository.findAllByRequestId(requestId).firstOrNull {
            it.status == WaveTaskStatus.WAITING && when(taskCode) {
                WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE -> it is ChangePersonalDataCaptureWaveTaskEntity
                WaveTaskPort.VALIDATION_DATA -> it is ChangePersonalDataValidateWaveTaskEntity
                else -> throw WaveTaskNotFoundException("Invalid task code: $taskCode")
            }
        } ?: throw WaveTaskNotFoundException("Wave task not found for request $requestId")
        log.info("Retrieved closed wave task with ID: $requestId")
        return waveTaskEntity.toDomainWaveTask()
    }

    override fun getWaveTaskEntityByRequestId(
        requestId: UUID,
        taskType: String
    ): WaveTask {
        val clazz = taskTypeResolver.resolve(taskType)
        val request = requestRepository.findByIdOrNull(requestId)
            ?: throw WaveTaskNotFoundException("Wave task not found for request $requestId")

        val waveTaskEntity = request.waveTasks
            .firstOrNull { clazz.isInstance(it) }
            ?: throw WaveTaskNotFoundException("Wave task not found in the request $requestId")
        return waveTaskEntity.toDomainWaveTask()
    }

    override fun removeWaveTaskEntityByRequestId(requestId: UUID, taskType: String) {
        val clazz = taskTypeResolver.resolve(taskType)
        val request = requestRepository.findById(requestId)
            .orElseThrow { IllegalArgumentException("Request with ID $requestId not found") }
        request.waveTasks.removeIf { clazz.isInstance(it) }
    }
}