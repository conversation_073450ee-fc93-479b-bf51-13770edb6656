/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.PublicEC32WorkerDeclaration;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.PublicEC32WorkerIdentification;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.PublicEC32WorkerRequest;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.PublicSignature;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PublicEC32
 */
@JsonPropertyOrder({
  PublicEC32.JSON_PROPERTY_VERSION,
  PublicEC32.JSON_PROPERTY_REFERENCE_NBR,
  PublicEC32.JSON_PROPERTY_WORKER_IDENTIFICATION,
  PublicEC32.JSON_PROPERTY_REQUEST,
  PublicEC32.JSON_PROPERTY_DECLARATION,
  PublicEC32.JSON_PROPERTY_SIGNATURE
})
@JsonTypeName("publicEC32")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PublicEC32 {
  public static final String JSON_PROPERTY_VERSION = "version";
  private String version;

  public static final String JSON_PROPERTY_REFERENCE_NBR = "referenceNbr";
  private String referenceNbr;

  public static final String JSON_PROPERTY_WORKER_IDENTIFICATION = "workerIdentification";
  private PublicEC32WorkerIdentification workerIdentification;

  public static final String JSON_PROPERTY_REQUEST = "request";
  private PublicEC32WorkerRequest request;

  public static final String JSON_PROPERTY_DECLARATION = "declaration";
  private PublicEC32WorkerDeclaration declaration;

  public static final String JSON_PROPERTY_SIGNATURE = "signature";
  private PublicSignature signature;

  public PublicEC32() {
  }

  public PublicEC32 version(String version) {
    
    this.version = version;
    return this;
  }

  /**
   * Get version
   * @return version
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getVersion() {
    return version;
  }


  @JsonProperty(JSON_PROPERTY_VERSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVersion(String version) {
    this.version = version;
  }

  public PublicEC32 referenceNbr(String referenceNbr) {
    
    this.referenceNbr = referenceNbr;
    return this;
  }

  /**
   * Get referenceNbr
   * @return referenceNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFERENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferenceNbr() {
    return referenceNbr;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferenceNbr(String referenceNbr) {
    this.referenceNbr = referenceNbr;
  }

  public PublicEC32 workerIdentification(PublicEC32WorkerIdentification workerIdentification) {
    
    this.workerIdentification = workerIdentification;
    return this;
  }

  /**
   * Get workerIdentification
   * @return workerIdentification
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PublicEC32WorkerIdentification getWorkerIdentification() {
    return workerIdentification;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerIdentification(PublicEC32WorkerIdentification workerIdentification) {
    this.workerIdentification = workerIdentification;
  }

  public PublicEC32 request(PublicEC32WorkerRequest request) {
    
    this.request = request;
    return this;
  }

  /**
   * Get request
   * @return request
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REQUEST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PublicEC32WorkerRequest getRequest() {
    return request;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRequest(PublicEC32WorkerRequest request) {
    this.request = request;
  }

  public PublicEC32 declaration(PublicEC32WorkerDeclaration declaration) {
    
    this.declaration = declaration;
    return this;
  }

  /**
   * Get declaration
   * @return declaration
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PublicEC32WorkerDeclaration getDeclaration() {
    return declaration;
  }


  @JsonProperty(JSON_PROPERTY_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeclaration(PublicEC32WorkerDeclaration declaration) {
    this.declaration = declaration;
  }

  public PublicEC32 signature(PublicSignature signature) {
    
    this.signature = signature;
    return this;
  }

  /**
   * Get signature
   * @return signature
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public PublicSignature getSignature() {
    return signature;
  }


  @JsonProperty(JSON_PROPERTY_SIGNATURE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSignature(PublicSignature signature) {
    this.signature = signature;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PublicEC32 publicEC32 = (PublicEC32) o;
    return Objects.equals(this.version, publicEC32.version) &&
        Objects.equals(this.referenceNbr, publicEC32.referenceNbr) &&
        Objects.equals(this.workerIdentification, publicEC32.workerIdentification) &&
        Objects.equals(this.request, publicEC32.request) &&
        Objects.equals(this.declaration, publicEC32.declaration) &&
        Objects.equals(this.signature, publicEC32.signature);
  }

  @Override
  public int hashCode() {
    return Objects.hash(version, referenceNbr, workerIdentification, request, declaration, signature);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PublicEC32 {\n");
    sb.append("    version: ").append(toIndentedString(version)).append("\n");
    sb.append("    referenceNbr: ").append(toIndentedString(referenceNbr)).append("\n");
    sb.append("    workerIdentification: ").append(toIndentedString(workerIdentification)).append("\n");
    sb.append("    request: ").append(toIndentedString(request)).append("\n");
    sb.append("    declaration: ").append(toIndentedString(declaration)).append("\n");
    sb.append("    signature: ").append(toIndentedString(signature)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

