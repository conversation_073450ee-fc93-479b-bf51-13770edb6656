management:
  tracing:
    enabled: false
spring:
  thymeleaf:
    cache: false
    prefix: file:backend/src/main/resources/templates/
  web:
    resources:
      static-locations: file:src/main/resources/static/
      cache:
        period: 0
  devtools:
    livereload:
      enabled: true
    restart:
      enabled: true
      poll-interval: 2s
      quiet-period: 1s
  debug: false
  datasource:
    url: *********************************************
    username: sa
    password: D3vD3vD3v$
  jpa:
    properties:
      hibernate:
        format_sql: false
        enable_lazy_load_no_trans: true
    show-sql: true
  rabbitmq:
    host: localhost
    port: 5672
    username: guest  # Default RabbitMQ username
    password: guest  # Default RabbitMQ password
    virtual-host: onemrva
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 2s
          max-attempts: 5
          max-interval: 5s
          multiplier: 1.5
  security:
    redirect: http://localhost:4300
    realm: onemrva-agents
    authserverurl: http://localhost:8082/
    backendBasePath: nil
    jwt:
      check: no-check
    oauth2:
      resource-server:
        jwt:
          issuer-uri: 'http://localhost:8082/realms/onemrva-agents'
        provider:
          keycloak:
            authorization-uri: http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/auth
            issuer-uri: http://localhost:8082/realms/onemrva-agents
#            token-uri: http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/token
#            user-info-uri: http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/userinfo
#            jwk-set-uri: http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/certs
keycloak:
  auth-server-url: http://localhost:8082
  internal-auth-server-url: http://localhost:8082
  realm: onemrva-agents
  redirect: http://localhost:4300

logging:
  level:
    #    org.hibernate: info
    #    org.hibernate.SQL: debug
    #    org.hibernate.orm.jdbc.bind: trace
    #    org.hibernate.stat: debug
    #    org.hibernate.SQL_SLOW: info
    #    org.hibernate.cache: debug
    be:
      fgov:
        onerva:
          cu: DEBUG

onerva:
  observability:
    prometheus:
      enabled: false
  metrics:
    enabled: false


route:
  host: localhost:4300

application:
  cors: '*'

woThirdPartyApi:
  url: http://127.0.0.1:8070/thirdParties/v1
woOrganizationalChartApi:
  url: http://127.0.0.1:8070/rest/organizationalChart/nsso/v2/services

werkomgeving:
  enabled: true
  mock: true
  woFacadeApi:
    url: http://localhost:8077/api

woUserFacadeApi:
  url: http://localhost:9997/rest/WO+facade+API/v1

citizen:
  url: http://localhost:9997/rest/Person+API/1.0.0

barema:
  url: http://localhost:9998/rest/Bareme+REST+API/1.0.0

c9Api:
  url: http://localhost:9997/rest/C9+REST+API/1.0.0

backend:
  base-url: http://localhost:9091

lookup:
  url: http://services.onemrva.priv/lookupwpptservice/rest

registry:
  url: http://localhost:9997/rest/Register+Proxy+Service+public+API/1.0.0

c51:
  url: https://proc51.test.paas.onemrva.priv/proc51/home.jsf

app:
  allowMultipleC9s: true
  jobs:

flagsmith:
  api:
    key: Ltu7TyR4Gf8LrGpvDig4sy
    url: https://flagsmith.prod.paas.onemrva.priv/api/v1/
