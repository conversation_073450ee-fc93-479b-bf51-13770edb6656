package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.FieldSource
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param fieldSources Sources for individual fields
 */
data class SelectFieldSourcesRequest(

    @field:Valid
    @Schema(example = "[{\"fieldName\":\"birthDate\",\"source\":\"AUTHENTIC_SOURCES\"},{\"fieldName\":\"nationality\",\"source\":\"C1\"},{\"fieldName\":\"address.street\",\"source\":\"ONEM\"}]", required = true, description = "Sources for individual fields")
    @get:JsonProperty("fieldSources", required = true) val fieldSources: kotlin.collections.List<FieldSource>
    ) {

}

