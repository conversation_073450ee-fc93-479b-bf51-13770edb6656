<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>be.fgov.onerva.cu</groupId>
        <artifactId>cu-root</artifactId>
        <version>0.0.1-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>bff</artifactId>
    <name>cu bff</name>
    <description>Description</description>

    <properties>
        <apicurio-registry-maven-plugin.version>2.4.2.Final</apicurio-registry-maven-plugin.version>
        <jackson-databind-nullable.version>0.2.6</jackson-databind-nullable.version>
        <jacoco-maven-plugin.version>0.8.11</jacoco-maven-plugin.version>
        <jib-maven-plugin.version>3.4.2</jib-maven-plugin.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <openapi-generator-maven-plugin.version>7.9.0</openapi-generator-maven-plugin.version>
        <plugin-toolchains.version>3.2.0</plugin-toolchains.version>
        <spring-cloud-starter-gateway-mvc.version>4.1.6</spring-cloud-starter-gateway-mvc.version>
    </properties>
    <dependencies>
        <!-- Internal Modules -->
        <dependency>
            <groupId>be.fgov.onerva.cu</groupId>
            <artifactId>common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- SPRING-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-gateway-mvc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>be.fgov.onerva.observability</groupId>
            <artifactId>observability-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-layout-template-json</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-log4j2</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Kotlin -->
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>

        <!-- Coroutines -->
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-reactor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-webflux</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib-jdk8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit</artifactId>
            <version>${kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.mockk</groupId>
            <artifactId>mockk-jvm</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.ninja-squad</groupId>
            <artifactId>springmockk</artifactId>
            <version>${springmockk.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>
    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>io.apicurio</groupId>
                    <artifactId>apicurio-registry-maven-plugin</artifactId>
                    <version>${apicurio-registry-maven-plugin.version}</version>
                    <configuration>
                        <registryUrl>https://api-registry.test.paas.onemrva.priv/apis/registry/v2</registryUrl>
                    </configuration>
                    <executions>
                        <execution>
                            <phase>generate-sources</phase>
                            <goals>
                                <goal>download</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>io.apicurio</groupId>
                <artifactId>apicurio-registry-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <configuration>
                            <artifacts>
                                <artifact>
                                    <groupId>be.fgov.onerva.person.backend</groupId>
                                    <artifactId>person-rest-api</artifactId>
                                    <file>${project.basedir}/target/api/citizens-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.woconfigurator</groupId>
                                    <artifactId>wo-facade-api</artifactId>
                                    <file>${project.basedir}/target/api/wo-facade-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                                <artifact>
                                    <groupId>be.fgov.onerva.unemployment</groupId>
                                    <artifactId>c9-public-api</artifactId>
                                    <file>${project.basedir}/target/api/c9-public-api.json</file>
                                    <overwrite>true</overwrite>
                                </artifact>
                            </artifacts>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.openapitools</groupId>
                <artifactId>openapi-generator-maven-plugin</artifactId>
                <version>${openapi-generator-maven-plugin.version}</version>
                <configuration>
                    <skipIfSpecIsUnchanged>true</skipIfSpecIsUnchanged>
                </configuration>
                <executions>
                    <execution>
                        <id>bff</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>kotlin-spring</generatorName>
                            <inputSpec>${project.basedir}/../api/private/cu-bff.yaml</inputSpec>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                            </typeMappings>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <useResponseEntity>false</useResponseEntity>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <singleContentTypes>false</singleContentTypes>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <serializationLibrary>jackson</serializationLibrary>
                                <annotationLibrary>swagger2</annotationLibrary>
                                <sourceFolder>src/main/kotlin</sourceFolder>
                                <useBeanValidation>true</useBeanValidation>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                                <useDelegate>false</useDelegate>
                                <useSpringBoot3>true</useSpringBoot3>
                            </configOptions>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <packageName>be.fgov.onerva.cu.bff.rest.server.priv</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.server.priv.api</apiPackage>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.server.priv.invoker</invokerPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.server.priv.model</modelPackage>
                        </configuration>
                    </execution>
                    <execution>
                        <id>backend</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <generatorName>kotlin</generatorName>
                            <inputSpec>${project.basedir}/../api/private/cu-config.yaml</inputSpec>
                            <packageName>be.fgov.onerva.cu.bff.rest.client.priv.backend</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.client.priv.backend.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.client.priv.backend.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.client.priv.backend.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <useSpringBoot3>true</useSpringBoot3>
                                <library>jvm-spring-restclient</library>
                                <serializationLibrary>jackson</serializationLibrary>
                                <sourceFolder>src/main/kotlin</sourceFolder>
                                <enumPropertyNaming>UPPERCASE</enumPropertyNaming>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=java.time.LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>lookup-wppt-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/../api/external/lookup-wppt-api.yaml
                            </inputSpec>
                            <packageName>be.fgov.onerva.cu.bff.rest.client.lookup</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.client.lookup.wppt.invoker</invokerPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>lookup.wppt-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.cu.bff.rest.client.lookup.wppt.v1</basePackage>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>citizen-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/citizens-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.cu.bff.rest.client.citizen</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.client.citizen.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.client.citizen.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.client.citizen.invoker</invokerPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>citizen-rest</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.cu.bff.rest.client.citizen</basePackage>
                                <openApiNullable>false</openApiNullable>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>wo-facade-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/wo-facade-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.cu.bff.rest.client.wo.facade</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.client.wo.facade.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model</modelPackage>
                            <modelNameSuffix>DTO</modelNameSuffix>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <enablePostProcessFile>true</enablePostProcessFile>
                            <generatorName>java</generatorName>
                            <artifactId>wo-facade-api</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <generateModelDocumentation>false</generateModelDocumentation>
                            <generateApiDocumentation>false</generateApiDocumentation>
                            <configOptions>
                                <interfaceOnly>true</interfaceOnly>
                                <useTags>true</useTags>
                                <skipDefaultInterface>true</skipDefaultInterface>
                                <openApiNullable>false</openApiNullable>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                    <execution>
                        <id>c9-public-api</id>
                        <goals>
                            <goal>generate</goal>
                        </goals>
                        <configuration>
                            <inputSpec>
                                ${project.basedir}/target/api/c9-public-api.json
                            </inputSpec>
                            <packageName>be.fgov.onerva.cu.bff.rest.client.unemployment.c9</packageName>
                            <apiPackage>be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api</apiPackage>
                            <modelPackage>be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model</modelPackage>
                            <invokerPackage>be.fgov.onerva.cu.bff.rest.client.unemployment.c9.invoker</invokerPackage>
                            <addCompileSourceRoot>true</addCompileSourceRoot>
                            <generatorName>java</generatorName>
                            <artifactId>c9-public-api</artifactId>
                            <generateApiTests>false</generateApiTests>
                            <generateModelTests>false</generateModelTests>
                            <configOptions>
                                <library>resttemplate</library>
                                <useJakartaEe>true</useJakartaEe>
                                <basePackage>be.fgov.onerva.cu.bff.rest.client.unemployment.c9</basePackage>
                                <openApiNullable>false</openApiNullable> <!-- Disable JsonNullable -->
                            </configOptions>
                            <typeMappings>
                                <typeMapping>Double=java.math.BigDecimal</typeMapping>
                                <typeMapping>OffsetDateTime=LocalDateTime</typeMapping>
                            </typeMappings>
                            <importMappings>
                                <importMapping>java.time.OffsetDateTime=java.time.LocalDateTime</importMapping>
                            </importMappings>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>build-info</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.google.cloud.tools</groupId>
                <artifactId>jib-maven-plugin</artifactId>
                <version>${jib-maven-plugin.version}</version>
                <configuration>
                    <container>
                        <jvmFlags>
                            <jvmFlag>-XX:InitialRAMPercentage=50.0</jvmFlag>
                            <jvmFlag>-XX:MaxRAMPercentage=50.0</jvmFlag>
                        </jvmFlags>
                        <ports>
                            <port>8080</port>
                        </ports>
                        <creationTime>USE_CURRENT_TIMESTAMP</creationTime>
                    </container>
                    <allowInsecureRegistries>true</allowInsecureRegistries>
                    <to>
                        <auth>
                            <password>${env.bamboo_nexus_secret}</password>
                            <username>${env.bamboo_nexus_user}</username>
                        </auth>
                        <image>docker-alpha.onemrva.priv/onemrva/cu-bff</image>
                    </to>
                    <from>
                        <image>docker-release.onemrva.priv/onemrva/eclipse-temurin:21-jdk-1.3.0</image>
                    </from>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-toolchains-plugin</artifactId>
                <version>${plugin-toolchains.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>toolchain</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <toolchains>
                        <jdk>
                            <version>${java.version}</version>
                        </jdk>
                    </toolchains>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco-maven-plugin.version}</version>
                <configuration>
                    <excludes>
                        <exclude>be/fgov/onerva/cu/bff/CuBffApplication.*</exclude>
                        <exclude>be/fgov/onerva/cu/bff/rest/**</exclude>
                        <exclude>be/fgov/onerva/cu/bff/config/**</exclude>
                        <exclude>be/fgov/onerva/cu/bff/exceptions/**</exclude>
                        <exclude>be/fgov/onerva/unemployment/c9/**</exclude>
                        <exclude>be/fgov/onerva/cu/bff/adapter/out/CitizenInfoApiFactory.*</exclude>
                    </excludes>
                    <rules>
                        <rule>
                            <element>CLASS</element>
                            <limits>
                                <limit>
                                    <counter>LINE</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.85</minimum>
                                </limit>
                                <limit>
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.65</minimum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>jacoco-check</id>
                        <phase>post-integration-test</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <!--suppress UnresolvedMavenProperty -->
                            <skip>${maven.test.skip}</skip>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.jetbrains.kotlin</groupId>
                <artifactId>kotlin-maven-plugin</artifactId>
                <configuration>
                    <args>
                        <arg>
                            -Xnullability-annotations=@jakarta.annotation.Nullable:strict
                        </arg>
                        <arg>-Xjsr305=strict</arg>
                        <arg>-Xjsr305=@jakarta.annotation.Nullable:strict</arg>
                        <arg>-api-version=2.1</arg>
                        <arg>-language-version=2.1</arg>
                    </args>
                    <compilerPlugins>
                        <plugin>spring</plugin>
                    </compilerPlugins>
                    <jvmTarget>${java.version}</jvmTarget>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-allopen</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>org.jetbrains.kotlin</groupId>
                        <artifactId>kotlin-maven-noarg</artifactId>
                        <version>${kotlin.version}</version>
                    </dependency>
                </dependencies>
                <executions>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>src/main/java</sourceDir>
                                <sourceDir>${project.basedir}/target/generated-sources/openapi/src/main/kotlin
                                </sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                    <execution>
                        <id>test-compile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>test-compile</goal>
                        </goals>
                        <configuration>
                            <sourceDirs>
                                <sourceDir>${project.basedir}/src/test/kotlin</sourceDir>
                            </sourceDirs>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!--                    <properties>-->
                    <!--                        <configurationParameters>-->
                    <!--                            cucumber.junit-platform.naming-strategy=long-->
                    <!--                            junit.jupiter.execution.parallel.enabled = true-->
                    <!--                            junit.jupiter.execution.parallel.mode.default = concurrent-->
                    <!--                        </configurationParameters>-->
                    <!--                    </properties>-->
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <executions>
                    <execution>
                        <id>integration-test</id>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <properties>
                        <configurationParameters>
                            cucumber.junit-platform.naming-strategy=long
                        </configurationParameters>
                    </properties>
                    <redirectTestOutputToFile>true</redirectTestOutputToFile>
                    <reportsDirectory>${project.build.directory}/surefire-reports</reportsDirectory>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <executions>
                    <execution>
                        <id>default-compile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>default-testCompile</id>
                        <phase>none</phase>
                    </execution>
                    <execution>
                        <id>compile</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>compile</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>testCompile</id>
                        <phase>test-compile</phase>
                        <goals>
                            <goal>testCompile</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <profile>
            <id>sync</id>
            <dependencies>
                <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-devtools</artifactId>
                    <scope>runtime</scope>
                    <optional>true</optional>
                </dependency>
            </dependencies>
        </profile>
    </profiles>
</project>
