/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.wo.facade.rest.model.UserCriteriaDTO
import be.fgov.onerva.wo.facade.rest.model.UserDTO
import be.fgov.onerva.wo.facade.infrastructure.*

class UserApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun searchUsers(criteria: UserCriteriaDTO? = null): UserDTO {
        val result = searchUsersWithHttpInfo(criteria = criteria)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun searchUsersWithHttpInfo(criteria: UserCriteriaDTO? = null): ResponseEntity<UserDTO> {
        val localVariableConfig = searchUsersRequestConfig(criteria = criteria)
        return request<Unit, UserDTO>(
            localVariableConfig
        )
    }

    fun searchUsersRequestConfig(criteria: UserCriteriaDTO? = null) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                if (criteria != null) {
                    put("criteria", listOf(criteria.toString()))
                }
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/users",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
