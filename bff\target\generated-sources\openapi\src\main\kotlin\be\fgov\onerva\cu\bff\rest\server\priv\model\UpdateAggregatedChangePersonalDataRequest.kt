package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateBasicInfoRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateUnionContributionRequest
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param basicInfo 
 * @param citizenInformation 
 * @param modeOfPayment 
 * @param unionContribution 
 */
data class UpdateAggregatedChangePersonalDataRequest(

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("basicInfo") val basicInfo: UpdateBasicInfoRequest? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("citizenInformation") val citizenInformation: UpdateCitizenInformationRequest? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("modeOfPayment") val modeOfPayment: UpdateModeOfPaymentRequest? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("unionContribution") val unionContribution: UpdateUnionContributionRequest? = null
    ) {

}

