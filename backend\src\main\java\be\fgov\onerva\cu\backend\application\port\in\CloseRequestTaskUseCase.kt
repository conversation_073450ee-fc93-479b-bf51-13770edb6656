package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.*
import be.fgov.onerva.cu.backend.application.domain.WaveTask

/**
 * Use case interface for closing tasks associated with requests in the system.
 *
 * This interface defines the contract for closing specific tasks linked to requests,
 * typically used when a workflow task needs to be marked as completed.
 */
fun interface CloseRequestTaskUseCase {
    /**
     * Closes a specific task associated with a request.
     *
     * @param requestId The unique identifier of the request for which the task should be closed
     * @param taskCode The code identifying the specific type of task to be closed
     * @throws RequestIdNotFoundException if the request with the given ID is not found
     * @throws RuntimeException if there is an error while attempting to close the task
     * @return The new task coming after that one that was successfully closed (if any)
     */
    fun closeTaskForRequestAndCreateNext(requestId: UUID, taskCode: String): WaveTask?
}