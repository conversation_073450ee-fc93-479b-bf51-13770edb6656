import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  matSelectAnimations
} from "./chunk-JATID7Y6.js";
import "./chunk-VP7OCDXQ.js";
import "./chunk-W456NIV3.js";
import "./chunk-SY6O6U7L.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>ield,
  <PERSON><PERSON>int,
  Mat<PERSON>abel,
  MatPrefix,
  MatSuffix
} from "./chunk-T76J6HQT.js";
import "./chunk-C3EWM6A7.js";
import "./chunk-IEMWVBW3.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-UPOW3STX.js";
import "./chunk-IE44L42K.js";
import "./chunk-QNFKXUK7.js";
import "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
