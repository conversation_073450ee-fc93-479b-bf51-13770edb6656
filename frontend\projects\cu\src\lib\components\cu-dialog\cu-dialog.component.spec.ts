import {ComponentFixture, TestBed} from "@angular/core/testing";
import {CuDialogComponent} from "./cu-dialog.component";
import {MAT_DIALOG_DATA, MatDialogRef} from "@angular/material/dialog";
import {By} from "@angular/platform-browser";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";

describe('CuDialogComponent', () => {
  let component: CuDialogComponent;
  let fixture: ComponentFixture<CuDialogComponent>;
  let mockDialogRef: jest.Mocked<MatDialogRef<CuDialogComponent>>;

  beforeEach(async () => {
    mockDialogRef = {
      close: jest.fn()
    } as any;

    await TestBed.configureTestingModule({
      imports: [CuDialogComponent, NoopAnimationsModule],
      providers: [
        {provide: MAT_DIALOG_DATA, useValue: {}},
        {provide: MatDialogRef, useValue: mockDialogRef},
      ],
    })
        .compileComponents();

    fixture = TestBed.createComponent(CuDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Constructor with data', () => {
    it('should initialize with default values when no data provided', () => {
      expect(component.title).toBe('');
      expect(component.content).toBe('');
      expect(component.primaryActionText).toBe('Primary');
      expect(component.secondaryActionText).toBe('Secondary');
      expect(component.dialogType).toBe('');
      expect(component.dialogSize).toBe('medium');
      expect(component.onPrimaryAction).toBeDefined();
      expect(component.onSecondaryAction).toBeDefined();
    });

    it('should initialize with provided data values', async () => {
      const mockData = {
        title: 'Test Title',
        content: 'Test Content',
        primaryActionText: 'Confirm',
        secondaryActionText: 'Cancel',
        dialogType: 'warn',
        dialogSize: 'large',
        onPrimaryAction: jest.fn(),
        onSecondaryAction: jest.fn()
      };

      await TestBed.resetTestingModule();
      await TestBed.configureTestingModule({
        imports: [CuDialogComponent, NoopAnimationsModule],
        providers: [
          {provide: MAT_DIALOG_DATA, useValue: mockData},
          {provide: MatDialogRef, useValue: mockDialogRef},
        ],
      }).compileComponents();

      const newFixture = TestBed.createComponent(CuDialogComponent);
      const newComponent = newFixture.componentInstance;
      newFixture.detectChanges();

      expect(newComponent.title).toBe('Test Title');
      expect(newComponent.content).toBe('Test Content');
      expect(newComponent.primaryActionText).toBe('Confirm');
      expect(newComponent.secondaryActionText).toBe('Cancel');
      expect(newComponent.dialogType).toBe('warn');
      expect(newComponent.dialogSize).toBe('large');
      expect(newComponent.onPrimaryAction).toBe(mockData.onPrimaryAction);
      expect(newComponent.onSecondaryAction).toBe(mockData.onSecondaryAction);
    });

    it('should handle partial data correctly', async () => {
      const partialData = {
        title: 'Partial Title',
        dialogType: 'success'
      };

      await TestBed.resetTestingModule();
      await TestBed.configureTestingModule({
        imports: [CuDialogComponent, NoopAnimationsModule],
        providers: [
          {provide: MAT_DIALOG_DATA, useValue: partialData},
          {provide: MatDialogRef, useValue: mockDialogRef},
        ],
      }).compileComponents();

      const newFixture = TestBed.createComponent(CuDialogComponent);
      const newComponent = newFixture.componentInstance;

      expect(newComponent.title).toBe('Partial Title');
      expect(newComponent.content).toBe('');
      expect(newComponent.primaryActionText).toBe('Primary');
      expect(newComponent.dialogType).toBe('success');
    });
  });

  describe('Dialog rendering', () => {
    it('should render title correctly', () => {
      component.title = 'Test Dialog Title';
      fixture.detectChanges();

      const titleElement = fixture.debugElement.query(By.css('.dialog-title span:not(.icon-container):not(.close-dialog)'));
      expect(titleElement.nativeElement.textContent).toContain('Test Dialog Title');
    });

    it('should render content with HTML', () => {
      component.content = '<p>Test <strong>HTML</strong> content</p>';
      fixture.detectChanges();

      const contentElement = fixture.debugElement.query(By.css('mat-dialog-content div'));
      expect(contentElement.nativeElement.innerHTML).toBe('<p>Test <strong>HTML</strong> content</p>');
    });

    it('should render action buttons with correct text', () => {
      component.primaryActionText = 'Save';
      component.secondaryActionText = 'Cancel';
      fixture.detectChanges();

      const buttons = fixture.debugElement.queryAll(By.css('mat-dialog-actions button'));
      expect(buttons[0].nativeElement.textContent).toContain('Cancel');
      expect(buttons[1].nativeElement.textContent).toContain('Save');
    });
  });

  describe('Dialog types and icons', () => {
    it('should show warning icon for warn type', () => {
      component.dialogType = 'warn';
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.icon-container mat-icon'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.textContent).toBe('warning');
      expect(iconElement.nativeElement.getAttribute('color')).toBe('warn');
    });

    it('should show check_circle icon for success type', () => {
      component.dialogType = 'success';
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.icon-container mat-icon'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.textContent).toBe('check_circle');
      expect(iconElement.nativeElement.getAttribute('color')).toBe('accent');
    });

    it('should show error icon for error type', () => {
      component.dialogType = 'error';
      fixture.detectChanges();

      const iconElement = fixture.debugElement.query(By.css('.icon-container mat-icon'));
      expect(iconElement).toBeTruthy();
      expect(iconElement.nativeElement.textContent).toBe('error');
      expect(iconElement.nativeElement.getAttribute('color')).toBe('warn');
    });

    it('should not show any icon when dialogType is empty', () => {
      component.dialogType = '';
      fixture.detectChanges();

      const iconElements = fixture.debugElement.queryAll(By.css('.icon-container mat-icon'));
      expect(iconElements.length).toBe(0);
    });

    it('should apply correct CSS class to title based on dialogType', () => {
      const testCases = [
        { type: 'warn', expectedClass: 'warning' },
        { type: 'success', expectedClass: 'success' },
        { type: 'error', expectedClass: 'error' },
        { type: '', expectedClass: '' }
      ];

      testCases.forEach(testCase => {
        component.dialogType = testCase.type;
        fixture.detectChanges();

        const titleSpan = fixture.debugElement.query(By.css('.dialog-title > span:not(.icon-container):not(.close-dialog)'));

        if (testCase.expectedClass) {
          expect(titleSpan.nativeElement.classList.contains(testCase.expectedClass)).toBeTruthy();
        } else {
          expect(titleSpan.nativeElement.className).toBe('');
        }
      });
    });
  });

  describe('Dialog sizes', () => {
    it('should apply large-dialog class when size is large', () => {
      component.dialogSize = 'large';
      fixture.detectChanges();

      const dialogDiv = fixture.debugElement.query(By.css('div'));
      expect(dialogDiv.nativeElement.classList.contains('large-dialog')).toBeTruthy();
      expect(dialogDiv.nativeElement.classList.contains('medium-dialog')).toBeFalsy();
    });

    it('should apply medium-dialog class when size is medium', () => {
      component.dialogSize = 'medium';
      fixture.detectChanges();

      const dialogDiv = fixture.debugElement.query(By.css('div'));
      expect(dialogDiv.nativeElement.classList.contains('medium-dialog')).toBeTruthy();
      expect(dialogDiv.nativeElement.classList.contains('large-dialog')).toBeFalsy();
    });

    it('should not apply size classes for unknown sizes', () => {
      component.dialogSize = 'small';
      fixture.detectChanges();

      const dialogDiv = fixture.debugElement.query(By.css('div'));
      expect(dialogDiv.nativeElement.classList.contains('large-dialog')).toBeFalsy();
      expect(dialogDiv.nativeElement.classList.contains('medium-dialog')).toBeFalsy();
    });
  });

  describe('User interactions', () => {
    it('should call onPrimaryAction when primary button is clicked', () => {
      const onPrimaryActionSpy = jest.fn();
      component.onPrimaryAction = onPrimaryActionSpy;
      fixture.detectChanges();

      const primaryButton = fixture.debugElement.query(By.css('button[mat-flat-button]'));
      primaryButton.nativeElement.click();

      expect(onPrimaryActionSpy).toHaveBeenCalled();
    });

    it('should call onSecondaryAction when secondary button is clicked', () => {
      const onSecondaryActionSpy = jest.fn();
      component.onSecondaryAction = onSecondaryActionSpy;
      fixture.detectChanges();

      const secondaryButton = fixture.debugElement.query(By.css('button[mat-button]'));
      secondaryButton.nativeElement.click();

      expect(onSecondaryActionSpy).toHaveBeenCalled();
    });

    it('should have close button with matDialogClose directive', () => {
      const closeButton = fixture.debugElement.query(By.css('.close-dialog'));
      expect(closeButton).toBeTruthy();
      expect(closeButton.nativeElement.hasAttribute('matDialogClose')).toBeTruthy();
    });
  });

  describe('Default action functions', () => {
    it('should create default onPrimaryAction function that does nothing', () => {
      expect(() => component.onPrimaryAction()).not.toThrow();
    });

    it('should create default onSecondaryAction function that does nothing', () => {
      expect(() => component.onSecondaryAction()).not.toThrow();
    });
  });

  describe('Button styling', () => {
    it('should apply correct material button types', () => {
      fixture.detectChanges();

      const secondaryButton = fixture.debugElement.query(By.css('button[mat-button]'));
      const primaryButton = fixture.debugElement.query(By.css('button[mat-flat-button]'));

      expect(secondaryButton).toBeTruthy();
      expect(secondaryButton.nativeElement.getAttribute('color')).toBe('primary');

      expect(primaryButton).toBeTruthy();
      expect(primaryButton.nativeElement.getAttribute('color')).toBe('accent');
    });
  });

  describe('Icon container', () => {
    it('should apply dialogType class to icon container', () => {
      const types = ['warn', 'success', 'error'];

      types.forEach(type => {
        component.dialogType = type;
        fixture.detectChanges();

        const iconContainer = fixture.debugElement.query(By.css('.icon-container'));
        expect(iconContainer.nativeElement.classList.contains(type)).toBeTruthy();
      });
    });
  });

  describe('Edge cases', () => {
    it('should handle null data gracefully', async () => {
      await TestBed.resetTestingModule();
      await TestBed.configureTestingModule({
        imports: [CuDialogComponent, NoopAnimationsModule],
        providers: [
          {provide: MAT_DIALOG_DATA, useValue: null},
          {provide: MatDialogRef, useValue: mockDialogRef},
        ],
      }).compileComponents();

      const newFixture = TestBed.createComponent(CuDialogComponent);
      const newComponent = newFixture.componentInstance;

      expect(() => newFixture.detectChanges()).not.toThrow();
      expect(newComponent.title).toBe('');
      expect(newComponent.content).toBe('');
    });

    it('should handle undefined action callbacks', () => {
      const mockData = {
        title: 'Test',
        onPrimaryAction: undefined,
        onSecondaryAction: undefined
      };

      component = new CuDialogComponent(mockData);

      expect(() => component.onPrimaryAction()).not.toThrow();
      expect(() => component.onSecondaryAction()).not.toThrow();
    });
  });
});