/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.KnownHistory;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * KnownHistoryResponse
 */
@JsonPropertyOrder({
  KnownHistoryResponse.JSON_PROPERTY_GROUP,
  KnownHistoryResponse.JSON_PROPERTY_KNOWN_HISTORY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class KnownHistoryResponse {
  public static final String JSON_PROPERTY_GROUP = "group";
  private Long group;

  public static final String JSON_PROPERTY_KNOWN_HISTORY = "knownHistory";
  private List<KnownHistory> knownHistory = new ArrayList<>();

  public KnownHistoryResponse() {
  }

  public KnownHistoryResponse group(Long group) {
    
    this.group = group;
    return this;
  }

  /**
   * Get group
   * @return group
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GROUP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getGroup() {
    return group;
  }


  @JsonProperty(JSON_PROPERTY_GROUP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGroup(Long group) {
    this.group = group;
  }

  public KnownHistoryResponse knownHistory(List<KnownHistory> knownHistory) {
    
    this.knownHistory = knownHistory;
    return this;
  }

  public KnownHistoryResponse addKnownHistoryItem(KnownHistory knownHistoryItem) {
    if (this.knownHistory == null) {
      this.knownHistory = new ArrayList<>();
    }
    this.knownHistory.add(knownHistoryItem);
    return this;
  }

  /**
   * Get knownHistory
   * @return knownHistory
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KNOWN_HISTORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<KnownHistory> getKnownHistory() {
    return knownHistory;
  }


  @JsonProperty(JSON_PROPERTY_KNOWN_HISTORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKnownHistory(List<KnownHistory> knownHistory) {
    this.knownHistory = knownHistory;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KnownHistoryResponse knownHistoryResponse = (KnownHistoryResponse) o;
    return Objects.equals(this.group, knownHistoryResponse.group) &&
        Objects.equals(this.knownHistory, knownHistoryResponse.knownHistory);
  }

  @Override
  public int hashCode() {
    return Objects.hash(group, knownHistory);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KnownHistoryResponse {\n");
    sb.append("    group: ").append(toIndentedString(group)).append("\n");
    sb.append("    knownHistory: ").append(toIndentedString(knownHistory)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

