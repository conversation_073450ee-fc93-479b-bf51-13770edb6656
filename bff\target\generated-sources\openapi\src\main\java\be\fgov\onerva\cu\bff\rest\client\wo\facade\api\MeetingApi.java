package be.fgov.onerva.cu.bff.rest.client.wo.facade.api;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CancelEmailMeetingRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SendEmailMeetingRequestDTO;
import java.util.UUID;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class MeetingApi extends BaseApi {

    public MeetingApi() {
        super(new ApiClient());
    }

    public MeetingApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * 
     * <p><b>204</b> - appointment cancelled
     * @param meetingId  (required)
     * @param cancelEmailMeetingRequestDTO  (required)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void cancelEmailMeeting(UUID meetingId, CancelEmailMeetingRequestDTO cancelEmailMeetingRequestDTO) throws RestClientException {
        cancelEmailMeetingWithHttpInfo(meetingId, cancelEmailMeetingRequestDTO);
    }

    /**
     * 
     * 
     * <p><b>204</b> - appointment cancelled
     * @param meetingId  (required)
     * @param cancelEmailMeetingRequestDTO  (required)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> cancelEmailMeetingWithHttpInfo(UUID meetingId, CancelEmailMeetingRequestDTO cancelEmailMeetingRequestDTO) throws RestClientException {
        Object localVarPostBody = cancelEmailMeetingRequestDTO;
        
        // verify the required parameter 'meetingId' is set
        if (meetingId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'meetingId' when calling cancelEmailMeeting");
        }
        
        // verify the required parameter 'cancelEmailMeetingRequestDTO' is set
        if (cancelEmailMeetingRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'cancelEmailMeetingRequestDTO' when calling cancelEmailMeeting");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("meetingId", meetingId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/meetings/{meetingId}/cancel", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - appointment send
     * <p><b>400</b> - badRequest
     * @param sendEmailMeetingRequestDTO  (required)
     * @return UUID
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public UUID sendEmailMeeting(SendEmailMeetingRequestDTO sendEmailMeetingRequestDTO) throws RestClientException {
        return sendEmailMeetingWithHttpInfo(sendEmailMeetingRequestDTO).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - appointment send
     * <p><b>400</b> - badRequest
     * @param sendEmailMeetingRequestDTO  (required)
     * @return ResponseEntity&lt;UUID&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<UUID> sendEmailMeetingWithHttpInfo(SendEmailMeetingRequestDTO sendEmailMeetingRequestDTO) throws RestClientException {
        Object localVarPostBody = sendEmailMeetingRequestDTO;
        
        // verify the required parameter 'sendEmailMeetingRequestDTO' is set
        if (sendEmailMeetingRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'sendEmailMeetingRequestDTO' when calling sendEmailMeeting");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<UUID> localReturnType = new ParameterizedTypeReference<UUID>() {};
        return apiClient.invokeAPI("/meetings", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
