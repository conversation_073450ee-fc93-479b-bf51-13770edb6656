package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import be.fgov.onerva.cu.backend.adapter.out.mapper.toDomainRequestInformation
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.RequestInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestInformationRepository
import be.fgov.onerva.cu.backend.application.domain.RequestInformation
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.common.utils.logger

@Service("requestInformationAdapter")
@Transactional
class RequestInformationPersistenceAdapter(
    val requestInformationRepository: RequestInformationRepository,
    val changePersonalDataRepository: ChangePersonalDataRepository,
) : RequestInformationPort {
    private val log = logger
    override fun getRequestInformation(requestId: UUID): RequestInformation {
        val requestInformation = requestInformationRepository.findByRequestId(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        return requestInformation.toDomainRequestInformation()
    }
    
    override fun persistRequestInformation(requestId: UUID, newRequestInformation: RequestInformation) {
        val changePersonalData = changePersonalDataRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        val requestInformationEntity = RequestInformationEntity(
            request = changePersonalData,
            requestDate = newRequestInformation.requestDate
        )
        requestInformationRepository.save(requestInformationEntity)
    }

    override fun updateRequestInformation(requestId: UUID, updateRequestInformation: RequestInformation) {
        val updatedRequestInformation = requestInformationRepository.findByRequestId(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        updatedRequestInformation.apply {
            requestDate = updateRequestInformation.requestDate
        }
        requestInformationRepository.save(updatedRequestInformation)
    }

    override fun getLatestRevision(requestId: UUID): Int {
        val request = requestInformationRepository.findByRequestId(requestId)
            ?: throw RequestIdNotFoundException("Request ID not found: $requestId")
        return request.id?.let {
            requestInformationRepository
                .findLastChangeRevision(it)
                .getOrNull()?.revisionNumber?.getOrNull()
        } ?: 0
    }

    override fun getRequestInformationForRevision(requestId: UUID, revision: Int): RequestInformation {
        val id = getEntityId(requestId)
        return requestInformationRepository.findRevision(id, revision).orElseThrow {
            InvalidRequestIdException("Revision not found for request $requestId and revision $revision")
        }.entity.toDomainRequestInformation()
    }

    override fun getEntityId(requestId: UUID): UUID {
        return requestInformationRepository.findByRequestId(requestId)?.id
            ?: throw InvalidRequestIdException("Request ID not found: $requestId")
    }

    override fun patchCurrentDataWithRevision(requestId: UUID, revision: Int) {
        log.info("Patching current data for request ID: $requestId with the latest revision data")

        val currentData = requestInformationRepository.findByRequestId(requestId)

        if(currentData == null){
            throw InvalidRequestIdException("Request ID not found: $requestId")
        }

        val latestRevision = requestInformationRepository.findRevision(currentData.id, revision)

        currentData.apply {
            this.requestDate = latestRevision.get().entity.requestDate
        }

        log.info("Patching complete. Updated Request Information data")

        requestInformationRepository.save(currentData)

    }
}