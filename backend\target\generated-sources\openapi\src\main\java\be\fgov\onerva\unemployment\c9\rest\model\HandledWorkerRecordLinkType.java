/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HandledWorkerRecordLinkType
 */
@JsonPropertyOrder({
  HandledWorkerRecordLinkType.JSON_PROPERTY_EMPLOYER_CLASS,
  HandledWorkerRecordLinkType.JSON_PROPERTY_WORKER_CODE,
  HandledWorkerRecordLinkType.JSON_PROPERTY_ACTIVITY_CODE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HandledWorkerRecordLinkType {
  public static final String JSON_PROPERTY_EMPLOYER_CLASS = "employerClass";
  private String employerClass;

  public static final String JSON_PROPERTY_WORKER_CODE = "workerCode";
  private String workerCode;

  public static final String JSON_PROPERTY_ACTIVITY_CODE = "activityCode";
  private String activityCode;

  public HandledWorkerRecordLinkType() {
  }

  public HandledWorkerRecordLinkType employerClass(String employerClass) {
    
    this.employerClass = employerClass;
    return this;
  }

  /**
   * Get employerClass
   * @return employerClass
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_CLASS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerClass() {
    return employerClass;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_CLASS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerClass(String employerClass) {
    this.employerClass = employerClass;
  }

  public HandledWorkerRecordLinkType workerCode(String workerCode) {
    
    this.workerCode = workerCode;
    return this;
  }

  /**
   * Get workerCode
   * @return workerCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCode() {
    return workerCode;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCode(String workerCode) {
    this.workerCode = workerCode;
  }

  public HandledWorkerRecordLinkType activityCode(String activityCode) {
    
    this.activityCode = activityCode;
    return this;
  }

  /**
   * Get activityCode
   * @return activityCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTIVITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getActivityCode() {
    return activityCode;
  }


  @JsonProperty(JSON_PROPERTY_ACTIVITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setActivityCode(String activityCode) {
    this.activityCode = activityCode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandledWorkerRecordLinkType handledWorkerRecordLinkType = (HandledWorkerRecordLinkType) o;
    return Objects.equals(this.employerClass, handledWorkerRecordLinkType.employerClass) &&
        Objects.equals(this.workerCode, handledWorkerRecordLinkType.workerCode) &&
        Objects.equals(this.activityCode, handledWorkerRecordLinkType.activityCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(employerClass, workerCode, activityCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandledWorkerRecordLinkType {\n");
    sb.append("    employerClass: ").append(toIndentedString(employerClass)).append("\n");
    sb.append("    workerCode: ").append(toIndentedString(workerCode)).append("\n");
    sb.append("    activityCode: ").append(toIndentedString(activityCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

