<div class="onemrva-theme">
    <lib-loading-component></lib-loading-component>

    <lib-cu-task-status-message *ngIf="dataConsistencyData?.basicInfo"
                                [status]="status"
                                [decisionType]="dataConsistencyData.basicInfo?.decisionType"
                                [decisionBarema]="dataConsistencyData.basicInfo?.decisionBarema"
                                [pushbackStatus]="dataConsistencyData.basicInfo?.pushbackStatus">
    </lib-cu-task-status-message>

    <lib-cu-c9-annexes [language]="language" [annexes]="citizenData()?.annexes">
    </lib-cu-c9-annexes>

    <lib-data-consistency
            [language]="language"
            [taskStatus]="status"
            [task]="task"
            [dataConsistencyData]="dataConsistencyData"
            (fieldSourcesChange)="onFieldSourcesChange($event)"
            (regisVerificationEvent)="handleRegisVerificationChange($event)"
            (tableConsistencyChanged)="onTableConsistencyChanged($event)"
    ></lib-data-consistency>
    <div class="actions">
        <button mat-button
                id="saveAsDraftButton"
                [hidden]="isFormClosedOrWaiting()"
                (click)="save()"
                color="primary"
                aria-label="Basic"
                data-cy="saveAsDraftRequestButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SAVE_AS_DRAFT' | translate }}
        </button>

        <button mat-stroked-button
                id="sendC51Button"
                [hidden]="isFormClosedOrWaiting()"
                (click)="sendC51()"
                color="primary"
                aria-label="Basic"
                data-cy="sendC51RequestButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.SEND_C51' | translate }}
        </button>

        <button mat-flat-button
                id="validateButton"
                [hidden]="isFormClosedOrWaiting()"
                [disabled]="!areActionButtonsEnabled || !isTableConsistent"
                (click)="validateAndContinue()"
                color="accent"
                aria-label="Basic"
                data-cy="validateAndContinueButton"
        >{{ 'CU_DATA_CONSISTENCY.BUTTONS.VALIDATE' | translate }}
        </button>

        <lib-s24-action-button
                [isHidden]="isFormClosedOrWaiting()"
                [requestId]="requestId">
        </lib-s24-action-button>

    </div>
</div>
