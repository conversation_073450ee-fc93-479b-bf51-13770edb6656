/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * KnownHistory
 */
@JsonPropertyOrder({
  KnownHistory.JSON_PROPERTY_CURRENT,
  KnownHistory.JSON_PROPERTY_SSIN
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class KnownHistory {
  public static final String JSON_PROPERTY_CURRENT = "current";
  private Boolean current;

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public KnownHistory() {
  }

  public KnownHistory current(Boolean current) {
    
    this.current = current;
    return this;
  }

  /**
   * Get current
   * @return current
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CURRENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCurrent() {
    return current;
  }


  @JsonProperty(JSON_PROPERTY_CURRENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCurrent(Boolean current) {
    this.current = current;
  }

  public KnownHistory ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    KnownHistory knownHistory = (KnownHistory) o;
    return Objects.equals(this.current, knownHistory.current) &&
        Objects.equals(this.ssin, knownHistory.ssin);
  }

  @Override
  public int hashCode() {
    return Objects.hash(current, ssin);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class KnownHistory {\n");
    sb.append("    current: ").append(toIndentedString(current)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

