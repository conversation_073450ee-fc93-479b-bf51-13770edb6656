/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface RedirectApi {

    @Operation(
        tags = ["Redirect",],
        summary = "",
        operationId = "getC51Redirect",
        description = """Get a redirect URL to the C51 application""",
        responses = [
            ApiResponse(responseCode = "200", description = "Request information successfully retrieved", content = [Content(schema = Schema(implementation = kotlin.String::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/request/c51/{requestId}"],
            produces = ["text/plain"]
    )
    fun getC51Redirect(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<kotlin.String>

    @Operation(
        tags = ["Redirect",],
        summary = "",
        operationId = "getRegisRedirect",
        description = """Get a redirect URL to the Regis application""",
        responses = [
            ApiResponse(responseCode = "200", description = "Regis URL successfully generated", content = [Content(schema = Schema(implementation = kotlin.String::class))]),
            ApiResponse(responseCode = "404", description = "Request not found or failed to generate Regis URL")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/request/regis/{requestId}"],
            produces = ["text/plain"]
    )
    fun getRegisRedirect(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@NotNull @Parameter(description = "The language code (fr for French, nl for Dutch)", required = true, schema = Schema(allowableValues = ["fr", "nl"])) @Valid @RequestParam(value = "languageCode", required = true) languageCode: kotlin.String): ResponseEntity<kotlin.String>

    @Operation(
        tags = ["Redirect",],
        summary = "",
        operationId = "openS24Session",
        description = """Opens a session to the mainframe when a T27 session is open""",
        responses = [
            ApiResponse(responseCode = "200", description = "No content returned on success"),
            ApiResponse(responseCode = "404", description = "Request not found or failed to open S24 session")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/request/s24/{requestId}"]
    )
    fun openS24Session(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<Unit>
}
