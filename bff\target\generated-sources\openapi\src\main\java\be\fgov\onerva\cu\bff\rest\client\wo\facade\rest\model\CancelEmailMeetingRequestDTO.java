/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CancelEmailMeetingRequestDTO
 */
@JsonPropertyOrder({
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_RECIPIENTS,
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_TITLE,
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_START_DATE,
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_END_DATE,
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_CONTENT,
  CancelEmailMeetingRequestDTO.JSON_PROPERTY_RECURRING
})
@JsonTypeName("CancelEmailMeetingRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CancelEmailMeetingRequestDTO {
  public static final String JSON_PROPERTY_RECIPIENTS = "recipients";
  private List<String> recipients = new ArrayList<>();

  public static final String JSON_PROPERTY_TITLE = "title";
  private String title;

  public static final String JSON_PROPERTY_START_DATE = "startDate";
  private LocalDateTime startDate;

  public static final String JSON_PROPERTY_END_DATE = "endDate";
  private LocalDateTime endDate;

  public static final String JSON_PROPERTY_CONTENT = "content";
  private String content;

  public static final String JSON_PROPERTY_RECURRING = "recurring";
  private Boolean recurring = false;

  public CancelEmailMeetingRequestDTO() {
  }

  public CancelEmailMeetingRequestDTO recipients(List<String> recipients) {
    
    this.recipients = recipients;
    return this;
  }

  public CancelEmailMeetingRequestDTO addRecipientsItem(String recipientsItem) {
    if (this.recipients == null) {
      this.recipients = new ArrayList<>();
    }
    this.recipients.add(recipientsItem);
    return this;
  }

  /**
   * emails you want to send the meeting to
   * @return recipients
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RECIPIENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getRecipients() {
    return recipients;
  }


  @JsonProperty(JSON_PROPERTY_RECIPIENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRecipients(List<String> recipients) {
    this.recipients = recipients;
  }

  public CancelEmailMeetingRequestDTO title(String title) {
    
    this.title = title;
    return this;
  }

  /**
   * Meeting title. Will update old value
   * @return title
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTitle() {
    return title;
  }


  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTitle(String title) {
    this.title = title;
  }

  public CancelEmailMeetingRequestDTO startDate(LocalDateTime startDate) {
    
    this.startDate = startDate;
    return this;
  }

  /**
   * Start date (and optionally time) of the meeting. Must be a LocalDateTime format
   * @return startDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getStartDate() {
    return startDate;
  }


  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStartDate(LocalDateTime startDate) {
    this.startDate = startDate;
  }

  public CancelEmailMeetingRequestDTO endDate(LocalDateTime endDate) {
    
    this.endDate = endDate;
    return this;
  }

  /**
   * End date (and optionally time) of the meeting. Must be a LocalDateTime format
   * @return endDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getEndDate() {
    return endDate;
  }


  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEndDate(LocalDateTime endDate) {
    this.endDate = endDate;
  }

  public CancelEmailMeetingRequestDTO content(String content) {
    
    this.content = content;
    return this;
  }

  /**
   * Content of the meeting. Can be used to explain the cancellation of the meeting
   * @return content
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContent() {
    return content;
  }


  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContent(String content) {
    this.content = content;
  }

  public CancelEmailMeetingRequestDTO recurring(Boolean recurring) {
    
    this.recurring = recurring;
    return this;
  }

  /**
   * if set to true, will cancel the meetings. If the startDate day and the End date Day are the same, it will only cancel that day. Otherwise, cancel all meeting recurrence
   * @return recurring
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RECURRING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRecurring() {
    return recurring;
  }


  @JsonProperty(JSON_PROPERTY_RECURRING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRecurring(Boolean recurring) {
    this.recurring = recurring;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CancelEmailMeetingRequestDTO cancelEmailMeetingRequest = (CancelEmailMeetingRequestDTO) o;
    return Objects.equals(this.recipients, cancelEmailMeetingRequest.recipients) &&
        Objects.equals(this.title, cancelEmailMeetingRequest.title) &&
        Objects.equals(this.startDate, cancelEmailMeetingRequest.startDate) &&
        Objects.equals(this.endDate, cancelEmailMeetingRequest.endDate) &&
        Objects.equals(this.content, cancelEmailMeetingRequest.content) &&
        Objects.equals(this.recurring, cancelEmailMeetingRequest.recurring);
  }

  @Override
  public int hashCode() {
    return Objects.hash(recipients, title, startDate, endDate, content, recurring);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CancelEmailMeetingRequestDTO {\n");
    sb.append("    recipients: ").append(toIndentedString(recipients)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    startDate: ").append(toIndentedString(startDate)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    recurring: ").append(toIndentedString(recurring)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

