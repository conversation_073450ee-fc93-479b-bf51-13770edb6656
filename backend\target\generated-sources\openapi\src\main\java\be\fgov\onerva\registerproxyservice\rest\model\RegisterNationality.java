/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RegisterNationality
 */
@JsonPropertyOrder({
  RegisterNationality.JSON_PROPERTY_NATIONALITY_CODE,
  RegisterNationality.JSON_PROPERTY_VALIDITY_BEGIN_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RegisterNationality {
  public static final String JSON_PROPERTY_NATIONALITY_CODE = "nationalityCode";
  private Integer nationalityCode;

  public static final String JSON_PROPERTY_VALIDITY_BEGIN_DATE = "validityBeginDate";
  private Long validityBeginDate;

  public RegisterNationality() {
  }

  public RegisterNationality nationalityCode(Integer nationalityCode) {
    
    this.nationalityCode = nationalityCode;
    return this;
  }

  /**
   * Get nationalityCode
   * @return nationalityCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getNationalityCode() {
    return nationalityCode;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationalityCode(Integer nationalityCode) {
    this.nationalityCode = nationalityCode;
  }

  public RegisterNationality validityBeginDate(Long validityBeginDate) {
    
    this.validityBeginDate = validityBeginDate;
    return this;
  }

  /**
   * Validity start date represented as Unix timestamp in milliseconds (UTC-based)
   * @return validityBeginDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALIDITY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getValidityBeginDate() {
    return validityBeginDate;
  }


  @JsonProperty(JSON_PROPERTY_VALIDITY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValidityBeginDate(Long validityBeginDate) {
    this.validityBeginDate = validityBeginDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegisterNationality registerNationality = (RegisterNationality) o;
    return Objects.equals(this.nationalityCode, registerNationality.nationalityCode) &&
        Objects.equals(this.validityBeginDate, registerNationality.validityBeginDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nationalityCode, validityBeginDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegisterNationality {\n");
    sb.append("    nationalityCode: ").append(toIndentedString(nationalityCode)).append("\n");
    sb.append("    validityBeginDate: ").append(toIndentedString(validityBeginDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

