import {HttpClient, HttpHeaders} from "@angular/common/http";
import {Injectable} from "@angular/core";
import {FormControl} from "@angular/forms";
import {
    BehaviorSubject,
    debounceTime,
    distinctUntilChanged,
    finalize,
    map,
    Observable,
    of,
    switchMap,
    tap,
} from "rxjs";

import {Configuration as ConfigurationBff, LookupService, NationalityResponse} from "@rest-client/cu-bff";
import {ConfigService} from "./../config/config.service";

export type GeoEntity = NationalityResponse;

@Injectable({
    providedIn: "root",
})
export class GeoLookupService {
    private lookupService!: LookupService;
    private allGeoEntities: GeoEntity[] = [];
    readonly loading$ = new BehaviorSubject<boolean>(false);
    readonly dataReady$ = new BehaviorSubject<boolean>(false);

    constructor(
        readonly http: HttpClient,
        readonly configService: ConfigService,
    ) {
        this.initializeService();
    }

    displayLookup(lookup: GeoEntity | undefined, language: string): string {
        if (!lookup) {
            return "";
        }
        return language.toUpperCase() === "FR" ? lookup.descFr : lookup.descNl;
    }

    initializeService(token?: string) {
        const configBff = new ConfigurationBff({
            basePath: this.configService.getEnvironmentVariable("bffBaseUrl", true),
            credentials: token ? {"Bearer": token} : undefined,
        });

        const defaultHeaders = token ?
            new HttpHeaders()
                .set("Authorization", `Bearer ${token}`)
                .set("Content-Type", "application/json") :
            new HttpHeaders().set("Content-Type", "application/json");

        this.lookupService = new LookupService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );

        if (token) {
            this.lookupService.defaultHeaders = defaultHeaders;
        }

        this.loadAllGeoEntities(!!token);
    }

    private loadAllGeoEntities(force: boolean = false): void {
        if ((this.dataReady$.value || this.loading$.value) && !force) {
            return;
        }

        if (this.configService.isOnWO() && !force) {
            return;
        }

        this.loading$.next(true);

        this.lookupService.searchNationality("").pipe(
            tap(entities => {
                this.allGeoEntities = entities;
                this.dataReady$.next(true);
            }),
            finalize(() => this.loading$.next(false)),
        ).subscribe({
            error: error => console.error("Error loading geo entities:", error),
        });
    }

    getEntityByCode(code: string): GeoEntity | undefined {
        return this.allGeoEntities.find(entity => entity.code === code);
    }

    getEntityDescription(code: string, language: string): string {
        const entity = this.getEntityByCode(code);
        if (!entity) {
            return code;
        } // Return code if entity not found

        return language.toUpperCase() === "FR" ? entity.descFr : entity.descNl;
    }

    setupSearchControl(control: FormControl,
                       type: "country" | "nationality" = "nationality"): Observable<GeoEntity[]> {
        return control.valueChanges.pipe(
            debounceTime(300),
            distinctUntilChanged(),
            switchMap(value => {
                // If control value is an object, return empty
                if (value && typeof value === "object") {
                    return of([]);
                }

                if (value && typeof value === "string") {
                    // If data is loaded, filter locally
                    if (this.dataReady$.value && this.allGeoEntities.length > 0) {
                        const searchValue = value.toLowerCase();
                        const results = this.allGeoEntities.filter(item =>
                            this.normalizeText(item.code.toLowerCase()).includes(searchValue) ||
                            this.normalizeText(item.descFr.toLowerCase()).includes(searchValue) ||
                            this.normalizeText(item.descNl.toLowerCase()).includes(searchValue),
                        );
                        return of(results);
                    }

                    // If data is not loaded yet, make API call as fallback
                    return this.lookupService.searchNationality(value);
                }

                return of([]);
            }),
        );
    }

    searchCountryByCode(code: string): Observable<GeoEntity | null> {
        return this.searchEntityByCode(code);
    }

    searchNationalityByCode(code: string): Observable<GeoEntity | null> {
        return this.searchEntityByCode(code);
    }

    private searchEntityByCode(code: string): Observable<GeoEntity | null> {
        if (this.dataReady$.value) { // get cache if there else do call
            return of(this.getEntityByCode(code) || null);
        }
        return this.lookupService.searchNationality(code).pipe(
            map(entities => entities.find(entity => entity.code === code) || null),
        );
    }

    private normalizeText(text: string): string {
        return text.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
    }
}