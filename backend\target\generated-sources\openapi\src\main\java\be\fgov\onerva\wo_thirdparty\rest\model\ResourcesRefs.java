/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo_thirdparty.rest.model.Href;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ResourcesRefs
 */
@JsonPropertyOrder({
  ResourcesRefs.JSON_PROPERTY_API_DOC,
  ResourcesRefs.JSON_PROPERTY_SWAGGER,
  ResourcesRefs.JSON_PROPERTY_HEALTH_CHECK,
  ResourcesRefs.JSON_PROPERTY_RESOURCE_COLON_PARTY,
  ResourcesRefs.JSON_PROPERTY_RESOURCE_COLON_MERGES,
  ResourcesRefs.JSON_PROPERTY_RESOURCE_COLON_RELATED_PARTIES,
  ResourcesRefs.JSON_PROPERTY_RESOURCE_COLON_DEFINITIONS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ResourcesRefs {
  public static final String JSON_PROPERTY_API_DOC = "apiDoc";
  private Href apiDoc;

  public static final String JSON_PROPERTY_SWAGGER = "swagger";
  private Href swagger;

  public static final String JSON_PROPERTY_HEALTH_CHECK = "healthCheck";
  private Href healthCheck;

  public static final String JSON_PROPERTY_RESOURCE_COLON_PARTY = "resource:party";
  private List<Href> resourceColonParty = new ArrayList<>();

  public static final String JSON_PROPERTY_RESOURCE_COLON_MERGES = "resource:merges";
  private List<Href> resourceColonMerges = new ArrayList<>();

  public static final String JSON_PROPERTY_RESOURCE_COLON_RELATED_PARTIES = "resource:relatedParties";
  private List<Href> resourceColonRelatedParties = new ArrayList<>();

  public static final String JSON_PROPERTY_RESOURCE_COLON_DEFINITIONS = "resource:definitions";
  private List<Href> resourceColonDefinitions = new ArrayList<>();

  public ResourcesRefs() {
  }

  public ResourcesRefs apiDoc(Href apiDoc) {
    
    this.apiDoc = apiDoc;
    return this;
  }

  /**
   * Get apiDoc
   * @return apiDoc
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_API_DOC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Href getApiDoc() {
    return apiDoc;
  }


  @JsonProperty(JSON_PROPERTY_API_DOC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApiDoc(Href apiDoc) {
    this.apiDoc = apiDoc;
  }

  public ResourcesRefs swagger(Href swagger) {
    
    this.swagger = swagger;
    return this;
  }

  /**
   * Get swagger
   * @return swagger
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SWAGGER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Href getSwagger() {
    return swagger;
  }


  @JsonProperty(JSON_PROPERTY_SWAGGER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSwagger(Href swagger) {
    this.swagger = swagger;
  }

  public ResourcesRefs healthCheck(Href healthCheck) {
    
    this.healthCheck = healthCheck;
    return this;
  }

  /**
   * Get healthCheck
   * @return healthCheck
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HEALTH_CHECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Href getHealthCheck() {
    return healthCheck;
  }


  @JsonProperty(JSON_PROPERTY_HEALTH_CHECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHealthCheck(Href healthCheck) {
    this.healthCheck = healthCheck;
  }

  public ResourcesRefs resourceColonParty(List<Href> resourceColonParty) {
    
    this.resourceColonParty = resourceColonParty;
    return this;
  }

  public ResourcesRefs addResourceColonPartyItem(Href resourceColonPartyItem) {
    if (this.resourceColonParty == null) {
      this.resourceColonParty = new ArrayList<>();
    }
    this.resourceColonParty.add(resourceColonPartyItem);
    return this;
  }

  /**
   * Get resourceColonParty
   * @return resourceColonParty
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_PARTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Href> getResourceColonParty() {
    return resourceColonParty;
  }


  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_PARTY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResourceColonParty(List<Href> resourceColonParty) {
    this.resourceColonParty = resourceColonParty;
  }

  public ResourcesRefs resourceColonMerges(List<Href> resourceColonMerges) {
    
    this.resourceColonMerges = resourceColonMerges;
    return this;
  }

  public ResourcesRefs addResourceColonMergesItem(Href resourceColonMergesItem) {
    if (this.resourceColonMerges == null) {
      this.resourceColonMerges = new ArrayList<>();
    }
    this.resourceColonMerges.add(resourceColonMergesItem);
    return this;
  }

  /**
   * Get resourceColonMerges
   * @return resourceColonMerges
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_MERGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Href> getResourceColonMerges() {
    return resourceColonMerges;
  }


  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_MERGES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResourceColonMerges(List<Href> resourceColonMerges) {
    this.resourceColonMerges = resourceColonMerges;
  }

  public ResourcesRefs resourceColonRelatedParties(List<Href> resourceColonRelatedParties) {
    
    this.resourceColonRelatedParties = resourceColonRelatedParties;
    return this;
  }

  public ResourcesRefs addResourceColonRelatedPartiesItem(Href resourceColonRelatedPartiesItem) {
    if (this.resourceColonRelatedParties == null) {
      this.resourceColonRelatedParties = new ArrayList<>();
    }
    this.resourceColonRelatedParties.add(resourceColonRelatedPartiesItem);
    return this;
  }

  /**
   * Get resourceColonRelatedParties
   * @return resourceColonRelatedParties
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_RELATED_PARTIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Href> getResourceColonRelatedParties() {
    return resourceColonRelatedParties;
  }


  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_RELATED_PARTIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResourceColonRelatedParties(List<Href> resourceColonRelatedParties) {
    this.resourceColonRelatedParties = resourceColonRelatedParties;
  }

  public ResourcesRefs resourceColonDefinitions(List<Href> resourceColonDefinitions) {
    
    this.resourceColonDefinitions = resourceColonDefinitions;
    return this;
  }

  public ResourcesRefs addResourceColonDefinitionsItem(Href resourceColonDefinitionsItem) {
    if (this.resourceColonDefinitions == null) {
      this.resourceColonDefinitions = new ArrayList<>();
    }
    this.resourceColonDefinitions.add(resourceColonDefinitionsItem);
    return this;
  }

  /**
   * Get resourceColonDefinitions
   * @return resourceColonDefinitions
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_DEFINITIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Href> getResourceColonDefinitions() {
    return resourceColonDefinitions;
  }


  @JsonProperty(JSON_PROPERTY_RESOURCE_COLON_DEFINITIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setResourceColonDefinitions(List<Href> resourceColonDefinitions) {
    this.resourceColonDefinitions = resourceColonDefinitions;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ResourcesRefs resourcesRefs = (ResourcesRefs) o;
    return Objects.equals(this.apiDoc, resourcesRefs.apiDoc) &&
        Objects.equals(this.swagger, resourcesRefs.swagger) &&
        Objects.equals(this.healthCheck, resourcesRefs.healthCheck) &&
        Objects.equals(this.resourceColonParty, resourcesRefs.resourceColonParty) &&
        Objects.equals(this.resourceColonMerges, resourcesRefs.resourceColonMerges) &&
        Objects.equals(this.resourceColonRelatedParties, resourcesRefs.resourceColonRelatedParties) &&
        Objects.equals(this.resourceColonDefinitions, resourcesRefs.resourceColonDefinitions);
  }

  @Override
  public int hashCode() {
    return Objects.hash(apiDoc, swagger, healthCheck, resourceColonParty, resourceColonMerges, resourceColonRelatedParties, resourceColonDefinitions);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ResourcesRefs {\n");
    sb.append("    apiDoc: ").append(toIndentedString(apiDoc)).append("\n");
    sb.append("    swagger: ").append(toIndentedString(swagger)).append("\n");
    sb.append("    healthCheck: ").append(toIndentedString(healthCheck)).append("\n");
    sb.append("    resourceColonParty: ").append(toIndentedString(resourceColonParty)).append("\n");
    sb.append("    resourceColonMerges: ").append(toIndentedString(resourceColonMerges)).append("\n");
    sb.append("    resourceColonRelatedParties: ").append(toIndentedString(resourceColonRelatedParties)).append("\n");
    sb.append("    resourceColonDefinitions: ").append(toIndentedString(resourceColonDefinitions)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

