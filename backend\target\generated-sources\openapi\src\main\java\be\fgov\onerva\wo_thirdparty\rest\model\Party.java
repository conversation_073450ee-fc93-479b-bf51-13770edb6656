/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo_thirdparty.rest.model.Address;
import be.fgov.onerva.wo_thirdparty.rest.model.BusinessKey;
import be.fgov.onerva.wo_thirdparty.rest.model.ContactInfo;
import be.fgov.onerva.wo_thirdparty.rest.model.EmployerInformation;
import be.fgov.onerva.wo_thirdparty.rest.model.ExtraInformation;
import be.fgov.onerva.wo_thirdparty.rest.model.HalLinks;
import be.fgov.onerva.wo_thirdparty.rest.model.TranslatedDenomination;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Party
 */
@JsonPropertyOrder({
  Party.JSON_PROPERTY_ID,
  Party.JSON_PROPERTY_ACTIVE,
  Party.JSON_PROPERTY_SOURCE,
  Party.JSON_PROPERTY_DENOMINATION,
  Party.JSON_PROPERTY_TRANSLATED_DENOMINATION,
  Party.JSON_PROPERTY_FIRSTNAME,
  Party.JSON_PROPERTY_LASTNAME,
  Party.JSON_PROPERTY_PARTY_TYPE,
  Party.JSON_PROPERTY_PRODUCER,
  Party.JSON_PROPERTY_EMPLOYER_INFORMATION,
  Party.JSON_PROPERTY_BUSINESS_KEYS,
  Party.JSON_PROPERTY_SSIN,
  Party.JSON_PROPERTY_SOURCE_SSIN,
  Party.JSON_PROPERTY_COMPANY_I_D,
  Party.JSON_PROPERTY_SOURCE_COMPANY_ID,
  Party.JSON_PROPERTY_SSA,
  Party.JSON_PROPERTY_SOURCE_SSA,
  Party.JSON_PROPERTY_SFS,
  Party.JSON_PROPERTY_SOURCE_SFS,
  Party.JSON_PROPERTY_NSSO_REGISTRATION_NBR,
  Party.JSON_PROPERTY_SOURCE_NSSO_REGISTRATION_NBR,
  Party.JSON_PROPERTY_JURIST_ID,
  Party.JSON_PROPERTY_SOURCE_JURIST_ID,
  Party.JSON_PROPERTY_CURATOR_ID,
  Party.JSON_PROPERTY_SOURCE_CURATOR_ID,
  Party.JSON_PROPERTY_OSS_CONTRACT_NBR,
  Party.JSON_PROPERTY_SOURCE_OSS_CONTRACT_NBR,
  Party.JSON_PROPERTY_OSS_REGISTRATION_NBR,
  Party.JSON_PROPERTY_SOURCE_OSS_REGISTRATION_NBR,
  Party.JSON_PROPERTY_NIHII_NBR,
  Party.JSON_PROPERTY_SOURCE_NIHII_NBR,
  Party.JSON_PROPERTY_FEEN,
  Party.JSON_PROPERTY_SOURCE_FEEN,
  Party.JSON_PROPERTY_EU_V_A_T,
  Party.JSON_PROPERTY_NSSO_NODE_ID,
  Party.JSON_PROPERTY_SOURCE_NSSO_NODE_ID,
  Party.JSON_PROPERTY_AGENT_NBR,
  Party.JSON_PROPERTY_SOURCE_AGENT_NBR,
  Party.JSON_PROPERTY_SOURCEEU_V_A_T,
  Party.JSON_PROPERTY_FOLEEN_I_D,
  Party.JSON_PROPERTY_SOURCE_FOLEEN_I_D,
  Party.JSON_PROPERTY_DEPARTMENT_ID,
  Party.JSON_PROPERTY_SOURCE_DEPARTMENT_ID,
  Party.JSON_PROPERTY_OTHER,
  Party.JSON_PROPERTY_SOURCE_OTHER,
  Party.JSON_PROPERTY_ADDRESS,
  Party.JSON_PROPERTY_CONTACT_LANGUAGE,
  Party.JSON_PROPERTY_CONTACT_INFO,
  Party.JSON_PROPERTY_START_DATE,
  Party.JSON_PROPERTY_END_DATE,
  Party.JSON_PROPERTY_CREATED_BY,
  Party.JSON_PROPERTY_STATUS,
  Party.JSON_PROPERTY_EXTRA_INFORMATION,
  Party.JSON_PROPERTY_LINKS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Party {
  public static final String JSON_PROPERTY_ID = "id";
  private String id;

  public static final String JSON_PROPERTY_ACTIVE = "active";
  private Boolean active;

  public static final String JSON_PROPERTY_SOURCE = "source";
  private String source;

  public static final String JSON_PROPERTY_DENOMINATION = "denomination";
  private String denomination;

  public static final String JSON_PROPERTY_TRANSLATED_DENOMINATION = "translatedDenomination";
  private TranslatedDenomination translatedDenomination;

  public static final String JSON_PROPERTY_FIRSTNAME = "firstname";
  private String firstname;

  public static final String JSON_PROPERTY_LASTNAME = "lastname";
  private String lastname;

  public static final String JSON_PROPERTY_PARTY_TYPE = "partyType";
  private String partyType;

  public static final String JSON_PROPERTY_PRODUCER = "producer";
  private String producer;

  public static final String JSON_PROPERTY_EMPLOYER_INFORMATION = "employerInformation";
  private EmployerInformation employerInformation;

  public static final String JSON_PROPERTY_BUSINESS_KEYS = "businessKeys";
  private List<BusinessKey> businessKeys = new ArrayList<>();

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_SOURCE_SSIN = "sourceSsin";
  private String sourceSsin;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_SOURCE_COMPANY_ID = "sourceCompanyId";
  private String sourceCompanyId;

  public static final String JSON_PROPERTY_SSA = "ssa";
  private String ssa;

  public static final String JSON_PROPERTY_SOURCE_SSA = "sourceSsa";
  private String sourceSsa;

  public static final String JSON_PROPERTY_SFS = "sfs";
  private String sfs;

  public static final String JSON_PROPERTY_SOURCE_SFS = "sourceSfs";
  private String sourceSfs;

  public static final String JSON_PROPERTY_NSSO_REGISTRATION_NBR = "nssoRegistrationNbr";
  private String nssoRegistrationNbr;

  public static final String JSON_PROPERTY_SOURCE_NSSO_REGISTRATION_NBR = "sourceNssoRegistrationNbr";
  private String sourceNssoRegistrationNbr;

  public static final String JSON_PROPERTY_JURIST_ID = "juristId";
  private String juristId;

  public static final String JSON_PROPERTY_SOURCE_JURIST_ID = "sourceJuristId";
  private String sourceJuristId;

  public static final String JSON_PROPERTY_CURATOR_ID = "curatorId";
  private String curatorId;

  public static final String JSON_PROPERTY_SOURCE_CURATOR_ID = "sourceCuratorId";
  private String sourceCuratorId;

  public static final String JSON_PROPERTY_OSS_CONTRACT_NBR = "ossContractNbr";
  private String ossContractNbr;

  public static final String JSON_PROPERTY_SOURCE_OSS_CONTRACT_NBR = "sourceOssContractNbr";
  private String sourceOssContractNbr;

  public static final String JSON_PROPERTY_OSS_REGISTRATION_NBR = "ossRegistrationNbr";
  private String ossRegistrationNbr;

  public static final String JSON_PROPERTY_SOURCE_OSS_REGISTRATION_NBR = "sourceOssRegistrationNbr";
  private String sourceOssRegistrationNbr;

  public static final String JSON_PROPERTY_NIHII_NBR = "nihiiNbr";
  private String nihiiNbr;

  public static final String JSON_PROPERTY_SOURCE_NIHII_NBR = "sourceNihiiNbr";
  private String sourceNihiiNbr;

  public static final String JSON_PROPERTY_FEEN = "feen";
  private String feen;

  public static final String JSON_PROPERTY_SOURCE_FEEN = "sourceFeen";
  private String sourceFeen;

  public static final String JSON_PROPERTY_EU_V_A_T = "euVAT";
  private String euVAT;

  public static final String JSON_PROPERTY_NSSO_NODE_ID = "nssoNodeId";
  private String nssoNodeId;

  public static final String JSON_PROPERTY_SOURCE_NSSO_NODE_ID = "sourceNssoNodeId";
  private String sourceNssoNodeId;

  public static final String JSON_PROPERTY_AGENT_NBR = "agentNbr";
  private String agentNbr;

  public static final String JSON_PROPERTY_SOURCE_AGENT_NBR = "sourceAgentNbr";
  private String sourceAgentNbr;

  public static final String JSON_PROPERTY_SOURCEEU_V_A_T = "sourceeuVAT";
  private String sourceeuVAT;

  public static final String JSON_PROPERTY_FOLEEN_I_D = "foleenID";
  private String foleenID;

  public static final String JSON_PROPERTY_SOURCE_FOLEEN_I_D = "sourceFoleenID";
  private String sourceFoleenID;

  public static final String JSON_PROPERTY_DEPARTMENT_ID = "departmentId";
  private String departmentId;

  public static final String JSON_PROPERTY_SOURCE_DEPARTMENT_ID = "sourceDepartmentId";
  private String sourceDepartmentId;

  public static final String JSON_PROPERTY_OTHER = "other";
  private String other;

  public static final String JSON_PROPERTY_SOURCE_OTHER = "sourceOther";
  private String sourceOther;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private Address address;

  /**
   * Gets or Sets contactLanguage
   */
  public enum ContactLanguageEnum {
    BI("BI"),
    
    DE("DE"),
    
    EN("EN"),
    
    FR("FR"),
    
    NL("NL");

    private String value;

    ContactLanguageEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static ContactLanguageEnum fromValue(String value) {
      for (ContactLanguageEnum b : ContactLanguageEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_CONTACT_LANGUAGE = "contactLanguage";
  private ContactLanguageEnum contactLanguage;

  public static final String JSON_PROPERTY_CONTACT_INFO = "contactInfo";
  private ContactInfo contactInfo;

  public static final String JSON_PROPERTY_START_DATE = "startDate";
  private String startDate;

  public static final String JSON_PROPERTY_END_DATE = "endDate";
  private String endDate;

  public static final String JSON_PROPERTY_CREATED_BY = "createdBy";
  private String createdBy;

  /**
   * Gets or Sets status
   */
  public enum StatusEnum {
    AUTHENTICATED("AUTHENTICATED"),
    
    TEMPORARY("TEMPORARY");

    private String value;

    StatusEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static StatusEnum fromValue(String value) {
      for (StatusEnum b : StatusEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_STATUS = "status";
  private StatusEnum status;

  public static final String JSON_PROPERTY_EXTRA_INFORMATION = "extraInformation";
  private List<ExtraInformation> extraInformation = new ArrayList<>();

  public static final String JSON_PROPERTY_LINKS = "_links";
  private HalLinks links;

  public Party() {
  }

  public Party id(String id) {
    
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(String id) {
    this.id = id;
  }

  public Party active(Boolean active) {
    
    this.active = active;
    return this;
  }

  /**
   * Get active
   * @return active
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getActive() {
    return active;
  }


  @JsonProperty(JSON_PROPERTY_ACTIVE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setActive(Boolean active) {
    this.active = active;
  }

  public Party source(String source) {
    
    this.source = source;
    return this;
  }

  /**
   * Get source
   * @return source
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSource() {
    return source;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSource(String source) {
    this.source = source;
  }

  public Party denomination(String denomination) {
    
    this.denomination = denomination;
    return this;
  }

  /**
   * Get denomination
   * @return denomination
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDenomination() {
    return denomination;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDenomination(String denomination) {
    this.denomination = denomination;
  }

  public Party translatedDenomination(TranslatedDenomination translatedDenomination) {
    
    this.translatedDenomination = translatedDenomination;
    return this;
  }

  /**
   * Get translatedDenomination
   * @return translatedDenomination
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRANSLATED_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public TranslatedDenomination getTranslatedDenomination() {
    return translatedDenomination;
  }


  @JsonProperty(JSON_PROPERTY_TRANSLATED_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTranslatedDenomination(TranslatedDenomination translatedDenomination) {
    this.translatedDenomination = translatedDenomination;
  }

  public Party firstname(String firstname) {
    
    this.firstname = firstname;
    return this;
  }

  /**
   * Get firstname
   * @return firstname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstname() {
    return firstname;
  }


  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstname(String firstname) {
    this.firstname = firstname;
  }

  public Party lastname(String lastname) {
    
    this.lastname = lastname;
    return this;
  }

  /**
   * Get lastname
   * @return lastname
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastname() {
    return lastname;
  }


  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastname(String lastname) {
    this.lastname = lastname;
  }

  public Party partyType(String partyType) {
    
    this.partyType = partyType;
    return this;
  }

  /**
   * Get partyType
   * @return partyType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PARTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPartyType() {
    return partyType;
  }


  @JsonProperty(JSON_PROPERTY_PARTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPartyType(String partyType) {
    this.partyType = partyType;
  }

  public Party producer(String producer) {
    
    this.producer = producer;
    return this;
  }

  /**
   * Get producer
   * @return producer
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PRODUCER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getProducer() {
    return producer;
  }


  @JsonProperty(JSON_PROPERTY_PRODUCER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProducer(String producer) {
    this.producer = producer;
  }

  public Party employerInformation(EmployerInformation employerInformation) {
    
    this.employerInformation = employerInformation;
    return this;
  }

  /**
   * Get employerInformation
   * @return employerInformation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public EmployerInformation getEmployerInformation() {
    return employerInformation;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerInformation(EmployerInformation employerInformation) {
    this.employerInformation = employerInformation;
  }

  public Party businessKeys(List<BusinessKey> businessKeys) {
    
    this.businessKeys = businessKeys;
    return this;
  }

  public Party addBusinessKeysItem(BusinessKey businessKeysItem) {
    if (this.businessKeys == null) {
      this.businessKeys = new ArrayList<>();
    }
    this.businessKeys.add(businessKeysItem);
    return this;
  }

  /**
   * Get businessKeys
   * @return businessKeys
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_KEYS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<BusinessKey> getBusinessKeys() {
    return businessKeys;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_KEYS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessKeys(List<BusinessKey> businessKeys) {
    this.businessKeys = businessKeys;
  }

  public Party ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public Party sourceSsin(String sourceSsin) {
    
    this.sourceSsin = sourceSsin;
    return this;
  }

  /**
   * Get sourceSsin
   * @return sourceSsin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceSsin() {
    return sourceSsin;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceSsin(String sourceSsin) {
    this.sourceSsin = sourceSsin;
  }

  public Party companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public Party sourceCompanyId(String sourceCompanyId) {
    
    this.sourceCompanyId = sourceCompanyId;
    return this;
  }

  /**
   * Get sourceCompanyId
   * @return sourceCompanyId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_COMPANY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceCompanyId() {
    return sourceCompanyId;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_COMPANY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceCompanyId(String sourceCompanyId) {
    this.sourceCompanyId = sourceCompanyId;
  }

  public Party ssa(String ssa) {
    
    this.ssa = ssa;
    return this;
  }

  /**
   * Get ssa
   * @return ssa
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsa() {
    return ssa;
  }


  @JsonProperty(JSON_PROPERTY_SSA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsa(String ssa) {
    this.ssa = ssa;
  }

  public Party sourceSsa(String sourceSsa) {
    
    this.sourceSsa = sourceSsa;
    return this;
  }

  /**
   * Get sourceSsa
   * @return sourceSsa
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_SSA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceSsa() {
    return sourceSsa;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_SSA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceSsa(String sourceSsa) {
    this.sourceSsa = sourceSsa;
  }

  public Party sfs(String sfs) {
    
    this.sfs = sfs;
    return this;
  }

  /**
   * Get sfs
   * @return sfs
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SFS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSfs() {
    return sfs;
  }


  @JsonProperty(JSON_PROPERTY_SFS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSfs(String sfs) {
    this.sfs = sfs;
  }

  public Party sourceSfs(String sourceSfs) {
    
    this.sourceSfs = sourceSfs;
    return this;
  }

  /**
   * Get sourceSfs
   * @return sourceSfs
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_SFS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceSfs() {
    return sourceSfs;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_SFS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceSfs(String sourceSfs) {
    this.sourceSfs = sourceSfs;
  }

  public Party nssoRegistrationNbr(String nssoRegistrationNbr) {
    
    this.nssoRegistrationNbr = nssoRegistrationNbr;
    return this;
  }

  /**
   * Get nssoRegistrationNbr
   * @return nssoRegistrationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NSSO_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNssoRegistrationNbr() {
    return nssoRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NSSO_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNssoRegistrationNbr(String nssoRegistrationNbr) {
    this.nssoRegistrationNbr = nssoRegistrationNbr;
  }

  public Party sourceNssoRegistrationNbr(String sourceNssoRegistrationNbr) {
    
    this.sourceNssoRegistrationNbr = sourceNssoRegistrationNbr;
    return this;
  }

  /**
   * Get sourceNssoRegistrationNbr
   * @return sourceNssoRegistrationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_NSSO_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceNssoRegistrationNbr() {
    return sourceNssoRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_NSSO_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceNssoRegistrationNbr(String sourceNssoRegistrationNbr) {
    this.sourceNssoRegistrationNbr = sourceNssoRegistrationNbr;
  }

  public Party juristId(String juristId) {
    
    this.juristId = juristId;
    return this;
  }

  /**
   * Get juristId
   * @return juristId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_JURIST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getJuristId() {
    return juristId;
  }


  @JsonProperty(JSON_PROPERTY_JURIST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setJuristId(String juristId) {
    this.juristId = juristId;
  }

  public Party sourceJuristId(String sourceJuristId) {
    
    this.sourceJuristId = sourceJuristId;
    return this;
  }

  /**
   * Get sourceJuristId
   * @return sourceJuristId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_JURIST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceJuristId() {
    return sourceJuristId;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_JURIST_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceJuristId(String sourceJuristId) {
    this.sourceJuristId = sourceJuristId;
  }

  public Party curatorId(String curatorId) {
    
    this.curatorId = curatorId;
    return this;
  }

  /**
   * Get curatorId
   * @return curatorId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CURATOR_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCuratorId() {
    return curatorId;
  }


  @JsonProperty(JSON_PROPERTY_CURATOR_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCuratorId(String curatorId) {
    this.curatorId = curatorId;
  }

  public Party sourceCuratorId(String sourceCuratorId) {
    
    this.sourceCuratorId = sourceCuratorId;
    return this;
  }

  /**
   * Get sourceCuratorId
   * @return sourceCuratorId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_CURATOR_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceCuratorId() {
    return sourceCuratorId;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_CURATOR_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceCuratorId(String sourceCuratorId) {
    this.sourceCuratorId = sourceCuratorId;
  }

  public Party ossContractNbr(String ossContractNbr) {
    
    this.ossContractNbr = ossContractNbr;
    return this;
  }

  /**
   * Get ossContractNbr
   * @return ossContractNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OSS_CONTRACT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOssContractNbr() {
    return ossContractNbr;
  }


  @JsonProperty(JSON_PROPERTY_OSS_CONTRACT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOssContractNbr(String ossContractNbr) {
    this.ossContractNbr = ossContractNbr;
  }

  public Party sourceOssContractNbr(String sourceOssContractNbr) {
    
    this.sourceOssContractNbr = sourceOssContractNbr;
    return this;
  }

  /**
   * Get sourceOssContractNbr
   * @return sourceOssContractNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_OSS_CONTRACT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceOssContractNbr() {
    return sourceOssContractNbr;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_OSS_CONTRACT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceOssContractNbr(String sourceOssContractNbr) {
    this.sourceOssContractNbr = sourceOssContractNbr;
  }

  public Party ossRegistrationNbr(String ossRegistrationNbr) {
    
    this.ossRegistrationNbr = ossRegistrationNbr;
    return this;
  }

  /**
   * Get ossRegistrationNbr
   * @return ossRegistrationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOssRegistrationNbr() {
    return ossRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_OSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOssRegistrationNbr(String ossRegistrationNbr) {
    this.ossRegistrationNbr = ossRegistrationNbr;
  }

  public Party sourceOssRegistrationNbr(String sourceOssRegistrationNbr) {
    
    this.sourceOssRegistrationNbr = sourceOssRegistrationNbr;
    return this;
  }

  /**
   * Get sourceOssRegistrationNbr
   * @return sourceOssRegistrationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_OSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceOssRegistrationNbr() {
    return sourceOssRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_OSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceOssRegistrationNbr(String sourceOssRegistrationNbr) {
    this.sourceOssRegistrationNbr = sourceOssRegistrationNbr;
  }

  public Party nihiiNbr(String nihiiNbr) {
    
    this.nihiiNbr = nihiiNbr;
    return this;
  }

  /**
   * Get nihiiNbr
   * @return nihiiNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NIHII_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNihiiNbr() {
    return nihiiNbr;
  }


  @JsonProperty(JSON_PROPERTY_NIHII_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNihiiNbr(String nihiiNbr) {
    this.nihiiNbr = nihiiNbr;
  }

  public Party sourceNihiiNbr(String sourceNihiiNbr) {
    
    this.sourceNihiiNbr = sourceNihiiNbr;
    return this;
  }

  /**
   * Get sourceNihiiNbr
   * @return sourceNihiiNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_NIHII_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceNihiiNbr() {
    return sourceNihiiNbr;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_NIHII_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceNihiiNbr(String sourceNihiiNbr) {
    this.sourceNihiiNbr = sourceNihiiNbr;
  }

  public Party feen(String feen) {
    
    this.feen = feen;
    return this;
  }

  /**
   * Get feen
   * @return feen
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FEEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFeen() {
    return feen;
  }


  @JsonProperty(JSON_PROPERTY_FEEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFeen(String feen) {
    this.feen = feen;
  }

  public Party sourceFeen(String sourceFeen) {
    
    this.sourceFeen = sourceFeen;
    return this;
  }

  /**
   * Get sourceFeen
   * @return sourceFeen
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_FEEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceFeen() {
    return sourceFeen;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_FEEN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceFeen(String sourceFeen) {
    this.sourceFeen = sourceFeen;
  }

  public Party euVAT(String euVAT) {
    
    this.euVAT = euVAT;
    return this;
  }

  /**
   * Get euVAT
   * @return euVAT
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EU_V_A_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEuVAT() {
    return euVAT;
  }


  @JsonProperty(JSON_PROPERTY_EU_V_A_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEuVAT(String euVAT) {
    this.euVAT = euVAT;
  }

  public Party nssoNodeId(String nssoNodeId) {
    
    this.nssoNodeId = nssoNodeId;
    return this;
  }

  /**
   * Get nssoNodeId
   * @return nssoNodeId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NSSO_NODE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNssoNodeId() {
    return nssoNodeId;
  }


  @JsonProperty(JSON_PROPERTY_NSSO_NODE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNssoNodeId(String nssoNodeId) {
    this.nssoNodeId = nssoNodeId;
  }

  public Party sourceNssoNodeId(String sourceNssoNodeId) {
    
    this.sourceNssoNodeId = sourceNssoNodeId;
    return this;
  }

  /**
   * Get sourceNssoNodeId
   * @return sourceNssoNodeId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_NSSO_NODE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceNssoNodeId() {
    return sourceNssoNodeId;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_NSSO_NODE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceNssoNodeId(String sourceNssoNodeId) {
    this.sourceNssoNodeId = sourceNssoNodeId;
  }

  public Party agentNbr(String agentNbr) {
    
    this.agentNbr = agentNbr;
    return this;
  }

  /**
   * Get agentNbr
   * @return agentNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AGENT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAgentNbr() {
    return agentNbr;
  }


  @JsonProperty(JSON_PROPERTY_AGENT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAgentNbr(String agentNbr) {
    this.agentNbr = agentNbr;
  }

  public Party sourceAgentNbr(String sourceAgentNbr) {
    
    this.sourceAgentNbr = sourceAgentNbr;
    return this;
  }

  /**
   * Get sourceAgentNbr
   * @return sourceAgentNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_AGENT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceAgentNbr() {
    return sourceAgentNbr;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_AGENT_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceAgentNbr(String sourceAgentNbr) {
    this.sourceAgentNbr = sourceAgentNbr;
  }

  public Party sourceeuVAT(String sourceeuVAT) {
    
    this.sourceeuVAT = sourceeuVAT;
    return this;
  }

  /**
   * Get sourceeuVAT
   * @return sourceeuVAT
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCEEU_V_A_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceeuVAT() {
    return sourceeuVAT;
  }


  @JsonProperty(JSON_PROPERTY_SOURCEEU_V_A_T)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceeuVAT(String sourceeuVAT) {
    this.sourceeuVAT = sourceeuVAT;
  }

  public Party foleenID(String foleenID) {
    
    this.foleenID = foleenID;
    return this;
  }

  /**
   * Get foleenID
   * @return foleenID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FOLEEN_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFoleenID() {
    return foleenID;
  }


  @JsonProperty(JSON_PROPERTY_FOLEEN_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFoleenID(String foleenID) {
    this.foleenID = foleenID;
  }

  public Party sourceFoleenID(String sourceFoleenID) {
    
    this.sourceFoleenID = sourceFoleenID;
    return this;
  }

  /**
   * Get sourceFoleenID
   * @return sourceFoleenID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_FOLEEN_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceFoleenID() {
    return sourceFoleenID;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_FOLEEN_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceFoleenID(String sourceFoleenID) {
    this.sourceFoleenID = sourceFoleenID;
  }

  public Party departmentId(String departmentId) {
    
    this.departmentId = departmentId;
    return this;
  }

  /**
   * Get departmentId
   * @return departmentId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DEPARTMENT_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDepartmentId() {
    return departmentId;
  }


  @JsonProperty(JSON_PROPERTY_DEPARTMENT_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDepartmentId(String departmentId) {
    this.departmentId = departmentId;
  }

  public Party sourceDepartmentId(String sourceDepartmentId) {
    
    this.sourceDepartmentId = sourceDepartmentId;
    return this;
  }

  /**
   * Get sourceDepartmentId
   * @return sourceDepartmentId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_DEPARTMENT_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceDepartmentId() {
    return sourceDepartmentId;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_DEPARTMENT_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceDepartmentId(String sourceDepartmentId) {
    this.sourceDepartmentId = sourceDepartmentId;
  }

  public Party other(String other) {
    
    this.other = other;
    return this;
  }

  /**
   * Get other
   * @return other
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(String other) {
    this.other = other;
  }

  public Party sourceOther(String sourceOther) {
    
    this.sourceOther = sourceOther;
    return this;
  }

  /**
   * Get sourceOther
   * @return sourceOther
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSourceOther() {
    return sourceOther;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSourceOther(String sourceOther) {
    this.sourceOther = sourceOther;
  }

  public Party address(Address address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Address getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(Address address) {
    this.address = address;
  }

  public Party contactLanguage(ContactLanguageEnum contactLanguage) {
    
    this.contactLanguage = contactLanguage;
    return this;
  }

  /**
   * Get contactLanguage
   * @return contactLanguage
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ContactLanguageEnum getContactLanguage() {
    return contactLanguage;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactLanguage(ContactLanguageEnum contactLanguage) {
    this.contactLanguage = contactLanguage;
  }

  public Party contactInfo(ContactInfo contactInfo) {
    
    this.contactInfo = contactInfo;
    return this;
  }

  /**
   * Get contactInfo
   * @return contactInfo
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ContactInfo getContactInfo() {
    return contactInfo;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContactInfo(ContactInfo contactInfo) {
    this.contactInfo = contactInfo;
  }

  public Party startDate(String startDate) {
    
    this.startDate = startDate;
    return this;
  }

  /**
   * Get startDate
   * @return startDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStartDate() {
    return startDate;
  }


  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStartDate(String startDate) {
    this.startDate = startDate;
  }

  public Party endDate(String endDate) {
    
    this.endDate = endDate;
    return this;
  }

  /**
   * Get endDate
   * @return endDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEndDate() {
    return endDate;
  }


  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEndDate(String endDate) {
    this.endDate = endDate;
  }

  public Party createdBy(String createdBy) {
    
    this.createdBy = createdBy;
    return this;
  }

  /**
   * Get createdBy
   * @return createdBy
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreatedBy() {
    return createdBy;
  }


  @JsonProperty(JSON_PROPERTY_CREATED_BY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  public Party status(StatusEnum status) {
    
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public StatusEnum getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(StatusEnum status) {
    this.status = status;
  }

  public Party extraInformation(List<ExtraInformation> extraInformation) {
    
    this.extraInformation = extraInformation;
    return this;
  }

  public Party addExtraInformationItem(ExtraInformation extraInformationItem) {
    if (this.extraInformation == null) {
      this.extraInformation = new ArrayList<>();
    }
    this.extraInformation.add(extraInformationItem);
    return this;
  }

  /**
   * Get extraInformation
   * @return extraInformation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXTRA_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ExtraInformation> getExtraInformation() {
    return extraInformation;
  }


  @JsonProperty(JSON_PROPERTY_EXTRA_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExtraInformation(List<ExtraInformation> extraInformation) {
    this.extraInformation = extraInformation;
  }

  public Party links(HalLinks links) {
    
    this.links = links;
    return this;
  }

  /**
   * Get links
   * @return links
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LINKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public HalLinks getLinks() {
    return links;
  }


  @JsonProperty(JSON_PROPERTY_LINKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLinks(HalLinks links) {
    this.links = links;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Party party = (Party) o;
    return Objects.equals(this.id, party.id) &&
        Objects.equals(this.active, party.active) &&
        Objects.equals(this.source, party.source) &&
        Objects.equals(this.denomination, party.denomination) &&
        Objects.equals(this.translatedDenomination, party.translatedDenomination) &&
        Objects.equals(this.firstname, party.firstname) &&
        Objects.equals(this.lastname, party.lastname) &&
        Objects.equals(this.partyType, party.partyType) &&
        Objects.equals(this.producer, party.producer) &&
        Objects.equals(this.employerInformation, party.employerInformation) &&
        Objects.equals(this.businessKeys, party.businessKeys) &&
        Objects.equals(this.ssin, party.ssin) &&
        Objects.equals(this.sourceSsin, party.sourceSsin) &&
        Objects.equals(this.companyID, party.companyID) &&
        Objects.equals(this.sourceCompanyId, party.sourceCompanyId) &&
        Objects.equals(this.ssa, party.ssa) &&
        Objects.equals(this.sourceSsa, party.sourceSsa) &&
        Objects.equals(this.sfs, party.sfs) &&
        Objects.equals(this.sourceSfs, party.sourceSfs) &&
        Objects.equals(this.nssoRegistrationNbr, party.nssoRegistrationNbr) &&
        Objects.equals(this.sourceNssoRegistrationNbr, party.sourceNssoRegistrationNbr) &&
        Objects.equals(this.juristId, party.juristId) &&
        Objects.equals(this.sourceJuristId, party.sourceJuristId) &&
        Objects.equals(this.curatorId, party.curatorId) &&
        Objects.equals(this.sourceCuratorId, party.sourceCuratorId) &&
        Objects.equals(this.ossContractNbr, party.ossContractNbr) &&
        Objects.equals(this.sourceOssContractNbr, party.sourceOssContractNbr) &&
        Objects.equals(this.ossRegistrationNbr, party.ossRegistrationNbr) &&
        Objects.equals(this.sourceOssRegistrationNbr, party.sourceOssRegistrationNbr) &&
        Objects.equals(this.nihiiNbr, party.nihiiNbr) &&
        Objects.equals(this.sourceNihiiNbr, party.sourceNihiiNbr) &&
        Objects.equals(this.feen, party.feen) &&
        Objects.equals(this.sourceFeen, party.sourceFeen) &&
        Objects.equals(this.euVAT, party.euVAT) &&
        Objects.equals(this.nssoNodeId, party.nssoNodeId) &&
        Objects.equals(this.sourceNssoNodeId, party.sourceNssoNodeId) &&
        Objects.equals(this.agentNbr, party.agentNbr) &&
        Objects.equals(this.sourceAgentNbr, party.sourceAgentNbr) &&
        Objects.equals(this.sourceeuVAT, party.sourceeuVAT) &&
        Objects.equals(this.foleenID, party.foleenID) &&
        Objects.equals(this.sourceFoleenID, party.sourceFoleenID) &&
        Objects.equals(this.departmentId, party.departmentId) &&
        Objects.equals(this.sourceDepartmentId, party.sourceDepartmentId) &&
        Objects.equals(this.other, party.other) &&
        Objects.equals(this.sourceOther, party.sourceOther) &&
        Objects.equals(this.address, party.address) &&
        Objects.equals(this.contactLanguage, party.contactLanguage) &&
        Objects.equals(this.contactInfo, party.contactInfo) &&
        Objects.equals(this.startDate, party.startDate) &&
        Objects.equals(this.endDate, party.endDate) &&
        Objects.equals(this.createdBy, party.createdBy) &&
        Objects.equals(this.status, party.status) &&
        Objects.equals(this.extraInformation, party.extraInformation) &&
        Objects.equals(this.links, party.links);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, active, source, denomination, translatedDenomination, firstname, lastname, partyType, producer, employerInformation, businessKeys, ssin, sourceSsin, companyID, sourceCompanyId, ssa, sourceSsa, sfs, sourceSfs, nssoRegistrationNbr, sourceNssoRegistrationNbr, juristId, sourceJuristId, curatorId, sourceCuratorId, ossContractNbr, sourceOssContractNbr, ossRegistrationNbr, sourceOssRegistrationNbr, nihiiNbr, sourceNihiiNbr, feen, sourceFeen, euVAT, nssoNodeId, sourceNssoNodeId, agentNbr, sourceAgentNbr, sourceeuVAT, foleenID, sourceFoleenID, departmentId, sourceDepartmentId, other, sourceOther, address, contactLanguage, contactInfo, startDate, endDate, createdBy, status, extraInformation, links);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Party {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    active: ").append(toIndentedString(active)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    denomination: ").append(toIndentedString(denomination)).append("\n");
    sb.append("    translatedDenomination: ").append(toIndentedString(translatedDenomination)).append("\n");
    sb.append("    firstname: ").append(toIndentedString(firstname)).append("\n");
    sb.append("    lastname: ").append(toIndentedString(lastname)).append("\n");
    sb.append("    partyType: ").append(toIndentedString(partyType)).append("\n");
    sb.append("    producer: ").append(toIndentedString(producer)).append("\n");
    sb.append("    employerInformation: ").append(toIndentedString(employerInformation)).append("\n");
    sb.append("    businessKeys: ").append(toIndentedString(businessKeys)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    sourceSsin: ").append(toIndentedString(sourceSsin)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    sourceCompanyId: ").append(toIndentedString(sourceCompanyId)).append("\n");
    sb.append("    ssa: ").append(toIndentedString(ssa)).append("\n");
    sb.append("    sourceSsa: ").append(toIndentedString(sourceSsa)).append("\n");
    sb.append("    sfs: ").append(toIndentedString(sfs)).append("\n");
    sb.append("    sourceSfs: ").append(toIndentedString(sourceSfs)).append("\n");
    sb.append("    nssoRegistrationNbr: ").append(toIndentedString(nssoRegistrationNbr)).append("\n");
    sb.append("    sourceNssoRegistrationNbr: ").append(toIndentedString(sourceNssoRegistrationNbr)).append("\n");
    sb.append("    juristId: ").append(toIndentedString(juristId)).append("\n");
    sb.append("    sourceJuristId: ").append(toIndentedString(sourceJuristId)).append("\n");
    sb.append("    curatorId: ").append(toIndentedString(curatorId)).append("\n");
    sb.append("    sourceCuratorId: ").append(toIndentedString(sourceCuratorId)).append("\n");
    sb.append("    ossContractNbr: ").append(toIndentedString(ossContractNbr)).append("\n");
    sb.append("    sourceOssContractNbr: ").append(toIndentedString(sourceOssContractNbr)).append("\n");
    sb.append("    ossRegistrationNbr: ").append(toIndentedString(ossRegistrationNbr)).append("\n");
    sb.append("    sourceOssRegistrationNbr: ").append(toIndentedString(sourceOssRegistrationNbr)).append("\n");
    sb.append("    nihiiNbr: ").append(toIndentedString(nihiiNbr)).append("\n");
    sb.append("    sourceNihiiNbr: ").append(toIndentedString(sourceNihiiNbr)).append("\n");
    sb.append("    feen: ").append(toIndentedString(feen)).append("\n");
    sb.append("    sourceFeen: ").append(toIndentedString(sourceFeen)).append("\n");
    sb.append("    euVAT: ").append(toIndentedString(euVAT)).append("\n");
    sb.append("    nssoNodeId: ").append(toIndentedString(nssoNodeId)).append("\n");
    sb.append("    sourceNssoNodeId: ").append(toIndentedString(sourceNssoNodeId)).append("\n");
    sb.append("    agentNbr: ").append(toIndentedString(agentNbr)).append("\n");
    sb.append("    sourceAgentNbr: ").append(toIndentedString(sourceAgentNbr)).append("\n");
    sb.append("    sourceeuVAT: ").append(toIndentedString(sourceeuVAT)).append("\n");
    sb.append("    foleenID: ").append(toIndentedString(foleenID)).append("\n");
    sb.append("    sourceFoleenID: ").append(toIndentedString(sourceFoleenID)).append("\n");
    sb.append("    departmentId: ").append(toIndentedString(departmentId)).append("\n");
    sb.append("    sourceDepartmentId: ").append(toIndentedString(sourceDepartmentId)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("    sourceOther: ").append(toIndentedString(sourceOther)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    contactLanguage: ").append(toIndentedString(contactLanguage)).append("\n");
    sb.append("    contactInfo: ").append(toIndentedString(contactInfo)).append("\n");
    sb.append("    startDate: ").append(toIndentedString(startDate)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    extraInformation: ").append(toIndentedString(extraInformation)).append("\n");
    sb.append("    links: ").append(toIndentedString(links)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

