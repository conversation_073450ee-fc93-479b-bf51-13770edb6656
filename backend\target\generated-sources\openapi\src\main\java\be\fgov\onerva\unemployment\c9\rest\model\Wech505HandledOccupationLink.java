/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.OnlineDeclaredInterruptionInfoType;
import be.fgov.onerva.unemployment.c9.rest.model.OnlineDeclaredOccupationInfoType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505HandledOccupationLink
 */
@JsonPropertyOrder({
  Wech505HandledOccupationLink.JSON_PROPERTY_OCCUPATION_STARTING_DATE,
  Wech505HandledOccupationLink.JSON_PROPERTY_OCCUPATION_ENDING_DATE,
  Wech505HandledOccupationLink.JSON_PROPERTY_JOINT_COMMISSION_NBR,
  Wech505HandledOccupationLink.JSON_PROPERTY_WORKING_DAYS_SYSTEM,
  Wech505HandledOccupationLink.JSON_PROPERTY_MEAN_WORKING_HOURS,
  Wech505HandledOccupationLink.JSON_PROPERTY_REF_MEAN_WORKING_HOURS,
  Wech505HandledOccupationLink.JSON_PROPERTY_WORKER_STATUS,
  Wech505HandledOccupationLink.JSON_PROPERTY_RETIRED,
  Wech505HandledOccupationLink.JSON_PROPERTY_APPRENTICESHIP,
  Wech505HandledOccupationLink.JSON_PROPERTY_CONTRACT_TYPE,
  Wech505HandledOccupationLink.JSON_PROPERTY_REMUN_METHOD,
  Wech505HandledOccupationLink.JSON_PROPERTY_INTERNAL_OCCUPATION_NBR,
  Wech505HandledOccupationLink.JSON_PROPERTY_OCCUPATION_VALIDATION_CODE,
  Wech505HandledOccupationLink.JSON_PROPERTY_ONLINE_DECLARED_OCCUPATION_INFO,
  Wech505HandledOccupationLink.JSON_PROPERTY_ONLINE_DECLARED_INTERRUPTION_INFO
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505HandledOccupationLink {
  public static final String JSON_PROPERTY_OCCUPATION_STARTING_DATE = "occupationStartingDate";
  private LocalDate occupationStartingDate;

  public static final String JSON_PROPERTY_OCCUPATION_ENDING_DATE = "occupationEndingDate";
  private LocalDate occupationEndingDate;

  public static final String JSON_PROPERTY_JOINT_COMMISSION_NBR = "jointCommissionNbr";
  private String jointCommissionNbr;

  public static final String JSON_PROPERTY_WORKING_DAYS_SYSTEM = "workingDaysSystem";
  private String workingDaysSystem;

  public static final String JSON_PROPERTY_MEAN_WORKING_HOURS = "meanWorkingHours";
  private String meanWorkingHours;

  public static final String JSON_PROPERTY_REF_MEAN_WORKING_HOURS = "refMeanWorkingHours";
  private String refMeanWorkingHours;

  public static final String JSON_PROPERTY_WORKER_STATUS = "workerStatus";
  private String workerStatus;

  public static final String JSON_PROPERTY_RETIRED = "retired";
  private String retired;

  public static final String JSON_PROPERTY_APPRENTICESHIP = "apprenticeship";
  private String apprenticeship;

  public static final String JSON_PROPERTY_CONTRACT_TYPE = "contractType";
  private String contractType;

  public static final String JSON_PROPERTY_REMUN_METHOD = "remunMethod";
  private String remunMethod;

  public static final String JSON_PROPERTY_INTERNAL_OCCUPATION_NBR = "internalOccupationNbr";
  private String internalOccupationNbr;

  public static final String JSON_PROPERTY_OCCUPATION_VALIDATION_CODE = "occupationValidationCode";
  private String occupationValidationCode;

  public static final String JSON_PROPERTY_ONLINE_DECLARED_OCCUPATION_INFO = "onlineDeclaredOccupationInfo";
  private List<OnlineDeclaredOccupationInfoType> onlineDeclaredOccupationInfo;

  public static final String JSON_PROPERTY_ONLINE_DECLARED_INTERRUPTION_INFO = "onlineDeclaredInterruptionInfo";
  private OnlineDeclaredInterruptionInfoType onlineDeclaredInterruptionInfo;

  public Wech505HandledOccupationLink() {
  }

  public Wech505HandledOccupationLink occupationStartingDate(LocalDate occupationStartingDate) {
    
    this.occupationStartingDate = occupationStartingDate;
    return this;
  }

  /**
   * Get occupationStartingDate
   * @return occupationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getOccupationStartingDate() {
    return occupationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationStartingDate(LocalDate occupationStartingDate) {
    this.occupationStartingDate = occupationStartingDate;
  }

  public Wech505HandledOccupationLink occupationEndingDate(LocalDate occupationEndingDate) {
    
    this.occupationEndingDate = occupationEndingDate;
    return this;
  }

  /**
   * Get occupationEndingDate
   * @return occupationEndingDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getOccupationEndingDate() {
    return occupationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationEndingDate(LocalDate occupationEndingDate) {
    this.occupationEndingDate = occupationEndingDate;
  }

  public Wech505HandledOccupationLink jointCommissionNbr(String jointCommissionNbr) {
    
    this.jointCommissionNbr = jointCommissionNbr;
    return this;
  }

  /**
   * Get jointCommissionNbr
   * @return jointCommissionNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getJointCommissionNbr() {
    return jointCommissionNbr;
  }


  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setJointCommissionNbr(String jointCommissionNbr) {
    this.jointCommissionNbr = jointCommissionNbr;
  }

  public Wech505HandledOccupationLink workingDaysSystem(String workingDaysSystem) {
    
    this.workingDaysSystem = workingDaysSystem;
    return this;
  }

  /**
   * Get workingDaysSystem
   * @return workingDaysSystem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkingDaysSystem() {
    return workingDaysSystem;
  }


  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkingDaysSystem(String workingDaysSystem) {
    this.workingDaysSystem = workingDaysSystem;
  }

  public Wech505HandledOccupationLink meanWorkingHours(String meanWorkingHours) {
    
    this.meanWorkingHours = meanWorkingHours;
    return this;
  }

  /**
   * Get meanWorkingHours
   * @return meanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMeanWorkingHours() {
    return meanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMeanWorkingHours(String meanWorkingHours) {
    this.meanWorkingHours = meanWorkingHours;
  }

  public Wech505HandledOccupationLink refMeanWorkingHours(String refMeanWorkingHours) {
    
    this.refMeanWorkingHours = refMeanWorkingHours;
    return this;
  }

  /**
   * Get refMeanWorkingHours
   * @return refMeanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getRefMeanWorkingHours() {
    return refMeanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefMeanWorkingHours(String refMeanWorkingHours) {
    this.refMeanWorkingHours = refMeanWorkingHours;
  }

  public Wech505HandledOccupationLink workerStatus(String workerStatus) {
    
    this.workerStatus = workerStatus;
    return this;
  }

  /**
   * Get workerStatus
   * @return workerStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStatus() {
    return workerStatus;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStatus(String workerStatus) {
    this.workerStatus = workerStatus;
  }

  public Wech505HandledOccupationLink retired(String retired) {
    
    this.retired = retired;
    return this;
  }

  /**
   * Get retired
   * @return retired
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRetired() {
    return retired;
  }


  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRetired(String retired) {
    this.retired = retired;
  }

  public Wech505HandledOccupationLink apprenticeship(String apprenticeship) {
    
    this.apprenticeship = apprenticeship;
    return this;
  }

  /**
   * Get apprenticeship
   * @return apprenticeship
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApprenticeship() {
    return apprenticeship;
  }


  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApprenticeship(String apprenticeship) {
    this.apprenticeship = apprenticeship;
  }

  public Wech505HandledOccupationLink contractType(String contractType) {
    
    this.contractType = contractType;
    return this;
  }

  /**
   * Get contractType
   * @return contractType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContractType() {
    return contractType;
  }


  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContractType(String contractType) {
    this.contractType = contractType;
  }

  public Wech505HandledOccupationLink remunMethod(String remunMethod) {
    
    this.remunMethod = remunMethod;
    return this;
  }

  /**
   * Get remunMethod
   * @return remunMethod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemunMethod() {
    return remunMethod;
  }


  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemunMethod(String remunMethod) {
    this.remunMethod = remunMethod;
  }

  public Wech505HandledOccupationLink internalOccupationNbr(String internalOccupationNbr) {
    
    this.internalOccupationNbr = internalOccupationNbr;
    return this;
  }

  /**
   * Get internalOccupationNbr
   * @return internalOccupationNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERNAL_OCCUPATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInternalOccupationNbr() {
    return internalOccupationNbr;
  }


  @JsonProperty(JSON_PROPERTY_INTERNAL_OCCUPATION_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInternalOccupationNbr(String internalOccupationNbr) {
    this.internalOccupationNbr = internalOccupationNbr;
  }

  public Wech505HandledOccupationLink occupationValidationCode(String occupationValidationCode) {
    
    this.occupationValidationCode = occupationValidationCode;
    return this;
  }

  /**
   * Get occupationValidationCode
   * @return occupationValidationCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_VALIDATION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOccupationValidationCode() {
    return occupationValidationCode;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_VALIDATION_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationValidationCode(String occupationValidationCode) {
    this.occupationValidationCode = occupationValidationCode;
  }

  public Wech505HandledOccupationLink onlineDeclaredOccupationInfo(List<OnlineDeclaredOccupationInfoType> onlineDeclaredOccupationInfo) {
    
    this.onlineDeclaredOccupationInfo = onlineDeclaredOccupationInfo;
    return this;
  }

  public Wech505HandledOccupationLink addOnlineDeclaredOccupationInfoItem(OnlineDeclaredOccupationInfoType onlineDeclaredOccupationInfoItem) {
    if (this.onlineDeclaredOccupationInfo == null) {
      this.onlineDeclaredOccupationInfo = new ArrayList<>();
    }
    this.onlineDeclaredOccupationInfo.add(onlineDeclaredOccupationInfoItem);
    return this;
  }

  /**
   * Get onlineDeclaredOccupationInfo
   * @return onlineDeclaredOccupationInfo
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ONLINE_DECLARED_OCCUPATION_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<OnlineDeclaredOccupationInfoType> getOnlineDeclaredOccupationInfo() {
    return onlineDeclaredOccupationInfo;
  }


  @JsonProperty(JSON_PROPERTY_ONLINE_DECLARED_OCCUPATION_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOnlineDeclaredOccupationInfo(List<OnlineDeclaredOccupationInfoType> onlineDeclaredOccupationInfo) {
    this.onlineDeclaredOccupationInfo = onlineDeclaredOccupationInfo;
  }

  public Wech505HandledOccupationLink onlineDeclaredInterruptionInfo(OnlineDeclaredInterruptionInfoType onlineDeclaredInterruptionInfo) {
    
    this.onlineDeclaredInterruptionInfo = onlineDeclaredInterruptionInfo;
    return this;
  }

  /**
   * Get onlineDeclaredInterruptionInfo
   * @return onlineDeclaredInterruptionInfo
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ONLINE_DECLARED_INTERRUPTION_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OnlineDeclaredInterruptionInfoType getOnlineDeclaredInterruptionInfo() {
    return onlineDeclaredInterruptionInfo;
  }


  @JsonProperty(JSON_PROPERTY_ONLINE_DECLARED_INTERRUPTION_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOnlineDeclaredInterruptionInfo(OnlineDeclaredInterruptionInfoType onlineDeclaredInterruptionInfo) {
    this.onlineDeclaredInterruptionInfo = onlineDeclaredInterruptionInfo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505HandledOccupationLink wech505HandledOccupationLink = (Wech505HandledOccupationLink) o;
    return Objects.equals(this.occupationStartingDate, wech505HandledOccupationLink.occupationStartingDate) &&
        Objects.equals(this.occupationEndingDate, wech505HandledOccupationLink.occupationEndingDate) &&
        Objects.equals(this.jointCommissionNbr, wech505HandledOccupationLink.jointCommissionNbr) &&
        Objects.equals(this.workingDaysSystem, wech505HandledOccupationLink.workingDaysSystem) &&
        Objects.equals(this.meanWorkingHours, wech505HandledOccupationLink.meanWorkingHours) &&
        Objects.equals(this.refMeanWorkingHours, wech505HandledOccupationLink.refMeanWorkingHours) &&
        Objects.equals(this.workerStatus, wech505HandledOccupationLink.workerStatus) &&
        Objects.equals(this.retired, wech505HandledOccupationLink.retired) &&
        Objects.equals(this.apprenticeship, wech505HandledOccupationLink.apprenticeship) &&
        Objects.equals(this.contractType, wech505HandledOccupationLink.contractType) &&
        Objects.equals(this.remunMethod, wech505HandledOccupationLink.remunMethod) &&
        Objects.equals(this.internalOccupationNbr, wech505HandledOccupationLink.internalOccupationNbr) &&
        Objects.equals(this.occupationValidationCode, wech505HandledOccupationLink.occupationValidationCode) &&
        Objects.equals(this.onlineDeclaredOccupationInfo, wech505HandledOccupationLink.onlineDeclaredOccupationInfo) &&
        Objects.equals(this.onlineDeclaredInterruptionInfo, wech505HandledOccupationLink.onlineDeclaredInterruptionInfo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationStartingDate, occupationEndingDate, jointCommissionNbr, workingDaysSystem, meanWorkingHours, refMeanWorkingHours, workerStatus, retired, apprenticeship, contractType, remunMethod, internalOccupationNbr, occupationValidationCode, onlineDeclaredOccupationInfo, onlineDeclaredInterruptionInfo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505HandledOccupationLink {\n");
    sb.append("    occupationStartingDate: ").append(toIndentedString(occupationStartingDate)).append("\n");
    sb.append("    occupationEndingDate: ").append(toIndentedString(occupationEndingDate)).append("\n");
    sb.append("    jointCommissionNbr: ").append(toIndentedString(jointCommissionNbr)).append("\n");
    sb.append("    workingDaysSystem: ").append(toIndentedString(workingDaysSystem)).append("\n");
    sb.append("    meanWorkingHours: ").append(toIndentedString(meanWorkingHours)).append("\n");
    sb.append("    refMeanWorkingHours: ").append(toIndentedString(refMeanWorkingHours)).append("\n");
    sb.append("    workerStatus: ").append(toIndentedString(workerStatus)).append("\n");
    sb.append("    retired: ").append(toIndentedString(retired)).append("\n");
    sb.append("    apprenticeship: ").append(toIndentedString(apprenticeship)).append("\n");
    sb.append("    contractType: ").append(toIndentedString(contractType)).append("\n");
    sb.append("    remunMethod: ").append(toIndentedString(remunMethod)).append("\n");
    sb.append("    internalOccupationNbr: ").append(toIndentedString(internalOccupationNbr)).append("\n");
    sb.append("    occupationValidationCode: ").append(toIndentedString(occupationValidationCode)).append("\n");
    sb.append("    onlineDeclaredOccupationInfo: ").append(toIndentedString(onlineDeclaredOccupationInfo)).append("\n");
    sb.append("    onlineDeclaredInterruptionInfo: ").append(toIndentedString(onlineDeclaredInterruptionInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

