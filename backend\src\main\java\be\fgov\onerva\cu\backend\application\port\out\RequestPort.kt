package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.Request

/**
 * Port interface for managing request persistence operations.
 */
fun interface RequestPort {
    /**
     * Get the request in it's most basic form with only the common fields populated. Since the call is made with
     * a request id, it should be found unless a big issue in the system.
     *
     * @param requestId the id of the request
     *
     * @return the request
     * @throws be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException if the request is not found
     */
    fun getRequest(requestId: UUID): Request
}