/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param email 
 * @param language 
 */


data class AssigneeInfoDTO (

    @get:JsonProperty("email")
    val email: kotlin.String? = null,

    @get:JsonProperty("language")
    val language: kotlin.String? = null

) {


}

