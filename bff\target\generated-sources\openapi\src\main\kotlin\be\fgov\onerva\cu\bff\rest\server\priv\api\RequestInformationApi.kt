/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.WaveTaskResponse
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface RequestInformationApi {

    @Operation(
        tags = ["Request Information",],
        summary = "",
        operationId = "closeRequestTask",
        description = """Close a specific task associated with a request""",
        responses = [
            ApiResponse(responseCode = "201", description = "Task successfully closed - data for next task is returned", content = [Content(schema = Schema(implementation = WaveTaskResponse::class))]),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/{taskCode}/close"],
            produces = ["application/json"]
    )
    fun closeRequestTask(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "The code of the task to close", required = true) @PathVariable("taskCode") taskCode: kotlin.String): ResponseEntity<WaveTaskResponse>

    @Operation(
        tags = ["Request Information",],
        summary = "",
        operationId = "getRequestBasicInfo",
        description = """Retrieve basic information for a specific request""",
        responses = [
            ApiResponse(responseCode = "200", description = "Basic request information successfully retrieved", content = [Content(schema = Schema(implementation = RequestBasicInfoResponse::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/requests/{requestId}"],
            produces = ["application/json"]
    )
    fun getRequestBasicInfo(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<RequestBasicInfoResponse>

    @Operation(
        tags = ["Request Information",],
        summary = "",
        operationId = "reopenTask",
        description = """Reopen a specific task""",
        responses = [
            ApiResponse(responseCode = "201", description = "Task successfully reopened"),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/{taskCode}/reopen"]
    )
    fun reopenTask(@Parameter(description = "The code of the task to reopen", required = true) @PathVariable("taskCode") taskCode: kotlin.String,@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<Unit>
}
