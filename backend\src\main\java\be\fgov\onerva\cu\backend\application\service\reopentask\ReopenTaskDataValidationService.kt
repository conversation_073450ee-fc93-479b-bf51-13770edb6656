package be.fgov.onerva.cu.backend.application.service.reopentask

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort

@Service
class ReopenTaskDataValidationService (
    waveTaskPort: WaveTaskPort,
    waveTaskPersistencePort: WaveTaskPersistencePort
) : ReopenTaskAbstractService(waveTaskPort, waveTaskPersistencePort) {

    private val taskType = WaveTaskPort.VALIDATION_DATA;


    override fun deleteTask(requestId: UUID) {
        val dataValidationTask = getWaveTaskEntity(requestId, taskType)
        removeWaveTaskEntity(requestId, taskType)
        executeWaveTaskSoftDelete(dataValidationTask.taskId)

    }

    override fun reopenTask(requestId: UUID) {
        val dataValidationTask = getWaveTaskEntity(requestId, taskType)
        deleteNextTask(requestId)
        openTask(dataValidationTask)
        executeWoAwakeTask(dataValidationTask.taskId)
    }

    override fun deleteNextTask(requestId: UUID) {

    }
}
