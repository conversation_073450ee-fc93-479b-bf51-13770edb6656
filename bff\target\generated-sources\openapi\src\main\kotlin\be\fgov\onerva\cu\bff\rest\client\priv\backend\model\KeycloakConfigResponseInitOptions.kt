/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param redirectUri The uri where keycloak must return after login
 * @param checkLoginIframe 
 * @param onLoad 
 */


data class KeycloakConfigResponseInitOptions (

    /* The uri where keycloak must return after login */
    @get:JsonProperty("redirectUri")
    val redirectUri: kotlin.String,

    @get:JsonProperty("checkLoginIframe")
    val checkLoginIframe: kotlin.Boolean,

    @get:JsonProperty("onLoad")
    val onLoad: kotlin.String

) {


}

