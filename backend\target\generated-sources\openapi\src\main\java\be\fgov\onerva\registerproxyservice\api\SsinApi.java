package be.fgov.onerva.registerproxyservice.api;

import be.fgov.onerva.registerproxyservice.invoker.ApiClient;
import be.fgov.onerva.registerproxyservice.invoker.BaseApi;

import be.fgov.onerva.registerproxyservice.rest.model.KnownHistoryResponse;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SsinApi extends BaseApi {

    public SsinApi() {
        super(new ApiClient());
    }

    public SsinApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Returns historical information found in the database for the given SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin SSIN to search in the database (required)
     * @return List&lt;KnownHistoryResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<KnownHistoryResponse> getKnownHistory(String ssin) throws RestClientException {
        return getKnownHistoryWithHttpInfo(ssin).getBody();
    }

    /**
     * 
     * Returns historical information found in the database for the given SSIN
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param ssin SSIN to search in the database (required)
     * @return ResponseEntity&lt;List&lt;KnownHistoryResponse&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<KnownHistoryResponse>> getKnownHistoryWithHttpInfo(String ssin) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssin' is set
        if (ssin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssin' when calling getKnownHistory");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<KnownHistoryResponse>> localReturnType = new ParameterizedTypeReference<List<KnownHistoryResponse>>() {};
        return apiClient.invokeAPI("/ssin/knownHistory", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Retrieves the most recent SSIN for a citizen when their SSIN has been changed
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param oldSsin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @return Integer
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Integer getRecentSsin(String oldSsin, String xRemoteIdentity, String legalContext) throws RestClientException {
        return getRecentSsinWithHttpInfo(oldSsin, xRemoteIdentity, legalContext).getBody();
    }

    /**
     * 
     * Retrieves the most recent SSIN for a citizen when their SSIN has been changed
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param oldSsin  (required)
     * @param xRemoteIdentity  (optional)
     * @param legalContext  (optional, default to )
     * @return ResponseEntity&lt;Integer&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Integer> getRecentSsinWithHttpInfo(String oldSsin, String xRemoteIdentity, String legalContext) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'oldSsin' is set
        if (oldSsin == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'oldSsin' when calling getRecentSsin");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "legalContext", legalContext));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "oldSsin", oldSsin));
        

        if (xRemoteIdentity != null)
        localVarHeaderParams.add("X-Remote-Identity", apiClient.parameterToString(xRemoteIdentity));

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Integer> localReturnType = new ParameterizedTypeReference<Integer>() {};
        return apiClient.invokeAPI("/getRecentSsin", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json", "text/plain"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
