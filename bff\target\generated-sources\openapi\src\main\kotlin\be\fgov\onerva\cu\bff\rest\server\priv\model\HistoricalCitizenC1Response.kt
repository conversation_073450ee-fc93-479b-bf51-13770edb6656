package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionFields
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param otherPersonName 
 * @param iban 
 * @param bic 
 * @param firstName The first name of the person
 * @param lastName The last name of the person
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param nationality The nationality of the employee
 * @param address 
 * @param addressValueDate The value date for the bank account
 * @param bankAccountValueDate The value date for the bank account
 * @param unionContributionValueDate The value date for the bank account
 * @param unionDue 
 */
data class HistoricalCitizenC1Response(

    @Schema(example = "null", description = "")
    @get:JsonProperty("otherPersonName") val otherPersonName: kotlin.String? = null,

    @Schema(example = "null", description = "")
    @get:JsonProperty("iban") val iban: kotlin.String? = null,

    @Schema(example = "null", description = "")
    @get:JsonProperty("bic") val bic: kotlin.String? = null,

    @Schema(example = "null", description = "The first name of the person")
    @get:JsonProperty("firstName") val firstName: kotlin.String? = null,

    @Schema(example = "null", description = "The last name of the person")
    @get:JsonProperty("lastName") val lastName: kotlin.String? = null,

    @field:Valid
    @Schema(example = "null", description = "The birth date of the employee (format YYYY-MM-DD)")
    @get:JsonProperty("birthDate") val birthDate: java.time.LocalDate? = null,

    @Schema(example = "null", description = "The nationality of the employee")
    @get:JsonProperty("nationality") val nationality: kotlin.String? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("address") val address: AddressNullable? = null,

    @field:Valid
    @Schema(example = "null", description = "The value date for the bank account")
    @get:JsonProperty("addressValueDate") val addressValueDate: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "The value date for the bank account")
    @get:JsonProperty("bankAccountValueDate") val bankAccountValueDate: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "The value date for the bank account")
    @get:JsonProperty("unionContributionValueDate") val unionContributionValueDate: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("unionDue") val unionDue: UnionContributionFields? = null
    ) {

}

