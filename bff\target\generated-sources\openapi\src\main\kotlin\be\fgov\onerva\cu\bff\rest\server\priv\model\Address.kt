package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param country The country of residence
 * @param street The street name
 * @param houseNumber The house number
 * @param zipCode The postal/zip code
 * @param city The city name
 * @param boxNumber The box number (optional)
 */
data class Address(

    @Schema(example = "null", required = true, description = "The country of residence")
    @get:JsonProperty("country", required = true) val country: kotlin.String,

    @Schema(example = "null", required = true, description = "The street name")
    @get:JsonProperty("street", required = true) val street: kotlin.String,

    @Schema(example = "null", required = true, description = "The house number")
    @get:JsonProperty("houseNumber", required = true) val houseNumber: kotlin.String,

    @Schema(example = "null", required = true, description = "The postal/zip code")
    @get:JsonProperty("zipCode", required = true) val zipCode: kotlin.String,

    @Schema(example = "null", required = true, description = "The city name")
    @get:JsonProperty("city", required = true) val city: kotlin.String,

    @Schema(example = "null", description = "The box number (optional)")
    @get:JsonProperty("boxNumber") val boxNumber: kotlin.String? = null
    ) {

}

