import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { of, throwError } from 'rxjs';

import { DataValidationService } from './data-validation.service';
import { ConfigService } from '../config/config.service';
import {
    RequestInformationService,
    AggregatedChangePersonalDataValidateResponse,
    AggregateRequestInformationService,
    Configuration as ConfigurationBff,
    CitizenInformationService,
    ModeOfPaymentService,
    UnionContributionService,
    UpdateAggregatedChangePersonalDataRequest,
    UpdateUnionContributionRequest,
    UpdateModeOfPaymentRequest,
    UpdateCitizenInformationRequest,
    FieldSource
} from '@rest-client/cu-bff';

jest.mock('@rest-client/cu-config', () => ({
    Configuration: jest.fn()
}));

jest.mock('@rest-client/cu-bff', () => ({
    RequestInformationService: jest.fn(),
    AggregateRequestInformationService: jest.fn(),
    CitizenInformationService: jest.fn(),
    ModeOfPaymentService: jest.fn(),
    UnionContributionService: jest.fn(),
    Configuration: jest.fn()
}));

describe('DataValidationService', () => {
    let service: DataValidationService;
    let httpClient: HttpClient;
    let httpTestingController: HttpTestingController;
    let configService: ConfigService;
    let mockRequestInformationService: jest.Mocked<RequestInformationService>;
    let mockAggregateRequestInformationService: jest.Mocked<AggregateRequestInformationService>;
    let mockCitizenInformationService: jest.Mocked<CitizenInformationService>;
    let mockModeOfPaymentService: jest.Mocked<ModeOfPaymentService>;
    let mockUnionContributionService: jest.Mocked<UnionContributionService>;

    const mockApiBaseUrl = 'http://mock-api';
    const mockBffBaseUrl = 'http://mock-bff';

    beforeEach(() => {
        mockRequestInformationService = {
            defaultHeaders: new HttpHeaders(),
            closeRequestTask: jest.fn().mockReturnValue(of({} as any))
        } as unknown as jest.Mocked<RequestInformationService>;

        mockAggregateRequestInformationService = {
            defaultHeaders: new HttpHeaders(),
            getAggregatedChangePersonalDataValidateRequest: jest.fn().mockReturnValue(of({} as AggregatedChangePersonalDataValidateResponse)),
            updateAggregatedChangePersonalDataRequest: jest.fn().mockReturnValue(of({} as any))
        } as unknown as jest.Mocked<AggregateRequestInformationService>;

        mockCitizenInformationService = {
            defaultHeaders: new HttpHeaders(),
            updateCitizenInformation: jest.fn().mockReturnValue(of({} as any)),
            selectCitizenInformationSources: jest.fn().mockReturnValue(of({} as any))
        } as unknown as jest.Mocked<CitizenInformationService>;

        mockModeOfPaymentService = {
            defaultHeaders: new HttpHeaders(),
            updateModeOfPayment: jest.fn().mockReturnValue(of({} as any)),
            selectModeOfPaymentSources: jest.fn().mockReturnValue(of({} as any))
        } as unknown as jest.Mocked<ModeOfPaymentService>;

        mockUnionContributionService = {
            defaultHeaders: new HttpHeaders(),
            updateUnionContribution: jest.fn().mockReturnValue(of({} as any)),
            selectUnionContributionSources: jest.fn().mockReturnValue(of({} as any))
        } as unknown as jest.Mocked<UnionContributionService>;

        const configServiceMock = {
            getEnvironmentVariable: jest.fn((variable: string) => {
                if (variable === 'apiBaseUrl') return mockApiBaseUrl;
                if (variable === 'bffBaseUrl') return mockBffBaseUrl;
                return '';
            })
        };

        (RequestInformationService as jest.Mock).mockImplementation(() => mockRequestInformationService);
        (AggregateRequestInformationService as jest.Mock).mockImplementation(() => mockAggregateRequestInformationService);
        (CitizenInformationService as jest.Mock).mockImplementation(() => mockCitizenInformationService);
        (ModeOfPaymentService as jest.Mock).mockImplementation(() => mockModeOfPaymentService);
        (UnionContributionService as jest.Mock).mockImplementation(() => mockUnionContributionService);

        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
            providers: [
                DataValidationService,
                { provide: ConfigService, useValue: configServiceMock }
            ]
        });

        httpClient = TestBed.inject(HttpClient);
        httpTestingController = TestBed.inject(HttpTestingController);
        configService = TestBed.inject(ConfigService);
        service = TestBed.inject(DataValidationService);
    });

    afterEach(() => {
        httpTestingController.verify();
        jest.clearAllMocks();
    });

    it('should be created', () => {
        expect(service).toBeTruthy();
    });

    describe('Service initialization', () => {
        it('should initialize services without token', () => {
            service.initializeServices();

            expect(configService.getEnvironmentVariable).toHaveBeenCalledWith('bffBaseUrl', true);

            expect(RequestInformationService).toHaveBeenCalledWith(
                expect.any(Object),
                mockBffBaseUrl,
                expect.any(Object)
            );

            expect(AggregateRequestInformationService).toHaveBeenCalledWith(
                expect.any(Object),
                mockBffBaseUrl,
                expect.any(Object)
            );

            expect(CitizenInformationService).toHaveBeenCalledWith(
                expect.any(Object),
                mockBffBaseUrl,
                expect.any(Object)
            );

            expect(ModeOfPaymentService).toHaveBeenCalledWith(
                expect.any(Object),
                mockBffBaseUrl,
                expect.any(Object)
            );
        });

        it('should initialize services with token', () => {
            const token = 'test-token';

            service.initializeServices(token);

            expect(ConfigurationBff).toHaveBeenCalledWith({
                basePath: mockBffBaseUrl,
                credentials: { 'Bearer': token }
            });

            expect(mockRequestInformationService.defaultHeaders.get('Authorization')).toBeDefined();
            expect(mockAggregateRequestInformationService.defaultHeaders.get('Authorization')).toBeDefined();
            expect(mockCitizenInformationService.defaultHeaders.get('Authorization')).toBeDefined();
            expect(mockModeOfPaymentService.defaultHeaders.get('Authorization')).toBeDefined();
        });

        it('should set default headers when token is provided', () => {
            const token = 'test-token';

            service.initializeServices(token);

            expect(mockRequestInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token}`);
            expect(mockRequestInformationService.defaultHeaders.get('Content-Type')).toBe('application/json');

            expect(mockAggregateRequestInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token}`);
            expect(mockAggregateRequestInformationService.defaultHeaders.get('Content-Type')).toBe('application/json');

            expect(mockCitizenInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token}`);
            expect(mockCitizenInformationService.defaultHeaders.get('Content-Type')).toBe('application/json');

            expect(mockModeOfPaymentService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token}`);
            expect(mockModeOfPaymentService.defaultHeaders.get('Content-Type')).toBe('application/json');
        });
    });

    describe('getAggregatedData', () => {
        it('should call getAggregatedChangePersonalDataValidateRequest with correct requestId', (done) => {
            const requestId = 'REQ-123';
            const mockResponse: AggregatedChangePersonalDataValidateResponse = {
                requestId: requestId,
                data: 'test data'
            } as any;

            (mockAggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest as jest.Mock).mockReturnValue(of(mockResponse));

            service.getAggregatedData(requestId).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockAggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest).toHaveBeenCalledWith(requestId);
                done();
            });
        });

        it('should handle error when getAggregatedData fails', (done) => {
            const requestId = 'REQ-123';
            const error = new Error('Failed to get aggregated data');

            (mockAggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest as jest.Mock).mockReturnValue(throwError(() => error));

            service.getAggregatedData(requestId).subscribe({
                error: (err) => {
                    expect(err).toEqual(error);
                    done();
                }
            });
        });
    });

    describe('updateRequest', () => {
        it('should call updateAggregatedChangePersonalDataRequest with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const updateData: UpdateAggregatedChangePersonalDataRequest = {
                field1: 'value1',
                field2: 'value2'
            } as any;
            const mockResponse = { success: true };

            (mockAggregateRequestInformationService.updateAggregatedChangePersonalDataRequest as jest.Mock).mockReturnValue(of(mockResponse));

            service.updateRequest(requestId, updateData).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockAggregateRequestInformationService.updateAggregatedChangePersonalDataRequest).toHaveBeenCalledWith(requestId, updateData);
                done();
            });
        });
    });

    describe('closeTask', () => {
        it('should call closeRequestTask with default taskType', (done) => {
            const requestId = 'REQ-123';
            const mockResponse = { success: true };

            (mockRequestInformationService.closeRequestTask as jest.Mock).mockReturnValue(of(mockResponse));

            service.closeTask(requestId).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockRequestInformationService.closeRequestTask).toHaveBeenCalledWith(requestId, 'CHANGE_PERSONAL_DATA_CAPTURE');
                done();
            });
        });

        it('should call closeRequestTask with custom taskType', (done) => {
            const requestId = 'REQ-123';
            const taskType = 'CUSTOM_TASK';
            const mockResponse = { success: true };

            (mockRequestInformationService.closeRequestTask as jest.Mock).mockReturnValue(of(mockResponse));

            service.closeTask(requestId, taskType).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockRequestInformationService.closeRequestTask).toHaveBeenCalledWith(requestId, taskType);
                done();
            });
        });
    });

    describe('updateUnionContribution', () => {
        it('should call updateUnionContribution with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const updateData: UpdateUnionContributionRequest = {
                contribution: '100',
                effectiveDate: '2024-01-01'
            } as any;
            const mockResponse = { success: true };

            (mockUnionContributionService.updateUnionContribution as jest.Mock).mockReturnValue(of(mockResponse));

            service.updateUnionContribution(requestId, updateData).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockUnionContributionService.updateUnionContribution).toHaveBeenCalledWith(requestId, updateData);
                done();
            });
        });
    });

    describe('updateModeOfPayment', () => {
        it('should call updateModeOfPayment with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const updateData: UpdateModeOfPaymentRequest = {
                paymentMode: 'BANK_TRANSFER',
                accountNumber: '*********'
            } as any;
            const mockResponse = { success: true };

            (mockModeOfPaymentService.updateModeOfPayment as jest.Mock).mockReturnValue(of(mockResponse));

            service.updateModeOfPayment(requestId, updateData).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockModeOfPaymentService.updateModeOfPayment).toHaveBeenCalledWith(requestId, updateData);
                done();
            });
        });
    });

    describe('updateCitizenInformation', () => {
        it('should call updateCitizenInformation with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const updateData: UpdateCitizenInformationRequest = {
                name: 'John Doe',
                address: '123 Main St'
            } as any;
            const mockResponse = { success: true };

            (mockCitizenInformationService.updateCitizenInformation as jest.Mock).mockReturnValue(of(mockResponse));

            service.updateCitizenInformation(requestId, updateData).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockCitizenInformationService.updateCitizenInformation).toHaveBeenCalledWith(requestId, updateData);
                done();
            });
        });
    });

    describe('selectCitizenInformationSources', () => {
        it('should call selectCitizenInformationSources with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const fieldSources: FieldSource[] = [
                { fieldName: 'name', source: 'MANUAL' },
                { fieldName: 'address', source: 'AUTOMATIC' }
            ] as any;
            const mockResponse = { success: true };

            (mockCitizenInformationService.selectCitizenInformationSources as jest.Mock).mockReturnValue(of(mockResponse));

            service.selectCitizenInformationSources(requestId, fieldSources).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockCitizenInformationService.selectCitizenInformationSources).toHaveBeenCalledWith(
                    requestId,
                    { fieldSources: fieldSources }
                );
                done();
            });
        });

        it('should handle empty fieldSources array', (done) => {
            const requestId = 'REQ-123';
            const fieldSources: FieldSource[] = [];
            const mockResponse = { success: true };

            (mockCitizenInformationService.selectCitizenInformationSources as jest.Mock).mockReturnValue(of(mockResponse));

            service.selectCitizenInformationSources(requestId, fieldSources).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockCitizenInformationService.selectCitizenInformationSources).toHaveBeenCalledWith(
                    requestId,
                    { fieldSources: [] }
                );
                done();
            });
        });
    });

    describe('selectModeOfPaymentSources', () => {
        it('should call selectModeOfPaymentSources with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const fieldSources: FieldSource[] = [
                { fieldName: 'paymentMode', source: 'MANUAL' }
            ] as any;
            const mockResponse = { success: true };

            (mockModeOfPaymentService.selectModeOfPaymentSources as jest.Mock).mockReturnValue(of(mockResponse));

            service.selectModeOfPaymentSources(requestId, fieldSources).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockModeOfPaymentService.selectModeOfPaymentSources).toHaveBeenCalledWith(
                    requestId,
                    { fieldSources: fieldSources }
                );
                done();
            });
        });
    });

    describe('selectUnionContributionSources', () => {
        it('should call selectUnionContributionSources with correct parameters', (done) => {
            const requestId = 'REQ-123';
            const fieldSources: FieldSource[] = [
                { fieldName: 'contributionAmount', source: 'AUTOMATIC' }
            ] as any;
            const mockResponse = { success: true };

            (mockUnionContributionService.selectUnionContributionSources as jest.Mock).mockReturnValue(of(mockResponse));

            service.selectUnionContributionSources(requestId, fieldSources).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockUnionContributionService.selectUnionContributionSources).toHaveBeenCalledWith(
                    requestId,
                    { fieldSources: fieldSources }
                );
                done();
            });
        });
    });

    describe('Error handling', () => {
        it('should propagate errors from updateRequest', (done) => {
            const requestId = 'REQ-123';
            const updateData = {} as UpdateAggregatedChangePersonalDataRequest;
            const error = new Error('Update failed');

            (mockAggregateRequestInformationService.updateAggregatedChangePersonalDataRequest as jest.Mock).mockReturnValue(throwError(() => error));

            service.updateRequest(requestId, updateData).subscribe({
                error: (err) => {
                    expect(err).toEqual(error);
                    done();
                }
            });
        });

        it('should propagate errors from closeTask', (done) => {
            const requestId = 'REQ-123';
            const error = new Error('Close task failed');

            (mockRequestInformationService.closeRequestTask as jest.Mock).mockReturnValue(throwError(() => error));

            service.closeTask(requestId).subscribe({
                error: (err) => {
                    expect(err).toEqual(error);
                    done();
                }
            });
        });
    });

    describe('Service re-initialization', () => {
        it('should allow re-initialization with different token', () => {
            const token1 = 'token-1';
            const token2 = 'token-2';

            service.initializeServices(token1);
            expect(mockRequestInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token1}`);

            service.initializeServices(token2);
            expect(mockRequestInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token2}`);
        });

        it('should handle re-initialization from token to no token', () => {
            const token = 'test-token';

            service.initializeServices(token);
            expect(mockRequestInformationService.defaultHeaders.get('Authorization')).toBe(`Bearer ${token}`);

            service.initializeServices();
            // Headers should be set but without Authorization
            expect(mockRequestInformationService.defaultHeaders).toBeDefined();
        });
    });

    describe('Edge cases', () => {
        it('should handle null requestId gracefully', (done) => {
            const requestId: any = null;
            const mockResponse = { success: true } as any;

            (mockAggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest as jest.Mock).mockReturnValue(of(mockResponse));

            service.getAggregatedData(requestId).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockAggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest).toHaveBeenCalledWith(null);
                done();
            });
        });

        it('should handle undefined update data', (done) => {
            const requestId = 'REQ-123';
            const updateData: any = undefined;
            const mockResponse = { success: true };

            (mockAggregateRequestInformationService.updateAggregatedChangePersonalDataRequest as jest.Mock).mockReturnValue(of(mockResponse));

            service.updateRequest(requestId, updateData).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockAggregateRequestInformationService.updateAggregatedChangePersonalDataRequest).toHaveBeenCalledWith(requestId, undefined);
                done();
            });
        });

        it('should handle null fieldSources', (done) => {
            const requestId = 'REQ-123';
            const fieldSources: any = null;
            const mockResponse = { success: true };

            (mockCitizenInformationService.selectCitizenInformationSources as jest.Mock).mockReturnValue(of(mockResponse));

            service.selectCitizenInformationSources(requestId, fieldSources).subscribe(result => {
                expect(result).toEqual(mockResponse);
                expect(mockCitizenInformationService.selectCitizenInformationSources).toHaveBeenCalledWith(
                    requestId,
                    { fieldSources: null }
                );
                done();
            });
        });
    });
});