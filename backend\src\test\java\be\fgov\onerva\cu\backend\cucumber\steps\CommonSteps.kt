package be.fgov.onerva.cu.backend.cucumber.steps

import java.util.UUID
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.jdbc.core.JdbcTemplate
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.spring.ScenarioScope

@ScenarioScope
class CommonSteps : BaseSteps() {
    @Autowired
    private lateinit var jdbcTemplate: JdbcTemplate

    // In Cucumber, we need to handle SQL scripts programmatically
    @Given("the database is clean")
    fun cleanupDatabase() {
        jdbcTemplate.execute(loadSqlScript("/cleanup.sql"))
    }

    @Given("test data for change personal data is loaded")
    fun loadTestData() {
        jdbcTemplate.execute(loadSqlScript("/data-change-personal-data.sql"))
    }

    @Then("the response status should be {int}")
    fun verifyResponseStatus(status: Int) {
        testContext.result!!.andExpect(status().`is`(status))
    }

    @Given("a request with ID {string}")
    fun setRequestId(id: String) {
        testContext.requestId = UUID.fromString(id)
    }

    private fun loadSqlScript(path: String): String {
        return this::class.java.getResource(path)?.readText()
            ?: throw IllegalArgumentException("SQL script not found: $path")
    }
}