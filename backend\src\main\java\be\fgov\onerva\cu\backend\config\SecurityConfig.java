package be.fgov.onerva.cu.backend.config;

import be.fgov.onerva.cu.backend.security.OnemRvaAuthenticationConverter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;
import org.springframework.core.env.Profiles;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.core.oidc.user.OidcUserAuthority;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtDecoders;
import org.springframework.security.oauth2.jwt.JwtValidators;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.time.Duration;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Configuration
@EnableMethodSecurity
public class SecurityConfig {

    /**
     * <p>
     * Into this method with the <a href="https://docs.spring.io/spring-security/site/docs/3.0.x/reference/security-filter-chain.html">security filter chain</a> you can define the security aspects as:
     * <li><a href="https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS">The cors</a>(used by the browser)</li>
     * <li>what url's you don't want to secure and the ones that you want to secure via <a href="https://oauth.net/2/">OAuth 2.0</a></li>
     * </p>
     *
     * @throws Exception if the security filter chain can't be built
     */
    @Bean
    SecurityFilterChain securityFilterChain(HttpSecurity http,
                                            Environment env,
                                            OnemRvaAuthenticationConverter onemRvaAuthenticationConverter)
            throws Exception {
        http.csrf(c -> c.disable()).cors(Customizer.withDefaults());

        if (env.acceptsProfiles(Profiles.of("unsecured"))) {
            http.authorizeHttpRequests(authorize -> authorize.anyRequest().permitAll());
        } else {
            http.authorizeHttpRequests(authorize -> authorize
                            // Public endpoints
                            .requestMatchers("/", "/public/**", "/error")
                            .permitAll()
                            .requestMatchers("/actuator/**")
                            .permitAll()
                            .requestMatchers("/api/config/**")
                            .permitAll().requestMatchers("api/e2e/**").permitAll()
                            .requestMatchers("/api/config/**").permitAll().requestMatchers("api/e2e/**").permitAll()
                            .requestMatchers(HttpMethod.OPTIONS)
                            .permitAll()
                            // Secured endpoints
                            .requestMatchers("/api/test/**")
                            .authenticated()
                            .requestMatchers("/api/**")
                            .authenticated()
                            .requestMatchers("/admin/**")
                            .hasAuthority("cu_role_admin")
                            .requestMatchers("/dashboard/**")
                            .hasAnyAuthority("cu_role_admin", "cu_role_user")
                            .anyRequest()
                            .authenticated())
                    // Resource server config for API calls
                    .oauth2ResourceServer(server -> server.jwt(jwt -> jwt.jwtAuthenticationConverter(
                            onemRvaAuthenticationConverter)))
                    // OAuth2 login config for web pages
                    .oauth2Login(oauth2 -> oauth2.userInfoEndpoint(userInfo -> userInfo.userAuthoritiesMapper(
                            userAuthoritiesMapper())).defaultSuccessUrl("/dashboard", true))
                    .logout(logout -> logout.logoutSuccessUrl("/").permitAll());
        }

        return http.build();
    }

    @Bean
    public GrantedAuthoritiesMapper userAuthoritiesMapper() {
        return authorities -> {
            Set<GrantedAuthority> mappedAuthorities = new HashSet<>();

            authorities.forEach(authority -> {
                if (authority instanceof OidcUserAuthority oidcUserAuthority) {
                    // Map Keycloak roles to Spring Security roles
                    if (oidcUserAuthority.getAttributes().containsKey("realm_access")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> realmAccess =
                                (Map<String, Object>) oidcUserAuthority.getAttributes().get("realm_access");

                        if (realmAccess.containsKey("roles")) {
                            @SuppressWarnings("unchecked")
                            List<String> roles = (List<String>) realmAccess.get("roles");
                            roles.forEach(role -> mappedAuthorities.add(new SimpleGrantedAuthority(role)));
                        }
                    }
                }
            });

            return mappedAuthorities;
        };
    }

    /**
     * This decoder can be skipped during development phase when we want to avoid checking the issuer of the JWT token.<br />
     * This method allows to decode <a href="https://en.wikipedia.org/wiki/JSON_Web_Token">the jwt token</a><br />
     * <p>
     * Available feature flags:
     * <li>You can bypass this token checking if you include the 'unsecured' profile to start your spring-boot service.</li>
     * <li>You can check the timestamp without the issuer if the paremeter checkToken is set to false</li>
     */
    @Bean
    @Profile("!unsecured")
    JwtDecoder jwtDecoder(OAuth2ResourceServerProperties props,
                          @Value("${keycloak.checkToken:true}") boolean checkIssuer) {
        String issuerUri = props.getJwt().getIssuerUri();
        var validator = checkIssuer ? JwtValidators.createDefaultWithIssuer(issuerUri) : JwtValidators.createDefault();
        NimbusJwtDecoder jwtDecoder = JwtDecoders.fromOidcIssuerLocation(issuerUri);
        jwtDecoder.setJwtValidator(validator);

        return jwtDecoder;
    }

    @Bean
    CorsConfigurationSource corsConfigurationSource(@Value("${cors.allowedOrigins:*}") List<String> origins) {
        CorsConfiguration configuration = new CorsConfiguration();
        configuration.setAllowedOriginPatterns(origins);
        configuration.setAllowedMethods(List.of("*"));
        configuration.setAllowedHeaders(List.of("*"));
        configuration.setMaxAge(Duration.ofHours(1));

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration);

        return source;
    }
}
