package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.Instant
import java.util.UUID
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SyncFollowUpEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.RequestRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.SyncFollowUpRepository
import be.fgov.onerva.cu.backend.application.domain.SyncFollowUpStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.SyncFollowUpNotFoundException
import be.fgov.onerva.cu.backend.application.port.out.SyncFollowUpPort
import be.fgov.onerva.cu.common.aop.LogMethodCall

@Transactional
@Service
class SyncFollowUpPersistenceAdapter(
    val requestRepository: RequestRepository,
    val syncFollowUpRepository: SyncFollowUpRepository,
) : SyncFollowUpPort {

    @LogMethodCall
    override fun persistSyncFollowUpAsPending(requestId: UUID, correlationId: String) {
        val request = requestRepository.findByIdOrNull(requestId)
            ?: throw RequestIdNotFoundException("Request with id $requestId not found")
        val syncFollowUpEntity = SyncFollowUpEntity(
            request = request,
            correlationId = correlationId,
            status = SyncFollowUpStatus.PENDING,
            dateMessageSent = Instant.now(),
        )
        syncFollowUpRepository.save(syncFollowUpEntity)
    }

    @LogMethodCall
    override fun updateSyncFollowUp(correlationId: String, success: Boolean) {
        val syncFollowUpEntity = syncFollowUpRepository.findByCorrelationId(correlationId)
            ?: throw SyncFollowUpNotFoundException("Sync follow up with correlation id $correlationId not found")
        syncFollowUpEntity.apply {
            status = if (success) SyncFollowUpStatus.OK else SyncFollowUpStatus.NOK
            dateResponseReceived = Instant.now()
        }
    }

    @LogMethodCall
    override fun getSyncFollowUpStatusByRequestId(requestId: UUID): SyncFollowUpStatus? =
        syncFollowUpRepository.findByRequestId(requestId)?.status
}