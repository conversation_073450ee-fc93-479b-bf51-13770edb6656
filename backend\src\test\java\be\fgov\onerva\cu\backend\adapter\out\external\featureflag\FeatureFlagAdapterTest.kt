package be.fgov.onerva.cu.backend.adapter.out.external.featureflag

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import dev.openfeature.sdk.Client
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class FeatureFlagAdapterTest {

    @MockK
    lateinit var client: Client

    @InjectMockKs
    lateinit var featureFlagAdapter: FeatureFlagAdapter

    @Nested
    inner class IsFeatureEnabled {
        @Test
        fun `should return true when feature flag is enabled`() {
            // Given
            val flagName = "test-feature"
            every { client.getBooleanValue(flagName, any()) } returns true

            // When
            val result = featureFlagAdapter.isFeatureEnabled(flagName, false)

            // Then
            assertThat(result).isTrue()
            verify(exactly = 1) { client.getBooleanValue(flagName, false) }
        }

        @Test
        fun `should return false when feature flag is disabled`() {
            // Given
            val flagName = "test-feature"
            every { client.getBooleanValue(flagName, any()) } returns false

            // When
            val result = featureFlagAdapter.isFeatureEnabled(flagName, true)

            // Then
            assertThat(result).isFalse()
            verify(exactly = 1) { client.getBooleanValue(flagName, true) }
        }
    }

    @Nested
    inner class GetFeatureListValue {
        @Test
        fun `should return default value when feature is disabled`() {
            // Given
            val flagName = "test.flag"
            val defaultValue = listOf("default1", "default2")

            every { client.getBooleanValue(flagName, false) } returns false

            // When
            val result = featureFlagAdapter.getFeatureListValue(flagName, defaultValue)

            // Then
            assertThat(result).isEqualTo(defaultValue)
            verify(exactly = 1) { client.getBooleanValue(flagName, false) }
            verify(exactly = 0) { client.getStringValue(any(), any()) }
        }

        @Test
        fun `should return split string when feature is enabled`() {
            // Given
            val flagName = "test.flag"
            val defaultValue = listOf("default1", "default2")
            val featureValue = "value1,value2,value3"

            every { client.getBooleanValue(flagName, false) } returns true
            every { client.getStringValue(flagName, "") } returns featureValue

            // When
            val result = featureFlagAdapter.getFeatureListValue(flagName, defaultValue)

            // Then
            assertThat(result).isEqualTo(listOf("value1", "value2", "value3"))
            verify(exactly = 1) { client.getBooleanValue(flagName, false) }
            verify(exactly = 1) { client.getStringValue(flagName, "") }
        }

        @Test
        fun `should return empty list when feature is enabled but value is empty`() {
            // Given
            val flagName = "test.flag"
            val defaultValue = listOf("default1", "default2")

            every { client.getBooleanValue(flagName, false) } returns true
            every { client.getStringValue(flagName, "") } returns ""

            // When
            val result = featureFlagAdapter.getFeatureListValue(flagName, defaultValue)

            // Then
            assertThat(result).isEqualTo(listOf(""))
            verify(exactly = 1) { client.getBooleanValue(flagName, false) }
            verify(exactly = 1) { client.getStringValue(flagName, "") }
        }
    }
}