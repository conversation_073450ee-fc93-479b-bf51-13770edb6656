{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"cu": {"projectType": "library", "root": "projects/cu", "sourceRoot": "projects/cu/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/cu/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/cu/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/cu/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "jest.config.ts"}}, "e2e": {"builder": "playwright-ng-schematics:playwright", "options": {"devServerTarget": "cu:serve"}, "configurations": {"production": {"devServerTarget": "cu:serve:production"}}}}}, "cu-app": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/cu-app", "sourceRoot": "projects/cu-app/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/cu-app", "index": "projects/cu-app/src/index.html", "browser": "projects/cu-app/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/cu-app/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/cu-app/public"}], "styles": ["projects/cu-app/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "700kB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "100kB", "maximumError": "400kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "cu-app:build:production"}, "development": {"buildTarget": "cu-app:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "jest.config.ts"}}, "e2e": {"builder": "playwright-ng-schematics:playwright", "options": {"devServerTarget": "cu-app:serve"}, "configurations": {"production": {"devServerTarget": "cu-app:serve:production"}}}}}, "cu-test-local": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "projects/cu-test-local", "sourceRoot": "projects/cu-test-local/src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/cu-test-local", "index": "projects/cu-test-local/src/index.html", "browser": "projects/cu-test-local/src/main.ts", "polyfills": ["zone.js"], "optimization": true, "outputHashing": "none", "extractLicenses": false, "sourceMap": false, "namedChunks": false, "tsConfig": "projects/cu-test-local/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "projects/cu-test-local/src/assets/", "output": "/assets/"}], "styles": ["projects/cu-test-local/src/styles.scss"], "scripts": []}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "700kB", "maximumError": "2MB"}, {"type": "anyComponentStyle", "maximumWarning": "100kB", "maximumError": "400kB"}], "outputHashing": "all"}, "playwright": {"fileReplacements": [{"replace": "projects/cu/src/lib/environments/environment.ts", "with": "projects/cu/src/lib/environments/environment.playwrightint.ts"}], "sourceMap": true}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"liveReload": true, "hmr": false}, "configurations": {"production": {"buildTarget": "cu-test-local:build:production"}, "development": {"buildTarget": "cu-test-local:build:development", "port": 4300}, "playwright": {"buildTarget": "cu-test-local:build:playwright", "port": 4300}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-builders/jest:run", "options": {"configPath": "./jest.config.ts"}}, "build-coverage": {"builder": "@angular-builders/custom-webpack:browser", "options": {"outputPath": "dist/cu-test-local", "index": "projects/cu-test-local/src/index.html", "main": "projects/cu-test-local/src/main.ts", "polyfills": ["zone.js"], "tsConfig": "projects/cu-test-local/tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["projects/cu-test-local/src/favicon.ico", "projects/cu-test-local/src/assets", {"glob": "**/*", "input": "node_modules/@onemrvapublic/design-system-theme/assets", "output": "assets/"}, {"glob": "**/*", "input": "node_modules/@onemrvapublic/design-system/layout/assets", "output": "assets/"}, {"glob": "**/*", "input": "./node_modules/@onemrvapublic/design-system/page-not-found/assets", "output": "/assets/"}], "styles": ["projects/cu-test-local/src/styles.scss"], "scripts": []}, "configurations": {"e2e": {"fileReplacements": [{"replace": "projects/cu-test-local/src/app/environments/environment.ts", "with": "projects/cu-test-local/src/app/environments/environment.e2e.ts"}], "extractLicenses": false, "sourceMap": true, "namedChunks": true, "budgets": [], "vendorChunk": true, "customWebpackConfig": {"path": "projects/cu-test-local/cypress/coverage.webpack.ts", "replaceDuplicatePlugins": true}}}, "defaultConfiguration": "e2e"}, "e2e": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "cu-test-local:serve-coverage", "watch": true, "headless": false}, "configurations": {"production": {"devServerTarget": "cu-test-local:serve-coverage:production"}}}, "serve-coverage": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"buildTarget": "cu-test-local:build-coverage:e2e", "host": "0.0.0.0", "port": 4300}}, "cypress-run": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "cu-test-local:serve"}, "configurations": {"production": {"devServerTarget": "cu-test-local:serve:production"}}}, "cypress-open": {"builder": "@cypress/schematic:cypress", "options": {"watch": true, "headless": false}}, "e2e-ci": {"builder": "@cypress/schematic:cypress", "options": {"devServerTarget": "cu-test-local:serve-coverage", "headless": true, "watch": false}, "configurations": {"production": {"devServerTarget": "cu-test-local:serve-coverage:production"}}}, "playwright-ci": {"builder": "playwright-ng-schematics:playwright", "options": {"devServerTarget": "cu-test-local:serve-coverage", "commands": [{"command": "npm run playwright:headless"}]}, "configurations": {"production": {"devServerTarget": "cu-test-local:serve-coverage:production"}}}}}}, "cli": {"analytics": false}}