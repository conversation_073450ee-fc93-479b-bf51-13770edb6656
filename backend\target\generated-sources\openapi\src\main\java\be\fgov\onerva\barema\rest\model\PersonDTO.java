/*
 * Bareme REST API
 * Service to retieve bareme.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.barema.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PersonDTO
 */
@JsonPropertyOrder({
  PersonDTO.JSON_PROPERTY_SSIN,
  PersonDTO.JSON_PROPERTY_NUM_PENS
})
@JsonTypeName("Person")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:06.182361900+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PersonDTO {
  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_NUM_PENS = "numPens";
  private BigDecimal numPens;

  public PersonDTO() {
  }

  public PersonDTO ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public PersonDTO numPens(BigDecimal numPens) {
    
    this.numPens = numPens;
    return this;
  }

  /**
   * Get numPens
   * @return numPens
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNumPens() {
    return numPens;
  }


  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumPens(BigDecimal numPens) {
    this.numPens = numPens;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PersonDTO person = (PersonDTO) o;
    return Objects.equals(this.ssin, person.ssin) &&
        Objects.equals(this.numPens, person.numPens);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ssin, numPens);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PersonDTO {\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    numPens: ").append(toIndentedString(numPens)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

