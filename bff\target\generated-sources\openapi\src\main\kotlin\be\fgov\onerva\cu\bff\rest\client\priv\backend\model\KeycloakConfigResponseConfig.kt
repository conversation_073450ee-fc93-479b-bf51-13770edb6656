/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param url Base uri of the realm
 * @param clientId The id of the frontend client
 * @param realm The keycloak realm
 */


data class KeycloakConfigResponseConfig (

    /* Base uri of the realm */
    @get:JsonProperty("url")
    val url: kotlin.String,

    /* The id of the frontend client */
    @get:JsonProperty("clientId")
    val clientId: kotlin.String,

    /* The keycloak realm */
    @get:JsonProperty("realm")
    val realm: kotlin.String

) {


}

