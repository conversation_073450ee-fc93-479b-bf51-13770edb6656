package be.fgov.onerva.wo_thirdparty.api;

import be.fgov.onerva.wo_thirdparty.invoker.ApiClient;
import be.fgov.onerva.wo_thirdparty.invoker.BaseApi;

import be.fgov.onerva.wo_thirdparty.rest.model.Party;
import be.fgov.onerva.wo_thirdparty.rest.model.PartyList;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PartiesApi extends BaseApi {

    public PartiesApi() {
        super(new ApiClient());
    }

    public PartiesApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * Get a party by one ID
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param q Denomination of the tpms to retrieve party (optional)
     * @param searchKey Search Key to retrieve party (optional)
     * @param euVAT EU_VAT to retrieve party (optional)
     * @param feen FEEN to retrieve party (optional)
     * @param foleenId Foleen Id to retrieve party (optional)
     * @param bce BCE to retrieve party (optional)
     * @param nsso NOSS NR to retrieve party (optional)
     * @param ssa SSA to retrieve party (optional)
     * @param ssin NISS to retrieve party (optional)
     * @param jkey JKEY to retrieve party (optional)
     * @param ossContractNbr OSS_CON_NBR to retrieve party (optional)
     * @param ossRegistrationNbr OSS_REG_NBR to retrieve party (optional)
     * @param deptId Department Id to retrieve party (optional)
     * @param agentId Agent Id to retrieve party (optional)
     * @param agentNbr Agent Nbr to retrieve party (optional)
     * @param nihiiNbr NIHII_NBR to retrieve party (optional)
     * @param curatorId CURATOR_ID to retrieve party (optional)
     * @param establishmentUnitNbr Establishment Unit Number to retrieve party (optional)
     * @param pageSize Page Size (optional, default to 10)
     * @param page Page Index (optional, default to 0)
     * @param sortBy Sort by (denomination or type) (optional)
     * @param sortOrder Sort order, ASC (default) or DESC (optional, default to ASC)
     * @param lang Lang (optional)
     * @param type Type of party: ORGANISATION or PERSON (optional)
     * @param exactName Exact name to retrieve party (optional)
     * @param excludeKiwiSource Exclude KIWI source. Enabled by default. (optional)
     * @param authorization  (optional)
     * @return PartyList
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public PartyList getPartyByQueryParam(String q, String searchKey, String euVAT, String feen, String foleenId, String bce, String nsso, String ssa, String ssin, String jkey, String ossContractNbr, String ossRegistrationNbr, String deptId, String agentId, String agentNbr, String nihiiNbr, String curatorId, String establishmentUnitNbr, Integer pageSize, Integer page, String sortBy, String sortOrder, String lang, String type, Boolean exactName, Boolean excludeKiwiSource, String authorization) throws RestClientException {
        return getPartyByQueryParamWithHttpInfo(q, searchKey, euVAT, feen, foleenId, bce, nsso, ssa, ssin, jkey, ossContractNbr, ossRegistrationNbr, deptId, agentId, agentNbr, nihiiNbr, curatorId, establishmentUnitNbr, pageSize, page, sortBy, sortOrder, lang, type, exactName, excludeKiwiSource, authorization).getBody();
    }

    /**
     * Get a party by one ID
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param q Denomination of the tpms to retrieve party (optional)
     * @param searchKey Search Key to retrieve party (optional)
     * @param euVAT EU_VAT to retrieve party (optional)
     * @param feen FEEN to retrieve party (optional)
     * @param foleenId Foleen Id to retrieve party (optional)
     * @param bce BCE to retrieve party (optional)
     * @param nsso NOSS NR to retrieve party (optional)
     * @param ssa SSA to retrieve party (optional)
     * @param ssin NISS to retrieve party (optional)
     * @param jkey JKEY to retrieve party (optional)
     * @param ossContractNbr OSS_CON_NBR to retrieve party (optional)
     * @param ossRegistrationNbr OSS_REG_NBR to retrieve party (optional)
     * @param deptId Department Id to retrieve party (optional)
     * @param agentId Agent Id to retrieve party (optional)
     * @param agentNbr Agent Nbr to retrieve party (optional)
     * @param nihiiNbr NIHII_NBR to retrieve party (optional)
     * @param curatorId CURATOR_ID to retrieve party (optional)
     * @param establishmentUnitNbr Establishment Unit Number to retrieve party (optional)
     * @param pageSize Page Size (optional, default to 10)
     * @param page Page Index (optional, default to 0)
     * @param sortBy Sort by (denomination or type) (optional)
     * @param sortOrder Sort order, ASC (default) or DESC (optional, default to ASC)
     * @param lang Lang (optional)
     * @param type Type of party: ORGANISATION or PERSON (optional)
     * @param exactName Exact name to retrieve party (optional)
     * @param excludeKiwiSource Exclude KIWI source. Enabled by default. (optional)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;PartyList&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<PartyList> getPartyByQueryParamWithHttpInfo(String q, String searchKey, String euVAT, String feen, String foleenId, String bce, String nsso, String ssa, String ssin, String jkey, String ossContractNbr, String ossRegistrationNbr, String deptId, String agentId, String agentNbr, String nihiiNbr, String curatorId, String establishmentUnitNbr, Integer pageSize, Integer page, String sortBy, String sortOrder, String lang, String type, Boolean exactName, Boolean excludeKiwiSource, String authorization) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "searchKey", searchKey));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "euVAT", euVAT));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "feen", feen));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "foleenId", foleenId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "bce", bce));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "nsso", nsso));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssa", ssa));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ssin", ssin));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "jkey", jkey));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ossContractNbr", ossContractNbr));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "ossRegistrationNbr", ossRegistrationNbr));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "deptId", deptId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "agentId", agentId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "agentNbr", agentNbr));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "nihiiNbr", nihiiNbr));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "curator_id", curatorId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "establishmentUnitNbr", establishmentUnitNbr));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", page));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortBy", sortBy));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sortOrder", sortOrder));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "lang", lang));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "type", type));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "exactName", exactName));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "excludeKiwiSource", excludeKiwiSource));
        

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<PartyList> localReturnType = new ParameterizedTypeReference<PartyList>() {};
        return apiClient.invokeAPI("/parties", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Get a party by tpms ID
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param tpmsId ID of the party to retrieve (required)
     * @param lang Lang (optional)
     * @param excludeKiwiSource Exclude KIWI source. Enabled by default. (optional)
     * @param authorization  (optional)
     * @return Party
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Party getPartyByTpmsId(String tpmsId, String lang, Boolean excludeKiwiSource, String authorization) throws RestClientException {
        return getPartyByTpmsIdWithHttpInfo(tpmsId, lang, excludeKiwiSource, authorization).getBody();
    }

    /**
     * Get a party by tpms ID
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Invalid parameter
     * <p><b>404</b> - No party found for ID
     * @param tpmsId ID of the party to retrieve (required)
     * @param lang Lang (optional)
     * @param excludeKiwiSource Exclude KIWI source. Enabled by default. (optional)
     * @param authorization  (optional)
     * @return ResponseEntity&lt;Party&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Party> getPartyByTpmsIdWithHttpInfo(String tpmsId, String lang, Boolean excludeKiwiSource, String authorization) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'tpmsId' is set
        if (tpmsId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'tpmsId' when calling getPartyByTpmsId");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("tpmsId", tpmsId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "lang", lang));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "excludeKiwiSource", excludeKiwiSource));
        

        if (authorization != null)
        localVarHeaderParams.add("Authorization", apiClient.parameterToString(authorization));

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Party> localReturnType = new ParameterizedTypeReference<Party>() {};
        return apiClient.invokeAPI("/parties/{tpmsId}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
