# Feature Flags Documentation

## Overview

This document describes the feature flags used in the NEO Unemployment System. Feature flags are managed through Flagsmith and allow for controlled rollout of features and configuration management.

## Feature Flag Management

The application uses [<PERSON><PERSON>](https://flagsmith.com/) for feature flag management. This allows for:

- Runtime feature toggling without deployments
- Gradual feature rollouts
- A/B testing capabilities
- Environment-specific configurations
- Real-time configuration updates

## Current Feature Flags

### Production Feature Flags

#### `change-of-address-enabled`
**Type:** Boolean Flag  
**Description:** Enables the Change of Address feature for unemployment requests

**Purpose:**
- Controls access to the change of address functionality
- Allows for gradual rollout of this critical feature
- Also sets the list of accepted C9 types in supported-c9-types

**Usage:**
- When enabled, users can process change of address requests
- Related to C9 message types processing
- Affects both frontend UI and backend processing logic

**Dependencies:**
- Related to `supported-c9-types` configuration
- Impacts citizen information processing workflows

---

#### `supported-c9-types`
**Type:** String/Configuration Flag  
**Description:** Store the list of accepted C9 types as a comma separated list

**Purpose:**
- Defines which C9 message types are supported by the system
- Allows runtime configuration of supported message types
- Enables quick addition/removal of supported types without deployment

**Format:**
- Comma-separated list of C9 type codes
- Example: `"400,410"` for multiple types

**Usage:**
- Backend services use this to validate incoming C9 messages
- Frontend may use this for validation and UI display
- Message processing pipeline filters based on these types

---
### Archived/Legacy Feature Flags

#### `first-feature`
**Type:** Boolean Flag  
**Description:** Just a test feature

**Purpose:**
- Development/testing purposes
- Example implementation of feature flag pattern
- Can be used for training or demonstration

**Status:** Archived.

---

#### `supported-c9-types-list`
**Type:** String Flag  
**Current Value:** `"[\"400\"]"`  
**Status:** 🗑️ Obsolete - should be deleted  
**Description:** Legacy configuration for C9 types

**Purpose:**
- Previous implementation of C9 types configuration
- Replaced by `supported-c9-types`
- JSON array format (deprecated in favor of comma-separated string)

**Migration Notes:**
- This flag should be removed after confirming no code dependencies remain
- Functionality has been migrated to `supported-c9-types`

**Status:** Archived.

### Best Practices

1. **Naming Convention:**
   - Use kebab-case for flag names
   - Include descriptive prefixes for related features
   - Avoid abbreviations that might be unclear

2. **Documentation:**
   - Always document the purpose and impact of new flags
   - Update this documentation when flags are added/removed
   - Include rollback procedures for critical flags

3. **Lifecycle Management:**
   - Regularly review and clean up obsolete flags
   - Set expiration dates for temporary flags
   - Monitor flag usage to identify unused flags

4. **Testing:**
   - Test both enabled and disabled states
   - Include feature flag scenarios in integration tests
   - Verify flag fallback behaviors

## Environment Configuration

Feature flags should be configured consistently across environments:

- **Development:** Used for testing new features
- **CI:** Typically mirrors production for integration testing
- **Test:** May have different configurations for specific test scenarios
- **Validation:** Should mirror production configuration
- **Production:** Stable, well-tested configurations only

## Monitoring and Alerting

Consider implementing monitoring for:

- Feature flag configuration changes
- Usage patterns of feature-gated functionality
- Performance impact of flag evaluations
- Failed flag evaluations or fallbacks

## Security Considerations

- Sensitive configuration should not be exposed in client-side code
- Feature flags should not bypass security controls
- Flag changes should be audited and logged
- Consider the impact of flag states on security features

## Rollback Procedures

In case of issues with feature-flagged functionality:

1. **Immediate Response:** Disable the problematic flag via Flagsmith dashboard
2. **Verification:** Confirm the change has propagated to all instances
3. **Investigation:** Analyze logs and metrics to understand the issue
4. **Communication:** Notify relevant stakeholders of the incident
5. **Resolution:** Fix the underlying issue before re-enabling

## Related Documentation

- [Architecture Decision Records](../decisions)
- [System Documentation](doc.md)
- [Database Schema](../database/schema.md)
- [Hexagonal Architecture ADR](../adrs/0002-hexagonal-architecture.md)
