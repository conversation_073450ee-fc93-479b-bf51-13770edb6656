import {CommonModule} from "@angular/common";
import {Component, OnChanges, OnInit} from "@angular/core";
import {ActivatedRoute, RouterModule} from "@angular/router";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaLayoutModule} from "@onemrvapublic/design-system/layout";
import {OnemrvaMatToastModule} from "@onemrvapublic/design-system/mat-toast";
import {BehaviorSubject, combineLatest, EMPTY, filter, map, Observable, Subject, takeUntil, tap} from "rxjs";

import {ComponentType} from "@angular/cdk/overlay";
import {DataCaptureComponent} from "../../../../../cu/src/lib/webcomponents/data-capture/data-capture.component";
import {
    DataValidationComponent,
} from "../../../../../cu/src/lib/webcomponents/data-validation/data-validation.component";
import {WoMockUiLanguageStore} from "../../state/wo-mock-ui-language-store";

@Component({
    selector: "app-presentation",
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    TranslateModule,
    OnemrvaLayoutModule,
    OnemrvaMatToastModule,
  ],
    templateUrl: "./app-presentation.component.html",
    styleUrls: ["./app-presentation.component.scss"],
})
export class AppPresentationComponent implements OnInit, OnChanges {

  private webComponentLoaded = new BehaviorSubject<boolean>(false);
  private readonly destroyed$ = new Subject<void>();
    isOpen1: boolean = false;
    isOpen2: boolean = true;
    isOpen3: boolean = true;
    isOpen4: boolean = false;
    isOpen5: boolean = false;
    isOpenCompTitle: boolean = true;
    language$: Observable<string> = EMPTY;
    private webComponent: ComponentType<any> | undefined;
    isClosed = false;

    constructor(
      private route: ActivatedRoute,
      private languageStore: WoMockUiLanguageStore,
    ) {
    }

  ngOnChanges(){
      console.log("changes triggered")
    }

    ngOnInit() {

      combineLatest({
        params: this.route.queryParams,
        loaded: this.webComponentLoaded
      }).pipe(
        takeUntil(this.destroyed$),
        filter(({ loaded }) => loaded), // Only proceed when component is loaded
        tap(({ params }) => {
          console.log("params", params);
          console.log(this.webComponent);

          if (this.webComponent && (this.webComponent instanceof DataCaptureComponent || this.webComponent instanceof DataValidationComponent)) {
            console.log("add requestId to webcomponent");
            this.webComponent.requestId = params["requestId"];
            this.webComponent.language = params["language"];
            this.webComponent.status = params["status"];
            this.webComponent.task = params["task"]; //%7B%22state%22%3A%7B%22code%22%3A%22Wait%22%7D%7D is encoded for {"state":{"code":"Wait"}}
          }
        })
      ).subscribe();
      // this.route.queryParams.pipe(
      //   takeUntil(this.destroyed$),
      //   tap(params => {
      //     console.log("params", params);
      //
      //     console.log(this.webComponent)
      //     console.log(this.webComponent instanceof DataCaptureComponent)
      //
      //     if (this.webComponent && this.webComponent instanceof DataCaptureComponent) {
      //       console.log("add requestId to webcomponent")
      //       this.webComponent.requestId = params["requestId"];
      //     }
      //   }),
      // ).subscribe();



      this.languageStore.currentLanguage$.pipe(
        filter((language) => !!language),
        map((language) => language!),
        tap((language) => {
          if (this.webComponent && this.webComponent instanceof DataCaptureComponent) {
            this.webComponent.language = language.toString();
          }

          const languageToUpperCase = language.toUpperCase();
          fetch("../assets/i18n/" + languageToUpperCase + ".json")
            .then(response => response.json())
            .catch(error => {
              console.error("Error fetching JSON:", error);
            });
        }),
        takeUntil(this.destroyed$),
      ).subscribe();
    }

  ngOnDestroy() {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

    onOutletLoaded(component: ComponentType<any>) {
        if (!component) {
            console.error("Component is not provided.");
        } else {
          console.log(component)
            this.webComponent = component;
          this.webComponentLoaded.next(true);
        }
    }

    getNameAndInss(request: any | null, formatted: boolean = true) {
        const inss = request?.employeeData?.inss ?? "00000000097";
        const formatedInss = inss ? `${inss.slice(0, 6)}/${inss.slice(6, 9)}-${inss.slice(9, 11)}` : "";
        const firstName = request?.employeeData?.firstName ?? "JANE";
        const lastName = request?.employeeData?.lastName ?? "DO";
        const capitalizeFirstLetter = (str: string | undefined) => {
            return str ? str.charAt(0).toUpperCase() + str.slice(1).toLowerCase() : "";
        };
        const name = !formatted ?
            capitalizeFirstLetter(firstName) + " " + capitalizeFirstLetter(lastName) :
            firstName + " " + lastName;
        return name + " - " + (formatted ? formatedInss : inss);
    }

}
