package be.fgov.onerva.cu.backend.application.validation

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass
import be.fgov.onerva.cu.backend.application.domain.FieldSource

@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [ValidFieldNamesValidator::class])
annotation class ValidFieldNames(
    val names: Array<String> = [],
    val message: String = "Field names must be from the allowed list",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class ValidFieldNamesValidator : ConstraintValidator<ValidFieldNames, List<FieldSource>?> {
    private lateinit var validNames: Set<String>

    override fun initialize(constraintAnnotation: ValidFieldNames) {
        validNames = constraintAnnotation.names.toSet()
    }

    override fun isValid(fieldSources: List<FieldSource>?, context: ConstraintValidatorContext): Boolean {
        if (fieldSources.isNullOrEmpty()) {
            return true
        }

        // Check if all field names are in the valid names list
        return fieldSources.all { it.fieldName in validNames }
    }
}