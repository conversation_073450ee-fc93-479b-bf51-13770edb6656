/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model

import be.fgov.onerva.person.rest.model.AddressDTO
import be.fgov.onerva.person.rest.model.PaymentTypeDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Citizen creation request
 *
 * @param id 
 * @param created 
 * @param niss 
 * @param sent 
 * @param retryCount 
 * @param type 
 * @param firstname 
 * @param lastname 
 * @param correlationId 
 * @param updated 
 * @param returnCode 
 * @param error 
 * @param nationalityCode 
 * @param paymentType 
 * @param unionDue 
 * @param valueDate 
 * @param address 
 * @param username 
 */


data class CitizenRequestDTO (

    @get:JsonProperty("id")
    val id: java.math.BigDecimal,

    @get:JsonProperty("created")
    val created: java.time.OffsetDateTime,

    @get:JsonProperty("niss")
    val niss: kotlin.String,

    @get:JsonProperty("sent")
    val sent: kotlin.Boolean,

    @get:JsonProperty("retryCount")
    val retryCount: kotlin.Int,

    @get:JsonProperty("type")
    val type: CitizenRequestDTO.Type,

    @get:JsonProperty("firstname")
    val firstname: kotlin.String? = null,

    @get:JsonProperty("lastname")
    val lastname: kotlin.String? = null,

    @get:JsonProperty("correlationId")
    val correlationId: kotlin.String? = null,

    @get:JsonProperty("updated")
    val updated: java.time.OffsetDateTime? = null,

    @get:JsonProperty("returnCode")
    val returnCode: kotlin.Int? = null,

    @get:JsonProperty("error")
    val error: kotlin.String? = null,

    @get:JsonProperty("nationalityCode")
    val nationalityCode: kotlin.Int? = null,

    @get:JsonProperty("paymentType")
    val paymentType: PaymentTypeDTO? = null,

    @get:JsonProperty("unionDue")
    val unionDue: kotlin.Boolean? = null,

    @get:JsonProperty("valueDate")
    val valueDate: java.time.LocalDate? = null,

    @get:JsonProperty("address")
    val address: AddressDTO? = null,

    @get:JsonProperty("username")
    val username: kotlin.String? = null

) {

    /**
     * 
     *
     * Values: CREATE,UPDATE
     */
    enum class Type(val value: kotlin.String) {
        @JsonProperty(value = "CREATE") CREATE("CREATE"),
        @JsonProperty(value = "UPDATE") UPDATE("UPDATE");
    }

}

