/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { Annex } from './annex';


export interface RequestBasicInfoResponse { 
    /**
     * The date when the request was submitted
     */
    requestDate: string;
    /**
     * The first name of the person
     */
    firstName: string;
    /**
     * The last name of the person
     */
    lastName: string;
    /**
     * The Social Security Identification Number
     */
    ssin: string;
    /**
     * The introduction date of the request
     */
    introductionDate?: string;
    /**
     * The valid date of the request
     */
    dateValid?: string;
    annexes?: Array<Annex>;
    /**
     * The decision type of the request
     */
    decisionType?: RequestBasicInfoResponse.DecisionTypeEnum;
    decisionBarema?: string;
    /**
     * The ID of the related C9 request
     */
    c9Id: string;
    /**
     * The type of identity document used for the request
     */
    documentType: RequestBasicInfoResponse.DocumentTypeEnum;
    /**
     * The pushback status of the request
     */
    pushbackStatus?: RequestBasicInfoResponse.PushbackStatusEnum;
}
export namespace RequestBasicInfoResponse {
    export type DecisionTypeEnum = 'C2Y' | 'C2N' | 'C2F' | 'C2P' | 'C51' | 'C9B' | 'C2' | 'C9NA';
    export const DecisionTypeEnum = {
        C2Y: 'C2Y' as DecisionTypeEnum,
        C2N: 'C2N' as DecisionTypeEnum,
        C2F: 'C2F' as DecisionTypeEnum,
        C2P: 'C2P' as DecisionTypeEnum,
        C51: 'C51' as DecisionTypeEnum,
        C9B: 'C9B' as DecisionTypeEnum,
        C2: 'C2' as DecisionTypeEnum,
        C9Na: 'C9NA' as DecisionTypeEnum
    };
    export type DocumentTypeEnum = 'ELECTRONIC' | 'PAPER';
    export const DocumentTypeEnum = {
        Electronic: 'ELECTRONIC' as DocumentTypeEnum,
        Paper: 'PAPER' as DocumentTypeEnum
    };
    export type PushbackStatusEnum = 'PENDING' | 'OK' | 'NOK';
    export const PushbackStatusEnum = {
        Pending: 'PENDING' as PushbackStatusEnum,
        Ok: 'OK' as PushbackStatusEnum,
        Nok: 'NOK' as PushbackStatusEnum
    };
}


