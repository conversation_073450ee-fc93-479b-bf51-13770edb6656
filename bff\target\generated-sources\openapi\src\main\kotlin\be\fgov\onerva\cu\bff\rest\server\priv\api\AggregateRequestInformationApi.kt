/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataCaptureResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.AggregatedChangePersonalDataValidateResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateAggregatedChangePersonalDataRequest
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface AggregateRequestInformationApi {

    @Operation(
        tags = ["Aggregate Request Information",],
        summary = "",
        operationId = "getAggregatedChangePersonalDataCaptureRequest",
        description = """Retrieve aggregated information about a Change of Personal Data request""",
        responses = [
            ApiResponse(responseCode = "200", description = "Basic request information successfully retrieved", content = [Content(schema = Schema(implementation = AggregatedChangePersonalDataCaptureResponse::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/aggregate-requests/change-personal-data/CHANGE_PERSONAL_DATA_CAPTURE/{requestId}"],
            produces = ["application/json"]
    )
    fun getAggregatedChangePersonalDataCaptureRequest(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<AggregatedChangePersonalDataCaptureResponse>

    @Operation(
        tags = ["Aggregate Request Information",],
        summary = "",
        operationId = "getAggregatedChangePersonalDataValidateRequest",
        description = """Retrieve aggregated information about a Change of Personal Data request""",
        responses = [
            ApiResponse(responseCode = "200", description = "Basic request information successfully retrieved", content = [Content(schema = Schema(implementation = AggregatedChangePersonalDataValidateResponse::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/aggregate-requests/change-personal-data/VALIDATION_DATA/{requestId}"],
            produces = ["application/json"]
    )
    fun getAggregatedChangePersonalDataValidateRequest(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<AggregatedChangePersonalDataValidateResponse>

    @Operation(
        tags = ["Aggregate Request Information",],
        summary = "",
        operationId = "updateAggregatedChangePersonalDataRequest",
        description = """Update aggregated information about a Change of Personal Data request""",
        responses = [
            ApiResponse(responseCode = "204", description = "No content"),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/aggregate-requests/change-personal-data/{requestId}"],
            consumes = ["application/json"]
    )
    fun updateAggregatedChangePersonalDataRequest(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "", required = true) @Valid @RequestBody updateAggregatedChangePersonalDataRequest: UpdateAggregatedChangePersonalDataRequest): ResponseEntity<Unit>
}
