/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.Wech505WorkGridRestInner;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * WorkGrid
 */
@JsonPropertyOrder({
  WorkGrid.JSON_PROPERTY_REST
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class WorkGrid {
  public static final String JSON_PROPERTY_REST = "rest";
  private List<Wech505WorkGridRestInner> rest = new ArrayList<>();

  public WorkGrid() {
  }

  public WorkGrid rest(List<Wech505WorkGridRestInner> rest) {
    
    this.rest = rest;
    return this;
  }

  public WorkGrid addRestItem(Wech505WorkGridRestInner restItem) {
    if (this.rest == null) {
      this.rest = new ArrayList<>();
    }
    this.rest.add(restItem);
    return this;
  }

  /**
   * Get rest
   * @return rest
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<Wech505WorkGridRestInner> getRest() {
    return rest;
  }


  @JsonProperty(JSON_PROPERTY_REST)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRest(List<Wech505WorkGridRestInner> rest) {
    this.rest = rest;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WorkGrid workGrid = (WorkGrid) o;
    return Objects.equals(this.rest, workGrid.rest);
  }

  @Override
  public int hashCode() {
    return Objects.hash(rest);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WorkGrid {\n");
    sb.append("    rest: ").append(toIndentedString(rest)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

