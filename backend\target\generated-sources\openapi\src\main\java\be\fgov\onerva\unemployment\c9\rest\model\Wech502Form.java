/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.DclInformationType;
import be.fgov.onerva.unemployment.c9.rest.model.HandledOriginalFormType;
import be.fgov.onerva.unemployment.c9.rest.model.HandledReferenceType;
import be.fgov.onerva.unemployment.c9.rest.model.ReferenceType;
import be.fgov.onerva.unemployment.c9.rest.model.RiskIdentificationType;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502CoordinatesContactPerson;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502CoordinatesInformant;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502EmployerDeclarationLink;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502NOSSLPAEmployerDeclarationLink;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502Form
 */
@JsonPropertyOrder({
  Wech502Form.JSON_PROPERTY_IDENTIFICATION,
  Wech502Form.JSON_PROPERTY_FORM_CREATION_DATE,
  Wech502Form.JSON_PROPERTY_FORM_CREATION_HOUR,
  Wech502Form.JSON_PROPERTY_ATTESTATION_STATUS,
  Wech502Form.JSON_PROPERTY_TYPE_FORM,
  Wech502Form.JSON_PROPERTY_HANDLED_ORIGINAL_FORM,
  Wech502Form.JSON_PROPERTY_COORDINATES_CONTACT_PERSON,
  Wech502Form.JSON_PROPERTY_COORDINATES_INFORMANT,
  Wech502Form.JSON_PROPERTY_DCL_INFORMATION,
  Wech502Form.JSON_PROPERTY_RISK_IDENTIFICATION,
  Wech502Form.JSON_PROPERTY_REFERENCE,
  Wech502Form.JSON_PROPERTY_HANDLED_REFERENCE,
  Wech502Form.JSON_PROPERTY_EMPLOYER_DECLARATION_LINK,
  Wech502Form.JSON_PROPERTY_NOSSLPA_EMPLOYER_DECLARATION_LINK
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502Form {
  public static final String JSON_PROPERTY_IDENTIFICATION = "identification";
  private String identification;

  public static final String JSON_PROPERTY_FORM_CREATION_DATE = "formCreationDate";
  private LocalDate formCreationDate;

  public static final String JSON_PROPERTY_FORM_CREATION_HOUR = "formCreationHour";
  private String formCreationHour;

  public static final String JSON_PROPERTY_ATTESTATION_STATUS = "attestationStatus";
  private String attestationStatus;

  /**
   * Gets or Sets typeForm
   */
  public enum TypeFormEnum {
    SU("SU");

    private String value;

    TypeFormEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeFormEnum fromValue(String value) {
      for (TypeFormEnum b : TypeFormEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_TYPE_FORM = "typeForm";
  private TypeFormEnum typeForm;

  public static final String JSON_PROPERTY_HANDLED_ORIGINAL_FORM = "handledOriginalForm";
  private HandledOriginalFormType handledOriginalForm;

  public static final String JSON_PROPERTY_COORDINATES_CONTACT_PERSON = "coordinatesContactPerson";
  private Wech502CoordinatesContactPerson coordinatesContactPerson;

  public static final String JSON_PROPERTY_COORDINATES_INFORMANT = "coordinatesInformant";
  private Wech502CoordinatesInformant coordinatesInformant;

  public static final String JSON_PROPERTY_DCL_INFORMATION = "dclInformation";
  private DclInformationType dclInformation;

  public static final String JSON_PROPERTY_RISK_IDENTIFICATION = "riskIdentification";
  private RiskIdentificationType riskIdentification;

  public static final String JSON_PROPERTY_REFERENCE = "reference";
  private List<ReferenceType> reference = new ArrayList<>();

  public static final String JSON_PROPERTY_HANDLED_REFERENCE = "handledReference";
  private HandledReferenceType handledReference;

  public static final String JSON_PROPERTY_EMPLOYER_DECLARATION_LINK = "employerDeclarationLink";
  private Wech502EmployerDeclarationLink employerDeclarationLink;

  public static final String JSON_PROPERTY_NOSSLPA_EMPLOYER_DECLARATION_LINK = "nosslpaEmployerDeclarationLink";
  private Wech502NOSSLPAEmployerDeclarationLink nosslpaEmployerDeclarationLink;

  public Wech502Form() {
  }

  public Wech502Form identification(String identification) {
    
    this.identification = identification;
    return this;
  }

  /**
   * Get identification
   * @return identification
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getIdentification() {
    return identification;
  }


  @JsonProperty(JSON_PROPERTY_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setIdentification(String identification) {
    this.identification = identification;
  }

  public Wech502Form formCreationDate(LocalDate formCreationDate) {
    
    this.formCreationDate = formCreationDate;
    return this;
  }

  /**
   * Get formCreationDate
   * @return formCreationDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORM_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getFormCreationDate() {
    return formCreationDate;
  }


  @JsonProperty(JSON_PROPERTY_FORM_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormCreationDate(LocalDate formCreationDate) {
    this.formCreationDate = formCreationDate;
  }

  public Wech502Form formCreationHour(String formCreationHour) {
    
    this.formCreationHour = formCreationHour;
    return this;
  }

  /**
   * Get formCreationHour
   * @return formCreationHour
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORM_CREATION_HOUR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getFormCreationHour() {
    return formCreationHour;
  }


  @JsonProperty(JSON_PROPERTY_FORM_CREATION_HOUR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormCreationHour(String formCreationHour) {
    this.formCreationHour = formCreationHour;
  }

  public Wech502Form attestationStatus(String attestationStatus) {
    
    this.attestationStatus = attestationStatus;
    return this;
  }

  /**
   * Get attestationStatus
   * @return attestationStatus
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ATTESTATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getAttestationStatus() {
    return attestationStatus;
  }


  @JsonProperty(JSON_PROPERTY_ATTESTATION_STATUS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAttestationStatus(String attestationStatus) {
    this.attestationStatus = attestationStatus;
  }

  public Wech502Form typeForm(TypeFormEnum typeForm) {
    
    this.typeForm = typeForm;
    return this;
  }

  /**
   * Get typeForm
   * @return typeForm
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public TypeFormEnum getTypeForm() {
    return typeForm;
  }


  @JsonProperty(JSON_PROPERTY_TYPE_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTypeForm(TypeFormEnum typeForm) {
    this.typeForm = typeForm;
  }

  public Wech502Form handledOriginalForm(HandledOriginalFormType handledOriginalForm) {
    
    this.handledOriginalForm = handledOriginalForm;
    return this;
  }

  /**
   * Get handledOriginalForm
   * @return handledOriginalForm
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_ORIGINAL_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledOriginalFormType getHandledOriginalForm() {
    return handledOriginalForm;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_ORIGINAL_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledOriginalForm(HandledOriginalFormType handledOriginalForm) {
    this.handledOriginalForm = handledOriginalForm;
  }

  public Wech502Form coordinatesContactPerson(Wech502CoordinatesContactPerson coordinatesContactPerson) {
    
    this.coordinatesContactPerson = coordinatesContactPerson;
    return this;
  }

  /**
   * Get coordinatesContactPerson
   * @return coordinatesContactPerson
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COORDINATES_CONTACT_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Wech502CoordinatesContactPerson getCoordinatesContactPerson() {
    return coordinatesContactPerson;
  }


  @JsonProperty(JSON_PROPERTY_COORDINATES_CONTACT_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoordinatesContactPerson(Wech502CoordinatesContactPerson coordinatesContactPerson) {
    this.coordinatesContactPerson = coordinatesContactPerson;
  }

  public Wech502Form coordinatesInformant(Wech502CoordinatesInformant coordinatesInformant) {
    
    this.coordinatesInformant = coordinatesInformant;
    return this;
  }

  /**
   * Get coordinatesInformant
   * @return coordinatesInformant
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COORDINATES_INFORMANT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502CoordinatesInformant getCoordinatesInformant() {
    return coordinatesInformant;
  }


  @JsonProperty(JSON_PROPERTY_COORDINATES_INFORMANT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCoordinatesInformant(Wech502CoordinatesInformant coordinatesInformant) {
    this.coordinatesInformant = coordinatesInformant;
  }

  public Wech502Form dclInformation(DclInformationType dclInformation) {
    
    this.dclInformation = dclInformation;
    return this;
  }

  /**
   * Get dclInformation
   * @return dclInformation
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DCL_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public DclInformationType getDclInformation() {
    return dclInformation;
  }


  @JsonProperty(JSON_PROPERTY_DCL_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDclInformation(DclInformationType dclInformation) {
    this.dclInformation = dclInformation;
  }

  public Wech502Form riskIdentification(RiskIdentificationType riskIdentification) {
    
    this.riskIdentification = riskIdentification;
    return this;
  }

  /**
   * Get riskIdentification
   * @return riskIdentification
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RISK_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public RiskIdentificationType getRiskIdentification() {
    return riskIdentification;
  }


  @JsonProperty(JSON_PROPERTY_RISK_IDENTIFICATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRiskIdentification(RiskIdentificationType riskIdentification) {
    this.riskIdentification = riskIdentification;
  }

  public Wech502Form reference(List<ReferenceType> reference) {
    
    this.reference = reference;
    return this;
  }

  public Wech502Form addReferenceItem(ReferenceType referenceItem) {
    if (this.reference == null) {
      this.reference = new ArrayList<>();
    }
    this.reference.add(referenceItem);
    return this;
  }

  /**
   * Get reference
   * @return reference
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ReferenceType> getReference() {
    return reference;
  }


  @JsonProperty(JSON_PROPERTY_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReference(List<ReferenceType> reference) {
    this.reference = reference;
  }

  public Wech502Form handledReference(HandledReferenceType handledReference) {
    
    this.handledReference = handledReference;
    return this;
  }

  /**
   * Get handledReference
   * @return handledReference
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledReferenceType getHandledReference() {
    return handledReference;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledReference(HandledReferenceType handledReference) {
    this.handledReference = handledReference;
  }

  public Wech502Form employerDeclarationLink(Wech502EmployerDeclarationLink employerDeclarationLink) {
    
    this.employerDeclarationLink = employerDeclarationLink;
    return this;
  }

  /**
   * Get employerDeclarationLink
   * @return employerDeclarationLink
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502EmployerDeclarationLink getEmployerDeclarationLink() {
    return employerDeclarationLink;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerDeclarationLink(Wech502EmployerDeclarationLink employerDeclarationLink) {
    this.employerDeclarationLink = employerDeclarationLink;
  }

  public Wech502Form nosslpaEmployerDeclarationLink(Wech502NOSSLPAEmployerDeclarationLink nosslpaEmployerDeclarationLink) {
    
    this.nosslpaEmployerDeclarationLink = nosslpaEmployerDeclarationLink;
    return this;
  }

  /**
   * Get nosslpaEmployerDeclarationLink
   * @return nosslpaEmployerDeclarationLink
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NOSSLPA_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502NOSSLPAEmployerDeclarationLink getNosslpaEmployerDeclarationLink() {
    return nosslpaEmployerDeclarationLink;
  }


  @JsonProperty(JSON_PROPERTY_NOSSLPA_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNosslpaEmployerDeclarationLink(Wech502NOSSLPAEmployerDeclarationLink nosslpaEmployerDeclarationLink) {
    this.nosslpaEmployerDeclarationLink = nosslpaEmployerDeclarationLink;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502Form wech502Form = (Wech502Form) o;
    return Objects.equals(this.identification, wech502Form.identification) &&
        Objects.equals(this.formCreationDate, wech502Form.formCreationDate) &&
        Objects.equals(this.formCreationHour, wech502Form.formCreationHour) &&
        Objects.equals(this.attestationStatus, wech502Form.attestationStatus) &&
        Objects.equals(this.typeForm, wech502Form.typeForm) &&
        Objects.equals(this.handledOriginalForm, wech502Form.handledOriginalForm) &&
        Objects.equals(this.coordinatesContactPerson, wech502Form.coordinatesContactPerson) &&
        Objects.equals(this.coordinatesInformant, wech502Form.coordinatesInformant) &&
        Objects.equals(this.dclInformation, wech502Form.dclInformation) &&
        Objects.equals(this.riskIdentification, wech502Form.riskIdentification) &&
        Objects.equals(this.reference, wech502Form.reference) &&
        Objects.equals(this.handledReference, wech502Form.handledReference) &&
        Objects.equals(this.employerDeclarationLink, wech502Form.employerDeclarationLink) &&
        Objects.equals(this.nosslpaEmployerDeclarationLink, wech502Form.nosslpaEmployerDeclarationLink);
  }

  @Override
  public int hashCode() {
    return Objects.hash(identification, formCreationDate, formCreationHour, attestationStatus, typeForm, handledOriginalForm, coordinatesContactPerson, coordinatesInformant, dclInformation, riskIdentification, reference, handledReference, employerDeclarationLink, nosslpaEmployerDeclarationLink);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502Form {\n");
    sb.append("    identification: ").append(toIndentedString(identification)).append("\n");
    sb.append("    formCreationDate: ").append(toIndentedString(formCreationDate)).append("\n");
    sb.append("    formCreationHour: ").append(toIndentedString(formCreationHour)).append("\n");
    sb.append("    attestationStatus: ").append(toIndentedString(attestationStatus)).append("\n");
    sb.append("    typeForm: ").append(toIndentedString(typeForm)).append("\n");
    sb.append("    handledOriginalForm: ").append(toIndentedString(handledOriginalForm)).append("\n");
    sb.append("    coordinatesContactPerson: ").append(toIndentedString(coordinatesContactPerson)).append("\n");
    sb.append("    coordinatesInformant: ").append(toIndentedString(coordinatesInformant)).append("\n");
    sb.append("    dclInformation: ").append(toIndentedString(dclInformation)).append("\n");
    sb.append("    riskIdentification: ").append(toIndentedString(riskIdentification)).append("\n");
    sb.append("    reference: ").append(toIndentedString(reference)).append("\n");
    sb.append("    handledReference: ").append(toIndentedString(handledReference)).append("\n");
    sb.append("    employerDeclarationLink: ").append(toIndentedString(employerDeclarationLink)).append("\n");
    sb.append("    nosslpaEmployerDeclarationLink: ").append(toIndentedString(nosslpaEmployerDeclarationLink)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

