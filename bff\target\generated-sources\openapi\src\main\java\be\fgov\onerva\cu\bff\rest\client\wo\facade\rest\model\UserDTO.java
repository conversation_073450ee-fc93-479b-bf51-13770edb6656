/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * UserDTO
 */
@JsonPropertyOrder({
  UserDTO.JSON_PROPERTY_USERNAME,
  UserDTO.JSON_PROPERTY_FIRSTNAME,
  UserDTO.JSON_PROPERTY_LASTNAME,
  UserDTO.JSON_PROPERTY_OPERATOR_CODE,
  UserDTO.JSON_PROPERTY_OPERATOR_CODES,
  UserDTO.JSON_PROPERTY_INSS
})
@JsonTypeName("User")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class UserDTO {
  public static final String JSON_PROPERTY_USERNAME = "username";
  private String username;

  public static final String JSON_PROPERTY_FIRSTNAME = "firstname";
  private String firstname;

  public static final String JSON_PROPERTY_LASTNAME = "lastname";
  private String lastname;

  public static final String JSON_PROPERTY_OPERATOR_CODE = "operatorCode";
  private String operatorCode;

  public static final String JSON_PROPERTY_OPERATOR_CODES = "operatorCodes";
  private List<String> operatorCodes = new ArrayList<>();

  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public UserDTO() {
  }

  public UserDTO username(String username) {
    
    this.username = username;
    return this;
  }

  /**
   * Get username
   * @return username
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getUsername() {
    return username;
  }


  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setUsername(String username) {
    this.username = username;
  }

  public UserDTO firstname(String firstname) {
    
    this.firstname = firstname;
    return this;
  }

  /**
   * Get firstname
   * @return firstname
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getFirstname() {
    return firstname;
  }


  @JsonProperty(JSON_PROPERTY_FIRSTNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFirstname(String firstname) {
    this.firstname = firstname;
  }

  public UserDTO lastname(String lastname) {
    
    this.lastname = lastname;
    return this;
  }

  /**
   * Get lastname
   * @return lastname
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getLastname() {
    return lastname;
  }


  @JsonProperty(JSON_PROPERTY_LASTNAME)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setLastname(String lastname) {
    this.lastname = lastname;
  }

  public UserDTO operatorCode(String operatorCode) {
    
    this.operatorCode = operatorCode;
    return this;
  }

  /**
   * Get operatorCode
   * @return operatorCode
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOperatorCode() {
    return operatorCode;
  }


  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperatorCode(String operatorCode) {
    this.operatorCode = operatorCode;
  }

  public UserDTO operatorCodes(List<String> operatorCodes) {
    
    this.operatorCodes = operatorCodes;
    return this;
  }

  public UserDTO addOperatorCodesItem(String operatorCodesItem) {
    if (this.operatorCodes == null) {
      this.operatorCodes = new ArrayList<>();
    }
    this.operatorCodes.add(operatorCodesItem);
    return this;
  }

  /**
   * Get operatorCodes
   * @return operatorCodes
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OPERATOR_CODES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<String> getOperatorCodes() {
    return operatorCodes;
  }


  @JsonProperty(JSON_PROPERTY_OPERATOR_CODES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOperatorCodes(List<String> operatorCodes) {
    this.operatorCodes = operatorCodes;
  }

  public UserDTO inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserDTO user = (UserDTO) o;
    return Objects.equals(this.username, user.username) &&
        Objects.equals(this.firstname, user.firstname) &&
        Objects.equals(this.lastname, user.lastname) &&
        Objects.equals(this.operatorCode, user.operatorCode) &&
        Objects.equals(this.operatorCodes, user.operatorCodes) &&
        Objects.equals(this.inss, user.inss);
  }

  @Override
  public int hashCode() {
    return Objects.hash(username, firstname, lastname, operatorCode, operatorCodes, inss);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserDTO {\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    firstname: ").append(toIndentedString(firstname)).append("\n");
    sb.append("    lastname: ").append(toIndentedString(lastname)).append("\n");
    sb.append("    operatorCode: ").append(toIndentedString(operatorCode)).append("\n");
    sb.append("    operatorCodes: ").append(toIndentedString(operatorCodes)).append("\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

