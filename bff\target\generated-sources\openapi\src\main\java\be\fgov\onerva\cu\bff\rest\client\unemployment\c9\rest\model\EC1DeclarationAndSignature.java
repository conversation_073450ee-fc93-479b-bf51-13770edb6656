/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1DeclarationAndSignature
 */
@JsonPropertyOrder({
  EC1DeclarationAndSignature.JSON_PROPERTY_CERTIFICATE_FROM_MINISTRY_OF_SOCIAL_PRECAUTION,
  EC1DeclarationAndSignature.JSON_PROPERTY_COPY_PENSION_RECEIPT,
  EC1DeclarationAndSignature.JSON_PROPERTY_FORM_C1_ANNEXE_REGIS,
  EC1DeclarationAndSignature.JSON_PROPERTY_COPY_RESIDENCE_PERMIT_OR_PERMIT_WORK_MARKET,
  EC1DeclarationAndSignature.JSON_PROPERTY_OTHER_DECLARATION_EXPLANATION,
  EC1DeclarationAndSignature.JSON_PROPERTY_DECLARATION_OF_HONOR,
  EC1DeclarationAndSignature.JSON_PROPERTY_READ_INFORMATION_FORM,
  EC1DeclarationAndSignature.JSON_PROPERTY_COMMUNICATION_OF_MODIFICATION_TO_MY_PAYMENT_OFFICE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1DeclarationAndSignature {
  public static final String JSON_PROPERTY_CERTIFICATE_FROM_MINISTRY_OF_SOCIAL_PRECAUTION = "certificateFromMinistryOfSocialPrecaution";
  private Boolean certificateFromMinistryOfSocialPrecaution;

  public static final String JSON_PROPERTY_COPY_PENSION_RECEIPT = "copyPensionReceipt";
  private Boolean copyPensionReceipt;

  public static final String JSON_PROPERTY_FORM_C1_ANNEXE_REGIS = "formC1AnnexeRegis";
  private Boolean formC1AnnexeRegis;

  public static final String JSON_PROPERTY_COPY_RESIDENCE_PERMIT_OR_PERMIT_WORK_MARKET = "copyResidencePermitOrPermitWorkMarket";
  private Boolean copyResidencePermitOrPermitWorkMarket;

  public static final String JSON_PROPERTY_OTHER_DECLARATION_EXPLANATION = "otherDeclarationExplanation";
  private String otherDeclarationExplanation;

  public static final String JSON_PROPERTY_DECLARATION_OF_HONOR = "declarationOfHonor";
  private Boolean declarationOfHonor;

  public static final String JSON_PROPERTY_READ_INFORMATION_FORM = "readInformationForm";
  private Boolean readInformationForm;

  public static final String JSON_PROPERTY_COMMUNICATION_OF_MODIFICATION_TO_MY_PAYMENT_OFFICE = "communicationOfModificationToMyPaymentOffice";
  private Boolean communicationOfModificationToMyPaymentOffice;

  public EC1DeclarationAndSignature() {
  }

  public EC1DeclarationAndSignature certificateFromMinistryOfSocialPrecaution(Boolean certificateFromMinistryOfSocialPrecaution) {
    
    this.certificateFromMinistryOfSocialPrecaution = certificateFromMinistryOfSocialPrecaution;
    return this;
  }

  /**
   * Get certificateFromMinistryOfSocialPrecaution
   * @return certificateFromMinistryOfSocialPrecaution
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CERTIFICATE_FROM_MINISTRY_OF_SOCIAL_PRECAUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCertificateFromMinistryOfSocialPrecaution() {
    return certificateFromMinistryOfSocialPrecaution;
  }


  @JsonProperty(JSON_PROPERTY_CERTIFICATE_FROM_MINISTRY_OF_SOCIAL_PRECAUTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCertificateFromMinistryOfSocialPrecaution(Boolean certificateFromMinistryOfSocialPrecaution) {
    this.certificateFromMinistryOfSocialPrecaution = certificateFromMinistryOfSocialPrecaution;
  }

  public EC1DeclarationAndSignature copyPensionReceipt(Boolean copyPensionReceipt) {
    
    this.copyPensionReceipt = copyPensionReceipt;
    return this;
  }

  /**
   * Get copyPensionReceipt
   * @return copyPensionReceipt
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COPY_PENSION_RECEIPT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCopyPensionReceipt() {
    return copyPensionReceipt;
  }


  @JsonProperty(JSON_PROPERTY_COPY_PENSION_RECEIPT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCopyPensionReceipt(Boolean copyPensionReceipt) {
    this.copyPensionReceipt = copyPensionReceipt;
  }

  public EC1DeclarationAndSignature formC1AnnexeRegis(Boolean formC1AnnexeRegis) {
    
    this.formC1AnnexeRegis = formC1AnnexeRegis;
    return this;
  }

  /**
   * Get formC1AnnexeRegis
   * @return formC1AnnexeRegis
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FORM_C1_ANNEXE_REGIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFormC1AnnexeRegis() {
    return formC1AnnexeRegis;
  }


  @JsonProperty(JSON_PROPERTY_FORM_C1_ANNEXE_REGIS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFormC1AnnexeRegis(Boolean formC1AnnexeRegis) {
    this.formC1AnnexeRegis = formC1AnnexeRegis;
  }

  public EC1DeclarationAndSignature copyResidencePermitOrPermitWorkMarket(Boolean copyResidencePermitOrPermitWorkMarket) {
    
    this.copyResidencePermitOrPermitWorkMarket = copyResidencePermitOrPermitWorkMarket;
    return this;
  }

  /**
   * Get copyResidencePermitOrPermitWorkMarket
   * @return copyResidencePermitOrPermitWorkMarket
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COPY_RESIDENCE_PERMIT_OR_PERMIT_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCopyResidencePermitOrPermitWorkMarket() {
    return copyResidencePermitOrPermitWorkMarket;
  }


  @JsonProperty(JSON_PROPERTY_COPY_RESIDENCE_PERMIT_OR_PERMIT_WORK_MARKET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCopyResidencePermitOrPermitWorkMarket(Boolean copyResidencePermitOrPermitWorkMarket) {
    this.copyResidencePermitOrPermitWorkMarket = copyResidencePermitOrPermitWorkMarket;
  }

  public EC1DeclarationAndSignature otherDeclarationExplanation(String otherDeclarationExplanation) {
    
    this.otherDeclarationExplanation = otherDeclarationExplanation;
    return this;
  }

  /**
   * Get otherDeclarationExplanation
   * @return otherDeclarationExplanation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER_DECLARATION_EXPLANATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOtherDeclarationExplanation() {
    return otherDeclarationExplanation;
  }


  @JsonProperty(JSON_PROPERTY_OTHER_DECLARATION_EXPLANATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOtherDeclarationExplanation(String otherDeclarationExplanation) {
    this.otherDeclarationExplanation = otherDeclarationExplanation;
  }

  public EC1DeclarationAndSignature declarationOfHonor(Boolean declarationOfHonor) {
    
    this.declarationOfHonor = declarationOfHonor;
    return this;
  }

  /**
   * Get declarationOfHonor
   * @return declarationOfHonor
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECLARATION_OF_HONOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDeclarationOfHonor() {
    return declarationOfHonor;
  }


  @JsonProperty(JSON_PROPERTY_DECLARATION_OF_HONOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeclarationOfHonor(Boolean declarationOfHonor) {
    this.declarationOfHonor = declarationOfHonor;
  }

  public EC1DeclarationAndSignature readInformationForm(Boolean readInformationForm) {
    
    this.readInformationForm = readInformationForm;
    return this;
  }

  /**
   * Get readInformationForm
   * @return readInformationForm
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_READ_INFORMATION_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReadInformationForm() {
    return readInformationForm;
  }


  @JsonProperty(JSON_PROPERTY_READ_INFORMATION_FORM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReadInformationForm(Boolean readInformationForm) {
    this.readInformationForm = readInformationForm;
  }

  public EC1DeclarationAndSignature communicationOfModificationToMyPaymentOffice(Boolean communicationOfModificationToMyPaymentOffice) {
    
    this.communicationOfModificationToMyPaymentOffice = communicationOfModificationToMyPaymentOffice;
    return this;
  }

  /**
   * Get communicationOfModificationToMyPaymentOffice
   * @return communicationOfModificationToMyPaymentOffice
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMUNICATION_OF_MODIFICATION_TO_MY_PAYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getCommunicationOfModificationToMyPaymentOffice() {
    return communicationOfModificationToMyPaymentOffice;
  }


  @JsonProperty(JSON_PROPERTY_COMMUNICATION_OF_MODIFICATION_TO_MY_PAYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommunicationOfModificationToMyPaymentOffice(Boolean communicationOfModificationToMyPaymentOffice) {
    this.communicationOfModificationToMyPaymentOffice = communicationOfModificationToMyPaymentOffice;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1DeclarationAndSignature ec1DeclarationAndSignature = (EC1DeclarationAndSignature) o;
    return Objects.equals(this.certificateFromMinistryOfSocialPrecaution, ec1DeclarationAndSignature.certificateFromMinistryOfSocialPrecaution) &&
        Objects.equals(this.copyPensionReceipt, ec1DeclarationAndSignature.copyPensionReceipt) &&
        Objects.equals(this.formC1AnnexeRegis, ec1DeclarationAndSignature.formC1AnnexeRegis) &&
        Objects.equals(this.copyResidencePermitOrPermitWorkMarket, ec1DeclarationAndSignature.copyResidencePermitOrPermitWorkMarket) &&
        Objects.equals(this.otherDeclarationExplanation, ec1DeclarationAndSignature.otherDeclarationExplanation) &&
        Objects.equals(this.declarationOfHonor, ec1DeclarationAndSignature.declarationOfHonor) &&
        Objects.equals(this.readInformationForm, ec1DeclarationAndSignature.readInformationForm) &&
        Objects.equals(this.communicationOfModificationToMyPaymentOffice, ec1DeclarationAndSignature.communicationOfModificationToMyPaymentOffice);
  }

  @Override
  public int hashCode() {
    return Objects.hash(certificateFromMinistryOfSocialPrecaution, copyPensionReceipt, formC1AnnexeRegis, copyResidencePermitOrPermitWorkMarket, otherDeclarationExplanation, declarationOfHonor, readInformationForm, communicationOfModificationToMyPaymentOffice);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1DeclarationAndSignature {\n");
    sb.append("    certificateFromMinistryOfSocialPrecaution: ").append(toIndentedString(certificateFromMinistryOfSocialPrecaution)).append("\n");
    sb.append("    copyPensionReceipt: ").append(toIndentedString(copyPensionReceipt)).append("\n");
    sb.append("    formC1AnnexeRegis: ").append(toIndentedString(formC1AnnexeRegis)).append("\n");
    sb.append("    copyResidencePermitOrPermitWorkMarket: ").append(toIndentedString(copyResidencePermitOrPermitWorkMarket)).append("\n");
    sb.append("    otherDeclarationExplanation: ").append(toIndentedString(otherDeclarationExplanation)).append("\n");
    sb.append("    declarationOfHonor: ").append(toIndentedString(declarationOfHonor)).append("\n");
    sb.append("    readInformationForm: ").append(toIndentedString(readInformationForm)).append("\n");
    sb.append("    communicationOfModificationToMyPaymentOffice: ").append(toIndentedString(communicationOfModificationToMyPaymentOffice)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

