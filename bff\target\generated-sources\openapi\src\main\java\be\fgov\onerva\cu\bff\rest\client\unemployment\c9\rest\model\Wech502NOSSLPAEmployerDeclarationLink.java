/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.HandledNOSSLPAEmployerDclLinkType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech502NaturalPerson;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502NOSSLPAEmployerDeclarationLink
 */
@JsonPropertyOrder({
  Wech502NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR,
  Wech502NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_COMPANY_I_D,
  Wech502NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK,
  Wech502NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_NATURAL_PERSON
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502NOSSLPAEmployerDeclarationLink {
  public static final String JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR = "nosslpaRegistrationNbr";
  private Integer nosslpaRegistrationNbr;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK = "handledNOSSLPAEmployerDclLink";
  private HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink;

  public static final String JSON_PROPERTY_NATURAL_PERSON = "naturalPerson";
  private Wech502NaturalPerson naturalPerson;

  public Wech502NOSSLPAEmployerDeclarationLink() {
  }

  public Wech502NOSSLPAEmployerDeclarationLink nosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
    return this;
  }

  /**
   * Get nosslpaRegistrationNbr
   * @return nosslpaRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNosslpaRegistrationNbr() {
    return nosslpaRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
  }

  public Wech502NOSSLPAEmployerDeclarationLink companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public Wech502NOSSLPAEmployerDeclarationLink handledNOSSLPAEmployerDclLink(HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink) {
    
    this.handledNOSSLPAEmployerDclLink = handledNOSSLPAEmployerDclLink;
    return this;
  }

  /**
   * Get handledNOSSLPAEmployerDclLink
   * @return handledNOSSLPAEmployerDclLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledNOSSLPAEmployerDclLinkType getHandledNOSSLPAEmployerDclLink() {
    return handledNOSSLPAEmployerDclLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledNOSSLPAEmployerDclLink(HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink) {
    this.handledNOSSLPAEmployerDclLink = handledNOSSLPAEmployerDclLink;
  }

  public Wech502NOSSLPAEmployerDeclarationLink naturalPerson(Wech502NaturalPerson naturalPerson) {
    
    this.naturalPerson = naturalPerson;
    return this;
  }

  /**
   * Get naturalPerson
   * @return naturalPerson
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502NaturalPerson getNaturalPerson() {
    return naturalPerson;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPerson(Wech502NaturalPerson naturalPerson) {
    this.naturalPerson = naturalPerson;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502NOSSLPAEmployerDeclarationLink wech502NOSSLPAEmployerDeclarationLink = (Wech502NOSSLPAEmployerDeclarationLink) o;
    return Objects.equals(this.nosslpaRegistrationNbr, wech502NOSSLPAEmployerDeclarationLink.nosslpaRegistrationNbr) &&
        Objects.equals(this.companyID, wech502NOSSLPAEmployerDeclarationLink.companyID) &&
        Objects.equals(this.handledNOSSLPAEmployerDclLink, wech502NOSSLPAEmployerDeclarationLink.handledNOSSLPAEmployerDclLink) &&
        Objects.equals(this.naturalPerson, wech502NOSSLPAEmployerDeclarationLink.naturalPerson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nosslpaRegistrationNbr, companyID, handledNOSSLPAEmployerDclLink, naturalPerson);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502NOSSLPAEmployerDeclarationLink {\n");
    sb.append("    nosslpaRegistrationNbr: ").append(toIndentedString(nosslpaRegistrationNbr)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    handledNOSSLPAEmployerDclLink: ").append(toIndentedString(handledNOSSLPAEmployerDclLink)).append("\n");
    sb.append("    naturalPerson: ").append(toIndentedString(naturalPerson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

