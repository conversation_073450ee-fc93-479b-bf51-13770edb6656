/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyQualityDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyTypeDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ThirdPartyDTO
 */
@JsonPropertyOrder({
  ThirdPartyDTO.JSON_PROPERTY_ID,
  ThirdPartyDTO.JSON_PROPERTY_TYPE,
  ThirdPartyDTO.JSON_PROPERTY_QUALITY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
@JsonIgnoreProperties(
  value = "type", // ignore manually set type, it will be automatically generated by Jackson during serialization
  allowSetters = true // allows the type to be set during deserialization
)
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type", visible = true)
@JsonSubTypes({
  @JsonSubTypes.Type(value = CitizenDTO.class, name = "citizen"),
  @JsonSubTypes.Type(value = CompanyDTO.class, name = "company"),
  @JsonSubTypes.Type(value = UnknownDTO.class, name = "unknown"),
})

public class ThirdPartyDTO {
  public static final String JSON_PROPERTY_ID = "id";
  protected String id;

  public static final String JSON_PROPERTY_TYPE = "type";
  protected ThirdPartyTypeDTO type;

  public static final String JSON_PROPERTY_QUALITY = "quality";
  protected ThirdPartyQualityDTO quality;

  public ThirdPartyDTO() {
  }

  public ThirdPartyDTO id(String id) {
    
    this.id = id;
    return this;
  }

  /**
   * The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen
   * @return id
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(String id) {
    this.id = id;
  }

  public ThirdPartyDTO type(ThirdPartyTypeDTO type) {
    
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ThirdPartyTypeDTO getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setType(ThirdPartyTypeDTO type) {
    this.type = type;
  }

  public ThirdPartyDTO quality(ThirdPartyQualityDTO quality) {
    
    this.quality = quality;
    return this;
  }

  /**
   * Get quality
   * @return quality
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_QUALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ThirdPartyQualityDTO getQuality() {
    return quality;
  }


  @JsonProperty(JSON_PROPERTY_QUALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setQuality(ThirdPartyQualityDTO quality) {
    this.quality = quality;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ThirdPartyDTO thirdParty = (ThirdPartyDTO) o;
    return Objects.equals(this.id, thirdParty.id) &&
        Objects.equals(this.type, thirdParty.type) &&
        Objects.equals(this.quality, thirdParty.quality);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, type, quality);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ThirdPartyDTO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    quality: ").append(toIndentedString(quality)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

