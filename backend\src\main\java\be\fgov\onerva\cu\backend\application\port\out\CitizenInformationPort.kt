package be.fgov.onerva.cu.backend.application.port.out

import java.util.*
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation

/**
 * Port interface for managing employee information persistence operations.
 *
 * This interface defines the contract for loading and persisting employee information
 * as part of the hexagonal architecture's port pattern. It serves as an abstraction layer
 * between the domain logic and the persistence infrastructure.
 *
 * @see CitizenInformation Domain model containing employee personal data
 */
interface CitizenInformationPort {
    /**
     * Retrieves employee information associated with a specific request.
     *
     * @param requestId The unique identifier of the request to fetch employee information for
     * @return The [CitizenInformation] if found, null otherwise
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     */
    fun getCitizenInformation(requestId: UUID): CitizenInformation?

    /**
     * Persists or updates employee information for a specific request.
     *
     * If employee information already exists for the given request ID, it will be updated.
     * Otherwise, new employee information will be created.
     *
     * @param requestId The unique identifier of the request to associate the employee information with
     * @param citizenInformation The employee information to be persisted
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     */
    fun persistCitizenInformation(requestId: UUID, citizenInformation: CitizenInformation)

    fun getLatestRevision(requestId: UUID): Int

    fun getCitizenInformationForRevision(requestId: UUID, revision: Int): CitizenInformation?
    
    fun getEntityId(requestId: UUID): UUID

    fun patchCurrentDataWithRevision(requestId: UUID, revision: Int)
}