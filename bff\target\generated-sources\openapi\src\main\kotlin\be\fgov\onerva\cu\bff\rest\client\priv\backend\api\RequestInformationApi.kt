/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.RequestInformationResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UpdateRequestInformationRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.WaveTaskResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class RequestInformationApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun assignTaskToUser(requestId: java.util.UUID): Unit {
        assignTaskToUserWithHttpInfo(requestId = requestId)
    }

    @Throws(RestClientResponseException::class)
    fun assignTaskToUserWithHttpInfo(requestId: java.util.UUID): ResponseEntity<Unit> {
        val localVariableConfig = assignTaskToUserRequestConfig(requestId = requestId)
        return request<Unit, Unit>(
            localVariableConfig
        )
    }

    fun assignTaskToUserRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/assign-user",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun closeRequestTask(requestId: java.util.UUID, taskCode: kotlin.String): WaveTaskResponse {
        val result = closeRequestTaskWithHttpInfo(requestId = requestId, taskCode = taskCode)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun closeRequestTaskWithHttpInfo(requestId: java.util.UUID, taskCode: kotlin.String): ResponseEntity<WaveTaskResponse> {
        val localVariableConfig = closeRequestTaskRequestConfig(requestId = requestId, taskCode = taskCode)
        return request<Unit, WaveTaskResponse>(
            localVariableConfig
        )
    }

    fun closeRequestTaskRequestConfig(requestId: java.util.UUID, taskCode: kotlin.String) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
            "taskCode" to taskCode,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/{taskCode}/close",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getRequestBasicInfo(requestId: java.util.UUID): RequestBasicInfoResponse {
        val result = getRequestBasicInfoWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getRequestBasicInfoWithHttpInfo(requestId: java.util.UUID): ResponseEntity<RequestBasicInfoResponse> {
        val localVariableConfig = getRequestBasicInfoRequestConfig(requestId = requestId)
        return request<Unit, RequestBasicInfoResponse>(
            localVariableConfig
        )
    }

    fun getRequestBasicInfoRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getRequestInformation(requestId: java.util.UUID): RequestInformationResponse {
        val result = getRequestInformationWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getRequestInformationWithHttpInfo(requestId: java.util.UUID): ResponseEntity<RequestInformationResponse> {
        val localVariableConfig = getRequestInformationRequestConfig(requestId = requestId)
        return request<Unit, RequestInformationResponse>(
            localVariableConfig
        )
    }

    fun getRequestInformationRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/request-information",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun reopenTask(taskCode: kotlin.String, requestId: java.util.UUID): Unit {
        reopenTaskWithHttpInfo(taskCode = taskCode, requestId = requestId)
    }

    @Throws(RestClientResponseException::class)
    fun reopenTaskWithHttpInfo(taskCode: kotlin.String, requestId: java.util.UUID): ResponseEntity<Unit> {
        val localVariableConfig = reopenTaskRequestConfig(taskCode = taskCode, requestId = requestId)
        return request<Unit, Unit>(
            localVariableConfig
        )
    }

    fun reopenTaskRequestConfig(taskCode: kotlin.String, requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        
        val params = mutableMapOf<String, Any>(
            "taskCode" to taskCode,
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/{taskCode}/reopen",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateRequestInformation(requestId: java.util.UUID, updateRequestInformationRequest: UpdateRequestInformationRequest): Unit {
        updateRequestInformationWithHttpInfo(requestId = requestId, updateRequestInformationRequest = updateRequestInformationRequest)
    }

    @Throws(RestClientResponseException::class)
    fun updateRequestInformationWithHttpInfo(requestId: java.util.UUID, updateRequestInformationRequest: UpdateRequestInformationRequest): ResponseEntity<Unit> {
        val localVariableConfig = updateRequestInformationRequestConfig(requestId = requestId, updateRequestInformationRequest = updateRequestInformationRequest)
        return request<UpdateRequestInformationRequest, Unit>(
            localVariableConfig
        )
    }

    fun updateRequestInformationRequestConfig(requestId: java.util.UUID, updateRequestInformationRequest: UpdateRequestInformationRequest) : RequestConfig<UpdateRequestInformationRequest> {
        val localVariableBody = updateRequestInformationRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/request-information",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
