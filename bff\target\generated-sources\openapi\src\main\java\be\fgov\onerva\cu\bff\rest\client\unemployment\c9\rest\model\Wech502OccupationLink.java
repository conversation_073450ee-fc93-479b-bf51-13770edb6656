/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.HandledOccupationLinkType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.OccupationFeaturesType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech502AnnualDclTempUnemployment;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502OccupationLink
 */
@JsonPropertyOrder({
  Wech502OccupationLink.JSON_PROPERTY_OCCUPATION_STARTING_DATE,
  Wech502OccupationLink.JSON_PROPERTY_OCCUPATION_ENDING_DATE,
  Wech502OccupationLink.JSON_PROPERTY_JOINT_COMMISSION_NBR,
  Wech502OccupationLink.JSON_PROPERTY_WORKING_DAYS_SYSTEM,
  Wech502OccupationLink.JSON_PROPERTY_MEAN_WORKING_HOURS,
  Wech502OccupationLink.JSON_PROPERTY_REF_MEAN_WORKING_HOURS,
  Wech502OccupationLink.JSON_PROPERTY_WORKER_STATUS,
  Wech502OccupationLink.JSON_PROPERTY_RETIRED,
  Wech502OccupationLink.JSON_PROPERTY_APPRENTICESHIP,
  Wech502OccupationLink.JSON_PROPERTY_CONTRACT_TYPE,
  Wech502OccupationLink.JSON_PROPERTY_REMUN_METHOD,
  Wech502OccupationLink.JSON_PROPERTY_HANDLED_OCCUPATION_LINK,
  Wech502OccupationLink.JSON_PROPERTY_OCCUPATION_FEATURES,
  Wech502OccupationLink.JSON_PROPERTY_ANNUAL_DCL_TEMP_UNEMPLOYMENT
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502OccupationLink {
  public static final String JSON_PROPERTY_OCCUPATION_STARTING_DATE = "occupationStartingDate";
  private LocalDate occupationStartingDate;

  public static final String JSON_PROPERTY_OCCUPATION_ENDING_DATE = "occupationEndingDate";
  private LocalDate occupationEndingDate;

  public static final String JSON_PROPERTY_JOINT_COMMISSION_NBR = "jointCommissionNbr";
  private String jointCommissionNbr;

  public static final String JSON_PROPERTY_WORKING_DAYS_SYSTEM = "workingDaysSystem";
  private String workingDaysSystem;

  public static final String JSON_PROPERTY_MEAN_WORKING_HOURS = "meanWorkingHours";
  private String meanWorkingHours;

  public static final String JSON_PROPERTY_REF_MEAN_WORKING_HOURS = "refMeanWorkingHours";
  private String refMeanWorkingHours;

  public static final String JSON_PROPERTY_WORKER_STATUS = "workerStatus";
  private String workerStatus;

  public static final String JSON_PROPERTY_RETIRED = "retired";
  private String retired;

  public static final String JSON_PROPERTY_APPRENTICESHIP = "apprenticeship";
  private String apprenticeship;

  public static final String JSON_PROPERTY_CONTRACT_TYPE = "contractType";
  private String contractType;

  public static final String JSON_PROPERTY_REMUN_METHOD = "remunMethod";
  private String remunMethod;

  public static final String JSON_PROPERTY_HANDLED_OCCUPATION_LINK = "handledOccupationLink";
  private HandledOccupationLinkType handledOccupationLink;

  public static final String JSON_PROPERTY_OCCUPATION_FEATURES = "occupationFeatures";
  private OccupationFeaturesType occupationFeatures;

  public static final String JSON_PROPERTY_ANNUAL_DCL_TEMP_UNEMPLOYMENT = "annualDclTempUnemployment";
  private Wech502AnnualDclTempUnemployment annualDclTempUnemployment;

  public Wech502OccupationLink() {
  }

  public Wech502OccupationLink occupationStartingDate(LocalDate occupationStartingDate) {
    
    this.occupationStartingDate = occupationStartingDate;
    return this;
  }

  /**
   * Get occupationStartingDate
   * @return occupationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getOccupationStartingDate() {
    return occupationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationStartingDate(LocalDate occupationStartingDate) {
    this.occupationStartingDate = occupationStartingDate;
  }

  public Wech502OccupationLink occupationEndingDate(LocalDate occupationEndingDate) {
    
    this.occupationEndingDate = occupationEndingDate;
    return this;
  }

  /**
   * Get occupationEndingDate
   * @return occupationEndingDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getOccupationEndingDate() {
    return occupationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationEndingDate(LocalDate occupationEndingDate) {
    this.occupationEndingDate = occupationEndingDate;
  }

  public Wech502OccupationLink jointCommissionNbr(String jointCommissionNbr) {
    
    this.jointCommissionNbr = jointCommissionNbr;
    return this;
  }

  /**
   * Get jointCommissionNbr
   * @return jointCommissionNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getJointCommissionNbr() {
    return jointCommissionNbr;
  }


  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setJointCommissionNbr(String jointCommissionNbr) {
    this.jointCommissionNbr = jointCommissionNbr;
  }

  public Wech502OccupationLink workingDaysSystem(String workingDaysSystem) {
    
    this.workingDaysSystem = workingDaysSystem;
    return this;
  }

  /**
   * Get workingDaysSystem
   * @return workingDaysSystem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkingDaysSystem() {
    return workingDaysSystem;
  }


  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkingDaysSystem(String workingDaysSystem) {
    this.workingDaysSystem = workingDaysSystem;
  }

  public Wech502OccupationLink meanWorkingHours(String meanWorkingHours) {
    
    this.meanWorkingHours = meanWorkingHours;
    return this;
  }

  /**
   * Get meanWorkingHours
   * @return meanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMeanWorkingHours() {
    return meanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMeanWorkingHours(String meanWorkingHours) {
    this.meanWorkingHours = meanWorkingHours;
  }

  public Wech502OccupationLink refMeanWorkingHours(String refMeanWorkingHours) {
    
    this.refMeanWorkingHours = refMeanWorkingHours;
    return this;
  }

  /**
   * Get refMeanWorkingHours
   * @return refMeanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getRefMeanWorkingHours() {
    return refMeanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefMeanWorkingHours(String refMeanWorkingHours) {
    this.refMeanWorkingHours = refMeanWorkingHours;
  }

  public Wech502OccupationLink workerStatus(String workerStatus) {
    
    this.workerStatus = workerStatus;
    return this;
  }

  /**
   * Get workerStatus
   * @return workerStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStatus() {
    return workerStatus;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStatus(String workerStatus) {
    this.workerStatus = workerStatus;
  }

  public Wech502OccupationLink retired(String retired) {
    
    this.retired = retired;
    return this;
  }

  /**
   * Get retired
   * @return retired
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRetired() {
    return retired;
  }


  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRetired(String retired) {
    this.retired = retired;
  }

  public Wech502OccupationLink apprenticeship(String apprenticeship) {
    
    this.apprenticeship = apprenticeship;
    return this;
  }

  /**
   * Get apprenticeship
   * @return apprenticeship
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApprenticeship() {
    return apprenticeship;
  }


  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApprenticeship(String apprenticeship) {
    this.apprenticeship = apprenticeship;
  }

  public Wech502OccupationLink contractType(String contractType) {
    
    this.contractType = contractType;
    return this;
  }

  /**
   * Get contractType
   * @return contractType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContractType() {
    return contractType;
  }


  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContractType(String contractType) {
    this.contractType = contractType;
  }

  public Wech502OccupationLink remunMethod(String remunMethod) {
    
    this.remunMethod = remunMethod;
    return this;
  }

  /**
   * Get remunMethod
   * @return remunMethod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemunMethod() {
    return remunMethod;
  }


  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemunMethod(String remunMethod) {
    this.remunMethod = remunMethod;
  }

  public Wech502OccupationLink handledOccupationLink(HandledOccupationLinkType handledOccupationLink) {
    
    this.handledOccupationLink = handledOccupationLink;
    return this;
  }

  /**
   * Get handledOccupationLink
   * @return handledOccupationLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledOccupationLinkType getHandledOccupationLink() {
    return handledOccupationLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledOccupationLink(HandledOccupationLinkType handledOccupationLink) {
    this.handledOccupationLink = handledOccupationLink;
  }

  public Wech502OccupationLink occupationFeatures(OccupationFeaturesType occupationFeatures) {
    
    this.occupationFeatures = occupationFeatures;
    return this;
  }

  /**
   * Get occupationFeatures
   * @return occupationFeatures
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_FEATURES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OccupationFeaturesType getOccupationFeatures() {
    return occupationFeatures;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_FEATURES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationFeatures(OccupationFeaturesType occupationFeatures) {
    this.occupationFeatures = occupationFeatures;
  }

  public Wech502OccupationLink annualDclTempUnemployment(Wech502AnnualDclTempUnemployment annualDclTempUnemployment) {
    
    this.annualDclTempUnemployment = annualDclTempUnemployment;
    return this;
  }

  /**
   * Get annualDclTempUnemployment
   * @return annualDclTempUnemployment
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ANNUAL_DCL_TEMP_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502AnnualDclTempUnemployment getAnnualDclTempUnemployment() {
    return annualDclTempUnemployment;
  }


  @JsonProperty(JSON_PROPERTY_ANNUAL_DCL_TEMP_UNEMPLOYMENT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAnnualDclTempUnemployment(Wech502AnnualDclTempUnemployment annualDclTempUnemployment) {
    this.annualDclTempUnemployment = annualDclTempUnemployment;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502OccupationLink wech502OccupationLink = (Wech502OccupationLink) o;
    return Objects.equals(this.occupationStartingDate, wech502OccupationLink.occupationStartingDate) &&
        Objects.equals(this.occupationEndingDate, wech502OccupationLink.occupationEndingDate) &&
        Objects.equals(this.jointCommissionNbr, wech502OccupationLink.jointCommissionNbr) &&
        Objects.equals(this.workingDaysSystem, wech502OccupationLink.workingDaysSystem) &&
        Objects.equals(this.meanWorkingHours, wech502OccupationLink.meanWorkingHours) &&
        Objects.equals(this.refMeanWorkingHours, wech502OccupationLink.refMeanWorkingHours) &&
        Objects.equals(this.workerStatus, wech502OccupationLink.workerStatus) &&
        Objects.equals(this.retired, wech502OccupationLink.retired) &&
        Objects.equals(this.apprenticeship, wech502OccupationLink.apprenticeship) &&
        Objects.equals(this.contractType, wech502OccupationLink.contractType) &&
        Objects.equals(this.remunMethod, wech502OccupationLink.remunMethod) &&
        Objects.equals(this.handledOccupationLink, wech502OccupationLink.handledOccupationLink) &&
        Objects.equals(this.occupationFeatures, wech502OccupationLink.occupationFeatures) &&
        Objects.equals(this.annualDclTempUnemployment, wech502OccupationLink.annualDclTempUnemployment);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationStartingDate, occupationEndingDate, jointCommissionNbr, workingDaysSystem, meanWorkingHours, refMeanWorkingHours, workerStatus, retired, apprenticeship, contractType, remunMethod, handledOccupationLink, occupationFeatures, annualDclTempUnemployment);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502OccupationLink {\n");
    sb.append("    occupationStartingDate: ").append(toIndentedString(occupationStartingDate)).append("\n");
    sb.append("    occupationEndingDate: ").append(toIndentedString(occupationEndingDate)).append("\n");
    sb.append("    jointCommissionNbr: ").append(toIndentedString(jointCommissionNbr)).append("\n");
    sb.append("    workingDaysSystem: ").append(toIndentedString(workingDaysSystem)).append("\n");
    sb.append("    meanWorkingHours: ").append(toIndentedString(meanWorkingHours)).append("\n");
    sb.append("    refMeanWorkingHours: ").append(toIndentedString(refMeanWorkingHours)).append("\n");
    sb.append("    workerStatus: ").append(toIndentedString(workerStatus)).append("\n");
    sb.append("    retired: ").append(toIndentedString(retired)).append("\n");
    sb.append("    apprenticeship: ").append(toIndentedString(apprenticeship)).append("\n");
    sb.append("    contractType: ").append(toIndentedString(contractType)).append("\n");
    sb.append("    remunMethod: ").append(toIndentedString(remunMethod)).append("\n");
    sb.append("    handledOccupationLink: ").append(toIndentedString(handledOccupationLink)).append("\n");
    sb.append("    occupationFeatures: ").append(toIndentedString(occupationFeatures)).append("\n");
    sb.append("    annualDclTempUnemployment: ").append(toIndentedString(annualDclTempUnemployment)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

