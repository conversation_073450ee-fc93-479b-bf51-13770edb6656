package be.fgov.onerva.cu.bff.adapter.out

import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.api.parallel.Execution
import org.junit.jupiter.api.parallel.ExecutionMode
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpServerErrorException
import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.C9Api
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.C9
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
@Execution(ExecutionMode.SAME_THREAD)
class C9ServiceTest {

    @MockK
    lateinit var c9Api: C9Api

    @InjectMockKs
    lateinit var c9Service: C9Service

    @Test
    fun `getOPKey should return OPKEY when C9 API returns valid data`() {
        // Given
        val c9Id = "12345"
        val expectedOpKey = "OP123"
        val c9Response = C9().apply { opKey = expectedOpKey }

        every { c9Api.getC9s(12345) } returns c9Response

        // When
        val result = c9Service.getOPKey(c9Id)

        // Then
        assertThat(result).isEqualTo(expectedOpKey)
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getOPKey should throw C9NotFoundException when OPKEY is null`() {
        // Given
        val c9Id = "12345"
        val c9Response = C9()
        c9Response.unemploymentOffice = null

        every { c9Api.getC9s(12345) } returns c9Response

        // When/Then
        val exception = assertThrows<C9NotFoundException> {
            c9Service.getUnemploymentOffice(c9Id)
        }

        assertThat(exception.message).isEqualTo("Unexpected error retrieving unemployment office: Unemployment office not found for request ID: 12345")
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getOPKey should throw C9NotFoundException when client error occurs`() {
        // Given
        val c9Id = "12345"
        val clientException = io.mockk.mockk<HttpClientErrorException>()

        every { clientException.message } returns "Not Found"
        every { c9Api.getC9s(12345) } throws clientException

        // When/Then
        try {
            c9Service.getOPKey(c9Id)
        } catch (e: C9NotFoundException) {
            assertThat(e).isInstanceOf(C9NotFoundException::class.java)
            assertThat(e.message).isEqualTo("Failed to retrieve OPKEY: Not Found")
        }

        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getOPKey should throw C9NotFoundException when server error occurs`() {
        // Given
        val c9Id = "12345"
        val serverException = io.mockk.mockk<HttpServerErrorException>()

        every { serverException.message } returns "Internal Server Error"
        every { c9Api.getC9s(12345) } throws serverException

        // When/Then
        try {
            c9Service.getOPKey(c9Id)
        } catch (e: C9NotFoundException) {
            assertThat(e).isInstanceOf(C9NotFoundException::class.java)
            assertThat(e.message).isEqualTo("C9 service unavailable when retrieving OPKEY: Internal Server Error")
        }

        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getUnemploymentOffice should return office when C9 API returns valid data`() {
        // Given
        val requestId = "12345"
        val expectedOffice = "123" // Changed from "Office123" to avoid NumberFormatException
        val c9Response = C9().apply { unemploymentOffice = expectedOffice.toInt() }

        every { c9Api.getC9s(12345) } returns c9Response

        // When
        val result = c9Service.getUnemploymentOffice(requestId)

        // Then
        assertThat(result).isEqualTo(expectedOffice)
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getUnemploymentOffice should throw C9NotFoundException when office is null`() {
        // Given
        val requestId = "12345"
        val c9Response = C9().apply { unemploymentOffice = null }

        every { c9Api.getC9s(12345) } returns c9Response

        // When/Then
        try {
            c9Service.getUnemploymentOffice(requestId)
        } catch (e: C9NotFoundException) {
            assertThat(e).isInstanceOf(C9NotFoundException::class.java)
            assertThat(e.message).isEqualTo("Unexpected error retrieving unemployment office: Unemployment office not found for request ID: 12345")
        }

        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getUnemploymentOffice should throw C9NotFoundException when client error occurs`() {
        // Given
        val requestId = "12345"
        val clientException = io.mockk.mockk<HttpClientErrorException>()

        every { clientException.message } returns "Not Found"
        every { c9Api.getC9s(12345) } throws clientException

        // When/Then
        try {
            c9Service.getUnemploymentOffice(requestId)
        } catch (e: C9NotFoundException) {
            assertThat(e).isInstanceOf(C9NotFoundException::class.java)
            assertThat(e.message).isEqualTo("Failed to retrieve unemployment office: Not Found")
        }

        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @ParameterizedTest
    @MethodSource("exceptionScenarios")
    fun `service methods should handle different exception types`(
        methodToTest: String,
        exceptionToThrow: Exception,
        expectedErrorMessage: String
    ) {
        // Given
        val id = "12345"

        every { c9Api.getC9s(12345) } throws exceptionToThrow

        // When/Then
        try {
            when (methodToTest) {
                "getOPKey" -> c9Service.getOPKey(id)
                "getUnemploymentOffice" -> c9Service.getUnemploymentOffice(id)
                else -> throw IllegalArgumentException("Unknown method: $methodToTest")
            }
        } catch (e: C9NotFoundException) {
            assertThat(e).isInstanceOf(C9NotFoundException::class.java)
            assertThat(e.message).isEqualTo(expectedErrorMessage)
        }

        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getC9Details should return both values when C9 API returns valid data`() {
        // Given
        val c9Id = "12345"
        val expectedOffice = "7" // Will be padded to "007"
        val expectedOpKey = "OP123"
        val c9Response = C9().apply {
            unemploymentOffice = expectedOffice.toInt()
            opKey = expectedOpKey
        }

        every { c9Api.getC9s(12345) } returns c9Response

        // When
        val result = c9Service.getC9Details(c9Id)

        // Then
        assertThat(result.unemploymentOfficeCode).isEqualTo("007") // Padded
        assertThat(result.opKey).isEqualTo(expectedOpKey)
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getC9Details should throw C9NotFoundException when unemployment office is null`() {
        // Given
        val c9Id = "12345"
        val c9Response = C9().apply {
            unemploymentOffice = null
            opKey = "OP123"
        }

        every { c9Api.getC9s(12345) } returns c9Response

        // When/Then
        val exception = assertThrows<C9NotFoundException> {
            c9Service.getC9Details(c9Id)
        }

        assertThat(exception.message).isEqualTo("Unemployment office code not found for C9 ID: 12345")
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getC9Details should throw C9NotFoundException when opKey is null`() {
        // Given
        val c9Id = "12345"
        val c9Response = C9().apply {
            unemploymentOffice = 123
            opKey = null
        }

        every { c9Api.getC9s(12345) } returns c9Response

        // When/Then
        val exception = assertThrows<C9NotFoundException> {
            c9Service.getC9Details(c9Id)
        }

        assertThat(exception.message).isEqualTo("OP key not found for C9 ID: 12345")
        verify(exactly = 1) { c9Api.getC9s(12345) }
    }

    @Test
    fun `getC9Details should handle padding correctly for different office codes`() {
        // Given
        val testCases = listOf(
            "1" to "001",
            "12" to "012",
            "123" to "123",
            "1234" to "1234"
        )

        testCases.forEach { (input, expected) ->
            val c9Id = "12345"
            val c9Response = C9().apply {
                unemploymentOffice = input.toInt()
                opKey = "OP123"
            }

            every { c9Api.getC9s(12345) } returns c9Response

            // When
            val result = c9Service.getC9Details(c9Id)

            // Then
            assertThat(result.unemploymentOfficeCode).isEqualTo(expected)
        }
    }

    companion object {
        @JvmStatic
        fun exceptionScenarios(): Stream<Arguments> {
            val genericException = Exception("Generic error")

            return Stream.of(
                Arguments.of(
                    "getOPKey",
                    genericException,
                    "Unexpected error retrieving OPKEY: Generic error"
                ),
                Arguments.of(
                    "getUnemploymentOffice",
                    genericException,
                    "Unexpected error retrieving unemployment office: Generic error"
                )
            )
        }
    }
}