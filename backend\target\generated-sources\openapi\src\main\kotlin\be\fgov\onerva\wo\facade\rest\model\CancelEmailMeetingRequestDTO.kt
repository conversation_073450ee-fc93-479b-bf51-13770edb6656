/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param recipients emails you want to send the meeting to
 * @param title Meeting title. Will update old value
 * @param startDate Start date (and optionally time) of the meeting. Must be a LocalDateTime format
 * @param endDate End date (and optionally time) of the meeting. Must be a LocalDateTime format
 * @param content Content of the meeting. Can be used to explain the cancellation of the meeting
 * @param recurring if set to true, will cancel the meetings. If the startDate day and the End date Day are the same, it will only cancel that day. Otherwise, cancel all meeting recurrence
 */


data class CancelEmailMeetingRequestDTO (

    /* emails you want to send the meeting to */
    @get:JsonProperty("recipients")
    val recipients: kotlin.collections.List<kotlin.String>? = null,

    /* Meeting title. Will update old value */
    @get:JsonProperty("title")
    val title: kotlin.String? = null,

    /* Start date (and optionally time) of the meeting. Must be a LocalDateTime format */
    @get:JsonProperty("startDate")
    val startDate: java.time.OffsetDateTime? = null,

    /* End date (and optionally time) of the meeting. Must be a LocalDateTime format */
    @get:JsonProperty("endDate")
    val endDate: java.time.OffsetDateTime? = null,

    /* Content of the meeting. Can be used to explain the cancellation of the meeting */
    @get:JsonProperty("content")
    val content: kotlin.String? = null,

    /* if set to true, will cancel the meetings. If the startDate day and the End date Day are the same, it will only cancel that day. Otherwise, cancel all meeting recurrence */
    @get:JsonProperty("recurring")
    val recurring: kotlin.Boolean? = false

) {


}

