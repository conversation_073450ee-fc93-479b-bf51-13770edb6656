/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * OnlineDeclaredInterruptionInfoType
 */
@JsonPropertyOrder({
  OnlineDeclaredInterruptionInfoType.JSON_PROPERTY_DAYS_NBR
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class OnlineDeclaredInterruptionInfoType {
  public static final String JSON_PROPERTY_DAYS_NBR = "daysNbr";
  private Integer daysNbr;

  public OnlineDeclaredInterruptionInfoType() {
  }

  public OnlineDeclaredInterruptionInfoType daysNbr(Integer daysNbr) {
    
    this.daysNbr = daysNbr;
    return this;
  }

  /**
   * Get daysNbr
   * @return daysNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DAYS_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getDaysNbr() {
    return daysNbr;
  }


  @JsonProperty(JSON_PROPERTY_DAYS_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDaysNbr(Integer daysNbr) {
    this.daysNbr = daysNbr;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OnlineDeclaredInterruptionInfoType onlineDeclaredInterruptionInfoType = (OnlineDeclaredInterruptionInfoType) o;
    return Objects.equals(this.daysNbr, onlineDeclaredInterruptionInfoType.daysNbr);
  }

  @Override
  public int hashCode() {
    return Objects.hash(daysNbr);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OnlineDeclaredInterruptionInfoType {\n");
    sb.append("    daysNbr: ").append(toIndentedString(daysNbr)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

