package be.fgov.onerva.cu.backend.cucumber.steps

import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.core.authority.SimpleGrantedAuthority
import org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.jwt
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder
import be.fgov.onerva.cu.backend.cucumber.steps.context.TestContext
import be.fgov.onerva.cu.backend.security.Roles
import io.cucumber.spring.ScenarioScope

@ScenarioScope
abstract class BaseSteps {
    @Autowired
    protected lateinit var testContext: TestContext

    // Helper method for all step classes to use
    protected fun MockHttpServletRequestBuilder.withAuth(): MockHttpServletRequestBuilder {
        val username = testContext.authenticatedUsername ?: "test_user"
        val roles = testContext.authenticatedRoles.ifEmpty { arrayOf(Roles.CU_ROLE_USER) }

        return this.with(
            jwt().jwt { jwt -> jwt.claim("preferred_username", username) }
                .authorities(roles.map { SimpleGrantedAuthority(it) })
        )
    }
}