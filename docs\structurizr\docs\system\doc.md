## About

This documentation is heavily inspired by the [arc 42 template](https://arc42.org/overview).

## Introduction and goals

Describes the relevant requirements and the driving forces that software
architects and development team must consider. These include

-   underlying business goals,

-   essential features,

-   essential functional requirements,

-   quality goals for the architecture and

-   relevant stakeholders and their expectations

### Requirements

**Contents**

Short description of the functional requirements, driving forces,
extract (or abstract) of requirements. Link to (hopefully existing)
requirements documents (with version number and information where to
find it).

**Motivation**

From the point of view of the end users a system is created or modified
to improve support of a business activity and/or improve the quality.

**Form**

Short textual description, probably in tabular use-case format. If
requirements documents exist this overview should refer to these
documents.

Keep these excerpts as short as possible. Balance readability of this
document with potential redundancy w.r.t to requirements documents.

See [Introduction and Goals](https://docs.arc42.org/section-1/) in the
arc42 documentation.

### Quality Goals

**Contents**

The top three (max five) quality goals for the architecture whose
fulfillment is of highest importance to the major stakeholders. We
really mean quality goals for the architecture. Don’t confuse them with
project goals. They are not necessarily identical.

Consider this overview of potential topics:

1. **Reliability**: The system should consistently perform its intended functions without failure under normal operating conditions.
2. **Availability**: The system should be available for use whenever it is needed, with minimal downtime for maintenance or unexpected failures.
3. **Performance**: The system should respond to user interactions and process data within acceptable time limits, meeting defined performance goals.
4. **Scalability**: The system should be able to handle increased workload or user demand by efficiently allocating resources and scaling horizontally or vertically.
5. **Security**: The system should protect data, resources, and functionality from unauthorized access, ensuring confidentiality, integrity, and availability.
6. **Maintainability**: The system should be easy to maintain and modify, with clear documentation, well-structured code, and modular components.
7. **Flexibility**: The system should be adaptable to changing requirements or environments, allowing for easy configuration or customization without extensive rework.
8. **Interoperability**: The system should be able to communicate and exchange data with other systems or components seamlessly, using standard protocols and interfaces.
9. **Usability**: The system should be intuitive and easy to use, with a user-friendly interface and clear feedback for user interactions.
10. **Portability**: The system should be able to run on different hardware platforms, operating systems, or environments without significant modification.
11. **Compliance**: The system should adhere to relevant standards, regulations, and industry best practices, ensuring legal and regulatory compliance.
12. **Fault tolerance**: The system should gracefully handle errors or faults, recovering quickly and minimizing disruption to users or other systems.
13. **Testability**: The system should be designed for effective testing, with well-defined test cases, automated testing capabilities, and diagnostic tools.
14. **Reusability**: The system should facilitate reuse of components or modules across different projects or parts of the system, reducing development time and effort.
15. **Auditability**: The system should maintain comprehensive logs and records of user interactions, system events, and changes, supporting auditing and accountability.


**Motivation**

You should know the quality goals of your most important stakeholders,
since they will influence fundamental architectural decisions. Make sure
to be very concrete about these qualities, avoid buzzwords. If you as an
architect do not know how the quality of your work will be judged…

**Form**

A table with quality goals and concrete scenarios, ordered by priorities

### Stakeholders

**Contents**

Explicit overview of stakeholders of the system, i.e. all person, roles
or organizations that

-   should know the architecture

-   have to be convinced of the architecture

-   have to work with the architecture or with code

-   need the documentation of the architecture for their work

-   have to come up with decisions about the system or its development

**Motivation**

You should know all parties involved in development of the system or
affected by the system. Otherwise, you may get nasty surprises later in
the development process. These stakeholders determine the extent and the
level of detail of your work and its results.

**Form**

Table with role names, person names, and their expectations with respect
to the architecture and its documentation.

<table>
<colgroup>
<col style="width: 20%" />
<col style="width: 40%" />
<col style="width: 40%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">Role/Name</th>
<th style="text-align: left;">Contact</th>
<th style="text-align: left;">Expectations</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><p><em>&lt;Role-1&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;Contact-1&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;Expectation-1&gt;</em></p></td>
</tr>
<tr class="even">
<td style="text-align: left;"><p><em>&lt;Role-2&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;Contact-2&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;Expectation-2&gt;</em></p></td>
</tr>
</tbody>
</table>


## Architecture constraints

**Contents**

Any requirement that constraints software architects in their freedom of
design and implementation decisions or decision about the development
process. These constraints sometimes go beyond individual systems and
are valid for whole organizations and companies.

**Motivation**

Architects should know exactly where they are free in their design
decisions and where they must adhere to constraints. Constraints must
always be dealt with; they may be negotiable, though.

**Form**

Simple tables of constraints with explanations. If needed you can
subdivide them into technical constraints, organizational and political
constraints and conventions (e.g. programming or versioning guidelines,
documentation or naming conventions)

See [Architecture Constraints](https://docs.arc42.org/section-2/) in the
arc42 documentation.

## System scope and context

Here, embed a typical C4 context diagram. [Check structurizr documentation to see how to do that](https://docs.structurizr.com/ui/documentation/diagrams).

## Building blocks view

Here, embed a typical C4 container diagram. [Check structurizr documentation to see how to do that](https://docs.structurizr.com/ui/documentation/diagrams).

## Runtime view

**Contents**

The runtime view describes concrete behavior and interactions of the
system’s building blocks in form of scenarios from the following areas:

-   important use cases or features: how do building blocks execute
    them?

-   interactions at critical external interfaces: how do building blocks
    cooperate with users and neighboring systems?

-   operation and administration: launch, start-up, stop

-   error and exception scenarios

Remark: The main criterion for the choice of possible scenarios
(sequences, workflows) is their **architectural relevance**. It is
**not** important to describe a large number of scenarios. You should
rather document a representative selection.

**Motivation**

You should understand how (instances of) building blocks of your system
perform their job and communicate at runtime. You will mainly capture
scenarios in your documentation to communicate your architecture to
stakeholders that are less willing or able to read and understand the
static models (building block view, deployment view).

**Form**

There are many notations for describing scenarios, e.g.

-   numbered list of steps (in natural language)

-   activity diagrams or flow charts

-   sequence diagrams

-   BPMN or EPCs (event process chains)

-   state machines

-   …

See [Runtime View](https://docs.arc42.org/section-6/) in the arc42
documentation.

## Deployment view

Here, embed a typical C4 deployment diagram. [Check structurizr documentation to see how to do that](https://docs.structurizr.com/ui/documentation/diagrams).

## Crosscutting Concepts

**Content**

This section describes overall, principal regulations and solution ideas
that are relevant in multiple parts (= cross-cutting) of your system.
Such concepts are often related to multiple building blocks. They can
include many different topics, such as

-   models, especially domain models

-   architecture or design patterns

-   rules for using specific technology

-   principal, often technical decisions of an overarching (=
    cross-cutting) nature

-   implementation rules

### Feature Flags

The system uses feature flags for runtime configuration and controlled feature rollouts. See [Feature Flags Documentation](feature-flags.md) for detailed information about:

- Current feature flags and their purposes
- Implementation guidelines and best practices
- Environment configuration strategies
- Rollback procedures and monitoring

**Motivation**

Concepts form the basis for *conceptual integrity* (consistency,
homogeneity) of the architecture. Thus, they are an important
contribution to achieve inner qualities of your system.

Some of these concepts cannot be assigned to individual building blocks,
e.g. security or safety.

**Form**

The form can be varied:

-   concept papers with any kind of structure

-   cross-cutting model excerpts or scenarios using notations of the
    architecture views

-   sample implementations, especially for technical concepts

-   reference to typical usage of standard frameworks (e.g. using
    Hibernate for object/relational mapping)

**Structure**

A potential (but not mandatory) structure for this section could be:

-   Domain concepts

-   User Experience concepts (UX)

-   Safety and security concepts

-   Architecture and design patterns

-   "Under-the-hood"

-   development concepts

-   operational concepts

Note: it might be difficult to assign individual concepts to one
specific topic on this list.

See [Concepts](https://docs.arc42.org/section-8/) in the arc42
documentation.

## Architecture decisions

See [ADRS section of structurizr](../decisions)

## Quality Requirements

**Content**

This section contains all quality requirements as quality tree with
scenarios. The most important ones have already been described in
section 1.2. (quality goals)

Here you can also capture quality requirements with lesser priority,
which will not create high risks when they are not fully achieved.

**Motivation**

Since quality requirements will have a lot of influence on architectural
decisions you should know for every stakeholder what is really important
to them, concrete and measurable.

See [Quality Requirements](https://docs.arc42.org/section-10/) in the
arc42 documentation.


**Contents**

A list of identified technical risks or technical debts, ordered by
priority

**Motivation**

“Risk management is project management for grown-ups” (Tim Lister,
Atlantic Systems Guild.)

This should be your motto for systematic detection and evaluation of
risks and technical debts in the architecture, which will be needed by
management stakeholders (e.g. project managers, product owners) as part
of the overall risk analysis and measurement planning.

**Form**

List of risks and/or technical debts, probably including suggested
measures to minimize, mitigate or avoid risks or reduce technical debts.

See [Risks and Technical Debt](https://docs.arc42.org/section-11/) in
the arc42 documentation.

# Glossary

**Contents**

The most important domain and technical terms that your stakeholders use
when discussing the system.

You can also see the glossary as source for translations if you work in
multi-language teams.

**Motivation**

You should clearly define your terms, so that all stakeholders

-   have an identical understanding of these terms

-   do not use synonyms and homonyms

A table with columns &lt;Term> and &lt;Definition>.

Potentially more columns in case you need translations.

See [Glossary](https://docs.arc42.org/section-12/) in the arc42
documentation.

<table>
<colgroup>
<col style="width: 33%" />
<col style="width: 66%" />
</colgroup>
<thead>
<tr class="header">
<th style="text-align: left;">Term</th>
<th style="text-align: left;">Definition</th>
</tr>
</thead>
<tbody>
<tr class="odd">
<td style="text-align: left;"><p><em>&lt;Term-1&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;definition-1&gt;</em></p></td>
</tr>
<tr class="even">
<td style="text-align: left;"><p><em>&lt;Term-2&gt;</em></p></td>
<td style="text-align: left;"><p><em>&lt;definition-2&gt;</em></p></td>
</tr>
</tbody>
</table>
