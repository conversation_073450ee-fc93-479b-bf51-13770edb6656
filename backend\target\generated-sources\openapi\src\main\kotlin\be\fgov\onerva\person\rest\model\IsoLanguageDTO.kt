/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * Values: DE,FR,NL
 */

enum class IsoLanguageDTO(val value: kotlin.String) {

    @JsonProperty(value = "de")
    DE("de"),

    @JsonProperty(value = "fr")
    FR("fr"),

    @JsonProperty(value = "nl")
    NL("nl");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is IsoLanguageDTO) "$data" else null

        /**
         * Returns a valid [IsoLanguageDTO] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): IsoLanguageDTO? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

