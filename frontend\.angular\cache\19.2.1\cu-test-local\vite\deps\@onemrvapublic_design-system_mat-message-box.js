import {
  OnemrvaMatColor
} from "./chunk-X7GZ3B26.js";
import {
  Component,
  HostBinding,
  Input,
  NgModule,
  ViewChild,
  setClassMetadata,
  ɵɵattribute,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵloadQuery,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵqueryRefresh,
  ɵɵviewQuery
} from "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-message-box.mjs
var _c0 = ["mbcontent"];
var _c1 = ["*"];
var NEXT_ID = 0;
var OnemrvaMatMessageBoxComponent = class _OnemrvaMatMessageBoxComponent {
  constructor() {
    this.label = false;
    this.messageCode = "";
    this.ariaLabel = "message-box";
    this.role = "alert";
    this.cssClass = "onemrva-mat-message-box";
    this.id = `onemrva-mat-message-box-${NEXT_ID++}`;
    this.dataCy = `onemrva-mat-message-box-${NEXT_ID++}`;
    this._color = OnemrvaMatColor.NONE;
  }
  /**
   * Returns the color of the element.
   *
   * @example
   * ```typescript
   * let color = this.<element>.color;
   * ```
   */
  get color() {
    return this._color;
  }
  /**
   * Sets the size  of the element.
   * By default, the size is `"small"`. It can be set to `"medium"` or `"large"`.
   *
   * @example
   * ```html
   * <onemrva-mat-* size="large"></onemrva-mat-*>
   * ```
   */
  set color(value) {
    switch (value) {
      case OnemrvaMatColor.SUCCESS:
      case OnemrvaMatColor.WARN:
      case OnemrvaMatColor.NONE:
      case OnemrvaMatColor.INFO:
      case OnemrvaMatColor.GRAYSCALE:
      case OnemrvaMatColor.ERROR:
        this._color = value;
        break;
      default:
        this._color = OnemrvaMatColor.INFO;
    }
  }
  /** @hidden @internal */
  get _isError() {
    return this.color === OnemrvaMatColor.ERROR;
  }
  /** @hidden @internal */
  get _isWarn() {
    return this.color === OnemrvaMatColor.WARN;
  }
  /** @hidden @internal */
  get _isSuccess() {
    return this.color === OnemrvaMatColor.SUCCESS;
  }
  /** @hidden @internal */
  get isGrayscale() {
    return this.color === OnemrvaMatColor.GRAYSCALE || this.color === OnemrvaMatColor.NONE;
  }
  /** @hidden @internal */
  get _isInfo() {
    return this.color !== OnemrvaMatColor.ERROR && this.color !== OnemrvaMatColor.WARN && this.color !== OnemrvaMatColor.NONE && this.color !== OnemrvaMatColor.ACCENT && this.color !== OnemrvaMatColor.GRAYSCALE && this.color !== OnemrvaMatColor.SUCCESS;
  }
  ngAfterViewInit() {
    if (this.messageCode !== "") {
      console.warn("messageCode input has been deprecated in message-box component, please review documentation to fix this!");
      this.mbcontent.nativeElement.insertAdjacentHTML("beforeend", this.messageCode);
    }
  }
  static {
    this.ɵfac = function OnemrvaMatMessageBoxComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatMessageBoxComponent)();
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatMessageBoxComponent,
      selectors: [["onemrva-mat-message-box"]],
      viewQuery: function OnemrvaMatMessageBoxComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c0, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.mbcontent = _t.first);
        }
      },
      hostVars: 16,
      hostBindings: function OnemrvaMatMessageBoxComponent_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵattribute("aria-label", ctx.ariaLabel)("role", ctx.role)("id", ctx.id)("data-cy", ctx.dataCy);
          ɵɵclassProp("onemrva-mat-message-box", ctx.cssClass)("mat-error", ctx._isError)("mat-warn", ctx._isWarn)("mat-success", ctx._isSuccess)("mat-grayscale", ctx.isGrayscale)("mat-info", ctx._isInfo);
        }
      },
      inputs: {
        label: "label",
        messageCode: "messageCode",
        id: "id",
        dataCy: "dataCy",
        color: "color"
      },
      ngContentSelectors: _c1,
      decls: 3,
      vars: 0,
      consts: [["mbcontent", ""]],
      template: function OnemrvaMatMessageBoxComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵprojectionDef();
          ɵɵelementStart(0, "div", null, 0);
          ɵɵprojection(2);
          ɵɵelementEnd();
        }
      },
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatMessageBoxComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-message-box",
      standalone: true,
      template: "<div #mbcontent>\n  <ng-content></ng-content>\n</div>\n"
    }]
  }], null, {
    label: [{
      type: Input
    }],
    messageCode: [{
      type: Input
    }],
    mbcontent: [{
      type: ViewChild,
      args: ["mbcontent"]
    }],
    ariaLabel: [{
      type: HostBinding,
      args: ["attr.aria-label"]
    }],
    role: [{
      type: HostBinding,
      args: ["attr.role"]
    }],
    cssClass: [{
      type: HostBinding,
      args: ["class.onemrva-mat-message-box"]
    }],
    id: [{
      type: HostBinding,
      args: ["attr.id"]
    }, {
      type: Input
    }],
    dataCy: [{
      type: HostBinding,
      args: ["attr.data-cy"]
    }, {
      type: Input
    }],
    color: [{
      type: Input
    }],
    _isError: [{
      type: HostBinding,
      args: ["class.mat-error"]
    }],
    _isWarn: [{
      type: HostBinding,
      args: ["class.mat-warn"]
    }],
    _isSuccess: [{
      type: HostBinding,
      args: ["class.mat-success"]
    }],
    isGrayscale: [{
      type: HostBinding,
      args: ["class.mat-grayscale"]
    }],
    _isInfo: [{
      type: HostBinding,
      args: ["class.mat-info"]
    }]
  });
})();
var OnemrvaMatMessageBoxModule = class _OnemrvaMatMessageBoxModule {
  static {
    this.ɵfac = function OnemrvaMatMessageBoxModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatMessageBoxModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatMessageBoxModule,
      imports: [OnemrvaMatMessageBoxComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatMessageBoxModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OnemrvaMatMessageBoxComponent],
      exports: []
    }]
  }], null, null);
})();
export {
  OnemrvaMatMessageBoxComponent,
  OnemrvaMatMessageBoxModule
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-message-box.js.map
