/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * Values: C1,ONEM,AUTHENTIC_SOURCES
 */

enum class ExternalSource(val value: kotlin.String) {

    @JsonProperty(value = "C1")
    C1("C1"),

    @JsonProperty(value = "ONEM")
    ONEM("ONEM"),

    @JsonProperty(value = "AUTHENTIC_SOURCES")
    AUTHENTIC_SOURCES("AUTHENTIC_SOURCES");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is ExternalSource) "$data" else null

        /**
         * Returns a valid [ExternalSource] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): ExternalSource? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

