package be.fgov.onerva.cu.backend.adapter.out.external.configuration

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.junit.jupiter.MockitoExtension

@ExtendWith(MockitoExtension::class)
class AppConfigurationAdapterTest {

    @ParameterizedTest
    @ValueSource(booleans = [true, false])
    fun `allowMultipleC9s should return the configured value`(configValue: Boolean) {
        // Given
        val adapter = AppConfigurationAdapter(configValue)

        // When
        val result = adapter.allowMultipleC9s

        // Then
        assertThat(result).isEqualTo(configValue)
    }
}