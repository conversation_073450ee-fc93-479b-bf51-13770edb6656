package be.fgov.onerva.cu.backend.lookup

import java.time.LocalDate

abstract class BaseLookupDTO(
    open val lookupId: Int,
    open val code: String,
    open val descFr: String,
    open val descNl: String,
)

data class CountryDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class NationalityDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class CityDTO(
    val beginDate: LocalDate,
    val nisCode: String,
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class PostalCodeLanguageRegimeDTO(
    val beginDate: LocalDate?,
    val endDate: LocalDate?,
    val languageRegimeCode: String,
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)

data class StreetDTO(
    override val lookupId: Int,
    override val code: String,
    override val descFr: String,
    override val descNl: String,
) :
    BaseLookupDTO(lookupId, code, descFr, descNl)