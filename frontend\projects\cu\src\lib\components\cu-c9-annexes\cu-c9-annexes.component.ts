import {CommonModule} from "@angular/common";
import {
    Component,
    Input,
    ViewEncapsulation,
} from "@angular/core";
import {TranslateModule} from "@ngx-translate/core";
import {
    OnemrvaMatPanelComponent,
    OnemrvaMatPanelContentComponent,
    OnemrvaMatPanelTitleComponent,
} from "@onemrvapublic/design-system/mat-panel";
import {Annex} from "@rest-client/cu-bff";
import {OnemrvaMatMessageBoxModule} from "@onemrvapublic/design-system/mat-message-box";
import {OnemrvaMatStickerModule} from "@onemrvapublic/design-system/mat-sticker";

@Component({
    selector: "lib-cu-c9-annexes",
    standalone: true,
    imports: [
        OnemrvaMatMessageBoxModule,
        OnemrvaMatStickerModule,
        CommonModule,
        TranslateModule,
        OnemrvaMatPanelComponent,
        OnemrvaMatPanelTitleComponent,
        OnemrvaMatPanelContentComponent,

    ],
    providers: [],
    templateUrl: "./cu-c9-annexes.component.html",
    styleUrl: "./cu-c9-annexes.component.scss",
    encapsulation: ViewEncapsulation.None,
})

export class CuC9AnnexesComponent {

    @Input() language!: string;
    @Input() annexes?: Annex[];

}
