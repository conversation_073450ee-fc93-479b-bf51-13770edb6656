package be.fgov.onerva.cu.backend.application.service

import java.util.UUID
import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequest
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.Request
import be.fgov.onerva.cu.backend.application.domain.Snapshot
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.ExternalSourceNotImplementedException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.mapper.toHistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.mapper.toHistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.mapper.toHistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.port.`in`.HistoricalInformationUseCase
import be.fgov.onerva.cu.backend.application.port.out.BaremaPort
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.backend.application.port.out.RegistryPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.RequestPort
import be.fgov.onerva.cu.backend.application.port.out.SnapshotPort
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.common.aop.LogMethodCall

@Service
class HistoricalInformationService(
    private val requestPort: RequestPort,
    private val loadCitizenPort: LoadCitizenPort,
    private val snapshotPort: SnapshotPort,
    private val waveTaskPersistencePort: WaveTaskPersistencePort,
    private val citizenInformationPort: CitizenInformationPort,
    private val modeOfPaymentPort: ModeOfPaymentPort,
    private val unionContributionPort: UnionContributionPort,
    private val baremaPort: BaremaPort,
    private val requestInformationPort: RequestInformationPort,
    private val registryPort: RegistryPort,
) : HistoricalInformationUseCase {
    @LogMethodCall
    override fun getHistoricalCitizenOnem(requestId: UUID): HistoricalCitizenOnem {
        val request = requestPort.getRequest(requestId)
        return getCitizenInformationFromOnem(requestId, request)
    }

    @LogMethodCall
    override fun getHistoricalCitizenC1(requestId: UUID): HistoricalCitizenC1 {
        return getCitizenInformationFromC1(requestId)
    }

    @LogMethodCall
    override fun getHistoricalCitizenAuthenticSources(requestId: UUID): HistoricalCitizenAuthenticSources {
        val request = requestPort.getRequest(requestId)
        return getCitizenInformationFromAuthenticSources(requestId, request)
    }

    private fun getCitizenInformationFromAuthenticSources(
        requestId: UUID,
        request: Request,
    ): HistoricalCitizenAuthenticSources {
        val source = ExternalSource.AUTHENTIC_SOURCES
        return snapshotPort.getCitizenInformationSnapshot(requestId, source).let {
            when (it) {
                is Snapshot.Found -> {
                    if (!it.readonly) {
                        getAndSaveHistoricalCitizenFromAuthenticSources(request, requestId)
                    } else {
                        it.value.toHistoricalCitizenAuthenticSources()
                    }
                }

                is Snapshot.NotFound -> {
                    getAndSaveHistoricalCitizenFromAuthenticSources(request, requestId)
                }
            }
        }
    }

    private fun getAndSaveHistoricalCitizenOnem(
        request: Request,
        requestId: UUID,
        source: ExternalSource,
    ): HistoricalCitizenOnem {
        // We need to get the information from the external source
        val historicalCitizenOnem = loadCitizenPort.getCitizenWithAddress(request.ssin)
            ?: throw CitizenNotFoundException("Citizen is not found for requestID: $requestId")

        // Save the information in the snapshot repository
        snapshotPort.saveCitizenInformationSnapshot(
            requestId, source, historicalCitizenOnem.toHistoricalCitizenSnapshot()
        )
        return historicalCitizenOnem
    }

    private fun getAndSaveHistoricalCitizenFromAuthenticSources(
        request: Request,
        requestId: UUID,
    ): HistoricalCitizenAuthenticSources {
        val historicalCitizenAuthenticSources = registryPort.getRegistryInformationForCitizen(request.ssin)
        snapshotPort.saveCitizenInformationSnapshot(
            requestId,
            ExternalSource.AUTHENTIC_SOURCES,
            historicalCitizenAuthenticSources.toHistoricalCitizenSnapshot()
        )
        return historicalCitizenAuthenticSources
    }

    private fun getCitizenInformationFromOnem(
        requestId: UUID,
        request: Request,
    ): HistoricalCitizenOnem {
        // We need to get the citizen information from the snapshot repository, if it is not readonly,
        // we need to update the information from the external source
        // then we need to return the information
        val source = ExternalSource.ONEM
        return snapshotPort.getCitizenInformationSnapshot(requestId, ExternalSource.ONEM).let {
            when (it) {
                is Snapshot.Found -> {
                    // If it is not found or it is found but not readonly
                    if (!it.readonly) {
                        getAndSaveHistoricalCitizenOnem(request, requestId, source)
                    } else {
                        it.value.toHistoricalCitizenOnem()
                    }
                }

                is Snapshot.NotFound -> {
                    getAndSaveHistoricalCitizenOnem(request, requestId, source)
                }
            }
        }
    }

    private fun getCitizenInformationFromC1(requestId: UUID): HistoricalCitizenC1 {
        val revisionNumbers = waveTaskPersistencePort.getCitizenInformationRevision(
            requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
        )

        if (revisionNumbers.citizenInformationRevisionNumber == null || revisionNumbers.modeOfPaymentRevisionNumber == null || revisionNumbers.requestInformationRevisionNumber == null || revisionNumbers.unionContributionRevisionNumber == null) {
            throw RequestInvalidStateException("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")
        }

        val citizenInformation = citizenInformationPort.getCitizenInformationForRevision(
            requestId, revisionNumbers.citizenInformationRevisionNumber

        )
            ?: throw RequestInvalidStateException("Revision number not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

        val modeOfPayment = modeOfPaymentPort.getModeOfPaymentForRevision(
            requestId, revisionNumbers.modeOfPaymentRevisionNumber
        )

        val unionContribution = unionContributionPort.getUnionContributionForRevision(
            requestId, revisionNumbers.unionContributionRevisionNumber
        )

        val requestInformation = requestInformationPort.getRequestInformationForRevision(
            requestId,
            revisionNumbers.requestInformationRevisionNumber
        )

        return HistoricalCitizenC1(
            firstName = citizenInformation.firstName,
            lastName = citizenInformation.lastName,
            numbox = 0,
            nationality = citizenInformation.nationality,
            address = AddressNullable(
                street = citizenInformation.address.street,
                houseNumber = citizenInformation.address.houseNumber,
                boxNumber = citizenInformation.address.boxNumber,
                zipCode = citizenInformation.address.zipCode,
                country = citizenInformation.address.country,
                city = citizenInformation.address.city,
                valueDate = requestInformation.requestDate,
            ),
            iban = modeOfPayment.iban,
            bic = modeOfPayment.bic,
            otherPersonName = modeOfPayment.otherPersonName
                ?: "${citizenInformation.firstName} ${citizenInformation.lastName}",
            birthDate = citizenInformation.birthDate,
            bankAccountValueDate = requestInformation.requestDate,
            authorized = unionContribution?.authorized, // mgenique
            effectiveDate = unionContribution?.effectiveDate,
            paymentMode = null
        )
    }

    /**
     * Get the mode of payment from a historical source
     * Only the ONEM source is supported for this information.
     * @param requestId the id of the request
     * @param source the source of the information
     * @return the mode of payment
     */
    @LogMethodCall
    override fun getModeOfPayment(
        requestId: UUID,
        source: ExternalSource,
    ): ModeOfPayment {
        if (source == ExternalSource.C1) {
            val revisionNumbers = waveTaskPersistencePort.getCitizenInformationRevision(
                requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
            )

            if (revisionNumbers.modeOfPaymentRevisionNumber == null) {
                throw RequestInvalidStateException("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")
            }

            val modeOfPayment = modeOfPaymentPort.getModeOfPaymentForRevision(
                requestId, revisionNumbers.modeOfPaymentRevisionNumber
            )

            return ModeOfPayment(
                otherPersonName = modeOfPayment.otherPersonName,
                iban = modeOfPayment.iban,
                bic = modeOfPayment.bic,
            )
        } else {
            throw ExternalSourceNotImplementedException("Mode of payment is not implemented for source $source")
        }
    }

    @LogMethodCall
    override fun getUnionContribution(requestId: UUID, source: ExternalSource): UnionContribution {
        return when (source) {
            ExternalSource.C1 -> {
                val revisionNumbers = waveTaskPersistencePort.getCitizenInformationRevision(
                    requestId, WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE
                )

                if (revisionNumbers.unionContributionRevisionNumber == null) {
                    throw RequestInvalidStateException("Revision numbers not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")
                }

                val unionContribution = unionContributionPort.getUnionContributionForRevision(
                    requestId, revisionNumbers.unionContributionRevisionNumber
                )
                    ?: throw RequestInvalidStateException("Revision number not found for request $requestId and task CHANGE_PERSONAL_DATA_CAPTURE")

                unionContribution
            }

            else -> throw ExternalSourceNotImplementedException("Mode of payment is not implemented for source $source")
        }
    }

    override fun getBarema(requestId: UUID): Barema? {
        return when (val snapshot = snapshotPort.getBaremaSnapshot(requestId)) {
            is Snapshot.Found -> {
                if (!snapshot.readonly) {
                    getAndSaveBarema(requestId)
                } else {
                    snapshot.value
                }
            }

            is Snapshot.NotFound -> getAndSaveBarema(requestId)
        }
    }

    private fun getAndSaveBarema(requestId: UUID): Barema? {
        val request = requestPort.getRequest(requestId)
        val numbox = loadCitizenPort.getCitizenNumbox(request.ssin)

        val requestDate = when (request) {
            is ChangePersonalDataRequest -> request.requestInformation?.requestDate ?: request.requestDate
            else -> error("Request is not a change personal data request")
        }

        val barema = baremaPort.getLatestBarema(numbox, requestDate)
        snapshotPort.saveBaremaSnapshot(requestId, barema)
        return barema
    }
}