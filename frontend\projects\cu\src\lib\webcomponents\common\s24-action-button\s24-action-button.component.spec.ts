import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { TranslateService } from '@ngx-translate/core';
import { Observable, of } from 'rxjs';
import { S24ActionButtonComponent } from './s24-action-button.component';
import { RedirectHandlerService } from '../../../http/redirect-handler.service';
import { CuDialogComponent } from '../../../components/cu-dialog/cu-dialog.component';

describe('S24ActionButtonComponent', () => {
  let component: S24ActionButtonComponent;
  let fixture: ComponentFixture<S24ActionButtonComponent>;
  let matDialogMock: jest.Mocked<MatDialog>;
  let translateServiceMock: jest.Mocked<TranslateService>;
  let redirectHandlerServiceMock: jest.Mocked<RedirectHandlerService>;
  let dialogRefMock: jest.Mocked<MatDialogRef<CuDialogComponent>>;

  beforeEach(async () => {
    dialogRefMock = {
      close: jest.fn(),
    } as unknown as jest.Mocked<MatDialogRef<CuDialogComponent>>;

    matDialogMock = {
      open: jest.fn().mockReturnValue(dialogRefMock),
    } as unknown as jest.Mocked<MatDialog>;

    translateServiceMock = {
      instant: jest.fn((key: string) => key),
      get: jest.fn((key: string) => of(key)),
      use: jest.fn(),
      currentLang: 'en',
      defaultLang: 'en',
      onLangChange: of({ lang: 'en', translations: {} }),
      onTranslationChange: of({ lang: 'en', translations: {} }),
      onDefaultLangChange: of({ lang: 'en', translations: {} }),
    } as unknown as jest.Mocked<TranslateService>;

    redirectHandlerServiceMock = {
      openS24Session: jest.fn().mockReturnValue(of({})),
    } as unknown as jest.Mocked<RedirectHandlerService>;

    await TestBed.configureTestingModule({
      imports: [
        S24ActionButtonComponent,
      ],
      providers: [
        { provide: MatDialog, useValue: matDialogMock },
        { provide: TranslateService, useValue: translateServiceMock },
        { provide: RedirectHandlerService, useValue: redirectHandlerServiceMock },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(S24ActionButtonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Component Inputs', () => {
    it('should have default isHidden value as false', () => {
      expect(component.isHidden).toBe(false);
    });

    it('should accept requestId input', () => {
      const testRequestId = 'test-request-123';
      component.requestId = testRequestId;
      expect(component.requestId).toBe(testRequestId);
    });
  });

  describe('openS24Session', () => {
    it('should open dialog with correct configuration', () => {
      component.requestId = 'test-request-123';

      component.openS24Session();

      expect(matDialogMock.open).toHaveBeenCalledWith(CuDialogComponent, {
        data: expect.objectContaining({
          title: 'CU_DATA_CONSISTENCY.S24_DIALOG.TITLE',
          content: expect.stringContaining('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_1'),
          primaryActionText: 'CU_DATA_CONSISTENCY.S24_DIALOG.PRIMARY',
          secondaryActionText: 'CU_DATA_CONSISTENCY.S24_DIALOG.SECONDARY',
          dialogType: 'warn',
          dialogSize: 'medium',
          onPrimaryAction: expect.any(Function),
          onSecondaryAction: expect.any(Function),
        }),
      });
    });

    it('should translate all dialog texts', () => {
      component.openS24Session();

      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.TITLE');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_1');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_2');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_3');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.BODY_4');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.PRIMARY');
      expect(translateServiceMock.instant).toHaveBeenCalledWith('CU_DATA_CONSISTENCY.S24_DIALOG.SECONDARY');
    });

    it('should call redirectHandlerService.openS24Session when primary action is clicked', () => {
      component.requestId = 'test-request-123';

      component.openS24Session();

      const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
      const onPrimaryAction = dialogConfig.data.onPrimaryAction;

      onPrimaryAction();

      expect(redirectHandlerServiceMock.openS24Session).toHaveBeenCalledWith('test-request-123');
      expect(dialogRefMock.close).toHaveBeenCalledWith(true);
    });

    it('should close dialog with false when secondary action is clicked', () => {
      component.openS24Session();

      const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
      const onSecondaryAction = dialogConfig.data.onSecondaryAction;

      onSecondaryAction();

      expect(dialogRefMock.close).toHaveBeenCalledWith(false);
    });

    it('should handle error in openS24Session subscription', () => {
      const testError = new Error('Test error');
      redirectHandlerServiceMock.openS24Session.mockReturnValue(
          new Observable(subscriber => subscriber.error(testError))
      );

      component.requestId = 'test-request-123';
      component.openS24Session();

      const dialogConfig = (matDialogMock.open as jest.Mock).mock.calls[0][1];
      const onPrimaryAction = dialogConfig.data.onPrimaryAction;

      expect(() => onPrimaryAction()).not.toThrow();
    });
  });
});