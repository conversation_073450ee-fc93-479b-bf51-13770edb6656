import {
  MatIcon
} from "./chunk-JYJFRGNE.js";
import "./chunk-AU6DHCV6.js";
import {
  MatRadioButton
} from "./chunk-HRM7ISOX.js";
import "./chunk-5VYNQAP5.js";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ardContent,
  MatCardTitle
} from "./chunk-QRPWZGXI.js";
import {
  MatLabel
} from "./chunk-T76J6HQT.js";
import "./chunk-C3EWM6A7.js";
import "./chunk-IEMWVBW3.js";
import "./chunk-UPOW3STX.js";
import "./chunk-IE44L42K.js";
import "./chunk-QNFKXUK7.js";
import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  NgModule,
  Output,
  ViewChild,
  setClassMetadata,
  ɵɵadvance,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵgetCurrentView,
  ɵɵlistener,
  ɵɵloadQuery,
  ɵɵprojection,
  ɵɵprojectionDef,
  ɵɵproperty,
  ɵɵqueryRefresh,
  ɵɵresetView,
  ɵɵrestoreView,
  ɵɵviewQuery
} from "./chunk-6V6BWDKV.js";
import "./chunk-ISM5WLAM.js";
import "./chunk-IC62NIWK.js";
import "./chunk-ZZ67MR3E.js";
import "./chunk-WDMUDEB6.js";

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-selectable-box.mjs
var _c0 = ["radioButton"];
var _c1 = [[["", "title", ""]], [["", "icon", ""]], [["", "sticker", ""]], "*"];
var _c2 = ["[title]", "[icon]", "[sticker]", "*"];
var OnemrvaMatSelectableBoxComponent = class _OnemrvaMatSelectableBoxComponent {
  valueChanged(event) {
    this.change.emit(event);
  }
  constructor(changeDetectorRef) {
    this.changeDetectorRef = changeDetectorRef;
    this.value = "";
    this.disabled = false;
    this.mode = "primary";
    this.checked = false;
    this.change = new EventEmitter();
  }
  ngAfterViewInit() {
    this.changeDetectorRef.detectChanges();
  }
  isChecked() {
    return this.radioButton?._inputElement.nativeElement.checked ?? this.checked;
  }
  triggerRadioClick() {
    if (this.radioButton && !this.radioButton.checked && !this.radioButton.disabled) {
      this.radioButton._inputElement.nativeElement.dispatchEvent(new MouseEvent("click", {
        bubbles: false
      }));
    }
  }
  static {
    this.ɵfac = function OnemrvaMatSelectableBoxComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatSelectableBoxComponent)(ɵɵdirectiveInject(ChangeDetectorRef));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _OnemrvaMatSelectableBoxComponent,
      selectors: [["onemrva-mat-selectable-box"]],
      viewQuery: function OnemrvaMatSelectableBoxComponent_Query(rf, ctx) {
        if (rf & 1) {
          ɵɵviewQuery(_c0, 5);
        }
        if (rf & 2) {
          let _t;
          ɵɵqueryRefresh(_t = ɵɵloadQuery()) && (ctx.radioButton = _t.first);
        }
      },
      inputs: {
        value: "value",
        disabled: "disabled",
        mode: "mode",
        checked: "checked"
      },
      outputs: {
        change: "change"
      },
      ngContentSelectors: _c2,
      decls: 11,
      vars: 21,
      consts: [["radioButton", ""], [1, "m", "onemrva-shadow-highlight", 3, "click"], [1, "selectablebox-title"], ["aria-label", "Selected", 3, "change", "checked", "disabled", "value"], ["color", "primary", 1, "selectable-box-icon"]],
      template: function OnemrvaMatSelectableBoxComponent_Template(rf, ctx) {
        if (rf & 1) {
          const _r1 = ɵɵgetCurrentView();
          ɵɵprojectionDef(_c1);
          ɵɵelementStart(0, "mat-card", 1);
          ɵɵlistener("click", function OnemrvaMatSelectableBoxComponent_Template_mat_card_click_0_listener() {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.triggerRadioClick());
          });
          ɵɵelementStart(1, "mat-card-title", 2)(2, "mat-radio-button", 3, 0);
          ɵɵlistener("change", function OnemrvaMatSelectableBoxComponent_Template_mat_radio_button_change_2_listener($event) {
            ɵɵrestoreView(_r1);
            return ɵɵresetView(ctx.valueChanged($event));
          });
          ɵɵelementEnd();
          ɵɵelementStart(4, "mat-label");
          ɵɵprojection(5);
          ɵɵelementStart(6, "mat-icon", 4);
          ɵɵprojection(7, 1);
          ɵɵelementEnd()();
          ɵɵprojection(8, 2);
          ɵɵelementEnd();
          ɵɵelementStart(9, "mat-card-content");
          ɵɵprojection(10, 3);
          ɵɵelementEnd()();
        }
        if (rf & 2) {
          ɵɵclassProp("primary", !ctx.isChecked())("selected", ctx.isChecked());
          ɵɵadvance();
          ɵɵclassProp("primary", ctx.mode === "primary")("white", ctx.mode === "white")("selected", ctx.isChecked());
          ɵɵadvance();
          ɵɵproperty("checked", ctx.checked)("disabled", ctx.disabled)("value", ctx.value);
          ɵɵadvance(2);
          ɵɵclassProp("disabled", ctx.disabled);
          ɵɵadvance(5);
          ɵɵclassProp("primary", ctx.mode === "primary")("white", ctx.mode === "white")("selected", ctx.isChecked());
        }
      },
      dependencies: [MatCardTitle, MatCard, MatRadioButton, MatLabel, MatIcon, MatCardContent],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatSelectableBoxComponent, [{
    type: Component,
    args: [{
      selector: "onemrva-mat-selectable-box",
      standalone: true,
      imports: [MatCardTitle, MatCard, MatRadioButton, MatLabel, MatIcon, MatCardContent],
      template: `<mat-card
  class="m onemrva-shadow-highlight"
  [class.primary]="!isChecked()"
  [class.selected]="isChecked()"
  (click)="triggerRadioClick()"
>
  <mat-card-title
    class="selectablebox-title"
    [class.primary]="mode === 'primary'"
    [class.white]="mode === 'white'"
    [class.selected]="isChecked()"
  >
    <mat-radio-button
      #radioButton
      (change)="valueChanged($event)"
      aria-label="Selected"
      [checked]="checked"
      [disabled]="disabled"
      [value]="value"
    >
    </mat-radio-button>

    <mat-label [class.disabled]="disabled">
      <ng-content select="[title]"></ng-content>
      <mat-icon class="selectable-box-icon" color="primary">
        <ng-content select="[icon]"></ng-content>
      </mat-icon>
    </mat-label>

    <ng-content select="[sticker]"></ng-content>
  </mat-card-title>

  <mat-card-content
    [class.primary]="mode === 'primary'"
    [class.white]="mode === 'white'"
    [class.selected]="isChecked()"
  >
    <ng-content></ng-content>
  </mat-card-content>
</mat-card>
`
    }]
  }], () => [{
    type: ChangeDetectorRef
  }], {
    radioButton: [{
      type: ViewChild,
      args: ["radioButton"]
    }],
    value: [{
      type: Input
    }],
    disabled: [{
      type: Input
    }],
    mode: [{
      type: Input
    }],
    checked: [{
      type: Input
    }],
    change: [{
      type: Output
    }]
  });
})();
var OnemrvaMatSelectableBoxModule = class _OnemrvaMatSelectableBoxModule {
  static {
    this.ɵfac = function OnemrvaMatSelectableBoxModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMatSelectableBoxModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaMatSelectableBoxModule,
      imports: [OnemrvaMatSelectableBoxComponent],
      exports: [OnemrvaMatSelectableBoxComponent]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({
      imports: [OnemrvaMatSelectableBoxComponent]
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMatSelectableBoxModule, [{
    type: NgModule,
    args: [{
      declarations: [],
      imports: [OnemrvaMatSelectableBoxComponent],
      exports: [OnemrvaMatSelectableBoxComponent]
    }]
  }], null, null);
})();
export {
  OnemrvaMatSelectableBoxComponent,
  OnemrvaMatSelectableBoxModule
};
//# sourceMappingURL=@onemrvapublic_design-system_mat-selectable-box.js.map
