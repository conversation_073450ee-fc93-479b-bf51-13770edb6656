package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.WaveTaskStatus
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param processId 
 * @param taskId 
 * @param status 
 * @param waveUrl 
 */
data class WaveTaskResponse(

    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("processId", required = true) val processId: kotlin.String,

    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("taskId", required = true) val taskId: kotlin.String,

    @field:Valid
    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("status", required = true) val status: WaveTaskStatus,

    @Schema(example = "null", required = true, readOnly = true, description = "")
    @get:JsonProperty("waveUrl", required = true) val waveUrl: kotlin.String
    ) {

}

