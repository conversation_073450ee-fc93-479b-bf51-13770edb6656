/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param assignee 
 * @param reason 
 */


data class AskReviewRequestDTO (

    @get:JsonProperty("assignee")
    val assignee: kotlin.String? = null,

    @get:JsonProperty("reason")
    val reason: kotlin.String? = null

) {


}

