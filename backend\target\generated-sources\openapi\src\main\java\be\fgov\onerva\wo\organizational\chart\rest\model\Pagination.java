/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo.organizational.chart.rest.model.SelfLink;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.net.URI;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * The details regarding the pagination for a search
 */
@JsonPropertyOrder({
  Pagination.JSON_PROPERTY_TOTAL,
  Pagination.JSON_PROPERTY_PAGE_SIZE,
  Pagination.JSON_PROPERTY_FIRST,
  Pagination.JSON_PROPERTY_LAST,
  Pagination.JSON_PROPERTY_NEXT,
  Pagination.JSON_PROPERTY_PREV,
  Pagination.JSON_PROPERTY_SELF
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Pagination {
  public static final String JSON_PROPERTY_TOTAL = "total";
  private Integer total;

  public static final String JSON_PROPERTY_PAGE_SIZE = "pageSize";
  private Integer pageSize;

  public static final String JSON_PROPERTY_FIRST = "first";
  private URI first;

  public static final String JSON_PROPERTY_LAST = "last";
  private URI last;

  public static final String JSON_PROPERTY_NEXT = "next";
  private URI next;

  public static final String JSON_PROPERTY_PREV = "prev";
  private URI prev;

  public static final String JSON_PROPERTY_SELF = "self";
  private SelfLink self;

  public Pagination() {
  }

  public Pagination total(Integer total) {
    
    this.total = total;
    return this;
  }

  /**
   * The number of items
   * @return total
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotal() {
    return total;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotal(Integer total) {
    this.total = total;
  }

  public Pagination pageSize(Integer pageSize) {
    
    this.pageSize = pageSize;
    return this;
  }

  /**
   * The number of item in the page
   * minimum: 1
   * maximum: 4000
   * @return pageSize
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPageSize() {
    return pageSize;
  }


  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }

  public Pagination first(URI first) {
    
    this.first = first;
    return this;
  }

  /**
   * The URI of the first page
   * @return first
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getFirst() {
    return first;
  }


  @JsonProperty(JSON_PROPERTY_FIRST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirst(URI first) {
    this.first = first;
  }

  public Pagination last(URI last) {
    
    this.last = last;
    return this;
  }

  /**
   * The URI of the last page
   * @return last
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getLast() {
    return last;
  }


  @JsonProperty(JSON_PROPERTY_LAST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLast(URI last) {
    this.last = last;
  }

  public Pagination next(URI next) {
    
    this.next = next;
    return this;
  }

  /**
   * The URI of the next page
   * @return next
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getNext() {
    return next;
  }


  @JsonProperty(JSON_PROPERTY_NEXT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNext(URI next) {
    this.next = next;
  }

  public Pagination prev(URI prev) {
    
    this.prev = prev;
    return this;
  }

  /**
   * The URI of the previous page
   * @return prev
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PREV)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public URI getPrev() {
    return prev;
  }


  @JsonProperty(JSON_PROPERTY_PREV)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPrev(URI prev) {
    this.prev = prev;
  }

  public Pagination self(SelfLink self) {
    
    this.self = self;
    return this;
  }

  /**
   * Get self
   * @return self
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SELF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public SelfLink getSelf() {
    return self;
  }


  @JsonProperty(JSON_PROPERTY_SELF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSelf(SelfLink self) {
    this.self = self;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Pagination pagination = (Pagination) o;
    return Objects.equals(this.total, pagination.total) &&
        Objects.equals(this.pageSize, pagination.pageSize) &&
        Objects.equals(this.first, pagination.first) &&
        Objects.equals(this.last, pagination.last) &&
        Objects.equals(this.next, pagination.next) &&
        Objects.equals(this.prev, pagination.prev) &&
        Objects.equals(this.self, pagination.self);
  }

  @Override
  public int hashCode() {
    return Objects.hash(total, pageSize, first, last, next, prev, self);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Pagination {\n");
    sb.append("    total: ").append(toIndentedString(total)).append("\n");
    sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
    sb.append("    first: ").append(toIndentedString(first)).append("\n");
    sb.append("    last: ").append(toIndentedString(last)).append("\n");
    sb.append("    next: ").append(toIndentedString(next)).append("\n");
    sb.append("    prev: ").append(toIndentedString(prev)).append("\n");
    sb.append("    self: ").append(toIndentedString(self)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

