import {fakeAsync, TestBed, tick} from "@angular/core/testing";
import {FormBuilder, ReactiveFormsModule} from "@angular/forms";
import {of, throwError} from "rxjs";
import {ConfigService} from "../../config/config.service";
import {DataCaptureComponent} from "./data-capture.component";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {DataCaptureService} from "../../http/data-capture.service";
import {CdfFormService} from "../../services/cdf-form.service";
import {GeoLookupService} from "../../http/geo-lookup.service";
import {HttpClientTestingModule} from "@angular/common/http/testing";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";
import {Component} from "@angular/core";
import {ToastService} from "../../services/toast.service";
import {RedirectHandlerService} from "../../http/redirect-handler.service";

@Component({
    selector: "lib-cu-cdf",
    standalone: true,
    template: "",
})
class DummyCuCdfComponent {
}

@Component({
    selector: "lib-cu-c9-annexes",
    standalone: true,
    template: "",
})
class DummyCuC9AnnexesComponent {
}

@Component({
    selector: "lib-loading-component",
    standalone: true,
    template: "",
})
class DummyLoadingComponent {
}

@Component({
    selector: "lib-cu-closed-or-treated-on-main-frame",
    standalone: true,
    template: "",
})
class DummyCuClosedOrTreatedOnMainFrameComponent {
}

jest.mock("../../environments/environment", () => ({
    environment: {
        production: true,
        apiBasePath: "http://fakeapi",
    },
}));

describe("DataCaptureComponent", () => {
    let component: DataCaptureComponent;
    let dataCaptureService: {
        initializeServices: jest.Mock;
        getAggregatedData: jest.Mock;
        updateRequest: jest.Mock;
        closeTask: jest.Mock;
    };
    let cdfFormService: {
        createEmptyForm: jest.Mock;
        extractBasicInformation: jest.Mock;
        extractCitizenInformation: jest.Mock;
        extractPaymentInformation: jest.Mock;
        extractUnionContribution: jest.Mock;
    };
    let geoLookupService: {
        initializeService: jest.Mock;
    };
    let redirectHandlerService: {
        initializeServices: jest.Mock;
        getC51RedirectUrl: jest.Mock;
    };
    let translateService: TranslateService;
    let formBuilder: FormBuilder;
    let configService: ConfigService;
    let toastService: ToastService;

    beforeEach(async () => {
        TestBed.overrideComponent(DataCaptureComponent, {
            set: {
                imports: [
                    DummyCuCdfComponent,
                    DummyCuC9AnnexesComponent,
                    DummyLoadingComponent,
                    DummyCuClosedOrTreatedOnMainFrameComponent,
                ],
            },
        });

        dataCaptureService = {
            initializeServices: jest.fn(),
            getAggregatedData: jest.fn().mockReturnValue(
                of({
                    basicInfo: {name: "John Doe"},
                    citizenInformation: {id: "123"},
                    modeOfPayment: {method: "card"},
                    unionContribution: {contribution: 100},
                }),
            ),
            updateRequest: jest.fn().mockReturnValue(of({})),
            closeTask: jest.fn().mockReturnValue(of({waveUrl: "/wave-path"})),
        };

        cdfFormService = {
            createEmptyForm: jest.fn().mockReturnValue(new FormBuilder().group({})),
            extractBasicInformation: jest.fn().mockReturnValue({dummy: "basic"}),
            extractCitizenInformation: jest.fn().mockReturnValue({dummy: "citizen"}),
            extractPaymentInformation: jest.fn().mockReturnValue({dummy: "payment"}),
            extractUnionContribution: jest.fn().mockReturnValue({dummy: "union"}),
        };

        geoLookupService = {
            initializeService: jest.fn(),
        };

        redirectHandlerService = {
            initializeServices: jest.fn(),
            getC51RedirectUrl: jest.fn().mockReturnValue(of("http://c51-url.com")),
        };

        toastService = {
            success: jest.fn(),
            error: jest.fn(),
        } as unknown as ToastService;

        formBuilder = new FormBuilder();

        await TestBed.configureTestingModule({
            imports: [
                DataCaptureComponent,
                ReactiveFormsModule,
                TranslateModule.forRoot(),
                HttpClientTestingModule,
                NoopAnimationsModule,
            ],
            providers: [
                FormBuilder,
                {provide: DataCaptureService, useValue: dataCaptureService},
                {provide: CdfFormService, useValue: cdfFormService},
                {provide: GeoLookupService, useValue: geoLookupService},
                {provide: RedirectHandlerService, useValue: redirectHandlerService},
                {provide: ToastService, useValue: toastService},
                TranslateService,
                {
                    provide: ConfigService,
                    useValue: {
                        getWoDomain: jest.fn().mockReturnValue("http://wo-domain.com"),
                        getEnvironmentVariable: jest.fn(),
                        isOnWO: jest.fn().mockReturnValue(false),
                    },
                },
            ],
        }).compileComponents();

        const fixture = TestBed.createComponent(DataCaptureComponent);
        component = fixture.componentInstance;
        translateService = TestBed.inject(TranslateService);
        configService = TestBed.inject(ConfigService);

        jest.spyOn(console, "log").mockImplementation(() => {
        });
        jest.spyOn(console, "error").mockImplementation(() => {
        });
        jest.spyOn(configService, "isOnWO").mockImplementation();

        fixture.detectChanges();
    });

    it("should create the component", () => {
        expect(component).toBeTruthy();
    });

    it("should fetch aggregated data and update signals in getWoTaskById", fakeAsync(() => {
        component.cdfForm.set(formBuilder.group({}));
        const fakeRequestId = "req-456";
        (component as any).getWoTaskById(fakeRequestId);
        tick();
        expect(component.citizenData()).toEqual({name: "John Doe"});
        expect(component.citizenInformation()).toEqual({id: "123"});
        expect(component.paymentData()).toEqual({method: "card"});
        expect(component.unionData()).toEqual({contribution: 100});
    }));

    it("should handle error in getWoTaskById and log the error", fakeAsync(() => {
        dataCaptureService.getAggregatedData.mockReturnValue(throwError(() => new Error("error")));
        const fakeRequestId = "req-789";
        (component as any).getWoTaskById(fakeRequestId);
        tick();
        expect(console.error).toHaveBeenCalled();
    }));

    it("should call updateRequest in save", () => {
        const dummyForm = formBuilder.group({});
        component.cdfForm.set(dummyForm);
        component.requestId = "req-001";
        component.save(true);
        expect(cdfFormService.extractBasicInformation).toHaveBeenCalledWith(dummyForm);
        expect(cdfFormService.extractCitizenInformation).toHaveBeenCalledWith(dummyForm);
        expect(cdfFormService.extractPaymentInformation).toHaveBeenCalledWith(dummyForm);
        expect(cdfFormService.extractUnionContribution).toHaveBeenCalledWith(dummyForm);
        expect(dataCaptureService.updateRequest).toHaveBeenCalledWith("req-001", {
            basicInfo: {dummy: "basic"},
            citizenInformation: {dummy: "citizen"},
            modeOfPayment: {dummy: "payment"},
            unionContribution: {dummy: "union"},
        });
    });

    it("should complete destroy$ on ngOnDestroy", () => {
        const nextSpy = jest.spyOn(component["destroy$"], "next");
        const completeSpy = jest.spyOn(component["destroy$"], "complete");
        component.ngOnDestroy();
        expect(nextSpy).toHaveBeenCalled();
        expect(completeSpy).toHaveBeenCalled();
    });

    it("should initialize component services", () => {
        const dataCaptureServiceSpy = jest.spyOn(dataCaptureService, "initializeServices");
        const redirectHandlerServiceSpy = jest.spyOn(redirectHandlerService, "initializeServices");

        component["initializeComponentServices"]("test-token");

        expect(dataCaptureServiceSpy).toHaveBeenCalledWith("test-token");
        expect(redirectHandlerServiceSpy).toHaveBeenCalledWith("test-token");
    });

    describe("goToNextTask", () => {
        it("should open a new window with WO domain + wave URL when on WO and waveUrl exists", () => {
            const openSpy = jest.spyOn(window, "open").mockImplementation(() => null);
            const mockWoDomain = "http://wo-domain.com";
            jest.spyOn(configService, "isOnWO").mockReturnValue(true);
            jest.spyOn(configService, "getWoDomain").mockReturnValue(mockWoDomain);

            component.goToNextTask("/wave-path");

            expect(configService.isOnWO).toHaveBeenCalled();
            expect(configService.getWoDomain).toHaveBeenCalled();
            expect(openSpy).toHaveBeenCalledWith(
                "http://wo-domain.com/wave-path",
                "_self",
            );
        });

        it("should not open window when on WO and waveUrl is undefined", () => {
            const openSpy = jest.spyOn(window, "open").mockImplementation(() => null);
            jest.spyOn(configService, "isOnWO").mockReturnValue(true);

            component.goToNextTask(undefined);

            expect(configService.isOnWO).toHaveBeenCalled();
            expect(openSpy).not.toHaveBeenCalled();
        });

        it("should redirect to localhost when not on WO", () => {
            component.requestId = "mock-request-id";
            const openSpy = jest.spyOn(window, "open").mockImplementation(() => null);
            jest.spyOn(configService, "isOnWO").mockReturnValue(false);

            component.goToNextTask("any-wave-url");

            expect(configService.isOnWO).toHaveBeenCalled();
            expect(openSpy).toHaveBeenCalledWith(
                `http://localhost:4300/DataValidation?language=NL&requestId=mock-request-id`,
                "_self",
            );
        });
    });

    it("should disable the form when status is CLOSED", fakeAsync(() => {
        component.requestId = "req-123";
        component.status = "CLOSED";
        (component as any).getWoTaskById("req-123");
        tick();
        expect(component.cdfForm().disabled).toBe(true);
    }));

    it("should handle an error when saving with validation", fakeAsync(() => {
        dataCaptureService.updateRequest.mockReturnValue(throwError(() => new Error("Save error")));
        component.requestId = "req-001";

        component.save(true);
        tick();

        expect(console.error).toHaveBeenCalledWith("Error closing task:", expect.any(Error));
    }));

    it("should proceed to close the task if validation is true", fakeAsync(() => {
        component.requestId = "req-001";
        component.save(true);
        tick();
        expect(dataCaptureService.closeTask).toHaveBeenCalledWith("req-001");
    }));

    it("should not call closeTask if validation is false", fakeAsync(() => {
        component.requestId = "req-001";
        component.save(false);
        tick();
        expect(dataCaptureService.closeTask).not.toHaveBeenCalled();
    }));

    it("should call getC51RedirectUrl when sendC51 is called", fakeAsync(() => {
        const redirectSpy = jest.spyOn(redirectHandlerService, "getC51RedirectUrl");
        const windowOpenSpy = jest.spyOn(window, "open").mockImplementation(() => null);
        component.requestId = "req-001";

        component.sendC51();
        tick();

        expect(redirectSpy).toHaveBeenCalledWith("req-001");
        expect(windowOpenSpy).toHaveBeenCalledWith("http://c51-url.com", "_blank");
    }));

});