/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1Activity
 */
@JsonPropertyOrder({
  EC1Activity.JSON_PROPERTY_HAS_FULL_STUDIES_FROM_DATE,
  EC1Activity.JSON_PROPERTY_HAS_FOLLOW_APPRENTICESHIP_OR_WORK_LINKED_TRAINING_FROM_DATE,
  EC1Activity.JSON_PROPERTY_HAS_FOLLOW_A_TRAINING_WITH_AN_INTERNSHIP_FROM_DATE,
  EC1Activity.JSON_PROPERTY_HAS_PRACTICE_REMUNERATED_MANDATE_AS_A_MEMBER_OF_AN_ADVISORY,
  EC1Activity.JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C46,
  EC1Activity.JSON_PROPERTY_HAS_PRACTICE_POLITICAL_MANDATE,
  EC1Activity.JSON_PROPERTY_HAS_DECLARE_MODIFICATION_JOIN_C1_A,
  EC1Activity.JSON_PROPERTY_HAS_BENEFIT_FROM_ARTISTIC_OR_TECHNIC_ACTIVITIES,
  EC1Activity.JSON_PROPERTY_HAS_COMPLEMENTARY_INDEPENDENT_ACTIVITY,
  EC1Activity.JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C1_C,
  EC1Activity.JSON_PROPERTY_HAS_PRACTICE_COMPLEMENTARY_ACTIVITY_OR_HELP_INDEPENDENT,
  EC1Activity.JSON_PROPERTY_IS_ADMINISTRATOR_OF_COMPANY,
  EC1Activity.JSON_PROPERTY_HAS_REGISTER_AS_INDEPENDENT_COMPLEMENTARY_OR_MAIN
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1Activity {
  public static final String JSON_PROPERTY_HAS_FULL_STUDIES_FROM_DATE = "hasFullStudiesFromDate";
  private LocalDate hasFullStudiesFromDate;

  public static final String JSON_PROPERTY_HAS_FOLLOW_APPRENTICESHIP_OR_WORK_LINKED_TRAINING_FROM_DATE = "hasFollowApprenticeshipOrWorkLinkedTrainingFromDate";
  private LocalDate hasFollowApprenticeshipOrWorkLinkedTrainingFromDate;

  public static final String JSON_PROPERTY_HAS_FOLLOW_A_TRAINING_WITH_AN_INTERNSHIP_FROM_DATE = "hasFollowATrainingWithAnInternshipFromDate";
  private LocalDate hasFollowATrainingWithAnInternshipFromDate;

  public static final String JSON_PROPERTY_HAS_PRACTICE_REMUNERATED_MANDATE_AS_A_MEMBER_OF_AN_ADVISORY = "hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory";
  private Boolean hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory;

  public static final String JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C46 = "isFistTimeIndependentAndJoinC46";
  private Boolean isFistTimeIndependentAndJoinC46;

  public static final String JSON_PROPERTY_HAS_PRACTICE_POLITICAL_MANDATE = "hasPracticePoliticalMandate";
  private Boolean hasPracticePoliticalMandate;

  public static final String JSON_PROPERTY_HAS_DECLARE_MODIFICATION_JOIN_C1_A = "hasDeclareModificationJoinC1A";
  private Boolean hasDeclareModificationJoinC1A;

  public static final String JSON_PROPERTY_HAS_BENEFIT_FROM_ARTISTIC_OR_TECHNIC_ACTIVITIES = "hasBenefitFromArtisticOrTechnicActivities";
  private Boolean hasBenefitFromArtisticOrTechnicActivities;

  public static final String JSON_PROPERTY_HAS_COMPLEMENTARY_INDEPENDENT_ACTIVITY = "hasComplementaryIndependentActivity";
  private Boolean hasComplementaryIndependentActivity;

  public static final String JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C1_C = "isFistTimeIndependentAndJoinC1C";
  private Boolean isFistTimeIndependentAndJoinC1C;

  public static final String JSON_PROPERTY_HAS_PRACTICE_COMPLEMENTARY_ACTIVITY_OR_HELP_INDEPENDENT = "hasPracticeComplementaryActivityOrHelpIndependent";
  private Boolean hasPracticeComplementaryActivityOrHelpIndependent;

  public static final String JSON_PROPERTY_IS_ADMINISTRATOR_OF_COMPANY = "isAdministratorOfCompany";
  private Boolean isAdministratorOfCompany;

  public static final String JSON_PROPERTY_HAS_REGISTER_AS_INDEPENDENT_COMPLEMENTARY_OR_MAIN = "hasRegisterAsIndependentComplementaryOrMain";
  private Boolean hasRegisterAsIndependentComplementaryOrMain;

  public EC1Activity() {
  }

  public EC1Activity hasFullStudiesFromDate(LocalDate hasFullStudiesFromDate) {
    
    this.hasFullStudiesFromDate = hasFullStudiesFromDate;
    return this;
  }

  /**
   * Get hasFullStudiesFromDate
   * @return hasFullStudiesFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_FULL_STUDIES_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getHasFullStudiesFromDate() {
    return hasFullStudiesFromDate;
  }


  @JsonProperty(JSON_PROPERTY_HAS_FULL_STUDIES_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasFullStudiesFromDate(LocalDate hasFullStudiesFromDate) {
    this.hasFullStudiesFromDate = hasFullStudiesFromDate;
  }

  public EC1Activity hasFollowApprenticeshipOrWorkLinkedTrainingFromDate(LocalDate hasFollowApprenticeshipOrWorkLinkedTrainingFromDate) {
    
    this.hasFollowApprenticeshipOrWorkLinkedTrainingFromDate = hasFollowApprenticeshipOrWorkLinkedTrainingFromDate;
    return this;
  }

  /**
   * Get hasFollowApprenticeshipOrWorkLinkedTrainingFromDate
   * @return hasFollowApprenticeshipOrWorkLinkedTrainingFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_FOLLOW_APPRENTICESHIP_OR_WORK_LINKED_TRAINING_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getHasFollowApprenticeshipOrWorkLinkedTrainingFromDate() {
    return hasFollowApprenticeshipOrWorkLinkedTrainingFromDate;
  }


  @JsonProperty(JSON_PROPERTY_HAS_FOLLOW_APPRENTICESHIP_OR_WORK_LINKED_TRAINING_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasFollowApprenticeshipOrWorkLinkedTrainingFromDate(LocalDate hasFollowApprenticeshipOrWorkLinkedTrainingFromDate) {
    this.hasFollowApprenticeshipOrWorkLinkedTrainingFromDate = hasFollowApprenticeshipOrWorkLinkedTrainingFromDate;
  }

  public EC1Activity hasFollowATrainingWithAnInternshipFromDate(LocalDate hasFollowATrainingWithAnInternshipFromDate) {
    
    this.hasFollowATrainingWithAnInternshipFromDate = hasFollowATrainingWithAnInternshipFromDate;
    return this;
  }

  /**
   * Get hasFollowATrainingWithAnInternshipFromDate
   * @return hasFollowATrainingWithAnInternshipFromDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_FOLLOW_A_TRAINING_WITH_AN_INTERNSHIP_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getHasFollowATrainingWithAnInternshipFromDate() {
    return hasFollowATrainingWithAnInternshipFromDate;
  }


  @JsonProperty(JSON_PROPERTY_HAS_FOLLOW_A_TRAINING_WITH_AN_INTERNSHIP_FROM_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasFollowATrainingWithAnInternshipFromDate(LocalDate hasFollowATrainingWithAnInternshipFromDate) {
    this.hasFollowATrainingWithAnInternshipFromDate = hasFollowATrainingWithAnInternshipFromDate;
  }

  public EC1Activity hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory(Boolean hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory) {
    
    this.hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory = hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory;
    return this;
  }

  /**
   * Get hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory
   * @return hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_REMUNERATED_MANDATE_AS_A_MEMBER_OF_AN_ADVISORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasPracticeRemuneratedMandateAsAMemberOfAnAdvisory() {
    return hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory;
  }


  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_REMUNERATED_MANDATE_AS_A_MEMBER_OF_AN_ADVISORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasPracticeRemuneratedMandateAsAMemberOfAnAdvisory(Boolean hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory) {
    this.hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory = hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory;
  }

  public EC1Activity isFistTimeIndependentAndJoinC46(Boolean isFistTimeIndependentAndJoinC46) {
    
    this.isFistTimeIndependentAndJoinC46 = isFistTimeIndependentAndJoinC46;
    return this;
  }

  /**
   * Get isFistTimeIndependentAndJoinC46
   * @return isFistTimeIndependentAndJoinC46
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C46)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsFistTimeIndependentAndJoinC46() {
    return isFistTimeIndependentAndJoinC46;
  }


  @JsonProperty(JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C46)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsFistTimeIndependentAndJoinC46(Boolean isFistTimeIndependentAndJoinC46) {
    this.isFistTimeIndependentAndJoinC46 = isFistTimeIndependentAndJoinC46;
  }

  public EC1Activity hasPracticePoliticalMandate(Boolean hasPracticePoliticalMandate) {
    
    this.hasPracticePoliticalMandate = hasPracticePoliticalMandate;
    return this;
  }

  /**
   * Get hasPracticePoliticalMandate
   * @return hasPracticePoliticalMandate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_POLITICAL_MANDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasPracticePoliticalMandate() {
    return hasPracticePoliticalMandate;
  }


  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_POLITICAL_MANDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasPracticePoliticalMandate(Boolean hasPracticePoliticalMandate) {
    this.hasPracticePoliticalMandate = hasPracticePoliticalMandate;
  }

  public EC1Activity hasDeclareModificationJoinC1A(Boolean hasDeclareModificationJoinC1A) {
    
    this.hasDeclareModificationJoinC1A = hasDeclareModificationJoinC1A;
    return this;
  }

  /**
   * Get hasDeclareModificationJoinC1A
   * @return hasDeclareModificationJoinC1A
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_DECLARE_MODIFICATION_JOIN_C1_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasDeclareModificationJoinC1A() {
    return hasDeclareModificationJoinC1A;
  }


  @JsonProperty(JSON_PROPERTY_HAS_DECLARE_MODIFICATION_JOIN_C1_A)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasDeclareModificationJoinC1A(Boolean hasDeclareModificationJoinC1A) {
    this.hasDeclareModificationJoinC1A = hasDeclareModificationJoinC1A;
  }

  public EC1Activity hasBenefitFromArtisticOrTechnicActivities(Boolean hasBenefitFromArtisticOrTechnicActivities) {
    
    this.hasBenefitFromArtisticOrTechnicActivities = hasBenefitFromArtisticOrTechnicActivities;
    return this;
  }

  /**
   * Get hasBenefitFromArtisticOrTechnicActivities
   * @return hasBenefitFromArtisticOrTechnicActivities
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_BENEFIT_FROM_ARTISTIC_OR_TECHNIC_ACTIVITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasBenefitFromArtisticOrTechnicActivities() {
    return hasBenefitFromArtisticOrTechnicActivities;
  }


  @JsonProperty(JSON_PROPERTY_HAS_BENEFIT_FROM_ARTISTIC_OR_TECHNIC_ACTIVITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasBenefitFromArtisticOrTechnicActivities(Boolean hasBenefitFromArtisticOrTechnicActivities) {
    this.hasBenefitFromArtisticOrTechnicActivities = hasBenefitFromArtisticOrTechnicActivities;
  }

  public EC1Activity hasComplementaryIndependentActivity(Boolean hasComplementaryIndependentActivity) {
    
    this.hasComplementaryIndependentActivity = hasComplementaryIndependentActivity;
    return this;
  }

  /**
   * Get hasComplementaryIndependentActivity
   * @return hasComplementaryIndependentActivity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_COMPLEMENTARY_INDEPENDENT_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasComplementaryIndependentActivity() {
    return hasComplementaryIndependentActivity;
  }


  @JsonProperty(JSON_PROPERTY_HAS_COMPLEMENTARY_INDEPENDENT_ACTIVITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasComplementaryIndependentActivity(Boolean hasComplementaryIndependentActivity) {
    this.hasComplementaryIndependentActivity = hasComplementaryIndependentActivity;
  }

  public EC1Activity isFistTimeIndependentAndJoinC1C(Boolean isFistTimeIndependentAndJoinC1C) {
    
    this.isFistTimeIndependentAndJoinC1C = isFistTimeIndependentAndJoinC1C;
    return this;
  }

  /**
   * Get isFistTimeIndependentAndJoinC1C
   * @return isFistTimeIndependentAndJoinC1C
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C1_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsFistTimeIndependentAndJoinC1C() {
    return isFistTimeIndependentAndJoinC1C;
  }


  @JsonProperty(JSON_PROPERTY_IS_FIST_TIME_INDEPENDENT_AND_JOIN_C1_C)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsFistTimeIndependentAndJoinC1C(Boolean isFistTimeIndependentAndJoinC1C) {
    this.isFistTimeIndependentAndJoinC1C = isFistTimeIndependentAndJoinC1C;
  }

  public EC1Activity hasPracticeComplementaryActivityOrHelpIndependent(Boolean hasPracticeComplementaryActivityOrHelpIndependent) {
    
    this.hasPracticeComplementaryActivityOrHelpIndependent = hasPracticeComplementaryActivityOrHelpIndependent;
    return this;
  }

  /**
   * Get hasPracticeComplementaryActivityOrHelpIndependent
   * @return hasPracticeComplementaryActivityOrHelpIndependent
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_COMPLEMENTARY_ACTIVITY_OR_HELP_INDEPENDENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasPracticeComplementaryActivityOrHelpIndependent() {
    return hasPracticeComplementaryActivityOrHelpIndependent;
  }


  @JsonProperty(JSON_PROPERTY_HAS_PRACTICE_COMPLEMENTARY_ACTIVITY_OR_HELP_INDEPENDENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasPracticeComplementaryActivityOrHelpIndependent(Boolean hasPracticeComplementaryActivityOrHelpIndependent) {
    this.hasPracticeComplementaryActivityOrHelpIndependent = hasPracticeComplementaryActivityOrHelpIndependent;
  }

  public EC1Activity isAdministratorOfCompany(Boolean isAdministratorOfCompany) {
    
    this.isAdministratorOfCompany = isAdministratorOfCompany;
    return this;
  }

  /**
   * Get isAdministratorOfCompany
   * @return isAdministratorOfCompany
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_ADMINISTRATOR_OF_COMPANY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsAdministratorOfCompany() {
    return isAdministratorOfCompany;
  }


  @JsonProperty(JSON_PROPERTY_IS_ADMINISTRATOR_OF_COMPANY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsAdministratorOfCompany(Boolean isAdministratorOfCompany) {
    this.isAdministratorOfCompany = isAdministratorOfCompany;
  }

  public EC1Activity hasRegisterAsIndependentComplementaryOrMain(Boolean hasRegisterAsIndependentComplementaryOrMain) {
    
    this.hasRegisterAsIndependentComplementaryOrMain = hasRegisterAsIndependentComplementaryOrMain;
    return this;
  }

  /**
   * Get hasRegisterAsIndependentComplementaryOrMain
   * @return hasRegisterAsIndependentComplementaryOrMain
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_REGISTER_AS_INDEPENDENT_COMPLEMENTARY_OR_MAIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasRegisterAsIndependentComplementaryOrMain() {
    return hasRegisterAsIndependentComplementaryOrMain;
  }


  @JsonProperty(JSON_PROPERTY_HAS_REGISTER_AS_INDEPENDENT_COMPLEMENTARY_OR_MAIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasRegisterAsIndependentComplementaryOrMain(Boolean hasRegisterAsIndependentComplementaryOrMain) {
    this.hasRegisterAsIndependentComplementaryOrMain = hasRegisterAsIndependentComplementaryOrMain;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1Activity ec1Activity = (EC1Activity) o;
    return Objects.equals(this.hasFullStudiesFromDate, ec1Activity.hasFullStudiesFromDate) &&
        Objects.equals(this.hasFollowApprenticeshipOrWorkLinkedTrainingFromDate, ec1Activity.hasFollowApprenticeshipOrWorkLinkedTrainingFromDate) &&
        Objects.equals(this.hasFollowATrainingWithAnInternshipFromDate, ec1Activity.hasFollowATrainingWithAnInternshipFromDate) &&
        Objects.equals(this.hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory, ec1Activity.hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory) &&
        Objects.equals(this.isFistTimeIndependentAndJoinC46, ec1Activity.isFistTimeIndependentAndJoinC46) &&
        Objects.equals(this.hasPracticePoliticalMandate, ec1Activity.hasPracticePoliticalMandate) &&
        Objects.equals(this.hasDeclareModificationJoinC1A, ec1Activity.hasDeclareModificationJoinC1A) &&
        Objects.equals(this.hasBenefitFromArtisticOrTechnicActivities, ec1Activity.hasBenefitFromArtisticOrTechnicActivities) &&
        Objects.equals(this.hasComplementaryIndependentActivity, ec1Activity.hasComplementaryIndependentActivity) &&
        Objects.equals(this.isFistTimeIndependentAndJoinC1C, ec1Activity.isFistTimeIndependentAndJoinC1C) &&
        Objects.equals(this.hasPracticeComplementaryActivityOrHelpIndependent, ec1Activity.hasPracticeComplementaryActivityOrHelpIndependent) &&
        Objects.equals(this.isAdministratorOfCompany, ec1Activity.isAdministratorOfCompany) &&
        Objects.equals(this.hasRegisterAsIndependentComplementaryOrMain, ec1Activity.hasRegisterAsIndependentComplementaryOrMain);
  }

  @Override
  public int hashCode() {
    return Objects.hash(hasFullStudiesFromDate, hasFollowApprenticeshipOrWorkLinkedTrainingFromDate, hasFollowATrainingWithAnInternshipFromDate, hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory, isFistTimeIndependentAndJoinC46, hasPracticePoliticalMandate, hasDeclareModificationJoinC1A, hasBenefitFromArtisticOrTechnicActivities, hasComplementaryIndependentActivity, isFistTimeIndependentAndJoinC1C, hasPracticeComplementaryActivityOrHelpIndependent, isAdministratorOfCompany, hasRegisterAsIndependentComplementaryOrMain);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1Activity {\n");
    sb.append("    hasFullStudiesFromDate: ").append(toIndentedString(hasFullStudiesFromDate)).append("\n");
    sb.append("    hasFollowApprenticeshipOrWorkLinkedTrainingFromDate: ").append(toIndentedString(hasFollowApprenticeshipOrWorkLinkedTrainingFromDate)).append("\n");
    sb.append("    hasFollowATrainingWithAnInternshipFromDate: ").append(toIndentedString(hasFollowATrainingWithAnInternshipFromDate)).append("\n");
    sb.append("    hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory: ").append(toIndentedString(hasPracticeRemuneratedMandateAsAMemberOfAnAdvisory)).append("\n");
    sb.append("    isFistTimeIndependentAndJoinC46: ").append(toIndentedString(isFistTimeIndependentAndJoinC46)).append("\n");
    sb.append("    hasPracticePoliticalMandate: ").append(toIndentedString(hasPracticePoliticalMandate)).append("\n");
    sb.append("    hasDeclareModificationJoinC1A: ").append(toIndentedString(hasDeclareModificationJoinC1A)).append("\n");
    sb.append("    hasBenefitFromArtisticOrTechnicActivities: ").append(toIndentedString(hasBenefitFromArtisticOrTechnicActivities)).append("\n");
    sb.append("    hasComplementaryIndependentActivity: ").append(toIndentedString(hasComplementaryIndependentActivity)).append("\n");
    sb.append("    isFistTimeIndependentAndJoinC1C: ").append(toIndentedString(isFistTimeIndependentAndJoinC1C)).append("\n");
    sb.append("    hasPracticeComplementaryActivityOrHelpIndependent: ").append(toIndentedString(hasPracticeComplementaryActivityOrHelpIndependent)).append("\n");
    sb.append("    isAdministratorOfCompany: ").append(toIndentedString(isAdministratorOfCompany)).append("\n");
    sb.append("    hasRegisterAsIndependentComplementaryOrMain: ").append(toIndentedString(hasRegisterAsIndependentComplementaryOrMain)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

