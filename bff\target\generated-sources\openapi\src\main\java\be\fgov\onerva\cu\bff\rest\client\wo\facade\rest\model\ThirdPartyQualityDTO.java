/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * The default value for a citizen is SOCIAL_INSURED, and the default value for a company is EMPLOYER.
 */
public enum ThirdPartyQualityDTO {
  
  PAYMENT_ORGANISATION("PAYMENT_ORGANISATION"),
  
  ESTABLISHMENT_UNIT("ESTABLISHMENT_UNIT"),
  
  <PERSON><PERSON><PERSON><PERSON>Y<PERSON>("EMPLOYER"),
  
  W<PERSON><PERSON><PERSON>("WORKER"),
  
  FAMILY("FAMILY"),
  
  SOCIAL_INSURED("SOCIAL_INSURED");

  private String value;

  ThirdPartyQualityDTO(String value) {
    this.value = value;
  }

  @JsonValue
  public String getValue() {
    return value;
  }

  @Override
  public String toString() {
    return String.valueOf(value);
  }

  @JsonCreator
  public static ThirdPartyQualityDTO fromValue(String value) {
    for (ThirdPartyQualityDTO b : ThirdPartyQualityDTO.values()) {
      if (b.value.equals(value)) {
        return b;
      }
    }
    throw new IllegalArgumentException("Unexpected value '" + value + "'");
  }
}

