/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * UserCriteriaDTO
 */
@JsonPropertyOrder({
  UserCriteriaDTO.JSON_PROPERTY_OPERATOR_CODE,
  UserCriteriaDTO.JSON_PROPERTY_USERNAME,
  UserCriteriaDTO.JSON_PROPERTY_INSS
})
@JsonTypeName("UserCriteria")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class UserCriteriaDTO {
  public static final String JSON_PROPERTY_OPERATOR_CODE = "operatorCode";
  private String operatorCode;

  public static final String JSON_PROPERTY_USERNAME = "username";
  private String username;

  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public UserCriteriaDTO() {
  }

  public UserCriteriaDTO operatorCode(String operatorCode) {
    
    this.operatorCode = operatorCode;
    return this;
  }

  /**
   * Get operatorCode
   * @return operatorCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOperatorCode() {
    return operatorCode;
  }


  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperatorCode(String operatorCode) {
    this.operatorCode = operatorCode;
  }

  public UserCriteriaDTO username(String username) {
    
    this.username = username;
    return this;
  }

  /**
   * Get username
   * @return username
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsername() {
    return username;
  }


  @JsonProperty(JSON_PROPERTY_USERNAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsername(String username) {
    this.username = username;
  }

  public UserCriteriaDTO inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UserCriteriaDTO userCriteria = (UserCriteriaDTO) o;
    return Objects.equals(this.operatorCode, userCriteria.operatorCode) &&
        Objects.equals(this.username, userCriteria.username) &&
        Objects.equals(this.inss, userCriteria.inss);
  }

  @Override
  public int hashCode() {
    return Objects.hash(operatorCode, username, inss);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UserCriteriaDTO {\n");
    sb.append("    operatorCode: ").append(toIndentedString(operatorCode)).append("\n");
    sb.append("    username: ").append(toIndentedString(username)).append("\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

