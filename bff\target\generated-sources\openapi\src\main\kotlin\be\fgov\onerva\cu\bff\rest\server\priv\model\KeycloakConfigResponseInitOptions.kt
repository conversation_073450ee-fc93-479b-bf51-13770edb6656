package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param redirectUri The uri where keycloak must return after login
 * @param checkLoginIframe 
 * @param onLoad 
 */
data class KeycloakConfigResponseInitOptions(

    @Schema(example = "http://localhost:4300/", required = true, description = "The uri where keycloak must return after login")
    @get:JsonProperty("redirectUri", required = true) val redirectUri: kotlin.String,

    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("checkLoginIframe", required = true) val checkLoginIframe: kotlin.Boolean,

    @Schema(example = "check-sso", required = true, description = "")
    @get:JsonProperty("onLoad", required = true) val onLoad: kotlin.String
    ) {

}

