/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param authorized 
 * @param effectiveDate The effective date of the union contribution (format YYYY-MM-DD)
 */


data class UpdateUnionContributionRequest (

    @get:JsonProperty("authorized")
    val authorized: kotlin.Boolean? = null,

    /* The effective date of the union contribution (format YYYY-MM-DD) */
    @get:JsonProperty("effectiveDate")
    val effectiveDate: java.time.LocalDate? = null

) {


}

