/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SleepRequestDTO
 */
@JsonPropertyOrder({
  SleepRequestDTO.JSON_PROPERTY_REASON,
  SleepRequestDTO.JSON_PROPERTY_WAKING_UP_DATE
})
@JsonTypeName("SleepRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SleepRequestDTO {
  public static final String JSON_PROPERTY_REASON = "reason";
  private String reason;

  public static final String JSON_PROPERTY_WAKING_UP_DATE = "wakingUpDate";
  private LocalDate wakingUpDate;

  public SleepRequestDTO() {
  }

  public SleepRequestDTO reason(String reason) {
    
    this.reason = reason;
    return this;
  }

  /**
   * Reason why you want to change the status of the task.
   * @return reason
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REASON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getReason() {
    return reason;
  }


  @JsonProperty(JSON_PROPERTY_REASON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setReason(String reason) {
    this.reason = reason;
  }

  public SleepRequestDTO wakingUpDate(LocalDate wakingUpDate) {
    
    this.wakingUpDate = wakingUpDate;
    return this;
  }

  /**
   * This is the date when you want to wake the task up. At that date the task will automatically put back into treatment state.
   * @return wakingUpDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WAKING_UP_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getWakingUpDate() {
    return wakingUpDate;
  }


  @JsonProperty(JSON_PROPERTY_WAKING_UP_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWakingUpDate(LocalDate wakingUpDate) {
    this.wakingUpDate = wakingUpDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SleepRequestDTO sleepRequest = (SleepRequestDTO) o;
    return Objects.equals(this.reason, sleepRequest.reason) &&
        Objects.equals(this.wakingUpDate, sleepRequest.wakingUpDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(reason, wakingUpDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SleepRequestDTO {\n");
    sb.append("    reason: ").append(toIndentedString(reason)).append("\n");
    sb.append("    wakingUpDate: ").append(toIndentedString(wakingUpDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

