package be.fgov.onerva.cu.backend.application.port.`in`

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.backend.application.domain.PersonUpdated
import be.fgov.onerva.cu.backend.application.port.out.SyncFollowUpPort

@Service
class PersonUpdatedService(val syncFollowUpPort: SyncFollowUpPort) : PersonUpdatedUseCase {

    override fun receivedPersonUpdated(personUpdated: PersonUpdated) {
        syncFollowUpPort.updateSyncFollowUp(personUpdated.correlationId, personUpdated.success)
    }
}