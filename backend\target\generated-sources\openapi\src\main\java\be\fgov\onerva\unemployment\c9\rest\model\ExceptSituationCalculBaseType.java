/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ExceptSituationCalculBaseType
 */
@JsonPropertyOrder({
  ExceptSituationCalculBaseType.JSON_PROPERTY_EXCEPT_SITUATION_MEAN_WORKING_HOURS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ExceptSituationCalculBaseType {
  public static final String JSON_PROPERTY_EXCEPT_SITUATION_MEAN_WORKING_HOURS = "exceptSituationMeanWorkingHours";
  private String exceptSituationMeanWorkingHours;

  public ExceptSituationCalculBaseType() {
  }

  public ExceptSituationCalculBaseType exceptSituationMeanWorkingHours(String exceptSituationMeanWorkingHours) {
    
    this.exceptSituationMeanWorkingHours = exceptSituationMeanWorkingHours;
    return this;
  }

  /**
   * Get exceptSituationMeanWorkingHours
   * @return exceptSituationMeanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EXCEPT_SITUATION_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getExceptSituationMeanWorkingHours() {
    return exceptSituationMeanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_EXCEPT_SITUATION_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setExceptSituationMeanWorkingHours(String exceptSituationMeanWorkingHours) {
    this.exceptSituationMeanWorkingHours = exceptSituationMeanWorkingHours;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ExceptSituationCalculBaseType exceptSituationCalculBaseType = (ExceptSituationCalculBaseType) o;
    return Objects.equals(this.exceptSituationMeanWorkingHours, exceptSituationCalculBaseType.exceptSituationMeanWorkingHours);
  }

  @Override
  public int hashCode() {
    return Objects.hash(exceptSituationMeanWorkingHours);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ExceptSituationCalculBaseType {\n");
    sb.append("    exceptSituationMeanWorkingHours: ").append(toIndentedString(exceptSituationMeanWorkingHours)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

