/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CoordinatesInformantType
 */
@JsonPropertyOrder({
  CoordinatesInformantType.JSON_PROPERTY_USER_QUALITY,
  CoordinatesInformantType.JSON_PROPERTY_DENOMINATION
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CoordinatesInformantType {
  public static final String JSON_PROPERTY_USER_QUALITY = "userQuality";
  private String userQuality;

  public static final String JSON_PROPERTY_DENOMINATION = "denomination";
  private String denomination;

  public CoordinatesInformantType() {
  }

  public CoordinatesInformantType userQuality(String userQuality) {
    
    this.userQuality = userQuality;
    return this;
  }

  /**
   * Get userQuality
   * @return userQuality
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_USER_QUALITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getUserQuality() {
    return userQuality;
  }


  @JsonProperty(JSON_PROPERTY_USER_QUALITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setUserQuality(String userQuality) {
    this.userQuality = userQuality;
  }

  public CoordinatesInformantType denomination(String denomination) {
    
    this.denomination = denomination;
    return this;
  }

  /**
   * Get denomination
   * @return denomination
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDenomination() {
    return denomination;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDenomination(String denomination) {
    this.denomination = denomination;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CoordinatesInformantType coordinatesInformantType = (CoordinatesInformantType) o;
    return Objects.equals(this.userQuality, coordinatesInformantType.userQuality) &&
        Objects.equals(this.denomination, coordinatesInformantType.denomination);
  }

  @Override
  public int hashCode() {
    return Objects.hash(userQuality, denomination);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CoordinatesInformantType {\n");
    sb.append("    userQuality: ").append(toIndentedString(userQuality)).append("\n");
    sb.append("    denomination: ").append(toIndentedString(denomination)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

