package be.fgov.onerva.cu.backend.adapter.out.external.configuration

import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import be.fgov.onerva.cu.backend.application.port.out.AppConfigurationPort

@Component
class AppConfigurationAdapter(@Value("\${app.allowMultipleC9s}") private val allowMultipleC9sValue: Boolean) :
    AppConfigurationPort {

    override val allowMultipleC9s: Boolean
        get() = this.allowMultipleC9sValue
}