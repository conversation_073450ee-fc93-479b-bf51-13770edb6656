/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class HistoricalInformationApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun getBarema(requestId: java.util.UUID): HistoricalBaremaResponse {
        val result = getBaremaWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getBaremaWithHttpInfo(requestId: java.util.UUID): ResponseEntity<HistoricalBaremaResponse> {
        val localVariableConfig = getBaremaRequestConfig(requestId = requestId)
        return request<Unit, HistoricalBaremaResponse>(
            localVariableConfig
        )
    }

    fun getBaremaRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/historical/barema",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenAuthenticSources(requestId: java.util.UUID): HistoricalCitizenAuthenticSourcesResponse {
        val result = getHistoricalCitizenAuthenticSourcesWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenAuthenticSourcesWithHttpInfo(requestId: java.util.UUID): ResponseEntity<HistoricalCitizenAuthenticSourcesResponse> {
        val localVariableConfig = getHistoricalCitizenAuthenticSourcesRequestConfig(requestId = requestId)
        return request<Unit, HistoricalCitizenAuthenticSourcesResponse>(
            localVariableConfig
        )
    }

    fun getHistoricalCitizenAuthenticSourcesRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/historical/citizen-information/AUTHENTIC_SOURCES",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenC1(requestId: java.util.UUID): HistoricalCitizenC1Response {
        val result = getHistoricalCitizenC1WithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenC1WithHttpInfo(requestId: java.util.UUID): ResponseEntity<HistoricalCitizenC1Response> {
        val localVariableConfig = getHistoricalCitizenC1RequestConfig(requestId = requestId)
        return request<Unit, HistoricalCitizenC1Response>(
            localVariableConfig
        )
    }

    fun getHistoricalCitizenC1RequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/historical/citizen-information/C1",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenOnem(requestId: java.util.UUID): HistoricalCitizenOnemResponse {
        val result = getHistoricalCitizenOnemWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getHistoricalCitizenOnemWithHttpInfo(requestId: java.util.UUID): ResponseEntity<HistoricalCitizenOnemResponse> {
        val localVariableConfig = getHistoricalCitizenOnemRequestConfig(requestId = requestId)
        return request<Unit, HistoricalCitizenOnemResponse>(
            localVariableConfig
        )
    }

    fun getHistoricalCitizenOnemRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/historical/citizen-information/ONEM",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
