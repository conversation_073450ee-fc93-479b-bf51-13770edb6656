/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.CommentWorkGridType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505WorkGridRestInnerAnyOf
 */
@JsonPropertyOrder({
  Wech505WorkGridRestInnerAnyOf.JSON_PROPERTY_COMMENT_WORK_GRID
})
@JsonTypeName("Wech505WorkGrid_rest_inner_anyOf")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505WorkGridRestInnerAnyOf {
  public static final String JSON_PROPERTY_COMMENT_WORK_GRID = "CommentWorkGrid";
  private CommentWorkGridType commentWorkGrid;

  public Wech505WorkGridRestInnerAnyOf() {
  }

  public Wech505WorkGridRestInnerAnyOf commentWorkGrid(CommentWorkGridType commentWorkGrid) {
    
    this.commentWorkGrid = commentWorkGrid;
    return this;
  }

  /**
   * Get commentWorkGrid
   * @return commentWorkGrid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENT_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CommentWorkGridType getCommentWorkGrid() {
    return commentWorkGrid;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT_WORK_GRID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommentWorkGrid(CommentWorkGridType commentWorkGrid) {
    this.commentWorkGrid = commentWorkGrid;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505WorkGridRestInnerAnyOf wech505WorkGridRestInnerAnyOf = (Wech505WorkGridRestInnerAnyOf) o;
    return Objects.equals(this.commentWorkGrid, wech505WorkGridRestInnerAnyOf.commentWorkGrid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(commentWorkGrid);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505WorkGridRestInnerAnyOf {\n");
    sb.append("    commentWorkGrid: ").append(toIndentedString(commentWorkGrid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

