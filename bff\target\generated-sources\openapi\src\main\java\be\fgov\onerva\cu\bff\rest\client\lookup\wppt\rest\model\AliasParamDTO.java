/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AliasParamDTO
 */
@JsonPropertyOrder({
  AliasParamDTO.JSON_PROPERTY_ALIAS,
  AliasParamDTO.JSON_PROPERTY_ASSOCIATION_PATH
})
@JsonTypeName("AliasParam")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class AliasParamDTO {
  public static final String JSON_PROPERTY_ALIAS = "alias";
  private String alias;

  public static final String JSON_PROPERTY_ASSOCIATION_PATH = "associationPath";
  private String associationPath;

  public AliasParamDTO() {
  }

  public AliasParamDTO alias(String alias) {
    
    this.alias = alias;
    return this;
  }

  /**
   * Get alias
   * @return alias
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ALIAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAlias() {
    return alias;
  }


  @JsonProperty(JSON_PROPERTY_ALIAS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAlias(String alias) {
    this.alias = alias;
  }

  public AliasParamDTO associationPath(String associationPath) {
    
    this.associationPath = associationPath;
    return this;
  }

  /**
   * Get associationPath
   * @return associationPath
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASSOCIATION_PATH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAssociationPath() {
    return associationPath;
  }


  @JsonProperty(JSON_PROPERTY_ASSOCIATION_PATH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAssociationPath(String associationPath) {
    this.associationPath = associationPath;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AliasParamDTO aliasParam = (AliasParamDTO) o;
    return Objects.equals(this.alias, aliasParam.alias) &&
        Objects.equals(this.associationPath, aliasParam.associationPath);
  }

  @Override
  public int hashCode() {
    return Objects.hash(alias, associationPath);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AliasParamDTO {\n");
    sb.append("    alias: ").append(toIndentedString(alias)).append("\n");
    sb.append("    associationPath: ").append(toIndentedString(associationPath)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

