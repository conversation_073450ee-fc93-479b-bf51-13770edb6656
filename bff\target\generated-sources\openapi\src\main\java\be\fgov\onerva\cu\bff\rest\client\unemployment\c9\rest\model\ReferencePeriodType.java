/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ReferencePeriodType
 */
@JsonPropertyOrder({
  ReferencePeriodType.JSON_PROPERTY_REF_STARTING_DATE,
  ReferencePeriodType.JSON_PROPERTY_REF_ENDING_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ReferencePeriodType {
  public static final String JSON_PROPERTY_REF_STARTING_DATE = "refStartingDate";
  private LocalDate refStartingDate;

  public static final String JSON_PROPERTY_REF_ENDING_DATE = "refEndingDate";
  private LocalDate refEndingDate;

  public ReferencePeriodType() {
  }

  public ReferencePeriodType refStartingDate(LocalDate refStartingDate) {
    
    this.refStartingDate = refStartingDate;
    return this;
  }

  /**
   * Get refStartingDate
   * @return refStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getRefStartingDate() {
    return refStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_REF_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefStartingDate(LocalDate refStartingDate) {
    this.refStartingDate = refStartingDate;
  }

  public ReferencePeriodType refEndingDate(LocalDate refEndingDate) {
    
    this.refEndingDate = refEndingDate;
    return this;
  }

  /**
   * Get refEndingDate
   * @return refEndingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getRefEndingDate() {
    return refEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_REF_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefEndingDate(LocalDate refEndingDate) {
    this.refEndingDate = refEndingDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReferencePeriodType referencePeriodType = (ReferencePeriodType) o;
    return Objects.equals(this.refStartingDate, referencePeriodType.refStartingDate) &&
        Objects.equals(this.refEndingDate, referencePeriodType.refEndingDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(refStartingDate, refEndingDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReferencePeriodType {\n");
    sb.append("    refStartingDate: ").append(toIndentedString(refStartingDate)).append("\n");
    sb.append("    refEndingDate: ").append(toIndentedString(refEndingDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

