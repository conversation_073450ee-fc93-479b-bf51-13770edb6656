package be.fgov.onerva.cu.bff.controller

import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import org.junit.jupiter.api.Assertions
import java.net.URI
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito
import org.mockito.junit.jupiter.MockitoExtension
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.context.request.WebRequest
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException

@ExtendWith(MockitoExtension::class)
internal class GlobalExceptionHandlerTest {
    @InjectMocks
    private val globalExceptionHandler: GlobalExceptionHandler? = null

    @Mock
    private lateinit var webRequest: WebRequest

    @Test
    fun handleClientError_BadRequest_ReturnsBadRequestWithProblemDetail() {
        // Arrange
        val problemDetail = ProblemDetail.forStatus(HttpStatus.BAD_REQUEST)
        problemDetail.title = "Bad Request"
        problemDetail.detail = "Invalid input"
        problemDetail.instance = URI.create("/test")

        val exception = Mockito.mock(
            HttpClientErrorException.BadRequest::class.java
        )
        Mockito.`when`(exception.statusCode).thenReturn(HttpStatus.BAD_REQUEST)
        Mockito.`when`(exception.getResponseBodyAs(ProblemDetail::class.java)).thenReturn(problemDetail)

        // Act
        val response = globalExceptionHandler!!.handleClientError(exception)

        // Assert
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)
        assertThat(response.body).isNotNull
        assertThat(response.body!!.title).isEqualTo("Bad Request")
        assertThat(response.body!!.detail).isEqualTo("Invalid input")
        assertThat(response.body!!.instance).isEqualTo(URI.create("/test"))
    }

    @Test
    fun handleClientError_NotFound_ReturnsNotFoundWithProblemDetail() {
        // Arrange
        val problemDetail = ProblemDetail.forStatus(HttpStatus.NOT_FOUND)
        problemDetail.title = "Not Found"
        problemDetail.detail = "Resource not found"
        problemDetail.instance = URI.create("/test/123")

        val exception = Mockito.mock(
            HttpClientErrorException.NotFound::class.java
        )
        Mockito.`when`(exception.statusCode).thenReturn(HttpStatus.NOT_FOUND)
        Mockito.`when`(exception.getResponseBodyAs(ProblemDetail::class.java)).thenReturn(problemDetail)

        // Act
        val response = globalExceptionHandler!!.handleClientError(exception)

        // Assert
        assertThat(response.statusCode).isEqualTo(HttpStatus.NOT_FOUND)
        assertThat(response.body).isNotNull
        assertThat(response.body!!.title).isEqualTo("Not Found")
        assertThat(response.body!!.detail).isEqualTo("Resource not found")
        assertThat(response.body!!.instance).isEqualTo(URI.create("/test/123"))
    }

    @Test
    fun `handleCitizenNotFoundException should return NOT_FOUND with appropriate problem details`() {
        // Given
        val errorMessage = "Citizen with ID 12345 not found"
        val exception = CitizenNotFoundException(errorMessage)
        val webRequest = Mockito.mock(WebRequest::class.java)

        // When
        val responseEntity = globalExceptionHandler!!.handleCitizenNotFoundException(exception, webRequest)

        // Then
        assertThat(responseEntity.statusCode).isEqualTo(HttpStatus.NOT_FOUND)

        val problemDetail = responseEntity.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.title).isEqualTo("Request Not Found")
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.NOT_FOUND.value())
    }

    @Test
    fun handleInformationNotFoundException_ReturnsNotFoundWithProblemDetail() {
        // Arrange
        val errorMessage = "Unemployment office not found for C9 ID: 12345"
        val exception = C9NotFoundException(errorMessage)

        // Act
        val response = globalExceptionHandler!!.handleInformationNotFoundException(exception, webRequest)

        // Assert
        Assertions.assertEquals(HttpStatus.NOT_FOUND, response.statusCode)
        Assertions.assertNotNull(response.body)
        Assertions.assertEquals("Information Not Found", response.body!!.title)
        Assertions.assertEquals(errorMessage, response.body!!.detail)
    }
}