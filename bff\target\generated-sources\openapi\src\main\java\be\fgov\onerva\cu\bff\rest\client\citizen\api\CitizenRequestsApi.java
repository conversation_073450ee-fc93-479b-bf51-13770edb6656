package be.fgov.onerva.cu.bff.rest.client.citizen.api;

import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.BaseApi;

import java.math.BigDecimal;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenRequestPageDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenRequestsApi extends BaseApi {

    public CitizenRequestsApi() {
        super(new ApiClient());
    }

    public CitizenRequestsApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Get a citizen request by id
     * <p><b>200</b> - CitizenRequest
     * @param id  (required)
     * @return CitizenRequestDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenRequestDTO getById(BigDecimal id) throws RestClientException {
        return getByIdWithHttpInfo(id).getBody();
    }

    /**
     * 
     * Get a citizen request by id
     * <p><b>200</b> - CitizenRequest
     * @param id  (required)
     * @return ResponseEntity&lt;CitizenRequestDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenRequestDTO> getByIdWithHttpInfo(BigDecimal id) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'id' when calling getById");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id", id);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenRequestDTO> localReturnType = new ParameterizedTypeReference<CitizenRequestDTO>() {};
        return apiClient.invokeAPI("/citizen/requests/{id}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Search citizens requests based on a query
     * <p><b>200</b> - CitizenRequests
     * @param query Uses RSQL syntax to search through citizens requests (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;) (optional)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @param sort The format &#39;property,property(,ASC|DESC)(,IgnoreCase)&#39;.  Use multiple sort parameters if you want to switch direction or case sensitivity. Example: &#39;sort&#x3D;firstname&amp;sort&#x3D;lastname,asc&amp;sort&#x3D;city,ignorecase&#39; (optional)
     * @return CitizenRequestPageDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenRequestPageDTO searchCitizenRequests(String query, Integer pageNumber, Integer pageSize, String sort) throws RestClientException {
        return searchCitizenRequestsWithHttpInfo(query, pageNumber, pageSize, sort).getBody();
    }

    /**
     * 
     * Search citizens requests based on a query
     * <p><b>200</b> - CitizenRequests
     * @param query Uses RSQL syntax to search through citizens requests (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;) (optional)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @param sort The format &#39;property,property(,ASC|DESC)(,IgnoreCase)&#39;.  Use multiple sort parameters if you want to switch direction or case sensitivity. Example: &#39;sort&#x3D;firstname&amp;sort&#x3D;lastname,asc&amp;sort&#x3D;city,ignorecase&#39; (optional)
     * @return ResponseEntity&lt;CitizenRequestPageDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenRequestPageDTO> searchCitizenRequestsWithHttpInfo(String query, Integer pageNumber, Integer pageSize, String sort) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "query", query));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageNumber", pageNumber));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "sort", sort));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenRequestPageDTO> localReturnType = new ParameterizedTypeReference<CitizenRequestPageDTO>() {};
        return apiClient.invokeAPI("/citizen/requests", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
