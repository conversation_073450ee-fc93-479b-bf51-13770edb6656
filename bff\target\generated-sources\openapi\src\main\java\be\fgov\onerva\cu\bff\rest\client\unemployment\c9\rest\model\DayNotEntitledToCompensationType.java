/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * DayNotEntitledToCompensationType
 */
@JsonPropertyOrder({
  DayNotEntitledToCompensationType.JSON_PROPERTY_PUBLIC_HOLIDAY_INDICATOR,
  DayNotEntitledToCompensationType.JSON_PROPERTY_PUBLIC_HOLIDAY_REPLACEMENT_DAY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class DayNotEntitledToCompensationType {
  public static final String JSON_PROPERTY_PUBLIC_HOLIDAY_INDICATOR = "publicHolidayIndicator";
  private LocalDate publicHolidayIndicator;

  public static final String JSON_PROPERTY_PUBLIC_HOLIDAY_REPLACEMENT_DAY = "publicHolidayReplacementDay";
  private LocalDate publicHolidayReplacementDay;

  public DayNotEntitledToCompensationType() {
  }

  public DayNotEntitledToCompensationType publicHolidayIndicator(LocalDate publicHolidayIndicator) {
    
    this.publicHolidayIndicator = publicHolidayIndicator;
    return this;
  }

  /**
   * Get publicHolidayIndicator
   * @return publicHolidayIndicator
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_PUBLIC_HOLIDAY_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getPublicHolidayIndicator() {
    return publicHolidayIndicator;
  }


  @JsonProperty(JSON_PROPERTY_PUBLIC_HOLIDAY_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setPublicHolidayIndicator(LocalDate publicHolidayIndicator) {
    this.publicHolidayIndicator = publicHolidayIndicator;
  }

  public DayNotEntitledToCompensationType publicHolidayReplacementDay(LocalDate publicHolidayReplacementDay) {
    
    this.publicHolidayReplacementDay = publicHolidayReplacementDay;
    return this;
  }

  /**
   * Get publicHolidayReplacementDay
   * @return publicHolidayReplacementDay
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_PUBLIC_HOLIDAY_REPLACEMENT_DAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getPublicHolidayReplacementDay() {
    return publicHolidayReplacementDay;
  }


  @JsonProperty(JSON_PROPERTY_PUBLIC_HOLIDAY_REPLACEMENT_DAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setPublicHolidayReplacementDay(LocalDate publicHolidayReplacementDay) {
    this.publicHolidayReplacementDay = publicHolidayReplacementDay;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DayNotEntitledToCompensationType dayNotEntitledToCompensationType = (DayNotEntitledToCompensationType) o;
    return Objects.equals(this.publicHolidayIndicator, dayNotEntitledToCompensationType.publicHolidayIndicator) &&
        Objects.equals(this.publicHolidayReplacementDay, dayNotEntitledToCompensationType.publicHolidayReplacementDay);
  }

  @Override
  public int hashCode() {
    return Objects.hash(publicHolidayIndicator, publicHolidayReplacementDay);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DayNotEntitledToCompensationType {\n");
    sb.append("    publicHolidayIndicator: ").append(toIndentedString(publicHolidayIndicator)).append("\n");
    sb.append("    publicHolidayReplacementDay: ").append(toIndentedString(publicHolidayReplacementDay)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

