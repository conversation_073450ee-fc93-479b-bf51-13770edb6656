/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.FlagsmithConfigResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.KeycloakConfigResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class ConfigApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun getFlagsmithConfig(): FlagsmithConfigResponse {
        val result = getFlagsmithConfigWithHttpInfo()
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getFlagsmithConfigWithHttpInfo(): ResponseEntity<FlagsmithConfigResponse> {
        val localVariableConfig = getFlagsmithConfigRequestConfig()
        return request<Unit, FlagsmithConfigResponse>(
            localVariableConfig
        )
    }

    fun getFlagsmithConfigRequestConfig() : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/config/flagsmith",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getKeycloakConfig(): KeycloakConfigResponse {
        val result = getKeycloakConfigWithHttpInfo()
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getKeycloakConfigWithHttpInfo(): ResponseEntity<KeycloakConfigResponse> {
        val localVariableConfig = getKeycloakConfigRequestConfig()
        return request<Unit, KeycloakConfigResponse>(
            localVariableConfig
        )
    }

    fun getKeycloakConfigRequestConfig() : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/config/keycloak",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
