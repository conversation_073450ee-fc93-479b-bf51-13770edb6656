package be.fgov.onerva.cu.bff.controller

import be.fgov.onerva.cu.bff.lookup.LookupService
import be.fgov.onerva.cu.bff.mapper.toCityResponse
import be.fgov.onerva.cu.bff.mapper.toCountryResponse
import be.fgov.onerva.cu.bff.mapper.toNationalityResponse
import be.fgov.onerva.cu.bff.rest.server.priv.api.LookupApi
import be.fgov.onerva.cu.bff.rest.server.priv.model.CityResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CountryResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.NationalityResponse
import org.springframework.http.ResponseEntity
import org.springframework.http.ResponseEntity.ok
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/bff")
class LookupController(val lookupService: LookupService) : LookupApi {
    override fun searchCity(searchQuery: String?): ResponseEntity<List<CityResponse>> {
        return ok(
            lookupService.lookupCity(searchQuery)
                .map { it.toCityResponse() }.toList()
        )
    }

    override fun searchCountry(searchQuery: String?): ResponseEntity<List<CountryResponse>> =
        ok(
            lookupService.lookupCountry(searchQuery).asSequence()
            .map { it.toCountryResponse() }.toList()
        )

    override fun searchNationality(searchQuery: String?): ResponseEntity<List<NationalityResponse>> =
        ok(
            lookupService.lookupNationality(searchQuery)
                .map { it.toNationalityResponse() }.toList()
        )
}