/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CommentDeclarationType
 */
@JsonPropertyOrder({
  CommentDeclarationType.JSON_PROPERTY_COMMENT_OF_DECLARATION
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CommentDeclarationType {
  public static final String JSON_PROPERTY_COMMENT_OF_DECLARATION = "commentOfDeclaration";
  private String commentOfDeclaration;

  public CommentDeclarationType() {
  }

  public CommentDeclarationType commentOfDeclaration(String commentOfDeclaration) {
    
    this.commentOfDeclaration = commentOfDeclaration;
    return this;
  }

  /**
   * Get commentOfDeclaration
   * @return commentOfDeclaration
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMMENT_OF_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCommentOfDeclaration() {
    return commentOfDeclaration;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT_OF_DECLARATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCommentOfDeclaration(String commentOfDeclaration) {
    this.commentOfDeclaration = commentOfDeclaration;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CommentDeclarationType commentDeclarationType = (CommentDeclarationType) o;
    return Objects.equals(this.commentOfDeclaration, commentDeclarationType.commentOfDeclaration);
  }

  @Override
  public int hashCode() {
    return Objects.hash(commentOfDeclaration);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CommentDeclarationType {\n");
    sb.append("    commentOfDeclaration: ").append(toIndentedString(commentOfDeclaration)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

