package be.fgov.onerva.cu.backend.config;

import dev.openfeature.contrib.providers.flagsmith.FlagsmithProvider;
import dev.openfeature.contrib.providers.flagsmith.FlagsmithProviderOptions;
import dev.openfeature.sdk.Client;
import dev.openfeature.sdk.OpenFeatureAPI;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FeatureFlagConfig {

    @Value("${flagsmith.api.key}")
    private String apiKey;

    @Value("${flagsmith.api.url}")
    private String baseUrl;

    @Bean
    public OpenFeatureAPI openFeatureAPI() {
        // Create provider options
        FlagsmithProviderOptions options = FlagsmithProviderOptions.builder().apiKey(apiKey).baseUri(baseUrl)
                // Add other options as needed
                .enableAnalytics(true).build();

        // Initialize the provider with options
        FlagsmithProvider provider = new FlagsmithProvider(options);

        // Set the provider as the default for OpenFeature
        OpenFeatureAPI openFeatureAPI = OpenFeatureAPI.getInstance();
        openFeatureAPI.setProvider(provider);

        return openFeatureAPI;
    }

    @Bean
    public Client openFeatureClient(OpenFeatureAPI openFeatureAPI) {
        return openFeatureAPI.getClient("spring-boot-backend");
    }
}