package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param otherPersonName 
 * @param iban 
 * @param bic 
 */
data class ModeOfPaymentFields(

    @Schema(example = "null", description = "")
    @get:JsonProperty("otherPersonName") val otherPersonName: kotlin.String? = null,

    @Schema(example = "null", description = "")
    @get:JsonProperty("iban") val iban: kotlin.String? = null,

    @Schema(example = "null", description = "")
    @get:JsonProperty("bic") val bic: kotlin.String? = null
    ) {

}

