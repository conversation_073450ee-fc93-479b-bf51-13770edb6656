package be.fgov.onerva.cu.bff.exceptions

class MissingAuthException(message: String) : RuntimeException(message)

class CitizenNotFoundException(message: String) : RuntimeException(message)

class RequestNotFoundException(message: String) : RuntimeException(message)

class WaveUserNotFoundException(message: String) : RuntimeException(message)

class C9NotFoundException : RuntimeException {
    constructor(message: String) : super(message)
    constructor(message: String, cause: Throwable) : super(message, cause)
}

class S24Exception(message: String) : RuntimeException(message)

class InvalidC9Exception(message: String) : RuntimeException(message)