{"local": {"tokenUrl": "http://localhost:8082/realms/onemrva-agents/protocol/openid-connect/token", "backend-url": "http://localhost:9091", "bff-url": "http://localhost:9090", "wo-facade-url": "http://localhost:8077", "wo-smals-url": "http://localhost:8070", "microcks-url": "http://localhost:9997", "wiremock-url": "http://localhost:9998", "register-proxy-url": "http://localhost:9997/rest/Register+Proxy+Service+public+API/1.0.0", "Security": {"Auth": {"keycloak": {"Type": "OAuth2", "Grant Type": "Client Credentials", "Client ID": "cu-backend", "Client Secret": "cu-backend-secret", "Scope": "openid", "Token URL": "{{tokenUrl}}"}, "cu_user": {"Type": "OAuth2", "Grant Type": "Password", "Username": "cu_user", "Password": "password", "Client ID": "cu-frontend", "Token URL": "{{tokenUrl}}", "Scope": "openid profile roles onemrva"}, "cu_admin": {"Type": "OAuth2", "Grant Type": "Password", "Username": "cu_admin", "Password": "password", "Client ID": "cu-frontend", "Token URL": "{{tokenUrl}}", "Scope": "openid profile roles onemrva"}}}, "latestRequestId": "4ba04b57-2c02-4cff-acc0-b5e91fb96a6e"}, "test": {"tokenUrl": "https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token", "backend-url": "https://cu.test.paas.onemrva.priv/", "wo-facade-url": "https://wo-configurator.test.paas.onemrva.priv", "register-proxy-url": "https://registerproxyservice.test.paas.onemrva.priv/registerproxyservice/rest", "Security": {"Auth": {"keycloak": {"Type": "OAuth2", "Grant Type": "Client Credentials", "Client ID": "cu-backend", "Client Secret": "cu-backend-secret", "Scope": "openid", "Token URL": "{{tokenUrl}}"}, "cu_user": {"Type": "OAuth2", "Grant Type": "Password", "Username": "cu_user", "Password": "password", "Client ID": "cu-frontend", "Token URL": "{{tokenUrl}}", "Scope": "openid profile roles onemrva"}, "cu_admin": {"Type": "OAuth2", "Grant Type": "Password", "Username": "cu_admin", "Password": "password", "Client ID": "cu-frontend", "Token URL": "{{tokenUrl}}", "Scope": "openid profile roles onemrva"}}}}, "val": {"register-proxy-url": "https://registerproxyservice.val.paas.onemrva.priv/registerproxyservice/rest"}}