/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo.organizational.chart.rest.model.Contact;
import be.fgov.onerva.wo.organizational.chart.rest.model.Employee;
import be.fgov.onerva.wo.organizational.chart.rest.model.Group;
import be.fgov.onerva.wo.organizational.chart.rest.model.LinkStatus;
import be.fgov.onerva.wo.organizational.chart.rest.model.SelfLink;
import be.fgov.onerva.wo.organizational.chart.rest.model.TechnicalInformation;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * A node is a part of the Organizational Chart that can be of a different type (employee, group). Each node of the type group may have sub nodes as part of the whole Organizational Chart tree. A node may or may be not active. If a group is inactive, its sub nodes are also inactive.
 */
@JsonPropertyOrder({
  Node.JSON_PROPERTY_NODE_ID,
  Node.JSON_PROPERTY_TYPE,
  Node.JSON_PROPERTY_BUSINESS_IDENTIFIERS,
  Node.JSON_PROPERTY_EMPLOYEE,
  Node.JSON_PROPERTY_GROUP,
  Node.JSON_PROPERTY_ATTRIBUTIONS,
  Node.JSON_PROPERTY_MEMBER_OF,
  Node.JSON_PROPERTY_CONTACT,
  Node.JSON_PROPERTY_TECHNICAL_INFORMATION,
  Node.JSON_PROPERTY_LINK_STATUS,
  Node.JSON_PROPERTY_SELF
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Node {
  public static final String JSON_PROPERTY_NODE_ID = "nodeId";
  private String nodeId;

  /**
   * The indication of the type of the node, in the Organizational Chart there are two types, an employee and a group.
   */
  public enum TypeEnum {
    EMPLOYEE("employee"),
    
    GROUP("group");

    private String value;

    TypeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TypeEnum fromValue(String value) {
      for (TypeEnum b : TypeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_TYPE = "type";
  private TypeEnum type;

  public static final String JSON_PROPERTY_BUSINESS_IDENTIFIERS = "businessIdentifiers";
  private Map<String, String> businessIdentifiers = new HashMap<>();

  public static final String JSON_PROPERTY_EMPLOYEE = "employee";
  private Employee employee;

  public static final String JSON_PROPERTY_GROUP = "group";
  private Group group;

  public static final String JSON_PROPERTY_ATTRIBUTIONS = "attributions";
  private List<Map<String, String>> attributions = new ArrayList<>();

  public static final String JSON_PROPERTY_MEMBER_OF = "memberOf";
  private List<Node> memberOf = new ArrayList<>();

  public static final String JSON_PROPERTY_CONTACT = "contact";
  private Contact contact;

  public static final String JSON_PROPERTY_TECHNICAL_INFORMATION = "technicalInformation";
  private TechnicalInformation technicalInformation;

  public static final String JSON_PROPERTY_LINK_STATUS = "linkStatus";
  private LinkStatus linkStatus;

  public static final String JSON_PROPERTY_SELF = "self";
  private SelfLink self;

  public Node() {
  }
  /**
   * Constructor with only readonly parameters
   */
  @JsonCreator
  public Node(
    @JsonProperty(JSON_PROPERTY_NODE_ID) String nodeId, 
    @JsonProperty(JSON_PROPERTY_TYPE) TypeEnum type
  ) {
    this();
    this.nodeId = nodeId;
    this.type = type;
  }

  /**
   * The Organizational chart identifier of the node
   * @return nodeId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NODE_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNodeId() {
    return nodeId;
  }



  /**
   * The indication of the type of the node, in the Organizational Chart there are two types, an employee and a group.
   * @return type
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public TypeEnum getType() {
    return type;
  }



  public Node businessIdentifiers(Map<String, String> businessIdentifiers) {
    
    this.businessIdentifiers = businessIdentifiers;
    return this;
  }

  public Node putBusinessIdentifiersItem(String key, String businessIdentifiersItem) {
    if (this.businessIdentifiers == null) {
      this.businessIdentifiers = new HashMap<>();
    }
    this.businessIdentifiers.put(key, businessIdentifiersItem);
    return this;
  }

  /**
   * The BusinessIdentifiers of the node. A business identifier is specific information about the node, this can be a identifier for the department, an agent number of an employee or a SSIN of an employee.
   * @return businessIdentifiers
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_IDENTIFIERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Map<String, String> getBusinessIdentifiers() {
    return businessIdentifiers;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_IDENTIFIERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessIdentifiers(Map<String, String> businessIdentifiers) {
    this.businessIdentifiers = businessIdentifiers;
  }

  public Node employee(Employee employee) {
    
    this.employee = employee;
    return this;
  }

  /**
   * Get employee
   * @return employee
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Employee getEmployee() {
    return employee;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployee(Employee employee) {
    this.employee = employee;
  }

  public Node group(Group group) {
    
    this.group = group;
    return this;
  }

  /**
   * Get group
   * @return group
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GROUP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Group getGroup() {
    return group;
  }


  @JsonProperty(JSON_PROPERTY_GROUP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGroup(Group group) {
    this.group = group;
  }

  public Node attributions(List<Map<String, String>> attributions) {
    
    this.attributions = attributions;
    return this;
  }

  public Node addAttributionsItem(Map<String, String> attributionsItem) {
    if (this.attributions == null) {
      this.attributions = new ArrayList<>();
    }
    this.attributions.add(attributionsItem);
    return this;
  }

  /**
   * Get attributions
   * @return attributions
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ATTRIBUTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Map<String, String>> getAttributions() {
    return attributions;
  }


  @JsonProperty(JSON_PROPERTY_ATTRIBUTIONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAttributions(List<Map<String, String>> attributions) {
    this.attributions = attributions;
  }

  public Node memberOf(List<Node> memberOf) {
    
    this.memberOf = memberOf;
    return this;
  }

  public Node addMemberOfItem(Node memberOfItem) {
    if (this.memberOf == null) {
      this.memberOf = new ArrayList<>();
    }
    this.memberOf.add(memberOfItem);
    return this;
  }

  /**
   * Get memberOf
   * @return memberOf
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MEMBER_OF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Node> getMemberOf() {
    return memberOf;
  }


  @JsonProperty(JSON_PROPERTY_MEMBER_OF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMemberOf(List<Node> memberOf) {
    this.memberOf = memberOf;
  }

  public Node contact(Contact contact) {
    
    this.contact = contact;
    return this;
  }

  /**
   * Get contact
   * @return contact
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Contact getContact() {
    return contact;
  }


  @JsonProperty(JSON_PROPERTY_CONTACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContact(Contact contact) {
    this.contact = contact;
  }

  public Node technicalInformation(TechnicalInformation technicalInformation) {
    
    this.technicalInformation = technicalInformation;
    return this;
  }

  /**
   * Get technicalInformation
   * @return technicalInformation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TECHNICAL_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public TechnicalInformation getTechnicalInformation() {
    return technicalInformation;
  }


  @JsonProperty(JSON_PROPERTY_TECHNICAL_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTechnicalInformation(TechnicalInformation technicalInformation) {
    this.technicalInformation = technicalInformation;
  }

  public Node linkStatus(LinkStatus linkStatus) {
    
    this.linkStatus = linkStatus;
    return this;
  }

  /**
   * Get linkStatus
   * @return linkStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LINK_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LinkStatus getLinkStatus() {
    return linkStatus;
  }


  @JsonProperty(JSON_PROPERTY_LINK_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLinkStatus(LinkStatus linkStatus) {
    this.linkStatus = linkStatus;
  }

  public Node self(SelfLink self) {
    
    this.self = self;
    return this;
  }

  /**
   * Get self
   * @return self
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SELF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public SelfLink getSelf() {
    return self;
  }


  @JsonProperty(JSON_PROPERTY_SELF)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSelf(SelfLink self) {
    this.self = self;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Node node = (Node) o;
    return Objects.equals(this.nodeId, node.nodeId) &&
        Objects.equals(this.type, node.type) &&
        Objects.equals(this.businessIdentifiers, node.businessIdentifiers) &&
        Objects.equals(this.employee, node.employee) &&
        Objects.equals(this.group, node.group) &&
        Objects.equals(this.attributions, node.attributions) &&
        Objects.equals(this.memberOf, node.memberOf) &&
        Objects.equals(this.contact, node.contact) &&
        Objects.equals(this.technicalInformation, node.technicalInformation) &&
        Objects.equals(this.linkStatus, node.linkStatus) &&
        Objects.equals(this.self, node.self);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nodeId, type, businessIdentifiers, employee, group, attributions, memberOf, contact, technicalInformation, linkStatus, self);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Node {\n");
    sb.append("    nodeId: ").append(toIndentedString(nodeId)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    businessIdentifiers: ").append(toIndentedString(businessIdentifiers)).append("\n");
    sb.append("    employee: ").append(toIndentedString(employee)).append("\n");
    sb.append("    group: ").append(toIndentedString(group)).append("\n");
    sb.append("    attributions: ").append(toIndentedString(attributions)).append("\n");
    sb.append("    memberOf: ").append(toIndentedString(memberOf)).append("\n");
    sb.append("    contact: ").append(toIndentedString(contact)).append("\n");
    sb.append("    technicalInformation: ").append(toIndentedString(technicalInformation)).append("\n");
    sb.append("    linkStatus: ").append(toIndentedString(linkStatus)).append("\n");
    sb.append("    self: ").append(toIndentedString(self)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

