/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.KeycloakConfigResponse
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface ConfigApi {

    @Operation(
        tags = ["Config",],
        summary = "",
        operationId = "getKeycloakConfig",
        description = """""",
        responses = [
            ApiResponse(responseCode = "200", description = "Configuration of Keycloak", content = [Content(schema = Schema(implementation = KeycloakConfigResponse::class))])
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/config/keycloak"],
            produces = ["application/json"]
    )
    fun getKeycloakConfig(): ResponseEntity<KeycloakConfigResponse>
}
