/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.AddressType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.CommunicationType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505CoordinatesContactPerson
 */
@JsonPropertyOrder({
  Wech505CoordinatesContactPerson.JSON_PROPERTY_NAME,
  Wech505CoordinatesContactPerson.JSON_PROPERTY_FIRST_NAME,
  Wech505CoordinatesContactPerson.JSON_PROPERTY_COMMUNICATION,
  Wech505CoordinatesContactPerson.JSON_PROPERTY_ADDRESS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505CoordinatesContactPerson {
  public static final String JSON_PROPERTY_NAME = "name";
  private String name;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_COMMUNICATION = "communication";
  private CommunicationType communication;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private AddressType address;

  public Wech505CoordinatesContactPerson() {
  }

  public Wech505CoordinatesContactPerson name(String name) {
    
    this.name = name;
    return this;
  }

  /**
   * Get name
   * @return name
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getName() {
    return name;
  }


  @JsonProperty(JSON_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setName(String name) {
    this.name = name;
  }

  public Wech505CoordinatesContactPerson firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public Wech505CoordinatesContactPerson communication(CommunicationType communication) {
    
    this.communication = communication;
    return this;
  }

  /**
   * Get communication
   * @return communication
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMUNICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CommunicationType getCommunication() {
    return communication;
  }


  @JsonProperty(JSON_PROPERTY_COMMUNICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommunication(CommunicationType communication) {
    this.communication = communication;
  }

  public Wech505CoordinatesContactPerson address(AddressType address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public AddressType getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(AddressType address) {
    this.address = address;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505CoordinatesContactPerson wech505CoordinatesContactPerson = (Wech505CoordinatesContactPerson) o;
    return Objects.equals(this.name, wech505CoordinatesContactPerson.name) &&
        Objects.equals(this.firstName, wech505CoordinatesContactPerson.firstName) &&
        Objects.equals(this.communication, wech505CoordinatesContactPerson.communication) &&
        Objects.equals(this.address, wech505CoordinatesContactPerson.address);
  }

  @Override
  public int hashCode() {
    return Objects.hash(name, firstName, communication, address);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505CoordinatesContactPerson {\n");
    sb.append("    name: ").append(toIndentedString(name)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    communication: ").append(toIndentedString(communication)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

