/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RegisterPersonNames
 */
@JsonPropertyOrder({
  RegisterPersonNames.JSON_PROPERTY_FIRST_NAME,
  RegisterPersonNames.JSON_PROPERTY_SEQ
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RegisterPersonNames {
  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_SEQ = "seq";
  private Integer seq;

  public RegisterPersonNames() {
  }

  public RegisterPersonNames firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public RegisterPersonNames seq(Integer seq) {
    
    this.seq = seq;
    return this;
  }

  /**
   * Starting from 1
   * @return seq
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getSeq() {
    return seq;
  }


  @JsonProperty(JSON_PROPERTY_SEQ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSeq(Integer seq) {
    this.seq = seq;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegisterPersonNames registerPersonNames = (RegisterPersonNames) o;
    return Objects.equals(this.firstName, registerPersonNames.firstName) &&
        Objects.equals(this.seq, registerPersonNames.seq);
  }

  @Override
  public int hashCode() {
    return Objects.hash(firstName, seq);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegisterPersonNames {\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    seq: ").append(toIndentedString(seq)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

