package be.fgov.onerva.cu.backend.adapter.mapper

import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestReceivedCommand
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataRequestTreatedCommand
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.exception.InvalidC9Exception
import be.fgov.onerva.cu.common.utils.DateUtils
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef
import be.fgov.onerva.unemployment.c9.rest.model.C9
import be.fgov.onerva.unemployment.c9.rest.model.EC1Identity
import be.fgov.onerva.unemployment.c9.rest.model.EC1ModeOfPayment
import be.fgov.onerva.unemployment.c9.rest.model.EC1TradeUnionContribution

/**
 * Maps an EC1Identity to the domain Identity model
 */
fun EC1Identity.toDomainCitizenInformation(): CitizenInformation =
    CitizenInformation(
        firstName = this.firstName,
        lastName = this.lastName,
        birthDate = this.dateOfBirth?.let {
            DateUtils.parseDate(this.dateOfBirth)
        } ?: throw InvalidC9Exception("The C9 is invalid - Invalid date of birth"),
        nationality = this.nationality?.let { this.nationality.code } ?: "150",
        address = Address(
            street = this.street,
            houseNumber = this.houseNumber,
            boxNumber = this.boxNumber,
            zipCode = this.zipCode.zipCode,
            city = this.city,
            country = this.country.code,
        )
    )

/**
 * Maps an EC1ModeOfPayment to the domain ModeOfPayment model
 */
fun EC1ModeOfPayment.toDomainModeOfPayment(): ModeOfPayment =
    ModeOfPayment(
        otherPersonName = if (this.isMyBankAccount != null && !this.isMyBankAccount) this.bankAccountForOtherPersonName else null,
        iban = if (this.foreignBankAccountIBAN != null) this.foreignBankAccountIBAN else this.belgianSEPABankAccount,
        bic = this.foreignBankAccountBIC,
    )

fun EC1TradeUnionContribution.toDomainUnionContribution(): UnionContribution =
    UnionContribution(
        authorized = this.contributionDeductionFromTheMonth != null,
        effectiveDate = this.contributionDeductionFromTheMonth ?: this.stopContributionDeductionFromTheMonth,
    )

/**
 * Extension function to convert a C9 object to ChangePersonalDataReceived
 * Assumes the first EC1 attestRef is the relevant one for processing
 */
fun C9.toChangePersonalDataRequestCommand(): ChangePersonalDataRequestReceivedCommand =
    ChangePersonalDataRequestReceivedCommand(
        c9Id = this.id,
        c9Type = this.type,
        ssin = this.ssin,
        ec1Id = this.getEC1()?.id,
    )

fun C9.toChangePersonalDataRequestTreatedCommand(): ChangePersonalDataRequestTreatedCommand =
    ChangePersonalDataRequestTreatedCommand(
        c9Id = this.id,
        type = this.type,
        ssin = this.ssin,
        decisionDate = this.decisionDate,
        decisionType = DecisionType.valueOf(this.decisionType),
        user = this.decisionWindowsUser,
    )

fun C9.hasEC1(): Boolean = this.attestRefs.any { it.type == "EC1" }

fun C9.getEC1(): AttestRef? = this.attestRefs.filter { it.type == "EC1" }.firstOrNull()

fun C9.isChangedOfPersonalDataReady(): Boolean =
    (this.type == "400" || this.type == "410") && this.treatmentStatus == "READY_TO_BE_TREATED" && (this.hasEC1() || this.scanUrl != null)

fun C9.isChangedOfPersonalDataTreated(): Boolean =
    (this.type == "400" || this.type == "410") && this.treatmentStatus == "TREATED"
