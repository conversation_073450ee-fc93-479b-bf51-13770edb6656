/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.CohabitingPerson;
import be.fgov.onerva.unemployment.c9.rest.model.LonePerson;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1FamilySituation
 */
@JsonPropertyOrder({
  EC1FamilySituation.JSON_PROPERTY_LONE_PERSON,
  EC1FamilySituation.JSON_PROPERTY_COHABITING_PERSON
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1FamilySituation {
  public static final String JSON_PROPERTY_LONE_PERSON = "lonePerson";
  private LonePerson lonePerson;

  public static final String JSON_PROPERTY_COHABITING_PERSON = "cohabitingPerson";
  private CohabitingPerson cohabitingPerson;

  public EC1FamilySituation() {
  }

  public EC1FamilySituation lonePerson(LonePerson lonePerson) {
    
    this.lonePerson = lonePerson;
    return this;
  }

  /**
   * Get lonePerson
   * @return lonePerson
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LONE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LonePerson getLonePerson() {
    return lonePerson;
  }


  @JsonProperty(JSON_PROPERTY_LONE_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLonePerson(LonePerson lonePerson) {
    this.lonePerson = lonePerson;
  }

  public EC1FamilySituation cohabitingPerson(CohabitingPerson cohabitingPerson) {
    
    this.cohabitingPerson = cohabitingPerson;
    return this;
  }

  /**
   * Get cohabitingPerson
   * @return cohabitingPerson
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COHABITING_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CohabitingPerson getCohabitingPerson() {
    return cohabitingPerson;
  }


  @JsonProperty(JSON_PROPERTY_COHABITING_PERSON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCohabitingPerson(CohabitingPerson cohabitingPerson) {
    this.cohabitingPerson = cohabitingPerson;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1FamilySituation ec1FamilySituation = (EC1FamilySituation) o;
    return Objects.equals(this.lonePerson, ec1FamilySituation.lonePerson) &&
        Objects.equals(this.cohabitingPerson, ec1FamilySituation.cohabitingPerson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(lonePerson, cohabitingPerson);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1FamilySituation {\n");
    sb.append("    lonePerson: ").append(toIndentedString(lonePerson)).append("\n");
    sb.append("    cohabitingPerson: ").append(toIndentedString(cohabitingPerson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

