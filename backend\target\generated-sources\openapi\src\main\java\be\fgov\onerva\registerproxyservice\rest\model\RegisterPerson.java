/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.ErrorStatus;
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress;
import be.fgov.onerva.registerproxyservice.rest.model.RegisterNationality;
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPersonNames;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RegisterPerson
 */
@JsonPropertyOrder({
  RegisterPerson.JSON_PROPERTY_ADDRESSES,
  RegisterPerson.JSON_PROPERTY_BIRTHDATE,
  RegisterPerson.JSON_PROPERTY_DECEASE_DATE,
  RegisterPerson.JSON_PROPERTY_GENDER,
  RegisterPerson.JSON_PROPERTY_IDENTITIFICATION,
  RegisterPerson.JSON_PROPERTY_LAST_NAME,
  RegisterPerson.JSON_PROPERTY_NAMES,
  RegisterPerson.JSON_PROPERTY_NATIONALITIES,
  RegisterPerson.JSON_PROPERTY_REPLACED_SSIN,
  RegisterPerson.JSON_PROPERTY_SSIN,
  RegisterPerson.JSON_PROPERTY_STATUS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RegisterPerson {
  public static final String JSON_PROPERTY_ADDRESSES = "addresses";
  private List<RegisterAddress> addresses = new ArrayList<>();

  public static final String JSON_PROPERTY_BIRTHDATE = "birthdate";
  private LocalDate birthdate;

  public static final String JSON_PROPERTY_DECEASE_DATE = "deceaseDate";
  private Long deceaseDate;

  public static final String JSON_PROPERTY_GENDER = "gender";
  private String gender;

  public static final String JSON_PROPERTY_IDENTITIFICATION = "identitification";
  private Integer identitification;

  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_NAMES = "names";
  private List<RegisterPersonNames> names = new ArrayList<>();

  public static final String JSON_PROPERTY_NATIONALITIES = "nationalities";
  private List<RegisterNationality> nationalities = new ArrayList<>();

  public static final String JSON_PROPERTY_REPLACED_SSIN = "replacedSsin";
  private List<String> replacedSsin = new ArrayList<>();

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_STATUS = "status";
  private ErrorStatus status;

  public RegisterPerson() {
  }

  public RegisterPerson addresses(List<RegisterAddress> addresses) {
    
    this.addresses = addresses;
    return this;
  }

  public RegisterPerson addAddressesItem(RegisterAddress addressesItem) {
    if (this.addresses == null) {
      this.addresses = new ArrayList<>();
    }
    this.addresses.add(addressesItem);
    return this;
  }

  /**
   * Get addresses
   * @return addresses
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<RegisterAddress> getAddresses() {
    return addresses;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESSES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddresses(List<RegisterAddress> addresses) {
    this.addresses = addresses;
  }

  public RegisterPerson birthdate(LocalDate birthdate) {
    
    this.birthdate = birthdate;
    return this;
  }

  /**
   * The person&#39;s birthdate
   * @return birthdate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BIRTHDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getBirthdate() {
    return birthdate;
  }


  @JsonProperty(JSON_PROPERTY_BIRTHDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBirthdate(LocalDate birthdate) {
    this.birthdate = birthdate;
  }

  public RegisterPerson deceaseDate(Long deceaseDate) {
    
    this.deceaseDate = deceaseDate;
    return this;
  }

  /**
   * Decease date represented as Unix timestamp in milliseconds (UTC-based)
   * @return deceaseDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECEASE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getDeceaseDate() {
    return deceaseDate;
  }


  @JsonProperty(JSON_PROPERTY_DECEASE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeceaseDate(Long deceaseDate) {
    this.deceaseDate = deceaseDate;
  }

  public RegisterPerson gender(String gender) {
    
    this.gender = gender;
    return this;
  }

  /**
   * Get gender
   * @return gender
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GENDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGender() {
    return gender;
  }


  @JsonProperty(JSON_PROPERTY_GENDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGender(String gender) {
    this.gender = gender;
  }

  public RegisterPerson identitification(Integer identitification) {
    
    this.identitification = identitification;
    return this;
  }

  /**
   * Get identitification
   * @return identitification
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IDENTITIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getIdentitification() {
    return identitification;
  }


  @JsonProperty(JSON_PROPERTY_IDENTITIFICATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIdentitification(Integer identitification) {
    this.identitification = identitification;
  }

  public RegisterPerson lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public RegisterPerson names(List<RegisterPersonNames> names) {
    
    this.names = names;
    return this;
  }

  public RegisterPerson addNamesItem(RegisterPersonNames namesItem) {
    if (this.names == null) {
      this.names = new ArrayList<>();
    }
    this.names.add(namesItem);
    return this;
  }

  /**
   * Get names
   * @return names
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NAMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<RegisterPersonNames> getNames() {
    return names;
  }


  @JsonProperty(JSON_PROPERTY_NAMES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNames(List<RegisterPersonNames> names) {
    this.names = names;
  }

  public RegisterPerson nationalities(List<RegisterNationality> nationalities) {
    
    this.nationalities = nationalities;
    return this;
  }

  public RegisterPerson addNationalitiesItem(RegisterNationality nationalitiesItem) {
    if (this.nationalities == null) {
      this.nationalities = new ArrayList<>();
    }
    this.nationalities.add(nationalitiesItem);
    return this;
  }

  /**
   * Get nationalities
   * @return nationalities
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATIONALITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<RegisterNationality> getNationalities() {
    return nationalities;
  }


  @JsonProperty(JSON_PROPERTY_NATIONALITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationalities(List<RegisterNationality> nationalities) {
    this.nationalities = nationalities;
  }

  public RegisterPerson replacedSsin(List<String> replacedSsin) {
    
    this.replacedSsin = replacedSsin;
    return this;
  }

  public RegisterPerson addReplacedSsinItem(String replacedSsinItem) {
    if (this.replacedSsin == null) {
      this.replacedSsin = new ArrayList<>();
    }
    this.replacedSsin.add(replacedSsinItem);
    return this;
  }

  /**
   * Get replacedSsin
   * @return replacedSsin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REPLACED_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getReplacedSsin() {
    return replacedSsin;
  }


  @JsonProperty(JSON_PROPERTY_REPLACED_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReplacedSsin(List<String> replacedSsin) {
    this.replacedSsin = replacedSsin;
  }

  public RegisterPerson ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public RegisterPerson status(ErrorStatus status) {
    
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ErrorStatus getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(ErrorStatus status) {
    this.status = status;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RegisterPerson registerPerson = (RegisterPerson) o;
    return Objects.equals(this.addresses, registerPerson.addresses) &&
        Objects.equals(this.birthdate, registerPerson.birthdate) &&
        Objects.equals(this.deceaseDate, registerPerson.deceaseDate) &&
        Objects.equals(this.gender, registerPerson.gender) &&
        Objects.equals(this.identitification, registerPerson.identitification) &&
        Objects.equals(this.lastName, registerPerson.lastName) &&
        Objects.equals(this.names, registerPerson.names) &&
        Objects.equals(this.nationalities, registerPerson.nationalities) &&
        Objects.equals(this.replacedSsin, registerPerson.replacedSsin) &&
        Objects.equals(this.ssin, registerPerson.ssin) &&
        Objects.equals(this.status, registerPerson.status);
  }

  @Override
  public int hashCode() {
    return Objects.hash(addresses, birthdate, deceaseDate, gender, identitification, lastName, names, nationalities, replacedSsin, ssin, status);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RegisterPerson {\n");
    sb.append("    addresses: ").append(toIndentedString(addresses)).append("\n");
    sb.append("    birthdate: ").append(toIndentedString(birthdate)).append("\n");
    sb.append("    deceaseDate: ").append(toIndentedString(deceaseDate)).append("\n");
    sb.append("    gender: ").append(toIndentedString(gender)).append("\n");
    sb.append("    identitification: ").append(toIndentedString(identitification)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    names: ").append(toIndentedString(names)).append("\n");
    sb.append("    nationalities: ").append(toIndentedString(nationalities)).append("\n");
    sb.append("    replacedSsin: ").append(toIndentedString(replacedSsin)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

