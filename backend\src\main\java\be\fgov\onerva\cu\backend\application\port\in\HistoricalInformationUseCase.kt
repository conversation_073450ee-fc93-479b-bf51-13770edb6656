package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution

/**
 * This use case is to retrieve historical information linked to a request.
 * Historical information can be based on Person API (Citizen), authentic sources, or internal database.
 * Other source of information can exist.
 * Depending on the use case, when we get information we also save it so that it can
 * be reproduced later.
 */
interface HistoricalInformationUseCase {
    /**
     * Get the citizen information from a historical source
     * @param requestId the id of the request
     * @param source the source of the information
     * @return the citizen information
     */
    fun getHistoricalCitizenOnem(requestId: UUID): HistoricalCitizenOnem

    /**
     * Get the citizen information from a historical source
     * @param requestId the id of the request
     * @param source the source of the information
     * @return the citizen information
     */
    fun getHistoricalCitizenC1(requestId: UUID): HistoricalCitizenC1

    /**
     * Get the citizen information from a historical source
     * @param requestId the id of the request
     * @param source the source of the information
     * @return the citizen information
     */
    fun getHistoricalCitizenAuthenticSources(requestId: UUID): HistoricalCitizenAuthenticSources

    /**
     * Get the mode of payment from a historical source
     * Only the ONEM source is supported for this information.
     * @param requestId the id of the request
     * @param source the source of the information
     * @return the mode of payment
     */
    fun getModeOfPayment(requestId: UUID, source: ExternalSource): ModeOfPayment

    fun getUnionContribution(requestId: UUID, source: ExternalSource): UnionContribution

    fun getBarema(requestId: UUID): Barema?
}