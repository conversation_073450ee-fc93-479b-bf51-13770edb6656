/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param barema 
 * @param article 
 */


data class HistoricalBaremaResponse (

    @get:JsonProperty("barema")
    val barema: kotlin.String? = null,

    @get:JsonProperty("article")
    val article: kotlin.String? = null

) {


}

