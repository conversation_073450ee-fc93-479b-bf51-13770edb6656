package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param requestId 
 * @param basicInfo 
 * @param citizenInformation 
 * @param modeOfPayment 
 * @param unionContribution 
 */
data class AggregatedChangePersonalDataCaptureResponse(

    @Schema(example = "null", description = "")
    @get:JsonProperty("requestId") val requestId: java.util.UUID? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("basicInfo") val basicInfo: RequestBasicInfoResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("citizenInformation") val citizenInformation: CitizenInformationDetailNullableResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("modeOfPayment") val modeOfPayment: ModeOfPaymentDetailResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("unionContribution") val unionContribution: UnionContributionDetailResponse? = null
    ) {

}

