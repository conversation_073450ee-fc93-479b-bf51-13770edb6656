package be.fgov.onerva.cu.bff.rest.client.citizen.api;

import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenCreationRequestDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenPageDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenUpdateRequestDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenApi extends BaseApi {

    public CitizenApi() {
        super(new ApiClient());
    }

    public CitizenApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Create a new Citizen
     * <p><b>201</b> - A valid request to create a new citizen has been created
     * <p><b>400</b> - An error occurred during the processing of the request
     * <p><b>409</b> - The requested citizen already exists
     * @param businessDomain Reference the business domain where the citizen need to be created (required)
     * @param allowance True citizen will request allowance, false a citizen will not immediately request allowance (required)
     * @param citizenCreationRequestDTO  (required)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void createCitizen(String businessDomain, Boolean allowance, CitizenCreationRequestDTO citizenCreationRequestDTO) throws RestClientException {
        createCitizenWithHttpInfo(businessDomain, allowance, citizenCreationRequestDTO);
    }

    /**
     * 
     * Create a new Citizen
     * <p><b>201</b> - A valid request to create a new citizen has been created
     * <p><b>400</b> - An error occurred during the processing of the request
     * <p><b>409</b> - The requested citizen already exists
     * @param businessDomain Reference the business domain where the citizen need to be created (required)
     * @param allowance True citizen will request allowance, false a citizen will not immediately request allowance (required)
     * @param citizenCreationRequestDTO  (required)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> createCitizenWithHttpInfo(String businessDomain, Boolean allowance, CitizenCreationRequestDTO citizenCreationRequestDTO) throws RestClientException {
        Object localVarPostBody = citizenCreationRequestDTO;
        
        // verify the required parameter 'businessDomain' is set
        if (businessDomain == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'businessDomain' when calling createCitizen");
        }
        
        // verify the required parameter 'allowance' is set
        if (allowance == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'allowance' when calling createCitizen");
        }
        
        // verify the required parameter 'citizenCreationRequestDTO' is set
        if (citizenCreationRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'citizenCreationRequestDTO' when calling createCitizen");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "businessDomain", businessDomain));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "allowance", allowance));
        

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/citizen", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Get a citizen base on her niss
     * <p><b>200</b> - Citizen
     * @param niss  (required)
     * @return CitizenDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenDTO getByNiss(String niss) throws RestClientException {
        return getByNissWithHttpInfo(niss).getBody();
    }

    /**
     * 
     * Get a citizen base on her niss
     * <p><b>200</b> - Citizen
     * @param niss  (required)
     * @return ResponseEntity&lt;CitizenDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenDTO> getByNissWithHttpInfo(String niss) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'niss' is set
        if (niss == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'niss' when calling getByNiss");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("niss", niss);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenDTO> localReturnType = new ParameterizedTypeReference<CitizenDTO>() {};
        return apiClient.invokeAPI("/citizen/{niss}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Get a citizen base on her numbox
     * <p><b>200</b> - Citizen
     * @param numbox  (required)
     * @return CitizenDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenDTO getByNumbox(Integer numbox) throws RestClientException {
        return getByNumboxWithHttpInfo(numbox).getBody();
    }

    /**
     * 
     * Get a citizen base on her numbox
     * <p><b>200</b> - Citizen
     * @param numbox  (required)
     * @return ResponseEntity&lt;CitizenDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenDTO> getByNumboxWithHttpInfo(Integer numbox) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'numbox' is set
        if (numbox == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'numbox' when calling getByNumbox");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("numbox", numbox);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenDTO> localReturnType = new ParameterizedTypeReference<CitizenDTO>() {};
        return apiClient.invokeAPI("/citizen/numbox/{numbox}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Search citizen based on criteria
     * <p><b>200</b> - Citizen
     * @param query Uses RSQL syntax to search through citizens (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;)  Available fields are:   - numBox   - fullName   - id (That refers to numPens) (required)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @return CitizenPageDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenPageDTO searchCitizen(String query, Integer pageNumber, Integer pageSize) throws RestClientException {
        return searchCitizenWithHttpInfo(query, pageNumber, pageSize).getBody();
    }

    /**
     * 
     * Search citizen based on criteria
     * <p><b>200</b> - Citizen
     * @param query Uses RSQL syntax to search through citizens (See: https://molgenis.gitbooks.io/molgenis/content/guide-rsql.html\&quot;)  Available fields are:   - numBox   - fullName   - id (That refers to numPens) (required)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @return ResponseEntity&lt;CitizenPageDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenPageDTO> searchCitizenWithHttpInfo(String query, Integer pageNumber, Integer pageSize) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'query' is set
        if (query == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'query' when calling searchCitizen");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "query", query));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageNumber", pageNumber));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenPageDTO> localReturnType = new ParameterizedTypeReference<CitizenPageDTO>() {};
        return apiClient.invokeAPI("/citizen", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Update a citizen with a given niss
     * <p><b>204</b> - Citizen update request accepted
     * <p><b>400</b> - The request was invalid.
     * <p><b>404</b> - Citizen not found
     * @param niss  (required)
     * @param username The username that made the change for audit purposes. (required)
     * @param citizenUpdateRequestDTO  (required)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void updateCitizen(String niss, String username, CitizenUpdateRequestDTO citizenUpdateRequestDTO) throws RestClientException {
        updateCitizenWithHttpInfo(niss, username, citizenUpdateRequestDTO);
    }

    /**
     * 
     * Update a citizen with a given niss
     * <p><b>204</b> - Citizen update request accepted
     * <p><b>400</b> - The request was invalid.
     * <p><b>404</b> - Citizen not found
     * @param niss  (required)
     * @param username The username that made the change for audit purposes. (required)
     * @param citizenUpdateRequestDTO  (required)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> updateCitizenWithHttpInfo(String niss, String username, CitizenUpdateRequestDTO citizenUpdateRequestDTO) throws RestClientException {
        Object localVarPostBody = citizenUpdateRequestDTO;
        
        // verify the required parameter 'niss' is set
        if (niss == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'niss' when calling updateCitizen");
        }
        
        // verify the required parameter 'username' is set
        if (username == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'username' when calling updateCitizen");
        }
        
        // verify the required parameter 'citizenUpdateRequestDTO' is set
        if (citizenUpdateRequestDTO == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'citizenUpdateRequestDTO' when calling updateCitizen");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("niss", niss);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "username", username));
        

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {};
        return apiClient.invokeAPI("/citizen/{niss}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {  };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
