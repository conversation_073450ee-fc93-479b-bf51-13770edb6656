package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.Optional
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.history.Revision
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.UnionContributionRepository
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class UnionContributionPersistenceAdapterTest {

    @MockK
    lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @MockK
    lateinit var unionContributionRepository: UnionContributionRepository

    @InjectMockKs
    lateinit var adapter: UnionContributionPersistenceAdapter

    private val requestId = UUID.randomUUID()
    private val unionContributionId = UUID.randomUUID()
    private val effectiveDate = LocalDate.of(2024, 1, 1)

    @Nested
    inner class GetUnionContribution {

        @Test
        fun `should return union contribution when found in repository`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()
            val unionContribution = mockk<UnionContributionEntity>()

            every { unionContribution.authorized } returns true
            every { unionContribution.effectiveDate } returns effectiveDate

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution

            // When
            val result = adapter.getUnionContribution(requestId)

            // Then
            assertThat(result).isNotNull()
            assertThat(result?.authorized).isTrue()
            assertThat(result?.effectiveDate).isEqualTo(effectiveDate)

            verify(exactly = 1) {
                changePersonalDataRepository.findByIdOrNull(requestId)
                unionContributionRepository.findByRequestId(requestId)
            }
        }

        @Test
        fun `should return null when no union contribution exists`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { unionContributionRepository.findByRequestId(requestId) } returns null

            // When
            val result = adapter.getUnionContribution(requestId)

            // Then
            assertThat(result).isNull()

            verify(exactly = 1) {
                changePersonalDataRepository.findByIdOrNull(requestId)
                unionContributionRepository.findByRequestId(requestId)
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getUnionContribution(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { unionContributionRepository.findByRequestId(any()) }
        }
    }

    @Nested
    inner class PersistUnionContribution {

        @Test
        fun `should create new union contribution entity when none exists`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()
            val unionContribution = UnionContribution(
                authorized = true,
                effectiveDate = effectiveDate
            )
            val unionContributionEntitySlot = slot<UnionContributionEntity>()

            every { changePersonalData.unionContribution } returns null
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { unionContributionRepository.save(capture(unionContributionEntitySlot)) } answers { firstArg() }

            // When
            adapter.persistUnionContribution(requestId, unionContribution)

            // Then
            verify(exactly = 1) {
                changePersonalDataRepository.findByIdOrNull(requestId)
                unionContributionRepository.save(any())
            }

            assertThat(unionContributionEntitySlot.captured.authorized).isTrue()
            assertThat(unionContributionEntitySlot.captured.effectiveDate).isEqualTo(effectiveDate)
            assertThat(unionContributionEntitySlot.captured.updateStatus).isEqualTo(UpdateStatus.EDITED)
            assertThat(unionContributionEntitySlot.captured.request).isSameAs(changePersonalData)
        }

        @Test
        fun `should update existing union contribution entity`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()
            val existingUnionContribution = mockk<UnionContributionEntity>(relaxed = true)

            val updatedUnionContribution = UnionContribution(
                authorized = false,
                effectiveDate = LocalDate.of(2024, 2, 1)
            )

            every { changePersonalData.unionContribution } returns existingUnionContribution
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData

            // When
            adapter.persistUnionContribution(requestId, updatedUnionContribution)

            // Then
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { unionContributionRepository.save(any()) }

            verify {
                existingUnionContribution.authorized = false
                existingUnionContribution.effectiveDate = LocalDate.of(2024, 2, 1)
                existingUnionContribution.updateStatus = UpdateStatus.EDITED
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            val unionContribution = UnionContribution(
                authorized = true,
                effectiveDate = effectiveDate
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.persistUnionContribution(requestId, unionContribution) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request with id $requestId not found")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { unionContributionRepository.save(any()) }
        }
    }

    @Nested
    inner class GetLatestRevision {

//        @Test
//        fun `should return latest revision when unionContribution exists`() {
//            // Given
//            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()
//            val unionContribution = mockk<UnionContributionEntity>()
//            val revision = mockk<Revision<Int, UnionContributionEntity>>()
//            val revisionMetadata = mockk<org.springframework.data.history.RevisionMetadata<Int>>()
//
//            every { changePersonalData.unionContribution } returns unionContribution
//            every { unionContribution.id } returns unionContributionId
//            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
//            every { unionContributionRepository.findLastChangeRevision(unionContributionId) } returns Optional.of(
//                revision
//            )
//            every { revision.metadata } returns revisionMetadata
//            every { revisionMetadata.revisionNumber } returns Optional.of(1)
//
//            // When
//            val result = adapter.getLatestRevision(requestId)
//
//            // Then
//            assertThat(result).isEqualTo(1)
//
//            verify(exactly = 1) {
//                changePersonalDataRepository.findByIdOrNull(requestId)
//                unionContributionRepository.findLastChangeRevision(unionContributionId)
//                revision.metadata
//                revisionMetadata.revisionNumber
//            }
//        }

        @Test
        fun `should return 0 when no revision exists`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()
            val unionContribution = mockk<UnionContributionEntity>()

            every { changePersonalData.unionContribution } returns unionContribution
            every { unionContribution.id } returns unionContributionId
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { unionContributionRepository.findLastChangeRevision(unionContributionId) } returns Optional.empty()

            // When
            val result = adapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isEqualTo(0)

            verify(exactly = 1) {
                changePersonalDataRepository.findByIdOrNull(requestId)
                unionContributionRepository.findLastChangeRevision(unionContributionId)
            }
        }

        @Test
        fun `should return 0 when unionContribution is null`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>()

            every { changePersonalData.unionContribution } returns null
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData

            // When
            val result = adapter.getLatestRevision(requestId)

            // Then
            assertThat(result).isEqualTo(0)

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { unionContributionRepository.findLastChangeRevision(any()) }
        }

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getLatestRevision(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 0) { unionContributionRepository.findLastChangeRevision(any()) }
        }
    }

    @Nested
    inner class GetUnionContributionForRevision {

        @Test
        fun `should return union contribution for specific revision`() {
            // Given
            val unionContribution = mockk<UnionContributionEntity>()
            val revision = mockk<Revision<Int, UnionContributionEntity>>()

            every { unionContribution.authorized } returns true
            every { unionContribution.effectiveDate } returns effectiveDate
            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution
            every { unionContribution.id } returns unionContributionId
            every { unionContributionRepository.findRevision(unionContributionId, 1) } returns Optional.of(revision)
            every { revision.entity } returns unionContribution

            // When
            val result = adapter.getUnionContributionForRevision(requestId, 1)

            // Then
            assertThat(result).isNotNull()
            assertThat(result?.authorized).isTrue()
            assertThat(result?.effectiveDate).isEqualTo(effectiveDate)

            verify(exactly = 1) {
                unionContributionRepository.findByRequestId(requestId)
                unionContributionRepository.findRevision(unionContributionId, 1)
                revision.entity
            }
        }

        @Test
        fun `should throw InvalidRequestIdException when revision not found`() {
            // Given
            val unionContribution = mockk<UnionContributionEntity>()

            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution
            every { unionContribution.id } returns unionContributionId
            every { unionContributionRepository.findRevision(unionContributionId, 1) } returns Optional.empty()

            // When/Then
            assertThatThrownBy { adapter.getUnionContributionForRevision(requestId, 1) }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Revision not found for request $requestId and revision 1")

            verify(exactly = 1) {
                unionContributionRepository.findByRequestId(requestId)
                unionContributionRepository.findRevision(unionContributionId, 1)
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when entity not found`() {
            // Given
            every { unionContributionRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getUnionContributionForRevision(requestId, 1) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { unionContributionRepository.findByRequestId(requestId) }
            verify(exactly = 0) { unionContributionRepository.findRevision(any(), any()) }
        }
    }

    @Nested
    inner class DeleteUnionContribution {

        @Test
        fun `should delete union contribution and update parent entity`() {
            // Given
            val changePersonalData = mockk<ChangePersonalDataRequestEntity>(relaxed = true)
            val unionContribution = mockk<UnionContributionEntity>()

            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns changePersonalData
            every { changePersonalDataRepository.save(any()) } returns changePersonalData
            every { unionContributionRepository.delete(any()) } returns Unit

            // When
            adapter.deleteUnionContribution(requestId)

            // Then
            verify(exactly = 1) {
                unionContributionRepository.findByRequestId(requestId)
                changePersonalDataRepository.findByIdOrNull(requestId)
                changePersonalDataRepository.save(changePersonalData)
                unionContributionRepository.delete(unionContribution)
            }

            verify { changePersonalData.unionContribution = null }
        }

        @Test
        fun `should throw RequestIdNotFoundException when union contribution not found`() {
            // Given
            every { unionContributionRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.deleteUnionContribution(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { unionContributionRepository.findByRequestId(requestId) }
            verify(exactly = 0) {
                changePersonalDataRepository.findByIdOrNull(any())
                changePersonalDataRepository.save(any())
                unionContributionRepository.delete(any())
            }
        }

        @Test
        fun `should throw RequestIdNotFoundException when change personal data not found`() {
            // Given
            val unionContribution = mockk<UnionContributionEntity>()

            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.deleteUnionContribution(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) {
                unionContributionRepository.findByRequestId(requestId)
                changePersonalDataRepository.findByIdOrNull(requestId)
            }
            verify(exactly = 0) {
                changePersonalDataRepository.save(any())
                unionContributionRepository.delete(any())
            }
        }
    }

    @Nested
    inner class GetEntityId {

        @Test
        fun `should return entity id when union contribution exists`() {
            // Given
            val unionContribution = mockk<UnionContributionEntity>()

            every { unionContribution.id } returns unionContributionId
            every { unionContributionRepository.findByRequestId(requestId) } returns unionContribution

            // When
            val result = adapter.getEntityId(requestId)

            // Then
            assertThat(result).isEqualTo(unionContributionId)

            verify(exactly = 1) { unionContributionRepository.findByRequestId(requestId) }
        }

        @Test
        fun `should throw RequestIdNotFoundException when union contribution not found`() {
            // Given
            every { unionContributionRepository.findByRequestId(requestId) } returns null

            // When/Then
            assertThatThrownBy { adapter.getEntityId(requestId) }
                .isInstanceOf(RequestIdNotFoundException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 1) { unionContributionRepository.findByRequestId(requestId) }
        }
    }

    @Nested
    @DisplayName("Patch Current Data with Revision Tests")
    inner class PatchCurrentDataWithRevision {
        @Test
        fun `patchCurrentDataWithRevision should successfully patch current union contribution data with revision data`() {
            // Given
            val requestId = UUID.randomUUID()
            val revision = 2
            val entityId = UUID.randomUUID()

            val requestEntity = mockk<ChangePersonalDataRequestEntity>()

            val currentData = UnionContributionEntity(
                authorized = false,
                effectiveDate = LocalDate.of(2023, 1, 1),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            ).apply { id = entityId }

            val revisionData = UnionContributionEntity(
                authorized = true,
                effectiveDate = LocalDate.of(2024, 6, 15),
                request = requestEntity,
                updateStatus = UpdateStatus.EDITED
            )

            val revisionEntity = mockk<Revision<Int, UnionContributionEntity>>()
            every { revisionEntity.entity } returns revisionData

            every { unionContributionRepository.findByRequestId(requestId) } returns currentData
            every { unionContributionRepository.findRevision(entityId, revision) } returns Optional.of(revisionEntity)
            every { unionContributionRepository.save(any()) } returns currentData

            // When
            adapter.patchCurrentDataWithRevision(requestId, revision)

            // Then
            verify(exactly = 1) { unionContributionRepository.findByRequestId(requestId) }
            verify(exactly = 1) { unionContributionRepository.findRevision(entityId, revision) }
            verify(exactly = 1) { unionContributionRepository.save(currentData) }

            // Vérifier que les données ont été mises à jour
            assertThat(currentData.authorized).isTrue()
            assertThat(currentData.effectiveDate).isEqualTo(LocalDate.of(2024, 6, 15))
            assertThat(currentData.updateStatus).isEqualTo(UpdateStatus.EDITED)
        }
    }
}