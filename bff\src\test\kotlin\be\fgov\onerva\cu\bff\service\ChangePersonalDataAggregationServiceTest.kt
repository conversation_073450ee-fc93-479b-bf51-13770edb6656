package be.fgov.onerva.cu.bff.service

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.web.client.RestTemplate
import be.fgov.onerva.cu.bff.adapter.out.CitizenInfoService
import be.fgov.onerva.cu.bff.adapter.out.CuBackendService
import be.fgov.onerva.cu.bff.adapter.out.RestTemplateUtil
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException
import be.fgov.onerva.cu.bff.exceptions.MissingAuthException
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.bff.model.UnionContribution
import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import be.fgov.onerva.cu.bff.rest.server.priv.model.AddressNullable
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateAggregatedChangePersonalDataRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateBasicInfoRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateUnionContributionRequest
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import kotlinx.coroutines.runBlocking

@ExtendWith(MockKExtension::class)
class ChangePersonalDataAggregationServiceTest {

    @MockK
    private lateinit var citizenInfoService: CitizenInfoService

    @MockK
    private lateinit var cuBackendService: CuBackendService

    @MockK
    private lateinit var restTemplate: RestTemplate

    @MockK
    private lateinit var restTemplateUtil: RestTemplateUtil

    @InjectMockKs
    private lateinit var service: ChangePersonalDataAggregationService

    private val testRequestId = UUID.randomUUID()
    private val testAuthHeader = "Bearer test-token"
    private val testSsin = "12345678901"

    @Nested
    inner class GetAggregatedRequestDataCapture {

        @Test
        fun `should return aggregated data when all backend data is populated`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.requestId).isEqualTo(testRequestId)
            assertThat(result.basicInfo).isEqualTo(basicInfo)
            assertThat(result.citizenInformation?.address).isNotNull()
            assertThat(result.modeOfPayment).isEqualTo(modeOfPayment)
            assertThat(result.unionContribution).isEqualTo(unionContribution)

            verify(exactly = 0) { citizenInfoService.getCitizenInfo(any(), testAuthHeader) }
        }

        @Test
        fun `should fetch citizen service data when citizen information is not populated`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val emptyCitizenInfo = createEmptyCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()
            val citizenServiceInfo = createCitizenInfo()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every {
                runBlocking {
                    cuBackendService.getCitizenInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyCitizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.citizenInformation?.address?.street).isEqualTo("Service Street")
            verify(exactly = 1) { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) }
        }

        @Test
        fun `should fetch citizen service data when mode of payment is not populated`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val emptyModeOfPayment = createEmptyModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()
            val citizenServiceInfo = createCitizenInfo()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyModeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.modeOfPayment?.iban).isEqualTo("BE123456789")
            assertThat(result.modeOfPayment?.otherPersonName).isNull()
            verify(exactly = 1) { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) }
        }

        @Test
        fun `should set other person name when payment mode is 2`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val emptyModeOfPayment = createEmptyModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()
            val citizenServiceInfo = createCitizenInfo().copy(
                paymentMode = 2,
                otherPersonName = "Other Person"
            )

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyModeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.modeOfPayment?.otherPersonName).isEqualTo("Other Person")
            verify(exactly = 1) { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) }
        }

        @Test
        fun `should fetch citizen service data when union contribution is not populated`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val emptyUnionContribution = createEmptyUnionContribution()
            val citizenServiceInfo = createCitizenInfo()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyUnionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.unionContribution?.authorized).isTrue()
            assertThat(result.unionContribution?.effectiveDate).isEqualTo(LocalDate.of(1990, 1, 1))
            verify(exactly = 1) { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) }
        }

        @Test
        fun `should handle union contribution when mandate is not active`(): Unit = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val emptyUnionContribution = createEmptyUnionContribution()
            val citizenServiceInfo = createCitizenInfo().copy(
                unionDue = UnionContribution(
                    validFrom = LocalDate.of(2024, 1, 1),
                    mandateActive = false
                )
            )

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyUnionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.unionContribution?.authorized).isFalse()
            assertThat(result.unionContribution?.effectiveDate).isEqualTo(LocalDate.of(2024, 1, 1))
        }

        @Test
        fun `should handle union contribution when union due is null`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val emptyUnionContribution = createEmptyUnionContribution()
            val citizenServiceInfo = createCitizenInfo().copy(unionDue = null)

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyUnionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns citizenServiceInfo

            // When
            val result = service.getAggregatedRequestDataCapture(testRequestId)

            // Then
            assertThat(result.unionContribution?.authorized).isNull()
            assertThat(result.unionContribution?.effectiveDate).isNull()
        }

        @Test
        fun `should throw CitizenNotFoundException when citizen service returns null`(): Unit = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val emptyEmployeeInfo = createEmptyCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every {
                runBlocking {
                    cuBackendService.getCitizenInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns emptyEmployeeInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution
            every { citizenInfoService.getCitizenInfo(testSsin, testAuthHeader) } returns null

            // When/Then
            assertThatThrownBy { runBlocking { service.getAggregatedRequestDataCapture(testRequestId) } }
                .isInstanceOf(CitizenNotFoundException::class.java)
                .hasMessage("Citizen info not found for request $testRequestId")
        }

        @Test
        fun `should throw MissingAuthException when authorization header is missing`() {
            // Given
            every { restTemplateUtil.captureAuthorizationHeader() } returns null

            // When/Then
            assertThatThrownBy { runBlocking { service.getAggregatedRequestDataCapture(testRequestId) } }
                .isInstanceOf(MissingAuthException::class.java)
                .hasMessage("Authorization header is missing")
        }
    }

    @Nested
    inner class GetAggregatedRequestDataValidate {

        @Test
        fun `should return aggregated validation data with all backend calls`() = runBlocking {
            // Given
            val basicInfo = createBasicInfo()
            val citizenInfo = createPopulatedCitizenInfo()
            val modeOfPayment = createPopulatedModeOfPayment()
            val unionContribution = createPopulatedUnionContribution()
            val onemCitizenInfo = createHistoricalCitizenOnemResponse()
            val c1CitizenInfo = createHistoricalCitizenC1Response()
            val authenticCitizenInfo = createHistoricalCitizenAuthenticSourcesResponse()
            val barema = createHistoricalBaremaResponse()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } } returns basicInfo
            every { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } } returns citizenInfo
            every {
                runBlocking {
                    cuBackendService.getPaymentInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns modeOfPayment
            every {
                runBlocking {
                    cuBackendService.getUnionInfo(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns unionContribution
            every {
                runBlocking {
                    cuBackendService.getCitizenInfoFromOnem(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns onemCitizenInfo
            every {
                runBlocking {
                    cuBackendService.getCitizenInfoFromC1(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns c1CitizenInfo
            every {
                runBlocking {
                    cuBackendService.getCitizenInfoAuthentic(
                        testRequestId,
                        testAuthHeader
                    )
                }
            } returns authenticCitizenInfo
            every { runBlocking { cuBackendService.getHistoricalBarema(testRequestId, testAuthHeader) } } returns barema

            // When
            val result = service.getAggregatedRequestDataValidate(testRequestId)

            // Then
            assertThat(result.requestId).isEqualTo(testRequestId)
            assertThat(result.basicInfo).isEqualTo(basicInfo)
            assertThat(result.citizenInformation).isNotNull()
            assertThat(result.modeOfPayment).isEqualTo(modeOfPayment)
            assertThat(result.unionContribution).isEqualTo(unionContribution)
            assertThat(result.onemCitizenInformation).isEqualTo(onemCitizenInfo)
            assertThat(result.c1CitizenInformation).isEqualTo(c1CitizenInfo)
            assertThat(result.authenticCitizenInformation).isEqualTo(authenticCitizenInfo)
            assertThat(result.barema).isEqualTo(barema)

            verify(exactly = 1) { runBlocking { cuBackendService.getBasicInfo(testRequestId, testAuthHeader) } }
            verify(exactly = 1) { runBlocking { cuBackendService.getCitizenInfo(testRequestId, testAuthHeader) } }
            verify(exactly = 1) { runBlocking { cuBackendService.getPaymentInfo(testRequestId, testAuthHeader) } }
            verify(exactly = 1) { runBlocking { cuBackendService.getUnionInfo(testRequestId, testAuthHeader) } }
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.getCitizenInfoFromOnem(
                        testRequestId,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) { runBlocking { cuBackendService.getCitizenInfoFromC1(testRequestId, testAuthHeader) } }
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.getCitizenInfoAuthentic(
                        testRequestId,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) { runBlocking { cuBackendService.getHistoricalBarema(testRequestId, testAuthHeader) } }
        }

        @Test
        fun `should throw MissingAuthException when authorization header is missing`() {
            // Given
            every { restTemplateUtil.captureAuthorizationHeader() } returns null

            // When/Then
            assertThatThrownBy { runBlocking { service.getAggregatedRequestDataValidate(testRequestId) } }
                .isInstanceOf(MissingAuthException::class.java)
                .hasMessage("Authorization header is missing")
        }
    }

    @Nested
    inner class UpdateAggregateRequest {

        @Test
        fun `should update all aggregated data successfully`() = runBlocking {
            // Given
            val updateRequest = createUpdateAggregatedChangePersonalDataRequest()

            every { restTemplateUtil.captureAuthorizationHeader() } returns testAuthHeader
            every {
                runBlocking {
                    cuBackendService.updateBasicInfo(
                        testRequestId,
                        updateRequest.basicInfo,
                        testAuthHeader
                    )
                }
            } returns Unit
            every {
                runBlocking {
                    cuBackendService.updateCitizenInformation(
                        testRequestId,
                        updateRequest.citizenInformation,
                        testAuthHeader
                    )
                }
            } returns Unit
            every {
                runBlocking {
                    cuBackendService.updateModeOfPayment(
                        testRequestId,
                        updateRequest.modeOfPayment,
                        testAuthHeader
                    )
                }
            } returns Unit
            every {
                runBlocking {
                    cuBackendService.updateUnionContribution(
                        testRequestId,
                        updateRequest.unionContribution,
                        testAuthHeader
                    )
                }
            } returns Unit
            every {
                runBlocking { cuBackendService.assignTaskToUser(testRequestId, testAuthHeader) }
            } returns Unit
            // When
            service.updateAggregateRequest(testRequestId, updateRequest)

            // Then
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.updateBasicInfo(
                        testRequestId,
                        updateRequest.basicInfo,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.updateCitizenInformation(
                        testRequestId,
                        updateRequest.citizenInformation,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.updateModeOfPayment(
                        testRequestId,
                        updateRequest.modeOfPayment,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) {
                runBlocking {
                    cuBackendService.updateUnionContribution(
                        testRequestId,
                        updateRequest.unionContribution,
                        testAuthHeader
                    )
                }
            }
            verify(exactly = 1) { runBlocking { cuBackendService.assignTaskToUser(testRequestId, testAuthHeader) } }
        }

        @Test
        fun `should throw MissingAuthException when authorization header is missing`() {
            // Given
            val updateRequest = createUpdateAggregatedChangePersonalDataRequest()
            every { restTemplateUtil.captureAuthorizationHeader() } returns null

            // When/Then
            assertThatThrownBy { runBlocking { service.updateAggregateRequest(testRequestId, updateRequest) } }
                .isInstanceOf(MissingAuthException::class.java)
                .hasMessage("Authorization header is missing")
        }
    }

    // Helper methods to create test data
    private fun createBasicInfo() = RequestBasicInfoResponse(
        requestDate = LocalDate.of(2024, 1, 1),
        ssin = testSsin,
        firstName = "John",
        lastName = "Doe",
        decisionType = RequestBasicInfoResponse.DecisionType.C2,
        decisionBarema = "01/XXX",
        c9Id = "123",
        documentType = RequestBasicInfoResponse.DocumentType.PAPER,
    )

    private fun createPopulatedCitizenInfo() = CitizenInformationDetailResponse(
        birthDate = LocalDate.of(1990, 1, 1),
        nationality = "Belgian",
        address = Address(
            street = "Main Street",
            houseNumber = "123",
            city = "Brussels",
            zipCode = "1000",
            country = "Belgium"
        )
    )

    private fun createEmptyCitizenInfo() = CitizenInformationDetailResponse(
        birthDate = null,
        nationality = null,
        address = null
    )

    private fun createPopulatedModeOfPayment() = ModeOfPaymentDetailResponse(
        iban = "BE987654321",
        bic = "TESTBIC",
        otherPersonName = "Jane Doe"
    )

    private fun createEmptyModeOfPayment() = ModeOfPaymentDetailResponse(
        iban = null,
        bic = null,
        otherPersonName = null,
    )

    private fun createPopulatedUnionContribution() = UnionContributionDetailResponse(
        authorized = true,
        effectiveDate = LocalDate.of(2024, 1, 1)
    )

    private fun createEmptyUnionContribution() = UnionContributionDetailResponse(
        authorized = null,
        effectiveDate = null
    )

    private fun createCitizenInfo() = CitizenInfoWithAddress(
            firstName = "John",
            lastName = "Doe",
        numbox = 123456,
        address = be.fgov.onerva.cu.bff.model.Address(
            street = "Service Street",
            houseNumber = "456",
            city = "Service City",
            zipCode = "2000",
            country = "Belgium"
        ),
        nationality = "BE",
        iban = "BE123456789",
        bic = "SERVICEBIC",
        paymentMode = 1,
        otherPersonName = null,
        unionDue = UnionContribution(
            validFrom = LocalDate.of(1990, 1, 1),
            mandateActive = true
        )
    )

    private fun createHistoricalCitizenOnemResponse() = HistoricalCitizenOnemResponse(
        birthDate = LocalDate.of(1990, 1, 1),
        nationality = "Belgian",
        address = AddressNullable(
            street = "Test Street",
            houseNumber = "789",
            city = "Test City",
            zipCode = "3000",
            country = "Belgium"
        )
    )

    private fun createHistoricalCitizenAuthenticSourcesResponse() = HistoricalCitizenAuthenticSourcesResponse(
        birthDate = LocalDate.of(1990, 1, 1),
        nationality = "Belgian",
        address = AddressNullable(
            street = "Test Street",
            houseNumber = "789",
            city = "Test City",
            zipCode = "3000",
            country = "Belgium"
        )
    )

    private fun createHistoricalCitizenC1Response() = HistoricalCitizenC1Response(
        birthDate = LocalDate.of(1990, 1, 1),
        nationality = "Belgian",
        address = AddressNullable(
            street = "Test Street",
            houseNumber = "789",
            city = "Test City",
            zipCode = "3000",
            country = "Belgium"
        )
    )

    private fun createHistoricalBaremaResponse() = HistoricalBaremaResponse(
        barema = "Test Barema",
        article = "Test Article"
    )

    private fun createUpdateAggregatedChangePersonalDataRequest() = UpdateAggregatedChangePersonalDataRequest(
        basicInfo = UpdateBasicInfoRequest(
            requestDate = LocalDate.of(2024, 12, 31)
        ),
        citizenInformation = UpdateCitizenInformationRequest(
            birthDate = LocalDate.of(1990, 1, 1),
            nationality = "Updated Belgian",
            address = Address(
                street = "Updated Street",
                houseNumber = "999",
                city = "Updated City",
                zipCode = "9000",
                country = "Updated Country"
            )
        ),
        modeOfPayment = UpdateModeOfPaymentRequest(
            iban = "BE999888777",
            bic = "UPDATEBIC"
        ),
        unionContribution = UpdateUnionContributionRequest(
            authorized = false,
            effectiveDate = LocalDate.of(2024, 12, 31)
        )
    )
}