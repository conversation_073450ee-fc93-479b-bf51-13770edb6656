package be.fgov.onerva.cu.bff.rest.client.citizen.api;

import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoPageDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenInfoApi extends BaseApi {

    public CitizenInfoApi() {
        super(new ApiClient());
    }

    public CitizenInfoApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Search citizens based on a list of SSIN or ids
     * <p><b>200</b> - CitizenInfo
     * @param ssins Get citizen info using a list of ssins. Cannot be used with citizenId. (optional)
     * @param citizenId Search citizens by id(s). CitizenId is the MFX numbox. Cannot be used with ssins. (optional)
     * @param dataReturned This parameter is never used! (optional, default to SUMMARY)
     * @param pageNumber Paging doesn&#39;t make sense with a list of ids. (optional, default to 0)
     * @param pageSize Paging doesn&#39;t make sense with a list of ids. (optional, default to 10)
     * @return CitizenInfoPageDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public CitizenInfoPageDTO searchCitizenInfo(List<String> ssins, List<Integer> citizenId, String dataReturned, Integer pageNumber, Integer pageSize) throws RestClientException {
        return searchCitizenInfoWithHttpInfo(ssins, citizenId, dataReturned, pageNumber, pageSize).getBody();
    }

    /**
     * 
     * Search citizens based on a list of SSIN or ids
     * <p><b>200</b> - CitizenInfo
     * @param ssins Get citizen info using a list of ssins. Cannot be used with citizenId. (optional)
     * @param citizenId Search citizens by id(s). CitizenId is the MFX numbox. Cannot be used with ssins. (optional)
     * @param dataReturned This parameter is never used! (optional, default to SUMMARY)
     * @param pageNumber Paging doesn&#39;t make sense with a list of ids. (optional, default to 0)
     * @param pageSize Paging doesn&#39;t make sense with a list of ids. (optional, default to 10)
     * @return ResponseEntity&lt;CitizenInfoPageDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<CitizenInfoPageDTO> searchCitizenInfoWithHttpInfo(List<String> ssins, List<Integer> citizenId, String dataReturned, Integer pageNumber, Integer pageSize) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "ssins", ssins));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "citizenId", citizenId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "dataReturned", dataReturned));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageNumber", pageNumber));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenInfoPageDTO> localReturnType = new ParameterizedTypeReference<CitizenInfoPageDTO>() {};
        return apiClient.invokeAPI("/citizen/info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
