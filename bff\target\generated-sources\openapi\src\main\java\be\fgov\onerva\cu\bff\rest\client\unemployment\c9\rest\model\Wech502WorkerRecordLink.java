/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.HandledWorkerRecordLinkType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech502OccupationLink;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502WorkerRecordLink
 */
@JsonPropertyOrder({
  Wech502WorkerRecordLink.JSON_PROPERTY_EMPLOYER_CLASS,
  Wech502WorkerRecordLink.JSON_PROPERTY_WORKER_CODE,
  Wech502WorkerRecordLink.JSON_PROPERTY_HANDLED_WORKER_RECORD_LINK,
  Wech502WorkerRecordLink.JSON_PROPERTY_OCCUPATION_LINK
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502WorkerRecordLink {
  public static final String JSON_PROPERTY_EMPLOYER_CLASS = "employerClass";
  private String employerClass;

  public static final String JSON_PROPERTY_WORKER_CODE = "workerCode";
  private String workerCode;

  public static final String JSON_PROPERTY_HANDLED_WORKER_RECORD_LINK = "handledWorkerRecordLink";
  private HandledWorkerRecordLinkType handledWorkerRecordLink;

  public static final String JSON_PROPERTY_OCCUPATION_LINK = "occupationLink";
  private Wech502OccupationLink occupationLink;

  public Wech502WorkerRecordLink() {
  }

  public Wech502WorkerRecordLink employerClass(String employerClass) {
    
    this.employerClass = employerClass;
    return this;
  }

  /**
   * Get employerClass
   * @return employerClass
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_CLASS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerClass() {
    return employerClass;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_CLASS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerClass(String employerClass) {
    this.employerClass = employerClass;
  }

  public Wech502WorkerRecordLink workerCode(String workerCode) {
    
    this.workerCode = workerCode;
    return this;
  }

  /**
   * Get workerCode
   * @return workerCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_WORKER_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getWorkerCode() {
    return workerCode;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setWorkerCode(String workerCode) {
    this.workerCode = workerCode;
  }

  public Wech502WorkerRecordLink handledWorkerRecordLink(HandledWorkerRecordLinkType handledWorkerRecordLink) {
    
    this.handledWorkerRecordLink = handledWorkerRecordLink;
    return this;
  }

  /**
   * Get handledWorkerRecordLink
   * @return handledWorkerRecordLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_WORKER_RECORD_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledWorkerRecordLinkType getHandledWorkerRecordLink() {
    return handledWorkerRecordLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_WORKER_RECORD_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledWorkerRecordLink(HandledWorkerRecordLinkType handledWorkerRecordLink) {
    this.handledWorkerRecordLink = handledWorkerRecordLink;
  }

  public Wech502WorkerRecordLink occupationLink(Wech502OccupationLink occupationLink) {
    
    this.occupationLink = occupationLink;
    return this;
  }

  /**
   * Get occupationLink
   * @return occupationLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502OccupationLink getOccupationLink() {
    return occupationLink;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationLink(Wech502OccupationLink occupationLink) {
    this.occupationLink = occupationLink;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502WorkerRecordLink wech502WorkerRecordLink = (Wech502WorkerRecordLink) o;
    return Objects.equals(this.employerClass, wech502WorkerRecordLink.employerClass) &&
        Objects.equals(this.workerCode, wech502WorkerRecordLink.workerCode) &&
        Objects.equals(this.handledWorkerRecordLink, wech502WorkerRecordLink.handledWorkerRecordLink) &&
        Objects.equals(this.occupationLink, wech502WorkerRecordLink.occupationLink);
  }

  @Override
  public int hashCode() {
    return Objects.hash(employerClass, workerCode, handledWorkerRecordLink, occupationLink);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502WorkerRecordLink {\n");
    sb.append("    employerClass: ").append(toIndentedString(employerClass)).append("\n");
    sb.append("    workerCode: ").append(toIndentedString(workerCode)).append("\n");
    sb.append("    handledWorkerRecordLink: ").append(toIndentedString(handledWorkerRecordLink)).append("\n");
    sb.append("    occupationLink: ").append(toIndentedString(occupationLink)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

