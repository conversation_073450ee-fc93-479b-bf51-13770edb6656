package be.fgov.onerva.cu.bff.adapter.out

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoApi
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient
import io.mockk.every
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk

@ExtendWith(MockKExtension::class)
class CitizenInfoApiFactoryTest {

    @MockK
    lateinit var defaultCitizenInfoApi: CitizenInfoApi

    @Test
    fun `invoke should create CitizenInfoApi with auth token in headers`() {
        // Given
        val authToken = "Bearer token123"
        val basePath = "http://api.example.com"
        val apiClient = mockk<ApiClient>()

        every { defaultCitizenInfoApi.apiClient } returns apiClient
        every { apiClient.basePath } returns basePath

        val factory = CitizenInfoApiFactory(defaultCitizenInfoApi)

        // When
        val result = factory(authToken)

        // Then
        assertThat(result).isNotNull
        assertThat(result.apiClient.basePath).isEqualTo(basePath)
    }
}