package be.fgov.onerva.cu.backend.cucumber.steps

import java.math.BigDecimal
import java.sql.Date
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import be.fgov.onerva.cu.backend.integration.helpers.JdbcHelper
import be.fgov.onerva.cu.backend.lookup.CityDTO
import be.fgov.onerva.cu.backend.lookup.CountryDTO
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.cu.backend.lookup.NationalityDTO
import be.fgov.onerva.cu.backend.lookup.PostalCodeLanguageRegimeDTO
import be.fgov.onerva.cu.backend.lookup.StreetDTO
import be.fgov.onerva.cu.rest.priv.model.Address
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.FieldSource
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.person.rest.model.BankAccountDTO
import be.fgov.onerva.person.rest.model.CitizenInfoDTO
import be.fgov.onerva.person.rest.model.CitizenInfoPageDTO
import be.fgov.onerva.person.rest.model.CitizenInfoUnionDueDTO
import be.fgov.onerva.person.rest.model.ForeignAddressDTO
import be.fgov.onerva.registerproxyservice.api.CitizenApi
import be.fgov.onerva.registerproxyservice.rest.model.Period
import be.fgov.onerva.registerproxyservice.rest.model.RegisterAddress
import be.fgov.onerva.registerproxyservice.rest.model.RegisterNationality
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPerson
import be.fgov.onerva.registerproxyservice.rest.model.RegisterPersonNames
import be.fgov.onerva.registerproxyservice.rest.model.ResidentialAddress
import com.fasterxml.jackson.databind.ObjectMapper
import io.cucumber.datatable.DataTable
import io.cucumber.java.en.Given
import io.cucumber.java.en.Then
import io.cucumber.java.en.When
import io.cucumber.spring.ScenarioScope
import io.mockk.every

@ScenarioScope
class CitizenInformationSteps : BaseSteps() {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @Autowired
    private lateinit var objectMapper: ObjectMapper

    @Autowired
    private lateinit var jdbcHelper: JdbcHelper

    @Autowired
    private lateinit var citizenInfoApi: CitizenInfoApi

    @Autowired
    private lateinit var lookupService: LookupService

    @Autowired
    private lateinit var registryApi: CitizenApi

    private lateinit var updateRequestData: Map<String, String>

    @Given("a citizen with SSIN {string} exists with personal information")
    fun setupCitizenInfo(ssin: String) {
        every {
            citizenInfoApi.searchCitizenInfo(
                listOf(ssin),
                null,
                "SUMMARY",
                0,
                10
            )
        } returns CitizenInfoPageDTO(
            pageNumber = 0,
            pageSize = 10,
            totalElements = 1,
            isFirst = true,
            isLast = true,
            content = listOf(
                CitizenInfoDTO(
                    firstName = "John",
                    lastName = "Doe",
                    numBox = BigDecimal.valueOf(42),
                    flagNation = BigDecimal.valueOf(151),
                    iban = "****************",
                    address = "Main Street 123 Box A",
                    postalCode = "1000",
                    bankAccount = BankAccountDTO(
                        iban = "****************",
                        bic = null,
                    ),
                    addressObj = ForeignAddressDTO(
                        city = "Brussels",
                        street = "Main Street",
                        box = "Box A",
                        countryCode = 150,
                        zip = "1000",
                        number = "123",
                    ),
                    unionDue = CitizenInfoUnionDueDTO(
                        mandateActive = false,
                        validFrom = LocalDate.of(2021, 1, 1),
                    )
                )
            )
        )
        every {
            registryApi.getCitizenInfosBySsin(
                "***********", null, true, false, false, null, true, true, false
            )
        } returns RegisterPerson().also {
            it.addresses = listOf(
                ResidentialAddress().apply {
                    atType = RegisterAddress.AtTypeEnum.RESIDENTIAL_ADDRESS
                    validityPeriod =
                        Period().apply {
                            beginDate = 1490565600000
                            endDate = null
                        }
                    radiated = false
                    cityCode = 21015
                    streetCode = 329
                    postalCode = "1070"
                    houseNumber = "37"
                    boxNumber = "ET01"
                    countryCode = 1
                    regionCode = "9"
                }
            )
            it.birthdate = LocalDate.of(2000, 1, 1)
            it.deceaseDate = null
            it.gender = null
            it.identitification = 0
            it.lastName = "Doe"
            it.names = listOf(
                RegisterPersonNames().apply {
                    firstName = "John"
                    seq = 1
                },
                RegisterPersonNames().apply {
                    firstName = "Deere"
                    seq = 2
                }
            )
            it.nationalities = listOf(
                RegisterNationality().apply {
                    nationalityCode = 150
                    validityBeginDate = 946684800
                }
            )
            it.replacedSsin = null
            it.ssin = "85050599890"
            it.status = null
        }

        val streetDTO = StreetDTO(
            lookupId = 1,
            code = "1234",
            descNl = "Hoofdstraat",
            descFr = "Rue Principale"
        )

        every { lookupService.getStreetNameByStreetCode(329, 1070) } returns streetDTO

        every { lookupService.lookupPostalCodeLanguageRegime(any()) } returns PostalCodeLanguageRegimeDTO(
            languageRegimeCode = "N1",
            beginDate = LocalDate.of(2020, 1, 1),
            endDate = LocalDate.of(2023, 1, 1),
            lookupId = 21,
            code = "1000",
            descFr = "Bruxelles",
            descNl = "Brussel"
        )

        every { lookupService.getCityByPostalCode(any()) } returns CityDTO(
            code = "1000",
            descNl = "Brussel",
            descFr = "Bruxelles",
            lookupId = 50,
            nisCode = "4542456",
            beginDate = LocalDate.of(2020, 1, 1),
        )


        every { lookupService.getCountryByCode(any()) } returns CountryDTO(
            code = "150",
            descNl = "België",
            descFr = "Belgique",
            lookupId = 5455
        )

        every { lookupService.getNationalityByCode(any()) } returns NationalityDTO(
            code = "150",
            descNl = "België",
            descFr = "Belgique",
            lookupId = 5455
        )
    }

    @When("I select the following sources for citizen information:")
    fun selectSourcesForCitizenInformation(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val fieldSources = dataTable.asMaps().map { row ->
            FieldSource().apply {
                fieldName = row["fieldName"]
                source = ExternalSource.valueOf(row["source"]!!)
            }
        }

        val request = SelectFieldSourcesRequest().apply {
            this.fieldSources = fieldSources
        }

        testContext.result = mockMvc.perform(
            put("/api/requests/$requestId/citizen-information/select")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .withAuth()
        )
    }

    @Then("the citizen information should be updated with:")
    fun verifyCitizenInformationUpdated(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val expectedValues = dataTable.asMap()
        val citizenInformation = jdbcHelper.getCitizenInformation(requestId)

        expectedValues.forEach { (key, value) ->
            if (key == "birth_date") {
                // Handle date conversion
                assertThat(citizenInformation).containsEntry(key, java.sql.Date.valueOf(value))
            } else if (key == "city") {
                // Handle date conversion
                assertThat(citizenInformation).containsEntry(key, value ?: "")
            } else {
                assertThat(citizenInformation).containsEntry(key, value)
            }
        }
    }

    @Then("the citizen information response status should be {int}")
    fun verifyResponseStatus(status: Int) {
        testContext.result!!.andExpect(status().`is`(status))
    }

    // New methods for updating citizen information

    @Given("nationality lookups are configured")
    fun setupNationalityLookups() {
        // Setup nationality lookup mocks
        setupNationalityLookup(1, "150", "Belgique", "België")
        setupNationalityLookup(2, "111", "France", "Frankrijk")
        every { lookupService.lookupNationality("xxx") } returns emptyList()
    }

    @When("I update the citizen information with:")
    fun updateCitizenInformation(dataTable: DataTable) {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        updateRequestData = dataTable.asMap()

        val request = UpdateCitizenInformationRequest().apply {
            birthDate = if (updateRequestData["birthDate"].isNullOrBlank()) null
            else LocalDate.parse(updateRequestData["birthDate"])
            nationality = updateRequestData["nationality"]

            address = Address().apply {
                street = updateRequestData["street"] ?: ""
                houseNumber = updateRequestData["houseNumber"] ?: ""
                boxNumber = updateRequestData["boxNumber"] ?: ""
                zipCode = updateRequestData["zipCode"] ?: ""
                city = updateRequestData["city"] ?: ""
                country = updateRequestData["country"] ?: ""
            }
        }

        testContext.result = mockMvc.perform(
            put("/api/requests/$requestId/citizen-information")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .withAuth()
        )
    }

    @Then("the database should be updated with the citizen information")
    fun verifyDatabaseUpdatedWithCitizenInfo() {
        val requestId = requireNotNull(testContext.requestId) { "Request ID must not be null" }

        val citizenInformation = jdbcHelper.getCitizenInformation(requestId)

        assertThat(citizenInformation)
            .containsEntry("birth_date", Date.valueOf(updateRequestData["birthDate"]))
            .containsEntry("nationality", updateRequestData["nationality"])
            .containsEntry("street", updateRequestData["street"])
            .containsEntry("box_number", updateRequestData["boxNumber"])
            .containsEntry("house_number", updateRequestData["houseNumber"])
            .containsEntry("zip_code", updateRequestData["zipCode"])
            .containsEntry("city", updateRequestData["city"])
            .containsEntry("country", updateRequestData["country"])
            .containsEntry("update_status", "EDITED")
    }

    private fun setupNationalityLookup(id: Int, code: String, descFr: String, descNl: String) {
        every { lookupService.lookupNationality(code) } returns listOf(
            NationalityDTO(
                lookupId = id,
                code = code,
                descFr = descFr,
                descNl = descNl
            )
        )
    }
}