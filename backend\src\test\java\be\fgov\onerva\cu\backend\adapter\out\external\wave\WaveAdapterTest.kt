package be.fgov.onerva.cu.backend.adapter.out.external.wave

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.assertj.core.api.Assertions.tuple
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.ValueSource
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO
import be.fgov.onerva.cu.backend.wo.dto.WoTaskDTO
import be.fgov.onerva.cu.backend.wo.service.WoFacadeService
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class WaveAdapterTest {
    @MockK
    lateinit var woFacadeService: WoFacadeService

    @InjectMockKs
    lateinit var waveAdapter: WaveAdapter

    @Nested
    inner class CreateChangePersonalDataTask {
        @Test
        fun `should create task successfully`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val changePersonalData = CreateChangePersonalDataTaskCommand(
                c9id = 12345,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                entityCode = "456",
                paymentInstitution = 456,
                dossierId = "SO123-OP123",
                sectOp = "4412"
            )
            val processMetadataList = slot<List<WoMetadataDTO>>()
            val taskMetadataList = slot<List<WoMetadataDTO>>()

            every {
                woFacadeService.createTask(
                    null,
                    WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE,
                    42,
                    "E456",
                    capture(processMetadataList),
                    capture(taskMetadataList)
                )
            } returns WoTaskDTO("40001", "50001", "OPEN", "Treatment")

            // When
            val waveTask = waveAdapter.createChangePersonalDataTask(requestId, changePersonalData)

            // Then
            verify(exactly = 1) {
                woFacadeService.createTask(
                    null,
                    WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE,
                    42,
                    "E456",
                    any(),
                    any()
                )
            }

            assertThat(processMetadataList.captured).hasSize(9)
                .extracting("code", "value")
                .contains(
                    tuple("CU_REQUEST_ID", requestId.toString()),
                    tuple("CU_C9_TYPE", "400"),
                    tuple("CU_RECEPTION_DATE", "2024-01-01"),
                    tuple("CU_REQUEST_DATE", "2024-01-02"),
                    tuple("CU_DOSSIER_ID", "SO123-OP123"),
                    tuple("CU_ENTITY", "456"),
                    tuple("CU_PAYMENT_INSTITUTION", "4412"),
                    tuple("CU_DECISION_BAREMA", ""),
                    tuple("CU_DECISION_TYPE", "")
                )
            assertThat(taskMetadataList.captured).hasSize(4)
                .extracting("code", "value")
                .contains(
                    tuple("CU_REQUEST_ID", requestId.toString()),
                    tuple("CU_RECEPTION_DATE", "2024-01-01"),
                    tuple("CU_ENTITY", "456"),
                    tuple("CU_C9_TYPE", "400")
                )
            assertThat(waveTask).isNotNull()
            assertThat(waveTask.processId).isEqualTo("40001")
            assertThat(waveTask.taskId).isEqualTo("50001")
            assertThat(waveTask.status).isEqualTo(WaveTaskStatus.OPEN)
        }

        @Test
        fun `should use default assignee when entityCode is null`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val changePersonalData = CreateChangePersonalDataTaskCommand(
                c9id = 12345,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                entityCode = null,
                paymentInstitution = 456,
                dossierId = "SO123-OP123",
                sectOp = "4412"
            )

            every {
                woFacadeService.createTask(
                    null,
                    WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE,
                    42,
                    "RVA-ONEM",
                    any(),
                    any()
                )
            } returns WoTaskDTO("40001", "50001", "OPEN", "Treatment")

            // When
            waveAdapter.createChangePersonalDataTask(requestId, changePersonalData)

            // Then
            verify(exactly = 1) {
                woFacadeService.createTask(
                    null,
                    WaveTaskPort.CHANGE_PERSONAL_DATA_CAPTURE,
                    42,
                    "RVA-ONEM",
                    any(),
                    any()
                )
            }
        }

        @Test
        fun `should throw RuntimeException when WoFacadeService fails`() {
            // Given
            val requestId = UUID.fromString("123e4567-e89b-12d3-a456-************")
            val changePersonalData = CreateChangePersonalDataTaskCommand(
                c9id = 12345,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                dossierId = "DOS123",
                entityCode = "ENT456",
                paymentInstitution = 456,
                sectOp = "4412"
            )

            every {
                woFacadeService.createTask(any(), any(), any(), any(), any(), any())
            } throws RuntimeException("Service failed")

            // When/Then
            assertThatThrownBy { waveAdapter.createChangePersonalDataTask(requestId, changePersonalData) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Error creating task for c9id: 12345")
        }
    }

    @Nested
    inner class CloseTask {
        @Test
        fun `should close task and return true when task can be closed`() {
            // Given
            val taskId = "12345"
            every { woFacadeService.checkTaskCanBeUpdated(12345L) } returns true
            every { woFacadeService.closeTask(12345L) } returns Unit

            // When
            val result = waveAdapter.closeTask(taskId)

            // Then
            assertThat(result).isTrue()
            verify(exactly = 1) {
                woFacadeService.checkTaskCanBeUpdated(12345L)
                woFacadeService.closeTask(12345L)
            }
        }

        @Test
        fun `should return false when task cannot be closed`() {
            // Given
            val taskId = "12345"
            every { woFacadeService.checkTaskCanBeUpdated(12345L) } returns false

            // When
            val result = waveAdapter.closeTask(taskId)

            // Then
            assertThat(result).isFalse()
            verify(exactly = 1) { woFacadeService.checkTaskCanBeUpdated(12345L) }
            verify(exactly = 0) { woFacadeService.closeTask(any()) }
        }

        @ParameterizedTest
        @ValueSource(strings = ["invalid", "123abc", ""])
        fun `should throw NumberFormatException for invalid taskId formats`(taskId: String) {
            // When/Then
            assertThatThrownBy { waveAdapter.closeTask(taskId) }
                .isInstanceOf(NumberFormatException::class.java)
        }
    }

    @Nested
    inner class CreateChangePersonalDataValidateTask {
        @Test
        fun `should create validation task successfully`() {
            // Given
            val requestId = UUID.randomUUID()
            val processId = "12345"
            val assignee = "test-user"
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 123L,
                c9Type = "TEST_TYPE",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                entityCode = "TEST_ENTITY",
                paymentInstitution = 123,
                dossierId = "TEST_DOS",
                sectOp = "4412"
            )

            val taskMetadataSlot = slot<List<WoMetadataDTO>>()

            every {
                woFacadeService.createTask(
                    12345L,
                    WaveTaskPort.VALIDATION_DATA,
                    42,
                    assignee,
                    emptyList(),
                    capture(taskMetadataSlot)
                )
            } returns WoTaskDTO("67890", "11111", "OPEN", "Treatment")

            // When
            val result = waveAdapter.createChangePersonalDataValidateTask(requestId, processId, assignee, command)

            // Then
            verify(exactly = 1) {
                woFacadeService.createTask(
                    12345L,
                    WaveTaskPort.VALIDATION_DATA,
                    42,
                    assignee,
                    emptyList(),
                    any()
                )
            }

            assertThat(taskMetadataSlot.captured)
                .hasSize(4)
                .extracting("code", "value")
                .contains(
                    tuple("CU_REQUEST_ID", requestId.toString()),
                    tuple("CU_RECEPTION_DATE", "2024-01-01"),
                    tuple("CU_ENTITY", "TEST_ENTITY"),
                    tuple("CU_C9_TYPE", "TEST_TYPE")
                )

            assertThat(result).isNotNull()
            assertThat(result.processId).isEqualTo("67890")
            assertThat(result.taskId).isEqualTo("11111")
            assertThat(result.status).isEqualTo(WaveTaskStatus.OPEN)
        }

        @Test
        fun `should handle null entityCode in validation task`() {
            // Given
            val requestId = UUID.randomUUID()
            val processId = "12345"
            val assignee = "test-user"
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 123L,
                c9Type = "TEST_TYPE",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                entityCode = null,
                paymentInstitution = 123,
                dossierId = "TEST_DOS",
                sectOp = "4412",
            )

            val taskMetadataSlot = slot<List<WoMetadataDTO>>()

            every {
                woFacadeService.createTask(
                    12345L,
                    WaveTaskPort.VALIDATION_DATA,
                    42,
                    assignee,
                    emptyList(),
                    capture(taskMetadataSlot)
                )
            } returns WoTaskDTO("67890", "11111", "OPEN", "Treatment")

            // When
            val result = waveAdapter.createChangePersonalDataValidateTask(requestId, processId, assignee, command)

            // Then
            verify(exactly = 1) {
                woFacadeService.createTask(
                    12345L,
                    WaveTaskPort.VALIDATION_DATA,
                    42,
                    assignee,
                    emptyList(),
                    any()
                )
            }

            assertThat(taskMetadataSlot.captured)
                .hasSize(4)
                .extracting("code", "value")
                .contains(
                    tuple("CU_REQUEST_ID", requestId.toString()),
                    tuple("CU_RECEPTION_DATE", "2024-01-01"),
                    tuple("CU_ENTITY", null),
                    tuple("CU_C9_TYPE", "TEST_TYPE")
                )
        }

        @Test
        fun `should throw RuntimeException when woFacadeService fails for validation task`() {
            // Given
            val requestId = UUID.randomUUID()
            val processId = "12345"
            val assignee = "test-user"
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 123L,
                c9Type = "TEST_TYPE",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.of(2024, 1, 1),
                requestDate = LocalDate.of(2024, 1, 2),
                entityCode = "TEST_ENTITY",
                paymentInstitution = 123,
                dossierId = "TEST_DOS",
                sectOp = "4412",
            )

            every {
                woFacadeService.createTask(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any()
                )
            } throws RuntimeException("Service error")

            // When/Then
            assertThatThrownBy {
                waveAdapter.createChangePersonalDataValidateTask(requestId, processId, assignee, command)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Error creating task for c9id: 123")
        }
    }

    @Nested
    inner class AssignTaskToUser {
        @Test
        fun `should assign task to user successfully`() {
            // Given
            val taskId = "12345"
            val user = "test-user"

            every { woFacadeService.assignTaskToUser(12345L, user) } returns Unit
            every { woFacadeService.checkTaskCanBeUpdated(12345L) } returns true

            // When
            waveAdapter.assignTaskToUser(taskId, user)

            // Then
            verify(exactly = 1) { woFacadeService.assignTaskToUser(12345L, user) }
        }

        @Test
        fun `should throw NumberFormatException when taskId is not valid`() {
            // Given
            val taskId = "invalid"
            val user = "test-user"

            // When/Then
            assertThatThrownBy { waveAdapter.assignTaskToUser(taskId, user) }
                .isInstanceOf(NumberFormatException::class.java)

            verify(exactly = 0) { woFacadeService.assignTaskToUser(any(), any()) }
        }

        @Test
        fun `should propagate exception from assignTaskToUser`() {
            // Given
            val taskId = "12345"
            val user = "test-user"
            every { woFacadeService.checkTaskCanBeUpdated(12345L) } returns true
            
            every { woFacadeService.assignTaskToUser(12345L, user) } throws RuntimeException("Assignment failed")

            // When/Then
            assertThatThrownBy { waveAdapter.assignTaskToUser(taskId, user) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Assignment failed")

            verify(exactly = 1) { woFacadeService.assignTaskToUser(12345L, user) }
        }
    }

    @Nested
    inner class UpdateChangePersonalDataTaskDecision {
        @ParameterizedTest
        @EnumSource(DecisionType::class)
        fun `should update task decision with correct metadata for each decision type`(decisionType: DecisionType) {
            // Given
            val processId = "12345"
            val taskId = "67890"
            val decisionBarema = "TEST_BAREMA"
            val command = UpdateChangePersonalDataDecisionCommand(
                user = "the-user",
                decisionType = decisionType,
                decisionBarema = decisionBarema,
                decisionDate = LocalDate.of(2024, 1, 1)
            )

            val metadataSlot = slot<List<WoMetadataDTO>>()

            every {
                woFacadeService.patchProcessData(
                    12345L,
                    67890L,
                    capture(metadataSlot)
                )
            } returns Unit

            // When
            waveAdapter.updateChangePersonalDataTaskDecision(processId, taskId, command)

            // Then
            verify(exactly = 1) {
                woFacadeService.patchProcessData(
                    12345L,
                    67890L,
                    any()
                )
            }

            assertThat(metadataSlot.captured).hasSize(2)
                .extracting("code", "value")
                .containsExactly(
                    tuple("CU_DECISION_TYPE", decisionType.toString()),
                    tuple("CU_DECISION_BAREMA", decisionBarema)
                )
        }

        @Test
        fun `should use N-A value when decisionBarema is null`() {
            // Given
            val processId = "12345"
            val taskId = "67890"
            val command = UpdateChangePersonalDataDecisionCommand(
                user = "the-user",
                decisionType = DecisionType.C2,
                decisionBarema = null,
                decisionDate = LocalDate.of(2024, 1, 1)
            )

            val metadataSlot = slot<List<WoMetadataDTO>>()

            every {
                woFacadeService.patchProcessData(
                    12345L,
                    67890L,
                    capture(metadataSlot)
                )
            } returns Unit

            // When
            waveAdapter.updateChangePersonalDataTaskDecision(processId, taskId, command)

            // Then
            assertThat(metadataSlot.captured)
                .extracting("code", "value")
                .contains(tuple("CU_DECISION_BAREMA", "N/A"))
        }

        @ParameterizedTest
        @ValueSource(strings = ["invalid", "123abc", ""])
        fun `should throw NumberFormatException for invalid processId formats`(invalidProcessId: String) {
            // Given
            val taskId = "67890"
            val command = UpdateChangePersonalDataDecisionCommand(
                user = "the-user",
                decisionType = DecisionType.C2,
                decisionBarema = null,
                decisionDate = LocalDate.of(2024, 1, 1)
            )

            // When/Then
            assertThatThrownBy {
                waveAdapter.updateChangePersonalDataTaskDecision(invalidProcessId, taskId, command)
            }
                .isInstanceOf(NumberFormatException::class.java)

            verify(exactly = 0) { woFacadeService.patchProcessData(any(), any(), any()) }
        }

        @ParameterizedTest
        @ValueSource(strings = ["invalid", "123abc", ""])
        fun `should throw NumberFormatException for invalid taskId formats`(invalidTaskId: String) {
            // Given
            val processId = "12345"
            val command = UpdateChangePersonalDataDecisionCommand(
                user = "the-user",
                decisionType = DecisionType.C2,
                decisionBarema = null,
                decisionDate = LocalDate.of(2024, 1, 1)
            )

            // When/Then
            assertThatThrownBy {
                waveAdapter.updateChangePersonalDataTaskDecision(processId, invalidTaskId, command)
            }
                .isInstanceOf(NumberFormatException::class.java)

            verify(exactly = 0) { woFacadeService.patchProcessData(any(), any(), any()) }
        }

        @Test
        fun `should propagate exception from woFacadeService`() {
            // Given
            val processId = "12345"
            val taskId = "67890"
            val command = UpdateChangePersonalDataDecisionCommand(
                user = "the-user",
                decisionType = DecisionType.C2,
                decisionBarema = null,
                decisionDate = LocalDate.of(2024, 1, 1)
            )

            every {
                woFacadeService.patchProcessData(
                    12345L,
                    67890L,
                    any()
                )
            } throws RuntimeException("Service error")

            // When/Then
            assertThatThrownBy {
                waveAdapter.updateChangePersonalDataTaskDecision(processId, taskId, command)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Service error")
        }
    }

    @Nested
    inner class CloseProcess {
        @Test
        fun `should close process successfully`() {
            // Given
            val processId = "12345"

            every { woFacadeService.closeProcess(12345L) } returns Unit

            // When
            waveAdapter.closeProcess(processId)

            // Then
            verify(exactly = 1) { woFacadeService.closeProcess(12345L) }
        }

        @ParameterizedTest
        @ValueSource(strings = ["invalid", "123abc", ""])
        fun `should throw NumberFormatException for invalid processId formats`(invalidProcessId: String) {
            // When/Then
            assertThatThrownBy { waveAdapter.closeProcess(invalidProcessId) }
                .isInstanceOf(NumberFormatException::class.java)

            verify(exactly = 0) { woFacadeService.closeProcess(any()) }
        }

        @Test
        fun `should propagate exception from woFacadeService`() {
            // Given
            val processId = "12345"

            every { woFacadeService.closeProcess(12345L) } throws RuntimeException("Process close failed")

            // When/Then
            assertThatThrownBy { waveAdapter.closeProcess(processId) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Process close failed")

            verify(exactly = 1) { woFacadeService.closeProcess(12345L) }
        }
    }
}