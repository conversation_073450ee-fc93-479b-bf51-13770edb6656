package be.fgov.onerva.cu.backend.cucumber.steps.context

import java.util.UUID
import org.springframework.stereotype.Component
import org.springframework.test.web.servlet.ResultActions
import io.cucumber.spring.ScenarioScope

@Component
@ScenarioScope
class TestContext {
    var requestId: UUID? = null
    var authenticatedUsername: String? = null
    var authenticatedRoles: Array<String> = emptyArray()
    var result: ResultActions? = null
}