package be.fgov.onerva.cu.backend.application.mapper

import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenSnapshot
import be.fgov.onerva.cu.backend.application.exception.InvalidExternalDataException

class DomainMapperTest {

    @Nested
    inner class ToHistoricalCitizenAuthenticSourcesTest {

        @Test
        fun `should map all fields correctly when all required fields are present`() {
            // Given
            val snapshot = HistoricalCitizenSnapshot(
                firstName = "John",
                lastName = "Doe",
                numbox = 42,
                nationality = "BE",
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15)
                ),
                iban = "****************",
                bic = "CMRPL",
                otherPersonName = "Jane Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                bankAccountValueDate = LocalDate.of(2023, 5, 15),
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2023, 6, 1)
            )

            // When
            val result = snapshot.toHistoricalCitizenAuthenticSources()

            // Then
            assertThat(result).isNotNull
            assertThat(result).extracting(
                "firstName",
                "lastName",
                "nationality",
                "birthDate"
            ).containsExactly(
                "John",
                "Doe",
                "BE",
                LocalDate.of(1990, 1, 1)
            )

            assertThat(result.address).extracting(
                "country",
                "street",
                "houseNumber",
                "city",
                "zipCode",
                "boxNumber"
            ).containsExactly(
                "Belgium",
                "Main Street",
                "42",
                "Brussels",
                "1000",
                "A"
            )
        }

        @Test
        fun `should throw InvalidExternalDataException when birthDate is null`() {
            // Given
            val snapshot = HistoricalCitizenSnapshot(
                firstName = "John",
                lastName = "Doe",
                numbox = 42,
                nationality = "BE",
                birthDate = null,
                iban = "****************",
                bic = "CMRPL",
                otherPersonName = "Jane Doe",
                bankAccountValueDate = LocalDate.of(2023, 5, 15),
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2023, 6, 1),
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15)
                ),

                )

            // When/Then
            assertThrows<InvalidExternalDataException> {
                snapshot.toHistoricalCitizenAuthenticSources()
            }
        }
    }

    @Nested
    inner class ToHistoricalCitizenOnemTest {

        @Test
        fun `should map all fields correctly when all required fields are present`() {
            // Given
            val snapshot = HistoricalCitizenSnapshot(
                firstName = "John",
                lastName = "Doe",
                numbox = 42,
                nationality = "BE",
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15)
                ),
                iban = "****************",
                bic = "CMRPL",
                otherPersonName = "Jane Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                bankAccountValueDate = LocalDate.of(2023, 5, 15),
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2023, 6, 1)
            )

            // When
            val result = snapshot.toHistoricalCitizenOnem()

            // Then
            assertThat(result).isNotNull
            assertThat(result).extracting(
                "firstName",
                "lastName",
                "numbox",
                "nationality",
                "iban",
                "bic",
                "otherPersonName",
                "birthDate",
                "bankAccountValueDate",
                "paymentMode",
                "authorized",
                "effectiveDate"
            ).containsExactly(
                "John",
                "Doe",
                42,
                "BE",
                "****************",
                "CMRPL",
                "Jane Doe",
                LocalDate.of(1990, 1, 1),
                LocalDate.of(2023, 5, 15),
                1,
                true,
                LocalDate.of(2023, 6, 1)
            )

            assertThat(result.address).extracting(
                "country",
                "street",
                "houseNumber",
                "city",
                "zipCode",
                "boxNumber",
                "valueDate"
            ).containsExactly(
                "Belgium",
                "Main Street",
                "42",
                "Brussels",
                "1000",
                "A",
                LocalDate.of(2023, 5, 15)
            )
        }

        @Test
        fun `should throw InvalidExternalDataException when numbox is null`() {
            // Given
            val snapshot = HistoricalCitizenSnapshot(
                firstName = "John",
                lastName = "Doe",
                numbox = null,
                nationality = "BE",
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15)
                ),
                birthDate = LocalDate.of(1990, 1, 1),
                iban = "****************",
                bic = "CMRPL",
                otherPersonName = "Jane Doe",
                bankAccountValueDate = LocalDate.of(2023, 5, 15),
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2023, 6, 1)
            )

            // When/Then
            assertThrows<InvalidExternalDataException> {
                snapshot.toHistoricalCitizenOnem()
            }.also {
                assertThat(it.message).isEqualTo("No numbox found in snapshot for ONEM")
            }
        }
    }

    @Nested
    inner class HistoricalCitizenAuthenticSourcesToSnapshotTest {

        @Test
        fun `should map all fields correctly`() {
            // Given
            val authenticSources = HistoricalCitizenAuthenticSources(
                firstName = "John",
                lastName = "Doe",
                nationality = "BE",
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15),
                ),
                birthDate = LocalDate.of(1990, 1, 1),
            )

            // When
            val result = authenticSources.toHistoricalCitizenSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).extracting(
                "firstName",
                "lastName",
                "numbox",
                "nationality",
                "iban",
                "bic",
                "otherPersonName",
                "birthDate",
                "bankAccountValueDate",
                "paymentMode",
                "authorized",
                "effectiveDate"
            ).containsExactly(
                "John",
                "Doe",
                null,
                "BE",
                null,
                null,
                null,
                LocalDate.of(1990, 1, 1),
                null,
                null,
                null,
                null
            )

            assertThat(result.address).extracting(
                "country",
                "street",
                "houseNumber",
                "city",
                "zipCode",
                "boxNumber",
                "valueDate",
            ).containsExactly(
                "Belgium",
                "Main Street",
                "42",
                "Brussels",
                "1000",
                "A",
                LocalDate.of(2023, 5, 15)
            )
        }
    }

    @Nested
    inner class HistoricalCitizenOnemToSnapshotTest {

        @Test
        fun `should map all fields correctly`() {
            // Given
            val onem = HistoricalCitizenOnem(
                firstName = "John",
                lastName = "Doe",
                numbox = 42,
                nationality = "BE",
                address = AddressNullable(
                    country = "Belgium",
                    street = "Main Street",
                    houseNumber = "42",
                    city = "Brussels",
                    zipCode = "1000",
                    boxNumber = "A",
                    valueDate = LocalDate.of(2023, 5, 15),
                ),
                iban = "****************",
                bic = "CMRPL",
                otherPersonName = "Jane Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                bankAccountValueDate = LocalDate.of(2023, 5, 15),
                paymentMode = 1,
                authorized = true,
                effectiveDate = LocalDate.of(2023, 6, 1)
            )

            // When
            val result = onem.toHistoricalCitizenSnapshot()

            // Then
            assertThat(result).isNotNull
            assertThat(result).extracting(
                "firstName",
                "lastName",
                "numbox",
                "nationality",
                "iban",
                "bic",
                "otherPersonName",
                "birthDate",
                "bankAccountValueDate",
                "paymentMode",
                "authorized",
                "effectiveDate"
            ).containsExactly(
                "John",
                "Doe",
                42,
                "BE",
                "****************",
                "CMRPL",
                "Jane Doe",
                LocalDate.of(1990, 1, 1),
                LocalDate.of(2023, 5, 15),
                1,
                true,
                LocalDate.of(2023, 6, 1)
            )

            assertThat(result.address).extracting(
                "country",
                "street",
                "houseNumber",
                "city",
                "zipCode",
                "boxNumber",
                "valueDate"
            ).containsExactly(
                "Belgium",
                "Main Street",
                "42",
                "Brussels",
                "1000",
                "A",
                LocalDate.of(2023, 5, 15)
            )
        }
    }
}