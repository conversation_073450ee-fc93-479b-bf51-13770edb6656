/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * TranslatedDenomination
 */
@JsonPropertyOrder({
  TranslatedDenomination.JSON_PROPERTY_DENOMINATION_NL,
  TranslatedDenomination.JSON_PROPERTY_DENOMINATION_FR,
  TranslatedDenomination.JSON_PROPERTY_DENOMINATION_DE,
  TranslatedDenomination.JSON_PROPERTY_DENOMINATION_EN
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class TranslatedDenomination {
  public static final String JSON_PROPERTY_DENOMINATION_NL = "denominationNl";
  private String denominationNl;

  public static final String JSON_PROPERTY_DENOMINATION_FR = "denominationFr";
  private String denominationFr;

  public static final String JSON_PROPERTY_DENOMINATION_DE = "denominationDe";
  private String denominationDe;

  public static final String JSON_PROPERTY_DENOMINATION_EN = "denominationEn";
  private String denominationEn;

  public TranslatedDenomination() {
  }

  public TranslatedDenomination denominationNl(String denominationNl) {
    
    this.denominationNl = denominationNl;
    return this;
  }

  /**
   * Get denominationNl
   * @return denominationNl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DENOMINATION_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDenominationNl() {
    return denominationNl;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDenominationNl(String denominationNl) {
    this.denominationNl = denominationNl;
  }

  public TranslatedDenomination denominationFr(String denominationFr) {
    
    this.denominationFr = denominationFr;
    return this;
  }

  /**
   * Get denominationFr
   * @return denominationFr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DENOMINATION_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDenominationFr() {
    return denominationFr;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDenominationFr(String denominationFr) {
    this.denominationFr = denominationFr;
  }

  public TranslatedDenomination denominationDe(String denominationDe) {
    
    this.denominationDe = denominationDe;
    return this;
  }

  /**
   * Get denominationDe
   * @return denominationDe
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DENOMINATION_DE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDenominationDe() {
    return denominationDe;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION_DE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDenominationDe(String denominationDe) {
    this.denominationDe = denominationDe;
  }

  public TranslatedDenomination denominationEn(String denominationEn) {
    
    this.denominationEn = denominationEn;
    return this;
  }

  /**
   * Get denominationEn
   * @return denominationEn
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DENOMINATION_EN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDenominationEn() {
    return denominationEn;
  }


  @JsonProperty(JSON_PROPERTY_DENOMINATION_EN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDenominationEn(String denominationEn) {
    this.denominationEn = denominationEn;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TranslatedDenomination translatedDenomination = (TranslatedDenomination) o;
    return Objects.equals(this.denominationNl, translatedDenomination.denominationNl) &&
        Objects.equals(this.denominationFr, translatedDenomination.denominationFr) &&
        Objects.equals(this.denominationDe, translatedDenomination.denominationDe) &&
        Objects.equals(this.denominationEn, translatedDenomination.denominationEn);
  }

  @Override
  public int hashCode() {
    return Objects.hash(denominationNl, denominationFr, denominationDe, denominationEn);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TranslatedDenomination {\n");
    sb.append("    denominationNl: ").append(toIndentedString(denominationNl)).append("\n");
    sb.append("    denominationFr: ").append(toIndentedString(denominationFr)).append("\n");
    sb.append("    denominationDe: ").append(toIndentedString(denominationDe)).append("\n");
    sb.append("    denominationEn: ").append(toIndentedString(denominationEn)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

