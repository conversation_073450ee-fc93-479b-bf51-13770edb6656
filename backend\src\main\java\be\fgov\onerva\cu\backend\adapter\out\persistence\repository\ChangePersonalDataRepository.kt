package be.fgov.onerva.cu.backend.adapter.out.persistence.repository

import java.util.UUID
import org.springframework.data.domain.Limit
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.repository.JpaRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity

interface ChangePersonalDataRepository : JpaRepository<ChangePersonalDataRequestEntity, UUID> {
    fun findByC9Id(c9Id: Long, sort: Sort, limit: Limit): List<ChangePersonalDataRequestEntity>
}