/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.AttestRef;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * A C9 is an envelope for a collection of attests/forms, representing a specific request made by UI/OPs on behalf of a citizen. 
 */
@JsonPropertyOrder({
  C9.JSON_PROPERTY_ID,
  C9.JSON_PROPERTY_UNEMPLOYMENT_OFFICE,
  C9.JSON_PROPERTY_PAYMENT_INSTITUTION,
  C9.JSON_PROPERTY_TYPE,
  C9.JSON_PROPERTY_OP_KEY,
  C9.JSON_PROPERTY_SECT_OP,
  C9.JSON_PROPERTY_REQUEST_DATE,
  C9.JSON_PROPERTY_INTRODUCTION_DATE,
  C9.JSON_PROPERTY_DATE_VALID,
  C9.JSON_PROPERTY_TREATMENT_STATUS,
  C9.JSON_PROPERTY_DECISION_TYPE,
  C9.JSON_PROPERTY_DECISION_VALUE,
  C9.JSON_PROPERTY_DECISION_WINDOWS_USER,
  C9.JSON_PROPERTY_DECISION_DATE,
  C9.JSON_PROPERTY_SCAN_NUMBER,
  C9.JSON_PROPERTY_SCAN_URL,
  C9.JSON_PROPERTY_OPERATOR_CODE,
  C9.JSON_PROPERTY_SSIN,
  C9.JSON_PROPERTY_ATTEST_REFS,
  C9.JSON_PROPERTY_ENTITY_CODE,
  C9.JSON_PROPERTY_INTRODUCTION_TYPE,
  C9.JSON_PROPERTY_DUE_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class C9 {
  public static final String JSON_PROPERTY_ID = "id";
  private Long id;

  public static final String JSON_PROPERTY_UNEMPLOYMENT_OFFICE = "unemploymentOffice";
  private Integer unemploymentOffice;

  public static final String JSON_PROPERTY_PAYMENT_INSTITUTION = "paymentInstitution";
  private Integer paymentInstitution;

  public static final String JSON_PROPERTY_TYPE = "type";
  private String type;

  public static final String JSON_PROPERTY_OP_KEY = "opKey";
  private String opKey;

  public static final String JSON_PROPERTY_SECT_OP = "sectOp";
  private String sectOp;

  public static final String JSON_PROPERTY_REQUEST_DATE = "requestDate";
  private LocalDate requestDate;

  public static final String JSON_PROPERTY_INTRODUCTION_DATE = "introductionDate";
  private LocalDate introductionDate;

  public static final String JSON_PROPERTY_DATE_VALID = "dateValid";
  private LocalDate dateValid;

  public static final String JSON_PROPERTY_TREATMENT_STATUS = "treatmentStatus";
  private String treatmentStatus;

  public static final String JSON_PROPERTY_DECISION_TYPE = "decisionType";
  private String decisionType;

  public static final String JSON_PROPERTY_DECISION_VALUE = "decisionValue";
  private String decisionValue;

  public static final String JSON_PROPERTY_DECISION_WINDOWS_USER = "decisionWindowsUser";
  private String decisionWindowsUser;

  public static final String JSON_PROPERTY_DECISION_DATE = "decisionDate";
  private LocalDate decisionDate;

  public static final String JSON_PROPERTY_SCAN_NUMBER = "scanNumber";
  private Long scanNumber;

  public static final String JSON_PROPERTY_SCAN_URL = "scanUrl";
  private String scanUrl;

  public static final String JSON_PROPERTY_OPERATOR_CODE = "operatorCode";
  private Integer operatorCode;

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_ATTEST_REFS = "attestRefs";
  private List<AttestRef> attestRefs = new ArrayList<>();

  public static final String JSON_PROPERTY_ENTITY_CODE = "entityCode";
  private String entityCode;

  public static final String JSON_PROPERTY_INTRODUCTION_TYPE = "introductionType";
  private String introductionType;

  public static final String JSON_PROPERTY_DUE_DATE = "dueDate";
  private LocalDate dueDate;

  public C9() {
  }

  public C9 id(Long id) {
    
    this.id = id;
    return this;
  }

  /**
   * C9 Technical Id
   * @return id
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Long getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(Long id) {
    this.id = id;
  }

  public C9 unemploymentOffice(Integer unemploymentOffice) {
    
    this.unemploymentOffice = unemploymentOffice;
    return this;
  }

  /**
   * The id of the unemployment office
   * @return unemploymentOffice
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getUnemploymentOffice() {
    return unemploymentOffice;
  }


  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setUnemploymentOffice(Integer unemploymentOffice) {
    this.unemploymentOffice = unemploymentOffice;
  }

  public C9 paymentInstitution(Integer paymentInstitution) {
    
    this.paymentInstitution = paymentInstitution;
    return this;
  }

  /**
   * The id of the payment institution
   * @return paymentInstitution
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_PAYMENT_INSTITUTION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getPaymentInstitution() {
    return paymentInstitution;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_INSTITUTION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setPaymentInstitution(Integer paymentInstitution) {
    this.paymentInstitution = paymentInstitution;
  }

  public C9 type(String type) {
    
    this.type = type;
    return this;
  }

  /**
   * The type of C9 as found in C9 Management. TODO - Is there a lookup
   * @return type
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setType(String type) {
    this.type = type;
  }

  public C9 opKey(String opKey) {
    
    this.opKey = opKey;
    return this;
  }

  /**
   * An identifier defined by one OP to identify the C9, it is common for every form contains inside the C9. This identifier is uniq for the OP which generate it.
   * @return opKey
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OP_KEY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getOpKey() {
    return opKey;
  }


  @JsonProperty(JSON_PROPERTY_OP_KEY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOpKey(String opKey) {
    this.opKey = opKey;
  }

  public C9 sectOp(String sectOp) {
    
    this.sectOp = sectOp;
    return this;
  }

  /**
   * 
   * @return sectOp
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SECT_OP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getSectOp() {
    return sectOp;
  }


  @JsonProperty(JSON_PROPERTY_SECT_OP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSectOp(String sectOp) {
    this.sectOp = sectOp;
  }

  public C9 requestDate(LocalDate requestDate) {
    
    this.requestDate = requestDate;
    return this;
  }

  /**
   * The reference date to execute the C9 request
   * @return requestDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REQUEST_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getRequestDate() {
    return requestDate;
  }


  @JsonProperty(JSON_PROPERTY_REQUEST_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRequestDate(LocalDate requestDate) {
    this.requestDate = requestDate;
  }

  public C9 introductionDate(LocalDate introductionDate) {
    
    this.introductionDate = introductionDate;
    return this;
  }

  /**
   * The date the C9 file was sent by the payment institution
   * @return introductionDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_INTRODUCTION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getIntroductionDate() {
    return introductionDate;
  }


  @JsonProperty(JSON_PROPERTY_INTRODUCTION_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setIntroductionDate(LocalDate introductionDate) {
    this.introductionDate = introductionDate;
  }

  public C9 dateValid(LocalDate dateValid) {
    
    this.dateValid = dateValid;
    return this;
  }

  /**
   * The date valid is the date which is use in the T27 to display a C9
   * @return dateValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getDateValid() {
    return dateValid;
  }


  @JsonProperty(JSON_PROPERTY_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDateValid(LocalDate dateValid) {
    this.dateValid = dateValid;
  }

  public C9 treatmentStatus(String treatmentStatus) {
    
    this.treatmentStatus = treatmentStatus;
    return this;
  }

  /**
   * Treatment status. 3 possible values: RECEIVED, READY_TO_BE_TREATED, TREATED
   * @return treatmentStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TREATMENT_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTreatmentStatus() {
    return treatmentStatus;
  }


  @JsonProperty(JSON_PROPERTY_TREATMENT_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTreatmentStatus(String treatmentStatus) {
    this.treatmentStatus = treatmentStatus;
  }

  public C9 decisionType(String decisionType) {
    
    this.decisionType = decisionType;
    return this;
  }

  /**
   * Decision type done by ONEMRVA agent
   * @return decisionType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECISION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDecisionType() {
    return decisionType;
  }


  @JsonProperty(JSON_PROPERTY_DECISION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecisionType(String decisionType) {
    this.decisionType = decisionType;
  }

  public C9 decisionValue(String decisionValue) {
    
    this.decisionValue = decisionValue;
    return this;
  }

  /**
   * Decision done by ONEMRVA agent
   * @return decisionValue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECISION_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDecisionValue() {
    return decisionValue;
  }


  @JsonProperty(JSON_PROPERTY_DECISION_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecisionValue(String decisionValue) {
    this.decisionValue = decisionValue;
  }

  public C9 decisionWindowsUser(String decisionWindowsUser) {
    
    this.decisionWindowsUser = decisionWindowsUser;
    return this;
  }

  /**
   * ONEMRVA agent who made the decision
   * @return decisionWindowsUser
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECISION_WINDOWS_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDecisionWindowsUser() {
    return decisionWindowsUser;
  }


  @JsonProperty(JSON_PROPERTY_DECISION_WINDOWS_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecisionWindowsUser(String decisionWindowsUser) {
    this.decisionWindowsUser = decisionWindowsUser;
  }

  public C9 decisionDate(LocalDate decisionDate) {
    
    this.decisionDate = decisionDate;
    return this;
  }

  /**
   * Date of the decision made by the ONEMRVA agent
   * @return decisionDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECISION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDecisionDate() {
    return decisionDate;
  }


  @JsonProperty(JSON_PROPERTY_DECISION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecisionDate(LocalDate decisionDate) {
    this.decisionDate = decisionDate;
  }

  public C9 scanNumber(Long scanNumber) {
    
    this.scanNumber = scanNumber;
    return this;
  }

  /**
   * The scan number associated with the scanned documents of a C9 request
   * @return scanNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SCAN_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getScanNumber() {
    return scanNumber;
  }


  @JsonProperty(JSON_PROPERTY_SCAN_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setScanNumber(Long scanNumber) {
    this.scanNumber = scanNumber;
  }

  public C9 scanUrl(String scanUrl) {
    
    this.scanUrl = scanUrl;
    return this;
  }

  /**
   * The scan number associated with the scanned documents of a C9 request
   * @return scanUrl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SCAN_URL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getScanUrl() {
    return scanUrl;
  }


  @JsonProperty(JSON_PROPERTY_SCAN_URL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setScanUrl(String scanUrl) {
    this.scanUrl = scanUrl;
  }

  public C9 operatorCode(Integer operatorCode) {
    
    this.operatorCode = operatorCode;
    return this;
  }

  /**
   * operator code responsible for encoding the C9 request in the Digitar application
   * @return operatorCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getOperatorCode() {
    return operatorCode;
  }


  @JsonProperty(JSON_PROPERTY_OPERATOR_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperatorCode(Integer operatorCode) {
    this.operatorCode = operatorCode;
  }

  public C9 ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * A reusable citizen&#39;s SSIN definition
   * @return ssin
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public C9 attestRefs(List<AttestRef> attestRefs) {
    
    this.attestRefs = attestRefs;
    return this;
  }

  public C9 addAttestRefsItem(AttestRef attestRefsItem) {
    if (this.attestRefs == null) {
      this.attestRefs = new ArrayList<>();
    }
    this.attestRefs.add(attestRefsItem);
    return this;
  }

  /**
   * Get attestRefs
   * @return attestRefs
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ATTEST_REFS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<AttestRef> getAttestRefs() {
    return attestRefs;
  }


  @JsonProperty(JSON_PROPERTY_ATTEST_REFS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAttestRefs(List<AttestRef> attestRefs) {
    this.attestRefs = attestRefs;
  }

  public C9 entityCode(String entityCode) {
    
    this.entityCode = entityCode;
    return this;
  }

  /**
   * entity code responsible to handle the C9 request. It is a translation of the Unemployment office by lookupWPPT service
   * @return entityCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ENTITY_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEntityCode() {
    return entityCode;
  }


  @JsonProperty(JSON_PROPERTY_ENTITY_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEntityCode(String entityCode) {
    this.entityCode = entityCode;
  }

  public C9 introductionType(String introductionType) {
    
    this.introductionType = introductionType;
    return this;
  }

  /**
   * Introduction Type. 5 possible values: INTRO_FIRST_DEMAND, INTRO_AFTER_C51, INTRO_OTHERS, INTRO_LARGE_FIRST_DEMAND, EXEMPTIONS_2xx
   * @return introductionType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTRODUCTION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIntroductionType() {
    return introductionType;
  }


  @JsonProperty(JSON_PROPERTY_INTRODUCTION_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntroductionType(String introductionType) {
    this.introductionType = introductionType;
  }

  public C9 dueDate(LocalDate dueDate) {
    
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Due date, used to show the deadlines of C9 requests according to their category
   * @return dueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getDueDate() {
    return dueDate;
  }


  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    C9 C9 = (C9) o;
    return Objects.equals(this.id, C9.id) &&
        Objects.equals(this.unemploymentOffice, C9.unemploymentOffice) &&
        Objects.equals(this.paymentInstitution, C9.paymentInstitution) &&
        Objects.equals(this.type, C9.type) &&
        Objects.equals(this.opKey, C9.opKey) &&
        Objects.equals(this.sectOp, C9.sectOp) &&
        Objects.equals(this.requestDate, C9.requestDate) &&
        Objects.equals(this.introductionDate, C9.introductionDate) &&
        Objects.equals(this.dateValid, C9.dateValid) &&
        Objects.equals(this.treatmentStatus, C9.treatmentStatus) &&
        Objects.equals(this.decisionType, C9.decisionType) &&
        Objects.equals(this.decisionValue, C9.decisionValue) &&
        Objects.equals(this.decisionWindowsUser, C9.decisionWindowsUser) &&
        Objects.equals(this.decisionDate, C9.decisionDate) &&
        Objects.equals(this.scanNumber, C9.scanNumber) &&
        Objects.equals(this.scanUrl, C9.scanUrl) &&
        Objects.equals(this.operatorCode, C9.operatorCode) &&
        Objects.equals(this.ssin, C9.ssin) &&
        Objects.equals(this.attestRefs, C9.attestRefs) &&
        Objects.equals(this.entityCode, C9.entityCode) &&
        Objects.equals(this.introductionType, C9.introductionType) &&
        Objects.equals(this.dueDate, C9.dueDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, unemploymentOffice, paymentInstitution, type, opKey, sectOp, requestDate, introductionDate, dateValid, treatmentStatus, decisionType, decisionValue, decisionWindowsUser, decisionDate, scanNumber, scanUrl, operatorCode, ssin, attestRefs, entityCode, introductionType, dueDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class C9 {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    unemploymentOffice: ").append(toIndentedString(unemploymentOffice)).append("\n");
    sb.append("    paymentInstitution: ").append(toIndentedString(paymentInstitution)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    opKey: ").append(toIndentedString(opKey)).append("\n");
    sb.append("    sectOp: ").append(toIndentedString(sectOp)).append("\n");
    sb.append("    requestDate: ").append(toIndentedString(requestDate)).append("\n");
    sb.append("    introductionDate: ").append(toIndentedString(introductionDate)).append("\n");
    sb.append("    dateValid: ").append(toIndentedString(dateValid)).append("\n");
    sb.append("    treatmentStatus: ").append(toIndentedString(treatmentStatus)).append("\n");
    sb.append("    decisionType: ").append(toIndentedString(decisionType)).append("\n");
    sb.append("    decisionValue: ").append(toIndentedString(decisionValue)).append("\n");
    sb.append("    decisionWindowsUser: ").append(toIndentedString(decisionWindowsUser)).append("\n");
    sb.append("    decisionDate: ").append(toIndentedString(decisionDate)).append("\n");
    sb.append("    scanNumber: ").append(toIndentedString(scanNumber)).append("\n");
    sb.append("    scanUrl: ").append(toIndentedString(scanUrl)).append("\n");
    sb.append("    operatorCode: ").append(toIndentedString(operatorCode)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    attestRefs: ").append(toIndentedString(attestRefs)).append("\n");
    sb.append("    entityCode: ").append(toIndentedString(entityCode)).append("\n");
    sb.append("    introductionType: ").append(toIndentedString(introductionType)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

