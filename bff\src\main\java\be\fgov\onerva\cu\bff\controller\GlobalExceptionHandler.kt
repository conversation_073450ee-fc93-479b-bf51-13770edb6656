package be.fgov.onerva.cu.bff.controller

import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.context.request.WebRequest
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler
import be.fgov.onerva.cu.bff.exceptions.CitizenNotFoundException
import be.fgov.onerva.cu.common.utils.logger as thisLogger

@RestControllerAdvice
class GlobalExceptionHandler : ResponseEntityExceptionHandler() {
    private val log = thisLogger

    @ExceptionHandler(
        HttpClientErrorException.BadRequest::class,
        HttpClientErrorException.NotFound::class
    )
    fun handleClientError(ex: HttpClientErrorException): ResponseEntity<ProblemDetail> {
        log.error("Error while calling http service: ", ex)
        return ResponseEntity.status(ex.statusCode).body(
            ex.getResponseBodyAs(
                ProblemDetail::class.java
            )
        )
    }

    @ExceptionHandler(C9NotFoundException::class)
    fun handleInformationNotFoundException(
        ex: C9NotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.info("Information not found: {}", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Information Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }

    /**
     * Handles request ID not found exceptions.
     *
     * @param ex The caught CitizenNotFoundException
     * @param request The web request in which the exception occurred
     * @return ResponseEntity containing problem details with NOT_FOUND status
     */
    @ExceptionHandler(CitizenNotFoundException::class)
    fun handleCitizenNotFoundException(
        ex: CitizenNotFoundException,
        request: WebRequest,
    ): ResponseEntity<ProblemDetail> {
        log.error("Request ID not found: ", ex)
        val problemDetail = ProblemDetail.forStatusAndDetail(
            HttpStatus.NOT_FOUND,
            ex.message
        ).apply {
            title = "Request Not Found"
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND)
            .body(problemDetail)
    }
}
