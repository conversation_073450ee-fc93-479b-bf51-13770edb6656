/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ReplacementIncome
 */
@JsonPropertyOrder({
  ReplacementIncome.JSON_PROPERTY_NATURE_REPLACEMENT_INCOME,
  ReplacementIncome.JSON_PROPERTY_GROSS_MONTHLY_REPLACEMENT_INCOME
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ReplacementIncome {
  public static final String JSON_PROPERTY_NATURE_REPLACEMENT_INCOME = "natureReplacementIncome";
  private String natureReplacementIncome;

  public static final String JSON_PROPERTY_GROSS_MONTHLY_REPLACEMENT_INCOME = "grossMonthlyReplacementIncome";
  private String grossMonthlyReplacementIncome;

  public ReplacementIncome() {
  }

  public ReplacementIncome natureReplacementIncome(String natureReplacementIncome) {
    
    this.natureReplacementIncome = natureReplacementIncome;
    return this;
  }

  /**
   * Get natureReplacementIncome
   * @return natureReplacementIncome
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURE_REPLACEMENT_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNatureReplacementIncome() {
    return natureReplacementIncome;
  }


  @JsonProperty(JSON_PROPERTY_NATURE_REPLACEMENT_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNatureReplacementIncome(String natureReplacementIncome) {
    this.natureReplacementIncome = natureReplacementIncome;
  }

  public ReplacementIncome grossMonthlyReplacementIncome(String grossMonthlyReplacementIncome) {
    
    this.grossMonthlyReplacementIncome = grossMonthlyReplacementIncome;
    return this;
  }

  /**
   * Get grossMonthlyReplacementIncome
   * @return grossMonthlyReplacementIncome
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GROSS_MONTHLY_REPLACEMENT_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGrossMonthlyReplacementIncome() {
    return grossMonthlyReplacementIncome;
  }


  @JsonProperty(JSON_PROPERTY_GROSS_MONTHLY_REPLACEMENT_INCOME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGrossMonthlyReplacementIncome(String grossMonthlyReplacementIncome) {
    this.grossMonthlyReplacementIncome = grossMonthlyReplacementIncome;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ReplacementIncome replacementIncome = (ReplacementIncome) o;
    return Objects.equals(this.natureReplacementIncome, replacementIncome.natureReplacementIncome) &&
        Objects.equals(this.grossMonthlyReplacementIncome, replacementIncome.grossMonthlyReplacementIncome);
  }

  @Override
  public int hashCode() {
    return Objects.hash(natureReplacementIncome, grossMonthlyReplacementIncome);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ReplacementIncome {\n");
    sb.append("    natureReplacementIncome: ").append(toIndentedString(natureReplacementIncome)).append("\n");
    sb.append("    grossMonthlyReplacementIncome: ").append(toIndentedString(grossMonthlyReplacementIncome)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

