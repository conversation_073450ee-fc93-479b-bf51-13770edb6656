package be.fgov.onerva.cu.bff.rest.client.wo.facade.api;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.UserCriteriaDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.UserDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class UserApi extends BaseApi {

    public UserApi() {
        super(new ApiClient());
    }

    public UserApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * 
     * <p><b>200</b> - user fetched successfully
     * @param criteria  (optional)
     * @return UserDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public UserDTO searchUsers(UserCriteriaDTO criteria) throws RestClientException {
        return searchUsersWithHttpInfo(criteria).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - user fetched successfully
     * @param criteria  (optional)
     * @return ResponseEntity&lt;UserDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<UserDTO> searchUsersWithHttpInfo(UserCriteriaDTO criteria) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        
        if (criteria != null) {
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "operatorCode", criteria.getOperatorCode()));
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "username", criteria.getUsername()));
            localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "inss", criteria.getInss()));
        }

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<UserDTO> localReturnType = new ParameterizedTypeReference<UserDTO>() {};
        return apiClient.invokeAPI("/users", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
