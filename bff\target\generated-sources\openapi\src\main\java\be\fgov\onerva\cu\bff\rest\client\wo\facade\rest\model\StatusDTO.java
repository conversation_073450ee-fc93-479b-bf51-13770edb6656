/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.StateDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * StatusDTO
 */
@JsonPropertyOrder({
  StatusDTO.JSON_PROPERTY_TASK,
  StatusDTO.JSON_PROPERTY_TASK_STEP,
  StatusDTO.JSON_PROPERTY_PROCESS
})
@JsonTypeName("Status")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class StatusDTO {
  public static final String JSON_PROPERTY_TASK = "task";
  private StateDTO task;

  public static final String JSON_PROPERTY_TASK_STEP = "taskStep";
  private String taskStep;

  public static final String JSON_PROPERTY_PROCESS = "process";
  private StateDTO process;

  public StatusDTO() {
  }

  public StatusDTO task(StateDTO task) {
    
    this.task = task;
    return this;
  }

  /**
   * Get task
   * @return task
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TASK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public StateDTO getTask() {
    return task;
  }


  @JsonProperty(JSON_PROPERTY_TASK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTask(StateDTO task) {
    this.task = task;
  }

  public StatusDTO taskStep(String taskStep) {
    
    this.taskStep = taskStep;
    return this;
  }

  /**
   * Get taskStep
   * @return taskStep
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_STEP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTaskStep() {
    return taskStep;
  }


  @JsonProperty(JSON_PROPERTY_TASK_STEP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskStep(String taskStep) {
    this.taskStep = taskStep;
  }

  public StatusDTO process(StateDTO process) {
    
    this.process = process;
    return this;
  }

  /**
   * Get process
   * @return process
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_PROCESS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public StateDTO getProcess() {
    return process;
  }


  @JsonProperty(JSON_PROPERTY_PROCESS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setProcess(StateDTO process) {
    this.process = process;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    StatusDTO status = (StatusDTO) o;
    return Objects.equals(this.task, status.task) &&
        Objects.equals(this.taskStep, status.taskStep) &&
        Objects.equals(this.process, status.process);
  }

  @Override
  public int hashCode() {
    return Objects.hash(task, taskStep, process);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class StatusDTO {\n");
    sb.append("    task: ").append(toIndentedString(task)).append("\n");
    sb.append("    taskStep: ").append(toIndentedString(taskStep)).append("\n");
    sb.append("    process: ").append(toIndentedString(process)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

