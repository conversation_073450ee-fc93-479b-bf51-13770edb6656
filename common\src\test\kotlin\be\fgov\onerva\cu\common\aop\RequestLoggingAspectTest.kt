package be.fgov.onerva.cu.common.aop

import java.util.UUID
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.reflect.CodeSignature
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.slf4j.MDC
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class RequestLoggingAspectTest {

    @MockK
    private lateinit var joinPoint: JoinPoint

    @MockK
    private lateinit var codeSignature: CodeSignature

    @InjectMockKs
    private lateinit var requestLoggingAspect: RequestLoggingAspect

    @BeforeEach
    fun setUp() {
        mockkStatic(MDC::class)

        every { MDC.put(any(), any()) } returns Unit
        every { MDC.remove(any()) } returns Unit

        // Mock the target class without using target::class
        val targetClass = TestTarget::class.java
        every { joinPoint.target } returns TestTarget()
        every { joinPoint.signature } returns codeSignature
    }

    @AfterEach
    fun tearDown() {
        unmockkStatic(MDC::class)
    }

    // Simple class to use as a target
    private class TestTarget

    @Test
    fun `beforeMethod should set requestId in MDC when parameter is found`() {
        // Given
        val requestId = UUID.randomUUID()
        val args = arrayOf<Any>(requestId)
        val parameterNames = arrayOf("requestId")
        val parameterTypes = arrayOf<Class<*>>(UUID::class.java)

        every { joinPoint.args } returns args
        every { codeSignature.parameterNames } returns parameterNames
        every { codeSignature.parameterTypes } returns parameterTypes

        // When
        requestLoggingAspect.beforeMethod(joinPoint)

        // Then
        verify { MDC.put("requestId", requestId.toString()) }
    }

    @Test
    fun `beforeMethod should not set requestId in MDC when parameter name doesn't match`() {
        // Given
        val requestId = UUID.randomUUID()
        val args = arrayOf<Any>(requestId)
        val parameterNames = arrayOf("differentParamName")
        val parameterTypes = arrayOf<Class<*>>(UUID::class.java)

        every { joinPoint.args } returns args
        every { codeSignature.parameterNames } returns parameterNames
        every { codeSignature.parameterTypes } returns parameterTypes

        // When
        requestLoggingAspect.beforeMethod(joinPoint)

        // Then
        verify(exactly = 0) { MDC.put(any(), any()) }
    }

    @Test
    fun `beforeMethod should not set requestId in MDC when parameter type doesn't match`() {
        // Given
        val requestId = "12345"
        val args = arrayOf<Any>(requestId)
        val parameterNames = arrayOf("requestId")
        val parameterTypes = arrayOf<Class<*>>(String::class.java)

        every { joinPoint.args } returns args
        every { codeSignature.parameterNames } returns parameterNames
        every { codeSignature.parameterTypes } returns parameterTypes

        // When
        requestLoggingAspect.beforeMethod(joinPoint)

        // Then
        verify(exactly = 0) { MDC.put(any(), any()) }
    }

    @Test
    fun `afterMethod should remove requestId from MDC`() {
        // When
        requestLoggingAspect.afterMethod()

        // Then
        verify { MDC.remove("requestId") }
    }
}