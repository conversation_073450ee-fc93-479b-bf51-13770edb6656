/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * Values: SCANNED_DOCUMENTS,EC1
 */

enum class AnnexType(val value: kotlin.String) {

    @JsonProperty(value = "SCANNED_DOCUMENTS")
    SCANNED_DOCUMENTS("SCANNED_DOCUMENTS"),

    @JsonProperty(value = "EC1")
    EC1("EC1");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is AnnexType) "$data" else null

        /**
         * Returns a valid [AnnexType] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): AnnexType? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

