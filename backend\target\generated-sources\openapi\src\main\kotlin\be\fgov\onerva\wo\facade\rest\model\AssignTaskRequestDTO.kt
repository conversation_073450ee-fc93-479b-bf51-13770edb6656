/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param comment A comment you can provide to give a reason on that assignation
 */


data class AssignTaskRequestDTO (

    /* A comment you can provide to give a reason on that assignation */
    @get:JsonProperty("comment")
    val comment: kotlin.String? = null

) {


}

