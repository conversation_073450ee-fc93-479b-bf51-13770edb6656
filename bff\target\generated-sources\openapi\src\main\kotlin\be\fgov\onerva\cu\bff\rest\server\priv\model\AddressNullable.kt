package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param country The country of residence
 * @param street The street name
 * @param houseNumber The house number
 * @param boxNumber The box number (optional)
 * @param zipCode The postal/zip code
 * @param city The city name
 */
data class AddressNullable(

    @Schema(example = "null", description = "The country of residence")
    @get:JsonProperty("country") val country: kotlin.String? = null,

    @Schema(example = "null", description = "The street name")
    @get:JsonProperty("street") val street: kotlin.String? = null,

    @Schema(example = "null", description = "The house number")
    @get:JsonProperty("houseNumber") val houseNumber: kotlin.String? = null,

    @Schema(example = "null", description = "The box number (optional)")
    @get:JsonProperty("boxNumber") val boxNumber: kotlin.String? = null,

    @Schema(example = "null", description = "The postal/zip code")
    @get:JsonProperty("zipCode") val zipCode: kotlin.String? = null,

    @Schema(example = "null", description = "The city name")
    @get:JsonProperty("city") val city: kotlin.String? = null
    ) {

}

