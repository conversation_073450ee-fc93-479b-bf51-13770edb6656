---
global:
  routes:
    host: "cu.test.paas.onemrva.priv"
backend:
  extraEnv:
    - name: LOGGING_LEVEL_BE_FGOV_ONERVA_CU
      value: DEBUG
  image:
    registry: "docker-alpha.onemrva.priv"
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      rabbitmq:
        host: rabbitmq.rabbitmq.svc.cluster.local
      profiles:
        active: test
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents
      liquibase:
        default-schema: CU
        contexts: ddl,dml,ddl-test,dml-test

      datasource:
        url: *********************************************************************************************************************************************
        username: CU_USER
        password: "==INJECTED VIA BAMBOOSPEC SECRETS=="
    keycloak:
      auth-server-url: https://keycloak.test.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.test.paas.onemrva.priv/*
      realm: onemrva-agents
    rabbitmq:
      oauth:
        enabled: true

    app:
      allowMultipleC9s: true

    woThirdPartyApi:
      url: https://wo-thirdparty-api.test.paas.onemrva.priv/thirdParties/v1
    woOrganizationalChartApi:
      url: https://wo-organizational-chart-api.test.paas.onemrva.priv/rest/organizationalChart/nsso/v2/services
    werkomgeving:
      enabled: true
      mock: false
      woFacadeApi:
        url: https://wo-configurator.test.paas.onemrva.priv/api
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.test.paas.onemrva.priv/api
    barema:
      url: https://bareme.test.paas.onemrva.priv/api
    c9Api:
      url: "https://c9.test.paas.onemrva.priv/api"
    registry:
      url: https://registerproxyservice.test.paas.onemrva.priv/registerproxyservice/rest
    flagsmith:
      api:
        key: FDG9NYDVstLheuM2APEtDJ
        url: https://flagsmith.prod.paas.onemrva.priv/api/v1/

bff:
  extraEnv:
    - name: LOGGING_LEVEL_BE_FGOV_ONERVA_CU
      value: DEBUG
  image:
    registry: "docker-alpha.onemrva.priv"
  springConfiguration:
    client:
      security:
        enabled: true
    spring:
      security:
        oauth2:
          client:
            registration:
              keycloak:
                client-secret: "==INJECTED VIA BAMBOOSPEC SECRETS=="
            provider:
              keycloak:
                issuer-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents
                token-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents/protocol/openid-connect/token
          resourceserver:
            jwt:
              issuer-uri: https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents
    keycloak:
      auth-server-url: https://keycloak.test.paas.onemrva.priv
      checktoken: true
      redirect: https://cu.test.paas.onemrva.priv/*
      realm: onemrva-agents
    lookup:
      url: http://services.onemrva.priv/lookupwpptservice/rest
    citizen:
      url: https://person.test.paas.onemrva.priv/api
    backend:
      base-url: https://cu.test.paas.onemrva.priv
    woUserFacadeApi:
      url: https://wo-configurator.test.paas.onemrva.priv/api
    c51:
      url: https://proc51.test.paas.onemrva.priv/proc51/home.jsf
    c9Api:
      url: "https://c9.test.paas.onemrva.priv/api"
    regis:
      url: https://regis.test.paas.onemrva.priv/regis/regis/rew.seam
cu:
  image:
    registry: "docker-alpha.onemrva.priv"
  route:
    path: "/elements"
  readinessProbe:
    httpGet:
      path: "/elements/elements.js"
  livenessProbe:
    httpGet:
      path: "/elements/elements.js"
kcconfig:
  keycloak:
    url: "https://keycloak.test.paas.onemrva.priv"
  realm:
    clients:
      cu-frontend:
        redirectUris:
          - "https://cu.test.paas.onemrva.priv/*"
    users:
      cu_admin:
        enabled: true
      cu_user:
        enabled: true

woconfig:
  configClient:
    enabled: true
    baseUrl: https://wo-configurator.test.paas.onemrva.priv
    oauth:
      issuerUrl: "https://keycloak.test.paas.onemrva.priv/realms/onemrva-agents"
