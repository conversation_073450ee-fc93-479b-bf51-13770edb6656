package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.UUID

fun interface AssignTaskToUserUseCase {
    /**
     * Retrieves basic information for a specific request.
     *
     * @param requestId The unique identifier of the request
     * @Param taskCode The code of the task to close (required)
     * @return Task successfully assigned (status code 204)
     * or Request not found (status code 404)
     */
    fun assignTaskToUser(requestId: UUID)
}