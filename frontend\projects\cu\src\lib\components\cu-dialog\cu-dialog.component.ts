import {Component, Inject, ViewEncapsulation} from "@angular/core";
import {MAT_DIALOG_DATA, MatDialogModule} from "@angular/material/dialog";
import {MatButton} from "@angular/material/button";
import {MatIcon} from "@angular/material/icon";
import {CommonModule} from "@angular/common";

@Component({
    selector: "lib-cu-dialog",
    standalone: true,
    templateUrl: "./cu-dialog.component.html",
    styleUrl: "./cu-dialog.component.scss",
    imports: [
        MatIcon,
        MatDialogModule,
        MatButton,
        CommonModule,
    ],
    encapsulation: ViewEncapsulation.None,
})

export class CuDialogComponent {
    title: string;
    content: string;
    primaryActionText: string;
    secondaryActionText: string;
    dialogType: string;
    dialogSize: string;
    onPrimaryAction: () => void;
    onSecondaryAction: () => void;

    constructor(@Inject(MAT_DIALOG_DATA) public data: any) {
        this.title = data?.title || "";
        this.content = data?.content || "";
        this.primaryActionText = data?.primaryActionText || "Primary";
        this.secondaryActionText = data?.secondaryActionText || "Secondary";
        this.dialogType = data?.dialogType || "";
        this.dialogSize = data?.dialogSize || "medium";
        this.onPrimaryAction = data?.onPrimaryAction || (() => {
        });
        this.onSecondaryAction = data?.onSecondaryAction || (() => {
        });
    }
}
