package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api;

import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.C51Decisions;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class C51DecisionsApi extends BaseApi {

    public C51DecisionsApi() {
        super(new ApiClient());
    }

    public C51DecisionsApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * The endpoint to get all the decisions of C51 from a date and a numbox
     * <p><b>200</b> - list of decisions of C51
     * <p><b>404</b> - No C51 decision
     * @param dateValid the validation date (required)
     * @param numBox the num box (required)
     * @return C51Decisions
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public C51Decisions getC51DecisionsFromDateValidAndNumBox(String dateValid, String numBox) throws RestClientException {
        return getC51DecisionsFromDateValidAndNumBoxWithHttpInfo(dateValid, numBox).getBody();
    }

    /**
     * 
     * The endpoint to get all the decisions of C51 from a date and a numbox
     * <p><b>200</b> - list of decisions of C51
     * <p><b>404</b> - No C51 decision
     * @param dateValid the validation date (required)
     * @param numBox the num box (required)
     * @return ResponseEntity&lt;C51Decisions&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<C51Decisions> getC51DecisionsFromDateValidAndNumBoxWithHttpInfo(String dateValid, String numBox) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'dateValid' is set
        if (dateValid == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dateValid' when calling getC51DecisionsFromDateValidAndNumBox");
        }
        
        // verify the required parameter 'numBox' is set
        if (numBox == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'numBox' when calling getC51DecisionsFromDateValidAndNumBox");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("dateValid", dateValid);
        uriVariables.put("numBox", numBox);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<C51Decisions> localReturnType = new ParameterizedTypeReference<C51Decisions>() {};
        return apiClient.invokeAPI("/c51Decisions/{dateValid}/{numBox}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
