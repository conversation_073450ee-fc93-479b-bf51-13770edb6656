Feature: Union Contribution

  Background:
    Given the database is clean
    And test data for change personal data is loaded
    And I am authenticated as "test_user" with role "cu_role_user"

  Scenario: Select source for union contribution
    Given a request with ID "F1DA3F30-10A5-4124-AA5E-2D4E46A09B16"
    And a citizen with SSIN "18031307065" exists with union information
    When I select the following sources for union contribution:
      | fieldName    | source |
      | contribution | C1     |
    Then the union contribution response status should be 204
    And the union contribution should be updated with:
      | authorized    | true       |
      | effective_date| 2025-01-01 |
      | update_status | EDITED     |