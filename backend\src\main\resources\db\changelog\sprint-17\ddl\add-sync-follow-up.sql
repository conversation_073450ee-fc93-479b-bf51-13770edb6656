--liquibase formatted sql
--changeset bernard:add-sync-follow-up context:ddl
create table sync_follow_up
(
    id                     uniqueidentifier  not null,
    created_date           datetime2(6)      not null,
    correlation_id         varchar(255),
    date_message_sent      datetimeoffset(6) not null,
    date_response_received datetimeoffset(6),
    error                  varchar(255),
    status                 varchar(255)      not null,
    request_id             uniqueidentifier,
    primary key (id)
);

ALTER TABLE wave_task
    ADD CONSTRAINT CK__sync_follow_up__status
        CHECK (status in ('OPEN', 'CLOSED', 'WAITING'));

alter table sync_follow_up
    add constraint FK_sync_follow_up_request_id foreign key (request_id) references request;

create unique nonclustered index UIX_sync_follow_up_correlation_id on sync_follow_up (correlation_id);

