<div>
    <mat-accordion multi>
        <onemrva-mat-panel id="citizenInformation">
            <onemrva-mat-panel-title>
                <span>{{ 'CU_DATA_CONSISTENCY.DC.SUB_TITLE' | translate }}</span>
            </onemrva-mat-panel-title>
            <onemrva-mat-panel-content>
                <div class="requestDetail">
                    <lib-consistency-table [dataSource]="citizenInformation"
                                           [dataConsistencyData]="dataConsistencyData"
                                           [displayedColumns]="displayedColumns"
                                           [dataConsistencySelectedValues]="{}"
                                           [language]="language"
                                           [taskStatus]="taskStatus"
                                           [task]="task"
                                           (rowSelectionChanged)="onTableSelectionChange($event)"
                                           (tableConsistencyChanged)="onTableConsistencyChanged($event)"
                                           data-cy="consistency-table"
                    >
                    </lib-consistency-table>
                </div>
            </onemrva-mat-panel-content>
        </onemrva-mat-panel>
    </mat-accordion>

    <lib-family-composition [barema]="dataConsistencyData && dataConsistencyData.barema"
                            [requestId]="dataConsistencyData && dataConsistencyData.requestId"
                            [language]="language"
                            [taskStatus]="taskStatus"
                            [task]="task"
                            (regisVerificationEvent)="onVerificationChange($event)"></lib-family-composition>

</div>




