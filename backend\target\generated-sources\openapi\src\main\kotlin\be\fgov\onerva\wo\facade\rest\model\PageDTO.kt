/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param totalElements 
 * @param totalPages 
 * @param content 
 */


data class PageDTO (

    @get:JsonProperty("totalElements")
    val totalElements: kotlin.Long,

    @get:JsonProperty("totalPages")
    val totalPages: kotlin.Int,

    @get:JsonProperty("content")
    val content: kotlin.collections.List<kotlin.Any>

) {


}

