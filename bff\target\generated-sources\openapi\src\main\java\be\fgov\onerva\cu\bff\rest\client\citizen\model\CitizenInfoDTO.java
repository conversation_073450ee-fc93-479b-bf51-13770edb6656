/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.BankAccountDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoUnionDueDTO;
import be.fgov.onerva.cu.bff.rest.client.citizen.model.ForeignAddressDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CitizenInfoDTO
 */
@JsonPropertyOrder({
  CitizenInfoDTO.JSON_PROPERTY_ID,
  CitizenInfoDTO.JSON_PROPERTY_SSIN,
  CitizenInfoDTO.JSON_PROPERTY_NUM_PENS,
  CitizenInfoDTO.JSON_PROPERTY_LAST_NAME,
  CitizenInfoDTO.JSON_PROPERTY_FIRST_NAME,
  CitizenInfoDTO.JSON_PROPERTY_ADDRESS,
  CitizenInfoDTO.JSON_PROPERTY_POSTAL_CODE,
  CitizenInfoDTO.JSON_PROPERTY_ADDRESS_OBJ,
  CitizenInfoDTO.JSON_PROPERTY_NUM_BOX,
  CitizenInfoDTO.JSON_PROPERTY_O_P,
  CitizenInfoDTO.JSON_PROPERTY_UNEMPLOYMENT_OFFICE,
  CitizenInfoDTO.JSON_PROPERTY_IBAN,
  CitizenInfoDTO.JSON_PROPERTY_BANK_ACCOUNT,
  CitizenInfoDTO.JSON_PROPERTY_PAYMENT_MODE,
  CitizenInfoDTO.JSON_PROPERTY_LANGUAGE,
  CitizenInfoDTO.JSON_PROPERTY_SEX,
  CitizenInfoDTO.JSON_PROPERTY_FLAG_NATION,
  CitizenInfoDTO.JSON_PROPERTY_FLAG_V_CPTE,
  CitizenInfoDTO.JSON_PROPERTY_EMAIL,
  CitizenInfoDTO.JSON_PROPERTY_EMAIL_REG,
  CitizenInfoDTO.JSON_PROPERTY_FLAG_TO_PURGE,
  CitizenInfoDTO.JSON_PROPERTY_LAST_MODIF_DATE,
  CitizenInfoDTO.JSON_PROPERTY_TELEPHONE_ONEM,
  CitizenInfoDTO.JSON_PROPERTY_GSM_ONEM,
  CitizenInfoDTO.JSON_PROPERTY_TELEPHONE_REG,
  CitizenInfoDTO.JSON_PROPERTY_GSM_REG,
  CitizenInfoDTO.JSON_PROPERTY_BIRTH_DATE,
  CitizenInfoDTO.JSON_PROPERTY_DECEASED_DATE,
  CitizenInfoDTO.JSON_PROPERTY_BIS_NUMBER,
  CitizenInfoDTO.JSON_PROPERTY_EMPLOYMENT_CONTRACT,
  CitizenInfoDTO.JSON_PROPERTY_UNION_DUE
})
@JsonTypeName("CitizenInfo")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenInfoDTO {
  public static final String JSON_PROPERTY_ID = "id";
  private BigDecimal id;

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_NUM_PENS = "numPens";
  private BigDecimal numPens;

  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private String address;

  public static final String JSON_PROPERTY_POSTAL_CODE = "postalCode";
  private String postalCode;

  public static final String JSON_PROPERTY_ADDRESS_OBJ = "addressObj";
  private ForeignAddressDTO addressObj;

  public static final String JSON_PROPERTY_NUM_BOX = "numBox";
  private BigDecimal numBox;

  public static final String JSON_PROPERTY_O_P = "OP";
  private BigDecimal OP;

  public static final String JSON_PROPERTY_UNEMPLOYMENT_OFFICE = "unemploymentOffice";
  private BigDecimal unemploymentOffice;

  public static final String JSON_PROPERTY_IBAN = "iban";
  private String iban;

  public static final String JSON_PROPERTY_BANK_ACCOUNT = "bankAccount";
  private BankAccountDTO bankAccount;

  public static final String JSON_PROPERTY_PAYMENT_MODE = "paymentMode";
  private Integer paymentMode;

  public static final String JSON_PROPERTY_LANGUAGE = "language";
  private String language;

  public static final String JSON_PROPERTY_SEX = "sex";
  private String sex;

  public static final String JSON_PROPERTY_FLAG_NATION = "flagNation";
  private BigDecimal flagNation;

  public static final String JSON_PROPERTY_FLAG_V_CPTE = "flagVCpte";
  private BigDecimal flagVCpte;

  public static final String JSON_PROPERTY_EMAIL = "email";
  private String email;

  public static final String JSON_PROPERTY_EMAIL_REG = "emailReg";
  private String emailReg;

  public static final String JSON_PROPERTY_FLAG_TO_PURGE = "flagToPurge";
  private String flagToPurge;

  public static final String JSON_PROPERTY_LAST_MODIF_DATE = "lastModifDate";
  private Integer lastModifDate;

  public static final String JSON_PROPERTY_TELEPHONE_ONEM = "telephoneOnem";
  private String telephoneOnem;

  public static final String JSON_PROPERTY_GSM_ONEM = "gsmOnem";
  private String gsmOnem;

  public static final String JSON_PROPERTY_TELEPHONE_REG = "telephoneReg";
  private String telephoneReg;

  public static final String JSON_PROPERTY_GSM_REG = "gsmReg";
  private String gsmReg;

  public static final String JSON_PROPERTY_BIRTH_DATE = "birthDate";
  private LocalDate birthDate;

  public static final String JSON_PROPERTY_DECEASED_DATE = "deceasedDate";
  private LocalDate deceasedDate;

  public static final String JSON_PROPERTY_BIS_NUMBER = "bisNumber";
  private List<String> bisNumber;

  public static final String JSON_PROPERTY_EMPLOYMENT_CONTRACT = "employmentContract";
  private Integer employmentContract;

  public static final String JSON_PROPERTY_UNION_DUE = "unionDue";
  private CitizenInfoUnionDueDTO unionDue;

  public CitizenInfoDTO() {
  }

  public CitizenInfoDTO id(BigDecimal id) {
    
    this.id = id;
    return this;
  }

  /**
   * Was never implemented.
   * @return id
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(BigDecimal id) {
    this.id = id;
  }

  public CitizenInfoDTO ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public CitizenInfoDTO numPens(BigDecimal numPens) {
    
    this.numPens = numPens;
    return this;
  }

  /**
   * Get numPens
   * @return numPens
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNumPens() {
    return numPens;
  }


  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumPens(BigDecimal numPens) {
    this.numPens = numPens;
  }

  public CitizenInfoDTO lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public CitizenInfoDTO firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public CitizenInfoDTO address(String address) {
    
    this.address = address;
    return this;
  }

  /**
   * the address line, see addressObj for more info
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(String address) {
    this.address = address;
  }

  public CitizenInfoDTO postalCode(String postalCode) {
    
    this.postalCode = postalCode;
    return this;
  }

  /**
   * use the addressObj instead
   * @return postalCode
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostalCode() {
    return postalCode;
  }


  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }

  public CitizenInfoDTO addressObj(ForeignAddressDTO addressObj) {
    
    this.addressObj = addressObj;
    return this;
  }

  /**
   * Get addressObj
   * @return addressObj
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS_OBJ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ForeignAddressDTO getAddressObj() {
    return addressObj;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS_OBJ)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddressObj(ForeignAddressDTO addressObj) {
    this.addressObj = addressObj;
  }

  public CitizenInfoDTO numBox(BigDecimal numBox) {
    
    this.numBox = numBox;
    return this;
  }

  /**
   * Get numBox
   * @return numBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUM_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNumBox() {
    return numBox;
  }


  @JsonProperty(JSON_PROPERTY_NUM_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumBox(BigDecimal numBox) {
    this.numBox = numBox;
  }

  public CitizenInfoDTO OP(BigDecimal OP) {
    
    this.OP = OP;
    return this;
  }

  /**
   * Get OP
   * @return OP
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_O_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getOP() {
    return OP;
  }


  @JsonProperty(JSON_PROPERTY_O_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOP(BigDecimal OP) {
    this.OP = OP;
  }

  public CitizenInfoDTO unemploymentOffice(BigDecimal unemploymentOffice) {
    
    this.unemploymentOffice = unemploymentOffice;
    return this;
  }

  /**
   * Get unemploymentOffice
   * @return unemploymentOffice
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getUnemploymentOffice() {
    return unemploymentOffice;
  }


  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnemploymentOffice(BigDecimal unemploymentOffice) {
    this.unemploymentOffice = unemploymentOffice;
  }

  public CitizenInfoDTO iban(String iban) {
    
    this.iban = iban;
    return this;
  }

  /**
   * use the bankAccount instead
   * @return iban
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIban() {
    return iban;
  }


  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIban(String iban) {
    this.iban = iban;
  }

  public CitizenInfoDTO bankAccount(BankAccountDTO bankAccount) {
    
    this.bankAccount = bankAccount;
    return this;
  }

  /**
   * Get bankAccount
   * @return bankAccount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BankAccountDTO getBankAccount() {
    return bankAccount;
  }


  @JsonProperty(JSON_PROPERTY_BANK_ACCOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBankAccount(BankAccountDTO bankAccount) {
    this.bankAccount = bankAccount;
  }

  public CitizenInfoDTO paymentMode(Integer paymentMode) {
    
    this.paymentMode = paymentMode;
    return this;
  }

  /**
   * List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticPaymentWayList.seam
   * @return paymentMode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPaymentMode() {
    return paymentMode;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentMode(Integer paymentMode) {
    this.paymentMode = paymentMode;
  }

  public CitizenInfoDTO language(String language) {
    
    this.language = language;
    return this;
  }

  /**
   * Get language
   * @return language
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLanguage() {
    return language;
  }


  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLanguage(String language) {
    this.language = language;
  }

  public CitizenInfoDTO sex(String sex) {
    
    this.sex = sex;
    return this;
  }

  /**
   * Get sex
   * @return sex
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSex() {
    return sex;
  }


  @JsonProperty(JSON_PROPERTY_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSex(String sex) {
    this.sex = sex;
  }

  public CitizenInfoDTO flagNation(BigDecimal flagNation) {
    
    this.flagNation = flagNation;
    return this;
  }

  /**
   * Get flagNation
   * @return flagNation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getFlagNation() {
    return flagNation;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagNation(BigDecimal flagNation) {
    this.flagNation = flagNation;
  }

  public CitizenInfoDTO flagVCpte(BigDecimal flagVCpte) {
    
    this.flagVCpte = flagVCpte;
    return this;
  }

  /**
   * Was never implemented.
   * @return flagVCpte
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_V_CPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getFlagVCpte() {
    return flagVCpte;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_V_CPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagVCpte(BigDecimal flagVCpte) {
    this.flagVCpte = flagVCpte;
  }

  public CitizenInfoDTO email(String email) {
    
    this.email = email;
    return this;
  }

  /**
   * Get email
   * @return email
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmail() {
    return email;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmail(String email) {
    this.email = email;
  }

  public CitizenInfoDTO emailReg(String emailReg) {
    
    this.emailReg = emailReg;
    return this;
  }

  /**
   * Get emailReg
   * @return emailReg
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmailReg() {
    return emailReg;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmailReg(String emailReg) {
    this.emailReg = emailReg;
  }

  public CitizenInfoDTO flagToPurge(String flagToPurge) {
    
    this.flagToPurge = flagToPurge;
    return this;
  }

  /**
   * Get flagToPurge
   * @return flagToPurge
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_TO_PURGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFlagToPurge() {
    return flagToPurge;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_TO_PURGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagToPurge(String flagToPurge) {
    this.flagToPurge = flagToPurge;
  }

  public CitizenInfoDTO lastModifDate(Integer lastModifDate) {
    
    this.lastModifDate = lastModifDate;
    return this;
  }

  /**
   * Get lastModifDate
   * @return lastModifDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_MODIF_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getLastModifDate() {
    return lastModifDate;
  }


  @JsonProperty(JSON_PROPERTY_LAST_MODIF_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastModifDate(Integer lastModifDate) {
    this.lastModifDate = lastModifDate;
  }

  public CitizenInfoDTO telephoneOnem(String telephoneOnem) {
    
    this.telephoneOnem = telephoneOnem;
    return this;
  }

  /**
   * Get telephoneOnem
   * @return telephoneOnem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TELEPHONE_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTelephoneOnem() {
    return telephoneOnem;
  }


  @JsonProperty(JSON_PROPERTY_TELEPHONE_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTelephoneOnem(String telephoneOnem) {
    this.telephoneOnem = telephoneOnem;
  }

  public CitizenInfoDTO gsmOnem(String gsmOnem) {
    
    this.gsmOnem = gsmOnem;
    return this;
  }

  /**
   * Get gsmOnem
   * @return gsmOnem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GSM_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGsmOnem() {
    return gsmOnem;
  }


  @JsonProperty(JSON_PROPERTY_GSM_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGsmOnem(String gsmOnem) {
    this.gsmOnem = gsmOnem;
  }

  public CitizenInfoDTO telephoneReg(String telephoneReg) {
    
    this.telephoneReg = telephoneReg;
    return this;
  }

  /**
   * Get telephoneReg
   * @return telephoneReg
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TELEPHONE_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTelephoneReg() {
    return telephoneReg;
  }


  @JsonProperty(JSON_PROPERTY_TELEPHONE_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTelephoneReg(String telephoneReg) {
    this.telephoneReg = telephoneReg;
  }

  public CitizenInfoDTO gsmReg(String gsmReg) {
    
    this.gsmReg = gsmReg;
    return this;
  }

  /**
   * Get gsmReg
   * @return gsmReg
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GSM_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGsmReg() {
    return gsmReg;
  }


  @JsonProperty(JSON_PROPERTY_GSM_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGsmReg(String gsmReg) {
    this.gsmReg = gsmReg;
  }

  public CitizenInfoDTO birthDate(LocalDate birthDate) {
    
    this.birthDate = birthDate;
    return this;
  }

  /**
   * Get birthDate
   * @return birthDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BIRTH_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getBirthDate() {
    return birthDate;
  }


  @JsonProperty(JSON_PROPERTY_BIRTH_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBirthDate(LocalDate birthDate) {
    this.birthDate = birthDate;
  }

  public CitizenInfoDTO deceasedDate(LocalDate deceasedDate) {
    
    this.deceasedDate = deceasedDate;
    return this;
  }

  /**
   * Get deceasedDate
   * @return deceasedDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECEASED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDeceasedDate() {
    return deceasedDate;
  }


  @JsonProperty(JSON_PROPERTY_DECEASED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeceasedDate(LocalDate deceasedDate) {
    this.deceasedDate = deceasedDate;
  }

  public CitizenInfoDTO bisNumber(List<String> bisNumber) {
    
    this.bisNumber = bisNumber;
    return this;
  }

  public CitizenInfoDTO addBisNumberItem(String bisNumberItem) {
    if (this.bisNumber == null) {
      this.bisNumber = new ArrayList<>();
    }
    this.bisNumber.add(bisNumberItem);
    return this;
  }

  /**
   * Get bisNumber
   * @return bisNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BIS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getBisNumber() {
    return bisNumber;
  }


  @JsonProperty(JSON_PROPERTY_BIS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBisNumber(List<String> bisNumber) {
    this.bisNumber = bisNumber;
  }

  public CitizenInfoDTO employmentContract(Integer employmentContract) {
    
    this.employmentContract = employmentContract;
    return this;
  }

  /**
   * Type of employment contract. List of possible values http://services/lookupwppt/lookups/signaletic/SignaleticWorkContractTypeList.seam
   * @return employmentContract
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYMENT_CONTRACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getEmploymentContract() {
    return employmentContract;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYMENT_CONTRACT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmploymentContract(Integer employmentContract) {
    this.employmentContract = employmentContract;
  }

  public CitizenInfoDTO unionDue(CitizenInfoUnionDueDTO unionDue) {
    
    this.unionDue = unionDue;
    return this;
  }

  /**
   * Get unionDue
   * @return unionDue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CitizenInfoUnionDueDTO getUnionDue() {
    return unionDue;
  }


  @JsonProperty(JSON_PROPERTY_UNION_DUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnionDue(CitizenInfoUnionDueDTO unionDue) {
    this.unionDue = unionDue;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenInfoDTO citizenInfo = (CitizenInfoDTO) o;
    return Objects.equals(this.id, citizenInfo.id) &&
        Objects.equals(this.ssin, citizenInfo.ssin) &&
        Objects.equals(this.numPens, citizenInfo.numPens) &&
        Objects.equals(this.lastName, citizenInfo.lastName) &&
        Objects.equals(this.firstName, citizenInfo.firstName) &&
        Objects.equals(this.address, citizenInfo.address) &&
        Objects.equals(this.postalCode, citizenInfo.postalCode) &&
        Objects.equals(this.addressObj, citizenInfo.addressObj) &&
        Objects.equals(this.numBox, citizenInfo.numBox) &&
        Objects.equals(this.OP, citizenInfo.OP) &&
        Objects.equals(this.unemploymentOffice, citizenInfo.unemploymentOffice) &&
        Objects.equals(this.iban, citizenInfo.iban) &&
        Objects.equals(this.bankAccount, citizenInfo.bankAccount) &&
        Objects.equals(this.paymentMode, citizenInfo.paymentMode) &&
        Objects.equals(this.language, citizenInfo.language) &&
        Objects.equals(this.sex, citizenInfo.sex) &&
        Objects.equals(this.flagNation, citizenInfo.flagNation) &&
        Objects.equals(this.flagVCpte, citizenInfo.flagVCpte) &&
        Objects.equals(this.email, citizenInfo.email) &&
        Objects.equals(this.emailReg, citizenInfo.emailReg) &&
        Objects.equals(this.flagToPurge, citizenInfo.flagToPurge) &&
        Objects.equals(this.lastModifDate, citizenInfo.lastModifDate) &&
        Objects.equals(this.telephoneOnem, citizenInfo.telephoneOnem) &&
        Objects.equals(this.gsmOnem, citizenInfo.gsmOnem) &&
        Objects.equals(this.telephoneReg, citizenInfo.telephoneReg) &&
        Objects.equals(this.gsmReg, citizenInfo.gsmReg) &&
        Objects.equals(this.birthDate, citizenInfo.birthDate) &&
        Objects.equals(this.deceasedDate, citizenInfo.deceasedDate) &&
        Objects.equals(this.bisNumber, citizenInfo.bisNumber) &&
        Objects.equals(this.employmentContract, citizenInfo.employmentContract) &&
        Objects.equals(this.unionDue, citizenInfo.unionDue);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, ssin, numPens, lastName, firstName, address, postalCode, addressObj, numBox, OP, unemploymentOffice, iban, bankAccount, paymentMode, language, sex, flagNation, flagVCpte, email, emailReg, flagToPurge, lastModifDate, telephoneOnem, gsmOnem, telephoneReg, gsmReg, birthDate, deceasedDate, bisNumber, employmentContract, unionDue);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenInfoDTO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    numPens: ").append(toIndentedString(numPens)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    addressObj: ").append(toIndentedString(addressObj)).append("\n");
    sb.append("    numBox: ").append(toIndentedString(numBox)).append("\n");
    sb.append("    OP: ").append(toIndentedString(OP)).append("\n");
    sb.append("    unemploymentOffice: ").append(toIndentedString(unemploymentOffice)).append("\n");
    sb.append("    iban: ").append(toIndentedString(iban)).append("\n");
    sb.append("    bankAccount: ").append(toIndentedString(bankAccount)).append("\n");
    sb.append("    paymentMode: ").append(toIndentedString(paymentMode)).append("\n");
    sb.append("    language: ").append(toIndentedString(language)).append("\n");
    sb.append("    sex: ").append(toIndentedString(sex)).append("\n");
    sb.append("    flagNation: ").append(toIndentedString(flagNation)).append("\n");
    sb.append("    flagVCpte: ").append(toIndentedString(flagVCpte)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    emailReg: ").append(toIndentedString(emailReg)).append("\n");
    sb.append("    flagToPurge: ").append(toIndentedString(flagToPurge)).append("\n");
    sb.append("    lastModifDate: ").append(toIndentedString(lastModifDate)).append("\n");
    sb.append("    telephoneOnem: ").append(toIndentedString(telephoneOnem)).append("\n");
    sb.append("    gsmOnem: ").append(toIndentedString(gsmOnem)).append("\n");
    sb.append("    telephoneReg: ").append(toIndentedString(telephoneReg)).append("\n");
    sb.append("    gsmReg: ").append(toIndentedString(gsmReg)).append("\n");
    sb.append("    birthDate: ").append(toIndentedString(birthDate)).append("\n");
    sb.append("    deceasedDate: ").append(toIndentedString(deceasedDate)).append("\n");
    sb.append("    bisNumber: ").append(toIndentedString(bisNumber)).append("\n");
    sb.append("    employmentContract: ").append(toIndentedString(employmentContract)).append("\n");
    sb.append("    unionDue: ").append(toIndentedString(unionDue)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

