<div *ngIf="showMainFrameMessage()">
    <onemrva-mat-message-box color="success" *ngIf="pushbackStatus=='OK'">
        {{ 'MAINFRAME_RESPONSE.OK' | translate }}
    </onemrva-mat-message-box>

    <onemrva-mat-message-box color="error" *ngIf="pushbackStatus=='NOK'">
        {{ 'MAINFRAME_RESPONSE.NOK' | translate }}
    </onemrva-mat-message-box>

    <onemrva-mat-message-box color="warn" *ngIf="pushbackStatus=='PENDING'">
        {{ 'MAINFRAME_RESPONSE.PENDING' | translate }}
    </onemrva-mat-message-box>
</div>

<onemrva-mat-message-box color="success" *ngIf="isTreatedOnMainFrame()">
    {{ 'TREATED_ON_MAINFRAME.CLOSED' | translate }} {{ 'TREATED_ON_MAINFRAME.DECISION' | translate }} {{ 'TREATED_ON_MAINFRAME.CODE.' + decisionType | translate }} {{ 'TREATED_ON_MAINFRAME.BAREMA' | translate }} {{ decisionBarema || "N/A" }} {{ 'TREATED_ON_MAINFRAME.VALIDATED' | translate }}
</onemrva-mat-message-box>

<onemrva-mat-message-box color="success" *ngIf="isClosedAndNotTreated()">
    {{ nextTaskDescription | translate }} {{ nextTaskAction | translate }}
</onemrva-mat-message-box>

<onemrva-mat-message-box color="warn" *ngIf="isLogiclyDeleted()">
        <span [innerHTML]="'LOGICLY_DELETED.TITLE' | translate: {processLink: processLink }"
              class="error-title"
              style="margin: 0 0 0.5rem 0;"></span>
</onemrva-mat-message-box>