/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.HandledEmployerDeclarationLinkType;
import be.fgov.onerva.unemployment.c9.rest.model.Wech502NaturalPerson;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech502EmployerDeclarationLink
 */
@JsonPropertyOrder({
  Wech502EmployerDeclarationLink.JSON_PROPERTY_NOSS_REGISTRATION_NBR,
  Wech502EmployerDeclarationLink.JSON_PROPERTY_TRUSTEESHIP,
  Wech502EmployerDeclarationLink.JSON_PROPERTY_COMPANY_I_D,
  Wech502EmployerDeclarationLink.JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK,
  Wech502EmployerDeclarationLink.JSON_PROPERTY_NATURAL_PERSON
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech502EmployerDeclarationLink {
  public static final String JSON_PROPERTY_NOSS_REGISTRATION_NBR = "nossRegistrationNbr";
  private Integer nossRegistrationNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP = "trusteeship";
  private String trusteeship;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK = "handledEmployerDeclarationLink";
  private HandledEmployerDeclarationLinkType handledEmployerDeclarationLink;

  public static final String JSON_PROPERTY_NATURAL_PERSON = "naturalPerson";
  private Wech502NaturalPerson naturalPerson;

  public Wech502EmployerDeclarationLink() {
  }

  public Wech502EmployerDeclarationLink nossRegistrationNbr(Integer nossRegistrationNbr) {
    
    this.nossRegistrationNbr = nossRegistrationNbr;
    return this;
  }

  /**
   * Get nossRegistrationNbr
   * @return nossRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNossRegistrationNbr() {
    return nossRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNossRegistrationNbr(Integer nossRegistrationNbr) {
    this.nossRegistrationNbr = nossRegistrationNbr;
  }

  public Wech502EmployerDeclarationLink trusteeship(String trusteeship) {
    
    this.trusteeship = trusteeship;
    return this;
  }

  /**
   * Get trusteeship
   * @return trusteeship
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTrusteeship() {
    return trusteeship;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTrusteeship(String trusteeship) {
    this.trusteeship = trusteeship;
  }

  public Wech502EmployerDeclarationLink companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public Wech502EmployerDeclarationLink handledEmployerDeclarationLink(HandledEmployerDeclarationLinkType handledEmployerDeclarationLink) {
    
    this.handledEmployerDeclarationLink = handledEmployerDeclarationLink;
    return this;
  }

  /**
   * Get handledEmployerDeclarationLink
   * @return handledEmployerDeclarationLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledEmployerDeclarationLinkType getHandledEmployerDeclarationLink() {
    return handledEmployerDeclarationLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledEmployerDeclarationLink(HandledEmployerDeclarationLinkType handledEmployerDeclarationLink) {
    this.handledEmployerDeclarationLink = handledEmployerDeclarationLink;
  }

  public Wech502EmployerDeclarationLink naturalPerson(Wech502NaturalPerson naturalPerson) {
    
    this.naturalPerson = naturalPerson;
    return this;
  }

  /**
   * Get naturalPerson
   * @return naturalPerson
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech502NaturalPerson getNaturalPerson() {
    return naturalPerson;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPerson(Wech502NaturalPerson naturalPerson) {
    this.naturalPerson = naturalPerson;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech502EmployerDeclarationLink wech502EmployerDeclarationLink = (Wech502EmployerDeclarationLink) o;
    return Objects.equals(this.nossRegistrationNbr, wech502EmployerDeclarationLink.nossRegistrationNbr) &&
        Objects.equals(this.trusteeship, wech502EmployerDeclarationLink.trusteeship) &&
        Objects.equals(this.companyID, wech502EmployerDeclarationLink.companyID) &&
        Objects.equals(this.handledEmployerDeclarationLink, wech502EmployerDeclarationLink.handledEmployerDeclarationLink) &&
        Objects.equals(this.naturalPerson, wech502EmployerDeclarationLink.naturalPerson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nossRegistrationNbr, trusteeship, companyID, handledEmployerDeclarationLink, naturalPerson);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech502EmployerDeclarationLink {\n");
    sb.append("    nossRegistrationNbr: ").append(toIndentedString(nossRegistrationNbr)).append("\n");
    sb.append("    trusteeship: ").append(toIndentedString(trusteeship)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    handledEmployerDeclarationLink: ").append(toIndentedString(handledEmployerDeclarationLink)).append("\n");
    sb.append("    naturalPerson: ").append(toIndentedString(naturalPerson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

