export interface PanelsIdMap {
    [key: string]: string;
}

export interface Nationality {
    full: string,
    code: string | undefined
}
export interface Employee {
    niss?: string;
    firstName?: string;
    lastName?: string;
    street?: string;
    housNbr?: string;
    postBox?: string;
    zipCode?: string;
    city?: string;
    birthdate?: string;
    nationality?: string;
    nationalityCode?: string;
    bankAccount?: string;
}

export interface ProcessedRow {
    property: string;
    encodedValue: string;
    dbValue: string;
    sourceValue: string;
    isConsistent: boolean;
    selectedValue?: SelectedValue;
    nationalityCode?: string;
}

export interface SelectedValue {
    fieldName: string;
    origin: Origin;
    value: string;
    modificationDate: string;
}

export enum Origin {
    Employee = "EMPLOYEE",
    Onem = "ONEM",
    SourceAuthentiques = "SOURCE_AUTHENTIQUES"
}

export function getValueFromOrigin(origin: string, row: ProcessedRow): any {
    const originMappings: { [key: string]: any } = {
        "ONEM": row.dbValue,
        "EMPLOYEE": row.encodedValue,
        "SOURCE_AUTHENTIQUES": row.sourceValue,
    };

    if (origin in originMappings) {
        return originMappings[origin];
    } else {
        return null;
    }
}

export interface Lookup {
  code: string;
  descFr: string;
  descNl: string;
}
