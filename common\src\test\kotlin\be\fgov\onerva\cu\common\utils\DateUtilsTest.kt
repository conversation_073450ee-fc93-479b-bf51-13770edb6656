package be.fgov.onerva.cu.common.utils

import java.time.LocalDate
import java.time.Month
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import be.fgov.onerva.cu.common.utils.DateUtils.formatToYYYYMMDD
import be.fgov.onerva.cu.common.utils.DateUtils.formatToYYYYMMDDNoDash

class DateUtilsTest {

    @Nested
    inner class FormatToYYYYMMDDTest {

        @Test
        fun `formatToYYYYMMDD should format date correctly`() {
            // Given
            val date = LocalDate.of(2024, 12, 16)

            // When
            val result = date.formatToYYYYMMDD()

            // Then
            assertThat(result).isEqualTo("2024-12-16")
        }

        @Test
        fun `formatToYYYYMMDD should handle null date`() {
            // Given
            val date: LocalDate? = null

            // When
            val result = date.formatToYYYYMMDD()

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `formatToYYYYMMDD should format single digit month and day with leading zeros`() {
            // Given
            val date = LocalDate.of(2024, 1, 5)

            // When
            val result = date.formatToYYYYMMDD()

            // Then
            assertThat(result)
                .isNotNull()
                .matches("\\d{4}-\\d{2}-\\d{2}")  // Verifies format
                .isEqualTo("2024-01-05")
        }

        @Test
        fun `parseDate should parse valid date string correctly`() {
            // Given
            val dateStr = "2024-12-16"

            // When
            val result = DateUtils.parseDate(dateStr)

            // Then
            assertThat(result)
                .isNotNull()
                .isEqualTo(LocalDate.of(2024, 12, 16))
        }
    }

    @Nested
    inner class FormatToYYYYMMDDNoDashTest {
        @Test
        fun `formatToYYYYMMDDNoDash should format date correctly`() {
            // Given
            val date = LocalDate.of(2024, 12, 16)

            // When
            val result = date.formatToYYYYMMDDNoDash()

            // Then
            assertThat(result).isEqualTo("20241216")
        }

        @Test
        fun `formatToYYYYMMDDNoDash should handle null date`() {
            // Given
            val date: LocalDate? = null

            // When
            val result = date.formatToYYYYMMDDNoDash()

            // Then
            assertThat(result).isNull()
        }
    }

    @Nested
    inner class ParseDateTest {

        @Test
        fun `parseDate should return null for invalid date string`() {
            // Given
            val dateStr = "invalid-date"

            // When
            val result = DateUtils.parseDate(dateStr)

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `parseDate should return null for invalid date format`() {
            // Given
            val dateStr = "16-12-2024"  // Wrong format

            // When
            val result = DateUtils.parseDate(dateStr)

            // Then
            assertThat(result).isNull()
        }

        @Test
        fun `parseDate should handle edge cases for dates`() {
            // Test various edge cases
            assertThat(DateUtils.parseDate("2024-12-31"))
                .isEqualTo(LocalDate.of(2024, 12, 31))
                .hasYear(2024)
                .hasMonth(Month.DECEMBER)
                .hasDayOfMonth(31)

            assertThat(DateUtils.parseDate("2024-01-01"))
                .isEqualTo(LocalDate.of(2024, 1, 1))
                .hasYear(2024)
                .hasMonth(Month.JANUARY)
                .hasDayOfMonth(1)

            assertThat(DateUtils.parseDate("2024-02-29"))  // Leap year
                .isEqualTo(LocalDate.of(2024, 2, 29))
                .hasYear(2024)
                .hasMonth(Month.FEBRUARY)
                .hasDayOfMonth(29)
        }
    }
}