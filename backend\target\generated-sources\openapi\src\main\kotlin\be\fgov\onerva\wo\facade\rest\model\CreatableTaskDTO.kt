/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.CorrelationDTO
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.InputThirdPartyDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param concernedEntities 
 * @param assignee It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \"E\" + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup
 * @param taskTypeCode 
 * @param metadata 
 * @param dueDate 
 * @param correlation 
 * @param processMetadata 
 * @param processId 
 * @param permissionGroupId 
 * @param businessId 
 */


data class CreatableTaskDTO (

    @get:JsonProperty("concernedEntities")
    val concernedEntities: kotlin.collections.List<InputThirdPartyDTO>,

    /* It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \"E\" + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup */
    @get:JsonProperty("assignee")
    val assignee: kotlin.String,

    @get:JsonProperty("taskTypeCode")
    val taskTypeCode: kotlin.String,

    @get:JsonProperty("metadata")
    val metadata: kotlin.collections.List<InputMetaDataDTO>? = null,

    @get:JsonProperty("dueDate")
    val dueDate: java.time.LocalDate? = null,

    @get:JsonProperty("correlation")
    val correlation: CorrelationDTO? = null,

    @get:JsonProperty("processMetadata")
    val processMetadata: kotlin.collections.List<InputMetaDataDTO>? = null,

    @get:JsonProperty("processId")
    val processId: kotlin.Long? = null,

    @get:JsonProperty("permissionGroupId")
    val permissionGroupId: kotlin.String? = null,

    @get:JsonProperty("businessId")
    val businessId: kotlin.String? = null

) {


}

