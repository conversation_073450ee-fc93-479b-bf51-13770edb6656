--liquibase formatted sql
--changeset bernard:remove-mode-of-payment-own-account-mode-of-payment context:ddl dbms:mssql

-- Dynamically drop the default constraint for mode_of_payment table
DECLARE @constraintName nvarchar(200)
SELECT @constraintName = name
FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('mode_of_payment')
  AND parent_column_id = (SELECT column_id
                          FROM sys.columns
                          WHERE object_id = OBJECT_ID('mode_of_payment')
                            AND name = 'own_account')
IF @constraintName IS NOT NULL
    EXEC ('ALTER TABLE mode_of_payment DROP CONSTRAINT ' + @constraintName)

-- Drop the column
ALTER TABLE mode_of_payment
    DROP COLUMN own_account;

-- Dynamically drop the default constraint for mode_of_payment_aud table
DECLARE @constraintNameAud nvarchar(200)
SELECT @constraintNameAud = name
FROM sys.default_constraints
WHERE parent_object_id = OBJECT_ID('mode_of_payment_aud')
  AND parent_column_id = (SELECT column_id
                          FROM sys.columns
                          WHERE object_id = OBJECT_ID('mode_of_payment_aud')
                            AND name = 'own_account')
IF @constraintNameAud IS NOT NULL
    EXEC ('ALTER TABLE mode_of_payment_aud DROP CONSTRAINT ' + @constraintNameAud)

-- Drop the column from audit table
ALTER TABLE mode_of_payment_aud
    DROP COLUMN own_account;
