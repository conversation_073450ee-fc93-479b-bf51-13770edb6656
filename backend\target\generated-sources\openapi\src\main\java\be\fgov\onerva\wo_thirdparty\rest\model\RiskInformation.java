/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * RiskInformation
 */
@JsonPropertyOrder({
  RiskInformation.JSON_PROPERTY_RISK,
  RiskInformation.JSON_PROPERTY_LAST_UPDATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class RiskInformation {
  public static final String JSON_PROPERTY_RISK = "risk";
  private String risk;

  public static final String JSON_PROPERTY_LAST_UPDATE = "lastUpdate";
  private Object lastUpdate;

  public RiskInformation() {
  }

  public RiskInformation risk(String risk) {
    
    this.risk = risk;
    return this;
  }

  /**
   * Get risk
   * @return risk
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRisk() {
    return risk;
  }


  @JsonProperty(JSON_PROPERTY_RISK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRisk(String risk) {
    this.risk = risk;
  }

  public RiskInformation lastUpdate(Object lastUpdate) {
    
    this.lastUpdate = lastUpdate;
    return this;
  }

  /**
   * Get lastUpdate
   * @return lastUpdate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_UPDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getLastUpdate() {
    return lastUpdate;
  }


  @JsonProperty(JSON_PROPERTY_LAST_UPDATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastUpdate(Object lastUpdate) {
    this.lastUpdate = lastUpdate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RiskInformation riskInformation = (RiskInformation) o;
    return Objects.equals(this.risk, riskInformation.risk) &&
        Objects.equals(this.lastUpdate, riskInformation.lastUpdate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(risk, lastUpdate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RiskInformation {\n");
    sb.append("    risk: ").append(toIndentedString(risk)).append("\n");
    sb.append("    lastUpdate: ").append(toIndentedString(lastUpdate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

