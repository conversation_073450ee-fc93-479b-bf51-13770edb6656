/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.MetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.StateDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param taskTypeCode 
 * @param taskId 
 * @param status 
 * @param taskStep 
 * @param metadata 
 * @param concernedEntities 
 * @param assignee 
 * @param dueDate 
 * @param processId 
 * @param creationDate 
 */


data class SearchedTaskDTO (

    @get:JsonProperty("taskTypeCode")
    val taskTypeCode: kotlin.String? = null,

    @get:JsonProperty("taskId")
    val taskId: kotlin.Long? = null,

    @get:JsonProperty("status")
    val status: StateDTO? = null,

    @get:JsonProperty("taskStep")
    val taskStep: kotlin.String? = null,

    @get:JsonProperty("metadata")
    val metadata: kotlin.collections.List<MetaDataDTO>? = null,

    @get:JsonProperty("concernedEntities")
    val concernedEntities: kotlin.collections.List<ThirdPartyDTO>? = null,

    @get:JsonProperty("assignee")
    val assignee: kotlin.String? = null,

    @get:JsonProperty("dueDate")
    val dueDate: java.time.LocalDate? = null,

    @get:JsonProperty("processId")
    val processId: kotlin.Long? = null,

    @get:JsonProperty("creationDate")
    val creationDate: java.time.OffsetDateTime? = null

) {


}

