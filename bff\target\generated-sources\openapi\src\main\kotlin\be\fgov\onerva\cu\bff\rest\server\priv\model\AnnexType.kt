package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
* 
* Values: SCANNED_DOCUMENTS,EC1
*/
enum class AnnexType(@get:JsonValue val value: kotlin.String) {

    SCANNED_DOCUMENTS("SCANNED_DOCUMENTS"),
    EC1("EC1");

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: kotlin.String): AnnexType {
                return values().first{it -> it.value == value}
        }
    }
}

