/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CalculationBaseAllowanceType
 */
@JsonPropertyOrder({
  CalculationBaseAllowanceType.JSON_PROPERTY_REMUNERATION_TIME_UNIT,
  CalculationBaseAllowanceType.JSON_PROPERTY_CYCLE,
  CalculationBaseAllowanceType.JSON_PROPERTY_REMUNERATION_BASIS_AMOUNT,
  CalculationBaseAllowanceType.JSON_PROPERTY_PIECE_TASK_SERVICES_COMMISSION_REMUNERATION_BASIS_AMOUNT
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CalculationBaseAllowanceType {
  public static final String JSON_PROPERTY_REMUNERATION_TIME_UNIT = "remunerationTimeUnit";
  private String remunerationTimeUnit;

  public static final String JSON_PROPERTY_CYCLE = "cycle";
  private String cycle;

  public static final String JSON_PROPERTY_REMUNERATION_BASIS_AMOUNT = "remunerationBasisAmount";
  private String remunerationBasisAmount;

  public static final String JSON_PROPERTY_PIECE_TASK_SERVICES_COMMISSION_REMUNERATION_BASIS_AMOUNT = "pieceTaskServicesCommissionRemunerationBasisAmount";
  private String pieceTaskServicesCommissionRemunerationBasisAmount;

  public CalculationBaseAllowanceType() {
  }

  public CalculationBaseAllowanceType remunerationTimeUnit(String remunerationTimeUnit) {
    
    this.remunerationTimeUnit = remunerationTimeUnit;
    return this;
  }

  /**
   * Get remunerationTimeUnit
   * @return remunerationTimeUnit
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REMUNERATION_TIME_UNIT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getRemunerationTimeUnit() {
    return remunerationTimeUnit;
  }


  @JsonProperty(JSON_PROPERTY_REMUNERATION_TIME_UNIT)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRemunerationTimeUnit(String remunerationTimeUnit) {
    this.remunerationTimeUnit = remunerationTimeUnit;
  }

  public CalculationBaseAllowanceType cycle(String cycle) {
    
    this.cycle = cycle;
    return this;
  }

  /**
   * Get cycle
   * @return cycle
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CYCLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCycle() {
    return cycle;
  }


  @JsonProperty(JSON_PROPERTY_CYCLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCycle(String cycle) {
    this.cycle = cycle;
  }

  public CalculationBaseAllowanceType remunerationBasisAmount(String remunerationBasisAmount) {
    
    this.remunerationBasisAmount = remunerationBasisAmount;
    return this;
  }

  /**
   * Get remunerationBasisAmount
   * @return remunerationBasisAmount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMUNERATION_BASIS_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemunerationBasisAmount() {
    return remunerationBasisAmount;
  }


  @JsonProperty(JSON_PROPERTY_REMUNERATION_BASIS_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemunerationBasisAmount(String remunerationBasisAmount) {
    this.remunerationBasisAmount = remunerationBasisAmount;
  }

  public CalculationBaseAllowanceType pieceTaskServicesCommissionRemunerationBasisAmount(String pieceTaskServicesCommissionRemunerationBasisAmount) {
    
    this.pieceTaskServicesCommissionRemunerationBasisAmount = pieceTaskServicesCommissionRemunerationBasisAmount;
    return this;
  }

  /**
   * Get pieceTaskServicesCommissionRemunerationBasisAmount
   * @return pieceTaskServicesCommissionRemunerationBasisAmount
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PIECE_TASK_SERVICES_COMMISSION_REMUNERATION_BASIS_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPieceTaskServicesCommissionRemunerationBasisAmount() {
    return pieceTaskServicesCommissionRemunerationBasisAmount;
  }


  @JsonProperty(JSON_PROPERTY_PIECE_TASK_SERVICES_COMMISSION_REMUNERATION_BASIS_AMOUNT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPieceTaskServicesCommissionRemunerationBasisAmount(String pieceTaskServicesCommissionRemunerationBasisAmount) {
    this.pieceTaskServicesCommissionRemunerationBasisAmount = pieceTaskServicesCommissionRemunerationBasisAmount;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CalculationBaseAllowanceType calculationBaseAllowanceType = (CalculationBaseAllowanceType) o;
    return Objects.equals(this.remunerationTimeUnit, calculationBaseAllowanceType.remunerationTimeUnit) &&
        Objects.equals(this.cycle, calculationBaseAllowanceType.cycle) &&
        Objects.equals(this.remunerationBasisAmount, calculationBaseAllowanceType.remunerationBasisAmount) &&
        Objects.equals(this.pieceTaskServicesCommissionRemunerationBasisAmount, calculationBaseAllowanceType.pieceTaskServicesCommissionRemunerationBasisAmount);
  }

  @Override
  public int hashCode() {
    return Objects.hash(remunerationTimeUnit, cycle, remunerationBasisAmount, pieceTaskServicesCommissionRemunerationBasisAmount);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CalculationBaseAllowanceType {\n");
    sb.append("    remunerationTimeUnit: ").append(toIndentedString(remunerationTimeUnit)).append("\n");
    sb.append("    cycle: ").append(toIndentedString(cycle)).append("\n");
    sb.append("    remunerationBasisAmount: ").append(toIndentedString(remunerationBasisAmount)).append("\n");
    sb.append("    pieceTaskServicesCommissionRemunerationBasisAmount: ").append(toIndentedString(pieceTaskServicesCommissionRemunerationBasisAmount)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

