import {CommonModule} from "@angular/common";
import {Component, inject, OnInit} from "@angular/core";
import {FormControl, FormGroup, ReactiveFormsModule} from "@angular/forms";
import {MatButtonModule} from "@angular/material/button";
import {MatCardModule} from "@angular/material/card";
import {MAT_DIALOG_DATA, MatDialogModule, MatDialogRef} from "@angular/material/dialog";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatGridListModule} from "@angular/material/grid-list";
import {MatIconModule} from "@angular/material/icon";
import {MatInputModule} from "@angular/material/input";
import {MatRadioGroup} from "@angular/material/radio";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {Origin} from "../../../../model/types";
import {CuConsistencyCardComponent} from "./cu-consistency-card/cu-consistency-card.component";

@Component({
    selector: "lib-cu-maintain-value-dialog",
    standalone: true,
    imports: [
        MatDialogModule,
        CommonModule,
        MatIconModule,
        MatButtonModule,
        TranslateModule,
        MatFormFieldModule,
        MatInputModule,
        MatCardModule,
        MatGridListModule,
        CuConsistencyCardComponent,
        MatRadioGroup,
        ReactiveFormsModule,

    ],
    templateUrl: "./cu-maintain-value-dialog.component.html",
    styleUrl: "./cu-maintain-value-dialog.component.scss",
})
export class CuMaintainValueDialogComponent implements OnInit {
    readonly data = inject<any>(MAT_DIALOG_DATA);
    resultSelectedForm!: FormGroup;

    protected readonly Origins = Origin;

    constructor(
        public dialogRef: MatDialogRef<CuMaintainValueDialogComponent>,
        readonly translate: TranslateService) {
    }

    ngOnInit() {
        this.translate.use(this.data.language);
        this.resultSelectedForm = new FormGroup({
            resultSelected: new FormControl(this.data.defaultValue),
        });
    }

    hasValue(str: string) {
        return str != "-" && str != "";
    }

    get employeeValue() {
        return this.data.row.encodedValue;
    }

    get onemValue() {
        return this.data.row.dbValue;
    }

    get sourceAuthentiquesValue() {
        return this.data.row.sourceValue;
    }

    get employeeValueDate() {
        return this.data.row.encodedDate;
    }

    get OnemValueDate() {
        return this.data.row.dbDate;
    }

    get sourceAuthentiquesValueDate() {
        return this.data.row.sourceDate;
    }

    get resultSelected() {
        return this.resultSelectedForm.get("resultSelected") as FormControl;
    }

    onYesClick() {
        this.dialogRef.close(this.resultSelected.value);
    }

}
