package be.fgov.onerva.cu.backend.application.port.out

import java.util.*
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment

/**
 * Port interface for managing mode of payment persistence operations.
 *
 * This interface defines the contract for loading and persisting payment mode information
 * as part of the hexagonal architecture's port pattern. It serves as an abstraction layer
 * between the domain logic and the persistence infrastructure.
 *
 * @see ModeOfPayment Domain model containing payment mode details
 */
interface ModeOfPaymentPort {
    /**
     * Retrieves the mode of payment information associated with a specific request.
     *
     * @param requestId The unique identifier of the request to fetch payment mode information for
     * @return The [ModeOfPayment] if found for the given request ID, or null if no information exists
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     */
    fun getModeOfPayment(requestId: UUID): ModeOfPayment?

    /**
     * Persists or updates mode of payment information for a specific request.
     *
     * If payment mode information already exists for the given request ID, it will be updated.
     * Otherwise, new payment mode information will be created.
     *
     * @param requestId The unique identifier of the request to associate the payment mode information with
     * @param modeOfPayment The payment mode information to be persisted
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     * @throws InvalidInputException if the provided mode of payment data is invalid
     */
    fun persistModeOfPayment(requestId: UUID, modeOfPayment: ModeOfPayment)

    fun getLatestRevision(requestId: UUID): Int

    fun getModeOfPaymentForRevision(requestId: UUID, revision: Int): ModeOfPayment

    fun getEntityId(requestId: UUID): UUID

    fun patchCurrentDataWithRevision(requestId: UUID, revision: Int)
}