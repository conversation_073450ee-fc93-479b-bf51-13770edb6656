package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api;

import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.DetachedCriteriaParamDTO;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model.LookupSimpleItemizedDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class LookupApi extends BaseApi {

    public LookupApi() {
        super(new ApiClient());
    }

    public LookupApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Returns the greatest valid lookup (in time) for the code in a validity period.   Query params: class, fields required to complete the code, at least &#39;code&#39;, beginDate, endDate
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param beginDate  (required)
     * @param propertyClass the class (required)
     * @param endDate  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupFindGreatestValidLookupGet(String beginDate, String propertyClass, String endDate) throws RestClientException {
        return lookupFindGreatestValidLookupGetWithHttpInfo(beginDate, propertyClass, endDate).getBody();
    }

    /**
     * 
     * Returns the greatest valid lookup (in time) for the code in a validity period.   Query params: class, fields required to complete the code, at least &#39;code&#39;, beginDate, endDate
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param beginDate  (required)
     * @param propertyClass the class (required)
     * @param endDate  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupFindGreatestValidLookupGetWithHttpInfo(String beginDate, String propertyClass, String endDate) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'beginDate' is set
        if (beginDate == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'beginDate' when calling lookupFindGreatestValidLookupGet");
        }
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupFindGreatestValidLookupGet");
        }
        
        // verify the required parameter 'endDate' is set
        if (endDate == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'endDate' when calling lookupFindGreatestValidLookupGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "beginDate", beginDate));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "endDate", endDate));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/findGreatestValidLookup", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns a list of all Lookups for the given class.   Query params: class,  Query examples :       http://services/lookupwpptservice/rest/lookup/getAllLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetAllLookupsGet(String propertyClass) throws RestClientException {
        return lookupGetAllLookupsGetWithHttpInfo(propertyClass).getBody();
    }

    /**
     * 
     * Returns a list of all Lookups for the given class.   Query params: class,  Query examples :       http://services/lookupwpptservice/rest/lookup/getAllLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetAllLookupsGetWithHttpInfo(String propertyClass) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupGetAllLookupsGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getAllLookups", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param iban  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetBICForIBANGet(String date, String iban) throws RestClientException {
        return lookupGetBICForIBANGetWithHttpInfo(date, iban).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param iban  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetBICForIBANGetWithHttpInfo(String date, String iban) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetBICForIBANGet");
        }
        
        // verify the required parameter 'iban' is set
        if (iban == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'iban' when calling lookupGetBICForIBANGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "iban", iban));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getBICForIBAN", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param postalCode  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetBelgianCommunityByPostalCodeGet(String date, String postalCode) throws RestClientException {
        return lookupGetBelgianCommunityByPostalCodeGetWithHttpInfo(date, postalCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param postalCode  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetBelgianCommunityByPostalCodeGetWithHttpInfo(String date, String postalCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetBelgianCommunityByPostalCodeGet");
        }
        
        // verify the required parameter 'postalCode' is set
        if (postalCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'postalCode' when calling lookupGetBelgianCommunityByPostalCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "postalCode", postalCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getBelgianCommunityByPostalCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param courtType  (required)
     * @param date  (required)
     * @param language  (required)
     * @param postalCode  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetCourtForPostalCodeCourtTypeAndLanguageGet(String courtType, String date, String language, String postalCode) throws RestClientException {
        return lookupGetCourtForPostalCodeCourtTypeAndLanguageGetWithHttpInfo(courtType, date, language, postalCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param courtType  (required)
     * @param date  (required)
     * @param language  (required)
     * @param postalCode  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetCourtForPostalCodeCourtTypeAndLanguageGetWithHttpInfo(String courtType, String date, String language, String postalCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'courtType' is set
        if (courtType == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'courtType' when calling lookupGetCourtForPostalCodeCourtTypeAndLanguageGet");
        }
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetCourtForPostalCodeCourtTypeAndLanguageGet");
        }
        
        // verify the required parameter 'language' is set
        if (language == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'language' when calling lookupGetCourtForPostalCodeCourtTypeAndLanguageGet");
        }
        
        // verify the required parameter 'postalCode' is set
        if (postalCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'postalCode' when calling lookupGetCourtForPostalCodeCourtTypeAndLanguageGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "courtType", courtType));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "language", language));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "postalCode", postalCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getCourtForPostalCodeCourtTypeAndLanguage", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param courtLevel  (required)
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetCourtTypeByCourtLevelGet(String courtLevel, String date) throws RestClientException {
        return lookupGetCourtTypeByCourtLevelGetWithHttpInfo(courtLevel, date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param courtLevel  (required)
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetCourtTypeByCourtLevelGetWithHttpInfo(String courtLevel, String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'courtLevel' is set
        if (courtLevel == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'courtLevel' when calling lookupGetCourtTypeByCourtLevelGet");
        }
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetCourtTypeByCourtLevelGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "courtLevel", courtLevel));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getCourtTypeByCourtLevel", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param postalCode  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public List<LookupSimpleItemizedDTO> lookupGetInsPostByPostalCodeGet(String date, String postalCode) throws RestClientException {
        return lookupGetInsPostByPostalCodeGetWithHttpInfo(date, postalCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param postalCode  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetInsPostByPostalCodeGetWithHttpInfo(String date, String postalCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetInsPostByPostalCodeGet");
        }
        
        // verify the required parameter 'postalCode' is set
        if (postalCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'postalCode' when calling lookupGetInsPostByPostalCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "postalCode", postalCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getInsPostByPostalCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns the Lookup for the given code.   Query params: date, class and fields required to complete the code, at least &#39;code&#39;.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookupByCode?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;code&#x3D;724       http://services/lookupwpptservice/rest/lookup/getLookupByCode?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;code&#x3D;724&amp;date&#x3D;2015-01-01
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param code code (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetLookupByCodeGet(String propertyClass, String code) throws RestClientException {
        return lookupGetLookupByCodeGetWithHttpInfo(propertyClass, code).getBody();
    }

    /**
     * 
     * Returns the Lookup for the given code.   Query params: date, class and fields required to complete the code, at least &#39;code&#39;.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookupByCode?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;code&#x3D;724       http://services/lookupwpptservice/rest/lookup/getLookupByCode?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;code&#x3D;724&amp;date&#x3D;2015-01-01
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param code code (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetLookupByCodeGetWithHttpInfo(String propertyClass, String code) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupGetLookupByCodeGet");
        }
        
        // verify the required parameter 'code' is set
        if (code == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'code' when calling lookupGetLookupByCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "code", code));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getLookupByCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns the Lookup for the given Lookup id.   Query params: class, id.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookupById?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;id&#x3D;805
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param id the id of the lookup (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetLookupByIdGet(String propertyClass, String id) throws RestClientException {
        return lookupGetLookupByIdGetWithHttpInfo(propertyClass, id).getBody();
    }

    /**
     * 
     * Returns the Lookup for the given Lookup id.   Query params: class, id.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookupById?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;id&#x3D;805
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param id the id of the lookup (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetLookupByIdGetWithHttpInfo(String propertyClass, String id) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupGetLookupByIdGet");
        }
        
        // verify the required parameter 'id' is set
        if (id == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'id' when calling lookupGetLookupByIdGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "id", id));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getLookupById", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns the list of Lookups for the given criteria.   Entity: DetachedCriteriaParam
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param body the criteria (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetLookupsByCriteriaPost(DetachedCriteriaParamDTO body) throws RestClientException {
        return lookupGetLookupsByCriteriaPostWithHttpInfo(body).getBody();
    }

    /**
     * 
     * Returns the list of Lookups for the given criteria.   Entity: DetachedCriteriaParam
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param body the criteria (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetLookupsByCriteriaPostWithHttpInfo(DetachedCriteriaParamDTO body) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'body' is set
        if (body == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'body' when calling lookupGetLookupsByCriteriaPost");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getLookupsByCriteria", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns a list of Lookups for the given HQL query.   Entity: DetachedCriteriaParam
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class of the lookups to get. (required)
     * @param query the query (required)
     * @param body query parameters (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetLookupsByHqlPost(String propertyClass, String query, Object body) throws RestClientException {
        return lookupGetLookupsByHqlPostWithHttpInfo(propertyClass, query, body).getBody();
    }

    /**
     * 
     * Returns a list of Lookups for the given HQL query.   Entity: DetachedCriteriaParam
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class of the lookups to get. (required)
     * @param query the query (required)
     * @param body query parameters (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetLookupsByHqlPostWithHttpInfo(String propertyClass, String query, Object body) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupGetLookupsByHqlPost");
        }
        
        // verify the required parameter 'query' is set
        if (query == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'query' when calling lookupGetLookupsByHqlPost");
        }
        
        // verify the required parameter 'body' is set
        if (body == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'body' when calling lookupGetLookupsByHqlPost");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "query", query));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getLookupsByHql", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * Returns a list of Lookups for the given class and date.   Query params: class, date.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice       http://services/lookupwpptservice/rest/lookup/getLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;date&#x3D;2015-01-01
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param date the date (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetLookupsGet(String propertyClass, String date) throws RestClientException {
        return lookupGetLookupsGetWithHttpInfo(propertyClass, date).getBody();
    }

    /**
     * 
     * Returns a list of Lookups for the given class and date.   Query params: class, date.  Query examples :       http://services/lookupwpptservice/rest/lookup/getLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice       http://services/lookupwpptservice/rest/lookup/getLookups?class&#x3D;be.fgov.onerva.lookup.wppt.persistence.model.common.UnemploymentOffice&amp;date&#x3D;2015-01-01
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param propertyClass the class (required)
     * @param date the date (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetLookupsGetWithHttpInfo(String propertyClass, String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'propertyClass' is set
        if (propertyClass == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'propertyClass' when calling lookupGetLookupsGet");
        }
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetLookupsGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "class", propertyClass));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getLookups", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param countryCode  (required)
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetNationalityBcssByCountryCodeGet(String countryCode, String date) throws RestClientException {
        return lookupGetNationalityBcssByCountryCodeGetWithHttpInfo(countryCode, date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param countryCode  (required)
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetNationalityBcssByCountryCodeGetWithHttpInfo(String countryCode, String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'countryCode' is set
        if (countryCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'countryCode' when calling lookupGetNationalityBcssByCountryCodeGet");
        }
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetNationalityBcssByCountryCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "countryCode", countryCode));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getNationalityBcssByCountryCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param type  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetTemporaryUnemploymentReasonsByTypeGet(String date, String type) throws RestClientException {
        return lookupGetTemporaryUnemploymentReasonsByTypeGetWithHttpInfo(date, type).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param type  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetTemporaryUnemploymentReasonsByTypeGetWithHttpInfo(String date, String type) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetTemporaryUnemploymentReasonsByTypeGet");
        }
        
        // verify the required parameter 'type' is set
        if (type == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'type' when calling lookupGetTemporaryUnemploymentReasonsByTypeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "type", type));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getTemporaryUnemploymentReasonsByType", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param mfuBcssCode  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetUnemploymentOfficeByMFUBcssCodeGet(String date, String mfuBcssCode) throws RestClientException {
        return lookupGetUnemploymentOfficeByMFUBcssCodeGetWithHttpInfo(date, mfuBcssCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param mfuBcssCode  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeByMFUBcssCodeGetWithHttpInfo(String date, String mfuBcssCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeByMFUBcssCodeGet");
        }
        
        // verify the required parameter 'mfuBcssCode' is set
        if (mfuBcssCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mfuBcssCode' when calling lookupGetUnemploymentOfficeByMFUBcssCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "mfuBcssCode", mfuBcssCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeByMFUBcssCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param mfuCode  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetUnemploymentOfficeByMFUCodeGet(String date, String mfuCode) throws RestClientException {
        return lookupGetUnemploymentOfficeByMFUCodeGetWithHttpInfo(date, mfuCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param mfuCode  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeByMFUCodeGetWithHttpInfo(String date, String mfuCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeByMFUCodeGet");
        }
        
        // verify the required parameter 'mfuCode' is set
        if (mfuCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'mfuCode' when calling lookupGetUnemploymentOfficeByMFUCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "mfuCode", mfuCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeByMFUCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param optirecDossierCode  (required)
     * @return LookupSimpleItemizedDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public LookupSimpleItemizedDTO lookupGetUnemploymentOfficeByOptirecCodeGet(String date, String optirecDossierCode) throws RestClientException {
        return lookupGetUnemploymentOfficeByOptirecCodeGetWithHttpInfo(date, optirecDossierCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param optirecDossierCode  (required)
     * @return ResponseEntity&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeByOptirecCodeGetWithHttpInfo(String date, String optirecDossierCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeByOptirecCodeGet");
        }
        
        // verify the required parameter 'optirecDossierCode' is set
        if (optirecDossierCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'optirecDossierCode' when calling lookupGetUnemploymentOfficeByOptirecCodeGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "optirecDossierCode", optirecDossierCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<LookupSimpleItemizedDTO> localReturnType = new ParameterizedTypeReference<LookupSimpleItemizedDTO>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeByOptirecCode", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeForMFUBcssGet(String date) throws RestClientException {
        return lookupGetUnemploymentOfficeForMFUBcssGetWithHttpInfo(date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficeForMFUBcssGetWithHttpInfo(String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeForMFUBcssGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeForMFUBcss", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeForMFUGet(String date) throws RestClientException {
        return lookupGetUnemploymentOfficeForMFUGetWithHttpInfo(date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficeForMFUGetWithHttpInfo(String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeForMFUGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeForMFU", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeForOptirecGet(String date) throws RestClientException {
        return lookupGetUnemploymentOfficeForOptirecGetWithHttpInfo(date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficeForOptirecGetWithHttpInfo(String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeForOptirecGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeForOptirec", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficeOnlyGet(String date) throws RestClientException {
        return lookupGetUnemploymentOfficeOnlyGetWithHttpInfo(date).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficeOnlyGetWithHttpInfo(String date) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficeOnlyGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficeOnly", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param entityCode  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficesByEntityGet(String date, String entityCode) throws RestClientException {
        return lookupGetUnemploymentOfficesByEntityGetWithHttpInfo(date, entityCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param entityCode  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficesByEntityGetWithHttpInfo(String date, String entityCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficesByEntityGet");
        }
        
        // verify the required parameter 'entityCode' is set
        if (entityCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'entityCode' when calling lookupGetUnemploymentOfficesByEntityGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "entityCode", entityCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficesByEntity", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param unemploymentOfficeCode  (required)
     * @return List&lt;LookupSimpleItemizedDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<LookupSimpleItemizedDTO> lookupGetUnemploymentOfficesForSameEntityGet(String date, String unemploymentOfficeCode) throws RestClientException {
        return lookupGetUnemploymentOfficesForSameEntityGetWithHttpInfo(date, unemploymentOfficeCode).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad Request
     * <p><b>404</b> - Not Found
     * @param date  (required)
     * @param unemploymentOfficeCode  (required)
     * @return ResponseEntity&lt;List&lt;LookupSimpleItemizedDTO&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<LookupSimpleItemizedDTO>> lookupGetUnemploymentOfficesForSameEntityGetWithHttpInfo(String date, String unemploymentOfficeCode) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'date' is set
        if (date == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'date' when calling lookupGetUnemploymentOfficesForSameEntityGet");
        }
        
        // verify the required parameter 'unemploymentOfficeCode' is set
        if (unemploymentOfficeCode == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'unemploymentOfficeCode' when calling lookupGetUnemploymentOfficesForSameEntityGet");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "unemploymentOfficeCode", unemploymentOfficeCode));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<List<LookupSimpleItemizedDTO>> localReturnType = new ParameterizedTypeReference<List<LookupSimpleItemizedDTO>>() {};
        return apiClient.invokeAPI("/lookup/getUnemploymentOfficesForSameEntity", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
