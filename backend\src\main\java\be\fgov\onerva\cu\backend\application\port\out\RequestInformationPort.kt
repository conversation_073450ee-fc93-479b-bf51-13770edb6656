package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.RequestInformation

interface RequestInformationPort {
    /**
     * Get the date of the request information
     * @param requestId the id of the request
     * @return the date of the request information
     * @throws RequestIdNotFoundException if the request information is not found
     */
    fun getRequestInformation(requestId: UUID): RequestInformation

    /**
     * Update the date of the request information
     * @param requestId the id of the request
     * @param date the new date of the request information
     * @throws RequestIdNotFoundException if the request information is not found
     */
    fun updateRequestInformation(requestId: UUID, updateRequestInformation: RequestInformation)

    /**
     * Persist the request information
     * @param requestId the id of the request
     * @param newRequestInformation the new request information
     * @throws RequestIdNotFoundException if the request information is not found
     */
    fun persistRequestInformation(requestId: UUID, newRequestInformation: RequestInformation)

    /**
     * Get the latest revision of the request information
     * @param requestId the id of the request
     * @return the latest revision of the request information
     * @throws RequestIdNotFoundException if the request information is not found
     */
    fun getLatestRevision(requestId: UUID): Int

    fun getRequestInformationForRevision(requestId: UUID, revision: Int): RequestInformation

    fun getEntityId(requestId: UUID): UUID

    fun patchCurrentDataWithRevision(requestId: UUID, revision: Int)
}