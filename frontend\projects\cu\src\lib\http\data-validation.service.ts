import {Injectable} from "@angular/core";
import {HttpClient, HttpHeaders} from "@angular/common/http";
import {Observable} from "rxjs";
import {
    RequestInformationService,
    AggregatedChangePersonalDataValidateResponse,
    AggregateRequestInformationService,
    Configuration as ConfigurationBff,
    UpdateAggregatedChangePersonalDataRequest,
    UpdateUnionContributionRequest,
    UnionContributionService,
    ModeOfPaymentService,
    UpdateModeOfPaymentRequest,
    UpdateCitizenInformationRequest,
    FieldSource,
    CitizenInformationService,
    SelectFieldSourcesRequest,
} from "@rest-client/cu-bff";
import {ConfigService} from "../config/config.service";

@Injectable({
    providedIn: "root",
})
export class DataValidationService {
    private requestInformationService!: RequestInformationService;
    private aggregateRequestInformationService!: AggregateRequestInformationService;
    private unionContributionService!: UnionContributionService;
    private modeOfPaymentService!: ModeOfPaymentService;
    private citizenInformationService!: CitizenInformationService;

    constructor(
        readonly http: HttpClient,
        readonly configService: ConfigService,
    ) {
        this.initializeServices();
    }

    initializeServices(token?: string) {
        const configBff = new ConfigurationBff({
            basePath: this.configService.getEnvironmentVariable("bffBaseUrl", true),
            credentials: token ? {"Bearer": token} : undefined,
        });

        const defaultHeaders = token ?
            new HttpHeaders()
                .set("Authorization", `Bearer ${token}`)
                .set("Content-Type", "application/json") :
            new HttpHeaders().set("Content-Type", "application/json");

        this.aggregateRequestInformationService = new AggregateRequestInformationService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );
        this.requestInformationService = new RequestInformationService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );
        this.citizenInformationService = new CitizenInformationService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );
        this.modeOfPaymentService = new ModeOfPaymentService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );
        this.unionContributionService = new UnionContributionService(
            this.http,
            this.configService.getEnvironmentVariable("bffBaseUrl", true),
            configBff,
        );

        if (token) {
            [
                this.aggregateRequestInformationService,
                this.requestInformationService,
                this.citizenInformationService,
                this.modeOfPaymentService,
                this.unionContributionService,
            ].forEach(service => {
                service.defaultHeaders = defaultHeaders;
            });
        }
    }

    getAggregatedData(requestId: string): Observable<AggregatedChangePersonalDataValidateResponse> {
        return this.aggregateRequestInformationService.getAggregatedChangePersonalDataValidateRequest(requestId);
    }

    updateRequest(requestId: string, data: UpdateAggregatedChangePersonalDataRequest): Observable<any> {
        return this.aggregateRequestInformationService.updateAggregatedChangePersonalDataRequest(requestId, data);
    }

    closeTask(requestId: string, taskType: string = "CHANGE_PERSONAL_DATA_CAPTURE"): Observable<any> {
        return this.requestInformationService.closeRequestTask(requestId, taskType);
    }

    updateUnionContribution(requestId: string, data: UpdateUnionContributionRequest): Observable<any> {
        return this.unionContributionService.updateUnionContribution(requestId, data);
    }

    updateModeOfPayment(requestId: string, data: UpdateModeOfPaymentRequest): Observable<any> {
        return this.modeOfPaymentService.updateModeOfPayment(requestId, data);
    }

    updateCitizenInformation(requestId: string, data: UpdateCitizenInformationRequest): Observable<any> {
        return this.citizenInformationService.updateCitizenInformation(requestId, data);
    }

    selectCitizenInformationSources(requestId: string, fieldSources: FieldSource[]): Observable<any> {
        const request: SelectFieldSourcesRequest = {
            fieldSources: fieldSources,
        };
        return this.citizenInformationService.selectCitizenInformationSources(requestId, request);
    }

    selectModeOfPaymentSources(requestId: string, fieldSources: FieldSource[]): Observable<any> {
        const request: SelectFieldSourcesRequest = {
            fieldSources: fieldSources,
        };
        return this.modeOfPaymentService.selectModeOfPaymentSources(requestId, request);
    }

    selectUnionContributionSources(requestId: string, fieldSources: FieldSource[]): Observable<any> {
        const request: SelectFieldSourcesRequest = {
            fieldSources: fieldSources,
        };
        return this.unionContributionService.selectUnionContributionSources(requestId, request);
    }
}
