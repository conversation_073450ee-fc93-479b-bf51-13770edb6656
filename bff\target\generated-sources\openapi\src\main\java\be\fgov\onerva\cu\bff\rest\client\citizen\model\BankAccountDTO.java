/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * BankAccountDTO
 */
@JsonPropertyOrder({
  BankAccountDTO.JSON_PROPERTY_IBAN,
  BankAccountDTO.JSON_PROPERTY_BIC,
  BankAccountDTO.JSON_PROPERTY_HOLDER,
  BankAccountDTO.JSON_PROPERTY_VALID_FROM
})
@JsonTypeName("BankAccount")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class BankAccountDTO {
  public static final String JSON_PROPERTY_IBAN = "iban";
  private String iban;

  public static final String JSON_PROPERTY_BIC = "bic";
  private String bic;

  public static final String JSON_PROPERTY_HOLDER = "holder";
  private String holder;

  public static final String JSON_PROPERTY_VALID_FROM = "validFrom";
  private LocalDate validFrom;

  public BankAccountDTO() {
  }

  public BankAccountDTO iban(String iban) {
    
    this.iban = iban;
    return this;
  }

  /**
   * Get iban
   * @return iban
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIban() {
    return iban;
  }


  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIban(String iban) {
    this.iban = iban;
  }

  public BankAccountDTO bic(String bic) {
    
    this.bic = bic;
    return this;
  }

  /**
   * Get bic
   * @return bic
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BIC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBic() {
    return bic;
  }


  @JsonProperty(JSON_PROPERTY_BIC)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBic(String bic) {
    this.bic = bic;
  }

  public BankAccountDTO holder(String holder) {
    
    this.holder = holder;
    return this;
  }

  /**
   * The name of the bank account holder
   * @return holder
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HOLDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getHolder() {
    return holder;
  }


  @JsonProperty(JSON_PROPERTY_HOLDER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHolder(String holder) {
    this.holder = holder;
  }

  public BankAccountDTO validFrom(LocalDate validFrom) {
    
    this.validFrom = validFrom;
    return this;
  }

  /**
   * Get validFrom
   * @return validFrom
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getValidFrom() {
    return validFrom;
  }


  @JsonProperty(JSON_PROPERTY_VALID_FROM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValidFrom(LocalDate validFrom) {
    this.validFrom = validFrom;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BankAccountDTO bankAccount = (BankAccountDTO) o;
    return Objects.equals(this.iban, bankAccount.iban) &&
        Objects.equals(this.bic, bankAccount.bic) &&
        Objects.equals(this.holder, bankAccount.holder) &&
        Objects.equals(this.validFrom, bankAccount.validFrom);
  }

  @Override
  public int hashCode() {
    return Objects.hash(iban, bic, holder, validFrom);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BankAccountDTO {\n");
    sb.append("    iban: ").append(toIndentedString(iban)).append("\n");
    sb.append("    bic: ").append(toIndentedString(bic)).append("\n");
    sb.append("    holder: ").append(toIndentedString(holder)).append("\n");
    sb.append("    validFrom: ").append(toIndentedString(validFrom)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

