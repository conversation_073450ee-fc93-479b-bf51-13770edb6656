
GET {{microcks-url}}/rest/Person+API/1.0.0/citizen/18031307065
Accept: application/json

### Update Citizen
PUT {{microcks-url}}/rest/Person+API/1.0.0/citizen/18031307065?username=cu_user
Accept: application/json

{
}

### GET Citizen info V1
GET {{microcks-url}}/rest/Person+API/1.0.0/citizen/info?pageNumber=0&pageSize=10&ssins=18031307065&dataReturned=SUMMARY
Accept: application/json

### GET Citizen info V2
GET {{microcks-url}}/rest/Person+API/1.0.0/v2/citizen/info?pageNumber=0&pageSize=10&ssins=18031307065&dataReturned=SUMMARY
Accept: application/json

### GET C9 with id 1234
GET {{microcks-url}}/rest/C9 REST API/1.0.0/c9s/1234
Accept: application/json

### GET C9 with id 123
GET {{microcks-url}}/rest/C9 REST API/1.0.0/c9s/123
Accept: application/json

### GET Barema - 2022-09-22
GET {{microcks-url}}/rest/Bareme+REST+API/1.0.0/v2/bareme?citizenId=47000&date=2022-09-22&dateCompareOperator=LESS_OR_EQUAL&type=COMPLETE_UNEMPLOYMENT&dateOrderDirection=DESC

### GET Barema - 2022-09-22
GET {{wiremock-url}}/rest/Bareme+REST+API/1.0.0/v2/bareme?citizenId=47000&date=2022-09-22&dateCompareOperator=LESS_OR_EQUAL&type=COMPLETE_UNEMPLOYMENT&dateOrderDirection=DESC

### GET Barema
< {%
    let now = new Date();
    let month = String(now.getMonth() + 1).padStart(2, '0');
    let day = String(now.getDate()).padStart(2, '0');
    let formattedDate = `${now.getFullYear()}-${month}-${day}`;
    request.variables.set('currentDate', formattedDate);
%}
GET {{microcks-url}}/rest/Bareme+REST+API/1.0.0/v2/bareme?citizenId=47000&date={{currentDate}}&dateCompareOperator=LESS_OR_EQUAL&type=COMPLETE_UNEMPLOYMENT&dateOrderDirection=DESC


### GET Barema
< {%
    let now = new Date();
    let month = String(now.getMonth() + 1).padStart(2, '0');
    let day = String(now.getDate()).padStart(2, '0');
    let formattedDate = `${now.getFullYear()}-${month}-${day}`;
    request.variables.set('currentDate', formattedDate);
%}
GET {{wiremock-url}}/rest/Bareme+REST+API/1.0.0/v2/bareme?citizenId=47000&date={{currentDate}}&dateCompareOperator=LESS_OR_EQUAL&type=COMPLETE_UNEMPLOYMENT&dateOrderDirection=DESC
