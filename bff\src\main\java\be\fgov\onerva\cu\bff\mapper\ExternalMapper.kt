package be.fgov.onerva.cu.bff.mapper

import be.fgov.onerva.cu.bff.model.Address
import be.fgov.onerva.cu.bff.model.CitizenInfoWithAddress
import be.fgov.onerva.cu.bff.model.UnionContribution
import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoDTO

fun CitizenInfoDTO.toCitizenInfoWithAddress(): CitizenInfoWithAddress {
    return CitizenInfoWithAddress(
        firstName = this.firstName,
        lastName = this.lastName,
        numbox = this.numBox?.toInt() ?: 0,
        nationality = this.flagNation?.toString(),
        iban = this.bankAccount?.iban,
        bic = this.bankAccount?.bic,
        otherPersonName = this.bankAccount?.holder,
        paymentMode = this.paymentMode,
        address = Address(
            street = this.addressObj?.street,
            houseNumber = this.addressObj?.number,
            boxNumber = this.addressObj?.box,
            zipCode = this.addressObj?.zip,
            country = this.addressObj?.countryCode?.toString(),
            city = this.addressObj?.city,
        ),
        unionDue = UnionContribution(
            mandateActive = this.unionDue?.mandateActive ?: false,
            validFrom = this.unionDue?.validFrom,
        )
    )
}

