/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CorrelationDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.InputMetaDataDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.InputThirdPartyDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * UpdatableTaskDTO
 */
@JsonPropertyOrder({
  UpdatableTaskDTO.JSON_PROPERTY_METADATA,
  UpdatableTaskDTO.JSON_PROPERTY_CONCERNED_ENTITIES,
  UpdatableTaskDTO.JSON_PROPERTY_ASSIGNEE,
  UpdatableTaskDTO.JSON_PROPERTY_DUE_DATE,
  UpdatableTaskDTO.JSON_PROPERTY_CORRELATION
})
@JsonTypeName("UpdatableTask")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class UpdatableTaskDTO {
  public static final String JSON_PROPERTY_METADATA = "metadata";
  private List<InputMetaDataDTO> metadata;

  public static final String JSON_PROPERTY_CONCERNED_ENTITIES = "concernedEntities";
  private List<InputThirdPartyDTO> concernedEntities = new ArrayList<>();

  public static final String JSON_PROPERTY_ASSIGNEE = "assignee";
  private String assignee;

  public static final String JSON_PROPERTY_DUE_DATE = "dueDate";
  private LocalDate dueDate;

  public static final String JSON_PROPERTY_CORRELATION = "correlation";
  private CorrelationDTO correlation;

  public UpdatableTaskDTO() {
  }

  public UpdatableTaskDTO metadata(List<InputMetaDataDTO> metadata) {
    
    this.metadata = metadata;
    return this;
  }

  public UpdatableTaskDTO addMetadataItem(InputMetaDataDTO metadataItem) {
    if (this.metadata == null) {
      this.metadata = new ArrayList<>();
    }
    this.metadata.add(metadataItem);
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<InputMetaDataDTO> getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(List<InputMetaDataDTO> metadata) {
    this.metadata = metadata;
  }

  public UpdatableTaskDTO concernedEntities(List<InputThirdPartyDTO> concernedEntities) {
    
    this.concernedEntities = concernedEntities;
    return this;
  }

  public UpdatableTaskDTO addConcernedEntitiesItem(InputThirdPartyDTO concernedEntitiesItem) {
    if (this.concernedEntities == null) {
      this.concernedEntities = new ArrayList<>();
    }
    this.concernedEntities.add(concernedEntitiesItem);
    return this;
  }

  /**
   * Get concernedEntities
   * @return concernedEntities
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<InputThirdPartyDTO> getConcernedEntities() {
    return concernedEntities;
  }


  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setConcernedEntities(List<InputThirdPartyDTO> concernedEntities) {
    this.concernedEntities = concernedEntities;
  }

  public UpdatableTaskDTO assignee(String assignee) {
    
    this.assignee = assignee;
    return this;
  }

  /**
   * It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \&quot;E\&quot; + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup
   * @return assignee
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getAssignee() {
    return assignee;
  }


  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public UpdatableTaskDTO dueDate(LocalDate dueDate) {
    
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Get dueDate
   * @return dueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDueDate() {
    return dueDate;
  }


  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
  }

  public UpdatableTaskDTO correlation(CorrelationDTO correlation) {
    
    this.correlation = correlation;
    return this;
  }

  /**
   * Get correlation
   * @return correlation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CORRELATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CorrelationDTO getCorrelation() {
    return correlation;
  }


  @JsonProperty(JSON_PROPERTY_CORRELATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCorrelation(CorrelationDTO correlation) {
    this.correlation = correlation;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    UpdatableTaskDTO updatableTask = (UpdatableTaskDTO) o;
    return Objects.equals(this.metadata, updatableTask.metadata) &&
        Objects.equals(this.concernedEntities, updatableTask.concernedEntities) &&
        Objects.equals(this.assignee, updatableTask.assignee) &&
        Objects.equals(this.dueDate, updatableTask.dueDate) &&
        Objects.equals(this.correlation, updatableTask.correlation);
  }

  @Override
  public int hashCode() {
    return Objects.hash(metadata, concernedEntities, assignee, dueDate, correlation);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class UpdatableTaskDTO {\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    concernedEntities: ").append(toIndentedString(concernedEntities)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("    correlation: ").append(toIndentedString(correlation)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

