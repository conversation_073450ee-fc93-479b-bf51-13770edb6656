/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param recipients emails you want to send the meeting to
 * @param startDate Start date (and optionally time) of the meeting
 * @param title Meeting title
 * @param endDate End date (and optionally time) of the meeting
 * @param content Content of the meeting
 * @param recurring if set to true, will create a meeting by day from the start day to the end day.
 * @param visibility 
 */


data class SendEmailMeetingRequestDTO (

    /* emails you want to send the meeting to */
    @get:JsonProperty("recipients")
    val recipients: kotlin.collections.List<kotlin.String>,

    /* Start date (and optionally time) of the meeting */
    @get:JsonProperty("startDate")
    val startDate: java.time.OffsetDateTime,

    /* Meeting title */
    @get:JsonProperty("title")
    val title: kotlin.String? = null,

    /* End date (and optionally time) of the meeting */
    @get:JsonProperty("endDate")
    val endDate: java.time.OffsetDateTime? = null,

    /* Content of the meeting */
    @get:JsonProperty("content")
    val content: kotlin.String? = null,

    /* if set to true, will create a meeting by day from the start day to the end day. */
    @get:JsonProperty("recurring")
    val recurring: kotlin.Boolean? = false,

    @get:JsonProperty("visibility")
    val visibility: SendEmailMeetingRequestDTO.Visibility? = Visibility.BUSY

) {

    /**
     * 
     *
     * Values: BUSY,FREE,WORKING_ELSEWHERE
     */
    enum class Visibility(val value: kotlin.String) {
        @JsonProperty(value = "busy") BUSY("busy"),
        @JsonProperty(value = "free") FREE("free"),
        @JsonProperty(value = "workingElsewhere") WORKING_ELSEWHERE("workingElsewhere");
    }

}

