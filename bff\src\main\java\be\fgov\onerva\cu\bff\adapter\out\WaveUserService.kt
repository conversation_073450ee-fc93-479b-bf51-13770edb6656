package be.fgov.onerva.cu.bff.adapter.out

import org.springframework.stereotype.Service
import be.fgov.onerva.cu.bff.exceptions.WaveUserNotFoundException
import be.fgov.onerva.cu.bff.rest.client.wo.facade.api.UserApi
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.UserCriteriaDTO
import be.fgov.onerva.cu.common.utils.logger

@Service
class WaveUserService(val userApi: UserApi) {
    private val log = logger

    fun getOperatorCode(ssin: String): String {
        val userCriteriaDTO = UserCriteriaDTO().apply {
            this.inss = ssin
            this.username = null
            this.operatorCode = null
        }
        try {
            val searchUsers = userApi.searchUsers(userCriteriaDTO)
            log.debug("getOperatorCode: User found for ssin {}: {}", ssin, searchUsers)
            return searchUsers.operatorCodes[0]
        } catch (e: Exception) {
            throw WaveUserNotFoundException("Wave user not found for SSIN $ssin")
        }
    }
}