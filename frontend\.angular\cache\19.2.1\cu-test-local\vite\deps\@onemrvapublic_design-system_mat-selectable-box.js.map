{"version": 3, "sources": ["../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-mat-selectable-box.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { EventEmitter, Output, Input, ViewChild, Component, NgModule } from '@angular/core';\nimport { MatRadioButton } from '@angular/material/radio';\nimport { <PERSON><PERSON>ard<PERSON><PERSON>le, Mat<PERSON>ard, MatCardContent } from '@angular/material/card';\nimport { MatLabel } from '@angular/material/form-field';\nimport { MatIcon } from '@angular/material/icon';\nconst _c0 = [\"radioButton\"];\nconst _c1 = [[[\"\", \"title\", \"\"]], [[\"\", \"icon\", \"\"]], [[\"\", \"sticker\", \"\"]], \"*\"];\nconst _c2 = [\"[title]\", \"[icon]\", \"[sticker]\", \"*\"];\nclass OnemrvaMatSelectableBoxComponent {\n  valueChanged(event) {\n    this.change.emit(event);\n  }\n  constructor(changeDetectorRef) {\n    this.changeDetectorRef = changeDetectorRef;\n    this.value = '';\n    this.disabled = false;\n    this.mode = 'primary';\n    this.checked = false;\n    // eslint-disable-next-line @angular-eslint/no-output-native\n    this.change = new EventEmitter();\n  }\n  ngAfterViewInit() {\n    this.changeDetectorRef.detectChanges();\n  }\n  isChecked() {\n    return this.radioButton?._inputElement.nativeElement.checked ?? this.checked;\n  }\n  triggerRadioClick() {\n    if (this.radioButton && !this.radioButton.checked && !this.radioButton.disabled) {\n      // https://v11.material.angular.io/components/radio/api\n      // Changing checked prop does not trigger a change event on the button or radiogroup,\n      // only on user-interaction. Workaround trigger/simulate a click event.\n      this.radioButton._inputElement.nativeElement.dispatchEvent(new MouseEvent('click', {\n        bubbles: false\n      }));\n    }\n  }\n  static {\n    this.ɵfac = function OnemrvaMatSelectableBoxComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatSelectableBoxComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: OnemrvaMatSelectableBoxComponent,\n      selectors: [[\"onemrva-mat-selectable-box\"]],\n      viewQuery: function OnemrvaMatSelectableBoxComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.radioButton = _t.first);\n        }\n      },\n      inputs: {\n        value: \"value\",\n        disabled: \"disabled\",\n        mode: \"mode\",\n        checked: \"checked\"\n      },\n      outputs: {\n        change: \"change\"\n      },\n      ngContentSelectors: _c2,\n      decls: 11,\n      vars: 21,\n      consts: [[\"radioButton\", \"\"], [1, \"m\", \"onemrva-shadow-highlight\", 3, \"click\"], [1, \"selectablebox-title\"], [\"aria-label\", \"Selected\", 3, \"change\", \"checked\", \"disabled\", \"value\"], [\"color\", \"primary\", 1, \"selectable-box-icon\"]],\n      template: function OnemrvaMatSelectableBoxComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵelementStart(0, \"mat-card\", 1);\n          i0.ɵɵlistener(\"click\", function OnemrvaMatSelectableBoxComponent_Template_mat_card_click_0_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.triggerRadioClick());\n          });\n          i0.ɵɵelementStart(1, \"mat-card-title\", 2)(2, \"mat-radio-button\", 3, 0);\n          i0.ɵɵlistener(\"change\", function OnemrvaMatSelectableBoxComponent_Template_mat_radio_button_change_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.valueChanged($event));\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"mat-label\");\n          i0.ɵɵprojection(5);\n          i0.ɵɵelementStart(6, \"mat-icon\", 4);\n          i0.ɵɵprojection(7, 1);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵprojection(8, 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"mat-card-content\");\n          i0.ɵɵprojection(10, 3);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"primary\", !ctx.isChecked())(\"selected\", ctx.isChecked());\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"primary\", ctx.mode === \"primary\")(\"white\", ctx.mode === \"white\")(\"selected\", ctx.isChecked());\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"checked\", ctx.checked)(\"disabled\", ctx.disabled)(\"value\", ctx.value);\n          i0.ɵɵadvance(2);\n          i0.ɵɵclassProp(\"disabled\", ctx.disabled);\n          i0.ɵɵadvance(5);\n          i0.ɵɵclassProp(\"primary\", ctx.mode === \"primary\")(\"white\", ctx.mode === \"white\")(\"selected\", ctx.isChecked());\n        }\n      },\n      dependencies: [MatCardTitle, MatCard, MatRadioButton, MatLabel, MatIcon, MatCardContent],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatSelectableBoxComponent, [{\n    type: Component,\n    args: [{\n      selector: 'onemrva-mat-selectable-box',\n      standalone: true,\n      imports: [MatCardTitle, MatCard, MatRadioButton, MatLabel, MatIcon, MatCardContent],\n      template: \"<mat-card\\n  class=\\\"m onemrva-shadow-highlight\\\"\\n  [class.primary]=\\\"!isChecked()\\\"\\n  [class.selected]=\\\"isChecked()\\\"\\n  (click)=\\\"triggerRadioClick()\\\"\\n>\\n  <mat-card-title\\n    class=\\\"selectablebox-title\\\"\\n    [class.primary]=\\\"mode === 'primary'\\\"\\n    [class.white]=\\\"mode === 'white'\\\"\\n    [class.selected]=\\\"isChecked()\\\"\\n  >\\n    <mat-radio-button\\n      #radioButton\\n      (change)=\\\"valueChanged($event)\\\"\\n      aria-label=\\\"Selected\\\"\\n      [checked]=\\\"checked\\\"\\n      [disabled]=\\\"disabled\\\"\\n      [value]=\\\"value\\\"\\n    >\\n    </mat-radio-button>\\n\\n    <mat-label [class.disabled]=\\\"disabled\\\">\\n      <ng-content select=\\\"[title]\\\"></ng-content>\\n      <mat-icon class=\\\"selectable-box-icon\\\" color=\\\"primary\\\">\\n        <ng-content select=\\\"[icon]\\\"></ng-content>\\n      </mat-icon>\\n    </mat-label>\\n\\n    <ng-content select=\\\"[sticker]\\\"></ng-content>\\n  </mat-card-title>\\n\\n  <mat-card-content\\n    [class.primary]=\\\"mode === 'primary'\\\"\\n    [class.white]=\\\"mode === 'white'\\\"\\n    [class.selected]=\\\"isChecked()\\\"\\n  >\\n    <ng-content></ng-content>\\n  </mat-card-content>\\n</mat-card>\\n\"\n    }]\n  }], () => [{\n    type: i0.ChangeDetectorRef\n  }], {\n    radioButton: [{\n      type: ViewChild,\n      args: ['radioButton']\n    }],\n    value: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input\n    }],\n    mode: [{\n      type: Input\n    }],\n    checked: [{\n      type: Input\n    }],\n    change: [{\n      type: Output\n    }]\n  });\n})();\nclass OnemrvaMatSelectableBoxModule {\n  static {\n    this.ɵfac = function OnemrvaMatSelectableBoxModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMatSelectableBoxModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaMatSelectableBoxModule,\n      imports: [OnemrvaMatSelectableBoxComponent],\n      exports: [OnemrvaMatSelectableBoxComponent]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [OnemrvaMatSelectableBoxComponent]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMatSelectableBoxModule, [{\n    type: NgModule,\n    args: [{\n      declarations: [],\n      imports: [OnemrvaMatSelectableBoxComponent],\n      exports: [OnemrvaMatSelectableBoxComponent]\n    }]\n  }], null, null);\n})();\n\n/*\n * Public API Surface of mat-selectable-box\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { OnemrvaMatSelectableBoxComponent, OnemrvaMatSelectableBoxModule };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,MAAM,CAAC,aAAa;AAC1B,IAAM,MAAM,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,QAAQ,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,WAAW,EAAE,CAAC,GAAG,GAAG;AAChF,IAAM,MAAM,CAAC,WAAW,UAAU,aAAa,GAAG;AAClD,IAAM,mCAAN,MAAM,kCAAiC;AAAA,EACrC,aAAa,OAAO;AAClB,SAAK,OAAO,KAAK,KAAK;AAAA,EACxB;AAAA,EACA,YAAY,mBAAmB;AAC7B,SAAK,oBAAoB;AACzB,SAAK,QAAQ;AACb,SAAK,WAAW;AAChB,SAAK,OAAO;AACZ,SAAK,UAAU;AAEf,SAAK,SAAS,IAAI,aAAa;AAAA,EACjC;AAAA,EACA,kBAAkB;AAChB,SAAK,kBAAkB,cAAc;AAAA,EACvC;AAAA,EACA,YAAY;AACV,WAAO,KAAK,aAAa,cAAc,cAAc,WAAW,KAAK;AAAA,EACvE;AAAA,EACA,oBAAoB;AAClB,QAAI,KAAK,eAAe,CAAC,KAAK,YAAY,WAAW,CAAC,KAAK,YAAY,UAAU;AAI/E,WAAK,YAAY,cAAc,cAAc,cAAc,IAAI,WAAW,SAAS;AAAA,QACjF,SAAS;AAAA,MACX,CAAC,CAAC;AAAA,IACJ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,yCAAyC,mBAAmB;AAC/E,aAAO,KAAK,qBAAqB,mCAAqC,kBAAqB,iBAAiB,CAAC;AAAA,IAC/G;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,4BAA4B,CAAC;AAAA,MAC1C,WAAW,SAAS,uCAAuC,IAAI,KAAK;AAClE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,KAAK,CAAC;AAAA,QACvB;AACA,YAAI,KAAK,GAAG;AACV,cAAI;AACJ,UAAG,eAAe,KAAQ,YAAY,CAAC,MAAM,IAAI,cAAc,GAAG;AAAA,QACpE;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,QACP,UAAU;AAAA,QACV,MAAM;AAAA,QACN,SAAS;AAAA,MACX;AAAA,MACA,SAAS;AAAA,QACP,QAAQ;AAAA,MACV;AAAA,MACA,oBAAoB;AAAA,MACpB,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,eAAe,EAAE,GAAG,CAAC,GAAG,KAAK,4BAA4B,GAAG,OAAO,GAAG,CAAC,GAAG,qBAAqB,GAAG,CAAC,cAAc,YAAY,GAAG,UAAU,WAAW,YAAY,OAAO,GAAG,CAAC,SAAS,WAAW,GAAG,qBAAqB,CAAC;AAAA,MACnO,UAAU,SAAS,0CAA0C,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,gBAAM,MAAS,iBAAiB;AAChC,UAAG,gBAAgB,GAAG;AACtB,UAAG,eAAe,GAAG,YAAY,CAAC;AAClC,UAAG,WAAW,SAAS,SAAS,sEAAsE;AACpG,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,kBAAkB,CAAC;AAAA,UAC/C,CAAC;AACD,UAAG,eAAe,GAAG,kBAAkB,CAAC,EAAE,GAAG,oBAAoB,GAAG,CAAC;AACrE,UAAG,WAAW,UAAU,SAAS,6EAA6E,QAAQ;AACpH,YAAG,cAAc,GAAG;AACpB,mBAAU,YAAY,IAAI,aAAa,MAAM,CAAC;AAAA,UAChD,CAAC;AACD,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,WAAW;AAChC,UAAG,aAAa,CAAC;AACjB,UAAG,eAAe,GAAG,YAAY,CAAC;AAClC,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa,EAAE;AAClB,UAAG,aAAa,GAAG,CAAC;AACpB,UAAG,aAAa;AAChB,UAAG,eAAe,GAAG,kBAAkB;AACvC,UAAG,aAAa,IAAI,CAAC;AACrB,UAAG,aAAa,EAAE;AAAA,QACpB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,WAAW,CAAC,IAAI,UAAU,CAAC,EAAE,YAAY,IAAI,UAAU,CAAC;AACvE,UAAG,UAAU;AACb,UAAG,YAAY,WAAW,IAAI,SAAS,SAAS,EAAE,SAAS,IAAI,SAAS,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;AAC5G,UAAG,UAAU;AACb,UAAG,WAAW,WAAW,IAAI,OAAO,EAAE,YAAY,IAAI,QAAQ,EAAE,SAAS,IAAI,KAAK;AAClF,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,YAAY,IAAI,QAAQ;AACvC,UAAG,UAAU,CAAC;AACd,UAAG,YAAY,WAAW,IAAI,SAAS,SAAS,EAAE,SAAS,IAAI,SAAS,OAAO,EAAE,YAAY,IAAI,UAAU,CAAC;AAAA,QAC9G;AAAA,MACF;AAAA,MACA,cAAc,CAAC,cAAc,SAAS,gBAAgB,UAAU,SAAS,cAAc;AAAA,MACvF,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,kCAAkC,CAAC;AAAA,IACzG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,cAAc,SAAS,gBAAgB,UAAU,SAAS,cAAc;AAAA,MAClF,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,MACN,MAAM,CAAC,aAAa;AAAA,IACtB,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,IACR,CAAC;AAAA,IACD,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,IACR,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,IACR,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,gCAAN,MAAM,+BAA8B;AAAA,EAClC,OAAO;AACL,SAAK,OAAO,SAAS,sCAAsC,mBAAmB;AAC5E,aAAO,KAAK,qBAAqB,gCAA+B;AAAA,IAClE;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,gCAAgC;AAAA,MAC1C,SAAS,CAAC,gCAAgC;AAAA,IAC5C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,SAAS,CAAC,gCAAgC;AAAA,IAC5C,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,+BAA+B,CAAC;AAAA,IACtG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,cAAc,CAAC;AAAA,MACf,SAAS,CAAC,gCAAgC;AAAA,MAC1C,SAAS,CAAC,gCAAgC;AAAA,IAC5C,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;", "names": []}