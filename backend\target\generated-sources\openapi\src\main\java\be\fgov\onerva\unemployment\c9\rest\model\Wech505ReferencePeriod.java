/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.NatureOfDayType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505ReferencePeriod
 */
@JsonPropertyOrder({
  Wech505ReferencePeriod.JSON_PROPERTY_REF_STARTING_DATE,
  Wech505ReferencePeriod.JSON_PROPERTY_REF_ENDING_DATE,
  Wech505ReferencePeriod.JSON_PROPERTY_NATURE_OF_DAY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505ReferencePeriod {
  public static final String JSON_PROPERTY_REF_STARTING_DATE = "refStartingDate";
  private LocalDate refStartingDate;

  public static final String JSON_PROPERTY_REF_ENDING_DATE = "refEndingDate";
  private LocalDate refEndingDate;

  public static final String JSON_PROPERTY_NATURE_OF_DAY = "natureOfDay";
  private List<NatureOfDayType> natureOfDay = new ArrayList<>();

  public Wech505ReferencePeriod() {
  }

  public Wech505ReferencePeriod refStartingDate(LocalDate refStartingDate) {
    
    this.refStartingDate = refStartingDate;
    return this;
  }

  /**
   * Get refStartingDate
   * @return refStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getRefStartingDate() {
    return refStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_REF_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefStartingDate(LocalDate refStartingDate) {
    this.refStartingDate = refStartingDate;
  }

  public Wech505ReferencePeriod refEndingDate(LocalDate refEndingDate) {
    
    this.refEndingDate = refEndingDate;
    return this;
  }

  /**
   * Get refEndingDate
   * @return refEndingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getRefEndingDate() {
    return refEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_REF_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefEndingDate(LocalDate refEndingDate) {
    this.refEndingDate = refEndingDate;
  }

  public Wech505ReferencePeriod natureOfDay(List<NatureOfDayType> natureOfDay) {
    
    this.natureOfDay = natureOfDay;
    return this;
  }

  public Wech505ReferencePeriod addNatureOfDayItem(NatureOfDayType natureOfDayItem) {
    if (this.natureOfDay == null) {
      this.natureOfDay = new ArrayList<>();
    }
    this.natureOfDay.add(natureOfDayItem);
    return this;
  }

  /**
   * Get natureOfDay
   * @return natureOfDay
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURE_OF_DAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<NatureOfDayType> getNatureOfDay() {
    return natureOfDay;
  }


  @JsonProperty(JSON_PROPERTY_NATURE_OF_DAY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNatureOfDay(List<NatureOfDayType> natureOfDay) {
    this.natureOfDay = natureOfDay;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505ReferencePeriod wech505ReferencePeriod = (Wech505ReferencePeriod) o;
    return Objects.equals(this.refStartingDate, wech505ReferencePeriod.refStartingDate) &&
        Objects.equals(this.refEndingDate, wech505ReferencePeriod.refEndingDate) &&
        Objects.equals(this.natureOfDay, wech505ReferencePeriod.natureOfDay);
  }

  @Override
  public int hashCode() {
    return Objects.hash(refStartingDate, refEndingDate, natureOfDay);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505ReferencePeriod {\n");
    sb.append("    refStartingDate: ").append(toIndentedString(refStartingDate)).append("\n");
    sb.append("    refEndingDate: ").append(toIndentedString(refEndingDate)).append("\n");
    sb.append("    natureOfDay: ").append(toIndentedString(natureOfDay)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

