package be.fgov.onerva.cu.backend.application.port.out

import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import java.util.*

/**
 * Port interface for creating tasks related to change of address requests.
 *
 * This port defines the contract for creating tasks in an external task management system
 * (e.g., Wave) when processing change of address requests.
 */
interface WaveTaskPort {
    companion object {
        const val CHANGE_PERSONAL_DATA_CAPTURE = "CHANGE_PERSONAL_DATA_CAPTURE"
        const val VALIDATION_DATA = "VALIDATION_DATA"
    }

    /**
     * Creates a new task for a change of address request.
     *
     * @param requestId The unique identifier of the change of address request
     * @param changePersonalDataPersistCommand The change of address details needed for task creation
     */
    fun createChangePersonalDataTask(
        requestId: UUID,
        createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
    ): WaveTask

    fun closeTask(taskId: String): Boolean

    /**
     * Set the task to sleep while waiting for a decision.
     */
    fun sleepTask(taskId: String): Boolean

    fun createChangePersonalDataValidateTask(
        requestId: UUID,
        processId: String,
        assignee: String,
        createChangePersonalDataTaskCommand: CreateChangePersonalDataTaskCommand,
    ): WaveTask

    fun awakeTask(
        taskId: String,
        reason: String
    )

    fun assignTaskToUser(taskId: String, user: String)

    fun updateChangePersonalDataTaskDecision(
        processId: String,
        taskId: String,
        updateChangePersonalDataTaskDecisionCommand: UpdateChangePersonalDataDecisionCommand,
    )

    fun closeProcess(processId: String)
}