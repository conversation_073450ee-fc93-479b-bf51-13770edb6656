/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.AddressNullable

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param firstName The first name of the person
 * @param lastName The last name of the person
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param nationality The nationality of the employee
 * @param address 
 * @param valueDate The value date for the authentic source
 */


data class HistoricalCitizenAuthenticSourcesResponse (

    /* The first name of the person */
    @get:JsonProperty("firstName")
    val firstName: kotlin.String? = null,

    /* The last name of the person */
    @get:Json<PERSON>roperty("lastName")
    val lastName: kotlin.String? = null,

    /* The birth date of the employee (format YYYY-MM-DD) */
    @get:JsonProperty("birthDate")
    val birthDate: java.time.LocalDate? = null,

    /* The nationality of the employee */
    @get:JsonProperty("nationality")
    val nationality: kotlin.String? = null,

    @get:JsonProperty("address")
    val address: AddressNullable? = null,

    /* The value date for the authentic source */
    @get:JsonProperty("valueDate")
    val valueDate: java.time.LocalDate? = null

) {


}

