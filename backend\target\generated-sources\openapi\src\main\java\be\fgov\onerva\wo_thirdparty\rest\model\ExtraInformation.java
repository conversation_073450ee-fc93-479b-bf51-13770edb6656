/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Generic additional information on the party
 */
@JsonPropertyOrder({
  ExtraInformation.JSON_PROPERTY_INFO_TYPE,
  ExtraInformation.JSON_PROPERTY_DESCRIPTION,
  ExtraInformation.JSON_PROPERTY_VALUE,
  ExtraInformation.JSON_PROPERTY_DISPLAY_VALUE,
  ExtraInformation.JSON_PROPERTY_DISPLAY_VALUE_INFO
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ExtraInformation {
  public static final String JSON_PROPERTY_INFO_TYPE = "infoType";
  private String infoType;

  public static final String JSON_PROPERTY_DESCRIPTION = "description";
  private String description;

  public static final String JSON_PROPERTY_VALUE = "value";
  private String value;

  public static final String JSON_PROPERTY_DISPLAY_VALUE = "displayValue";
  private String displayValue;

  public static final String JSON_PROPERTY_DISPLAY_VALUE_INFO = "displayValueInfo";
  private String displayValueInfo;

  public ExtraInformation() {
  }

  public ExtraInformation infoType(String infoType) {
    
    this.infoType = infoType;
    return this;
  }

  /**
   * The unique code for the provided information
   * @return infoType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INFO_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInfoType() {
    return infoType;
  }


  @JsonProperty(JSON_PROPERTY_INFO_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInfoType(String infoType) {
    this.infoType = infoType;
  }

  public ExtraInformation description(String description) {
    
    this.description = description;
    return this;
  }

  /**
   * The translated description of the info type
   * @return description
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESCRIPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescription() {
    return description;
  }


  @JsonProperty(JSON_PROPERTY_DESCRIPTION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescription(String description) {
    this.description = description;
  }

  public ExtraInformation value(String value) {
    
    this.value = value;
    return this;
  }

  /**
   * The actual value of the information
   * @return value
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getValue() {
    return value;
  }


  @JsonProperty(JSON_PROPERTY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setValue(String value) {
    this.value = value;
  }

  public ExtraInformation displayValue(String displayValue) {
    
    this.displayValue = displayValue;
    return this;
  }

  /**
   * The value to be displayed (translated &amp; formatted)
   * @return displayValue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDisplayValue() {
    return displayValue;
  }


  @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDisplayValue(String displayValue) {
    this.displayValue = displayValue;
  }

  public ExtraInformation displayValueInfo(String displayValueInfo) {
    
    this.displayValueInfo = displayValueInfo;
    return this;
  }

  /**
   * Additional information concerning the value (translated &amp; formatted)
   * @return displayValueInfo
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDisplayValueInfo() {
    return displayValueInfo;
  }


  @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE_INFO)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDisplayValueInfo(String displayValueInfo) {
    this.displayValueInfo = displayValueInfo;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ExtraInformation extraInformation = (ExtraInformation) o;
    return Objects.equals(this.infoType, extraInformation.infoType) &&
        Objects.equals(this.description, extraInformation.description) &&
        Objects.equals(this.value, extraInformation.value) &&
        Objects.equals(this.displayValue, extraInformation.displayValue) &&
        Objects.equals(this.displayValueInfo, extraInformation.displayValueInfo);
  }

  @Override
  public int hashCode() {
    return Objects.hash(infoType, description, value, displayValue, displayValueInfo);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ExtraInformation {\n");
    sb.append("    infoType: ").append(toIndentedString(infoType)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("    value: ").append(toIndentedString(value)).append("\n");
    sb.append("    displayValue: ").append(toIndentedString(displayValue)).append("\n");
    sb.append("    displayValueInfo: ").append(toIndentedString(displayValueInfo)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

