/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.ExternalSource

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param fieldName The name of the field this source applies to
 * @param source 
 */


data class FieldSource (

    /* The name of the field this source applies to */
    @get:JsonProperty("fieldName")
    val fieldName: kotlin.String? = null,

    @get:JsonProperty("source")
    val source: ExternalSource? = null

) {


}

