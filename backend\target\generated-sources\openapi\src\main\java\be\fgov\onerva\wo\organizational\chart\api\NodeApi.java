package be.fgov.onerva.wo.organizational.chart.api;

import be.fgov.onerva.wo.organizational.chart.invoker.ApiClient;
import be.fgov.onerva.wo.organizational.chart.invoker.BaseApi;

import be.fgov.onerva.wo.organizational.chart.rest.model.InvalidParamProblem;
import be.fgov.onerva.wo.organizational.chart.rest.model.Node;
import be.fgov.onerva.wo.organizational.chart.rest.model.Nodes;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NodeApi extends BaseApi {

    public NodeApi() {
        super(new ApiClient());
    }

    public NodeApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * Create a node
     * This method allows to create a node within the Organizational Chart. To create a node you have to provide the following information:  * The type  * The nature (if nature is group)  * The parent node(s) (memberOf)  * The name
     * <p><b>201</b> - Created
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * <p><b>509</b> - A group with the same name already exists in the Organizational Chart
     * @param body Data for the node to be created (optional)
     * @return Node
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Node createNode(Node body) throws RestClientException {
        return createNodeWithHttpInfo(body).getBody();
    }

    /**
     * Create a node
     * This method allows to create a node within the Organizational Chart. To create a node you have to provide the following information:  * The type  * The nature (if nature is group)  * The parent node(s) (memberOf)  * The name
     * <p><b>201</b> - Created
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * <p><b>509</b> - A group with the same name already exists in the Organizational Chart
     * @param body Data for the node to be created (optional)
     * @return ResponseEntity&lt;Node&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Node> createNodeWithHttpInfo(Node body) throws RestClientException {
        Object localVarPostBody = body;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json;charset=utf-8"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Node> localReturnType = new ParameterizedTypeReference<Node>() {};
        return apiClient.invokeAPI("/nodes", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Update some data of a node
     * Partially update the details of a given node. This method allows the soft delete of a node
     * <p><b>200</b> - OK
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @param body The node data to update (optional)
     * @return Node
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Node patchNode(String nodeId, Node body) throws RestClientException {
        return patchNodeWithHttpInfo(nodeId, body).getBody();
    }

    /**
     * Update some data of a node
     * Partially update the details of a given node. This method allows the soft delete of a node
     * <p><b>200</b> - OK
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @param body The node data to update (optional)
     * @return ResponseEntity&lt;Node&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Node> patchNodeWithHttpInfo(String nodeId, Node body) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'nodeId' is set
        if (nodeId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'nodeId' when calling patchNode");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("nodeId", nodeId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json;charset=utf-8"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Node> localReturnType = new ParameterizedTypeReference<Node>() {};
        return apiClient.invokeAPI("/nodes/{nodeId}", HttpMethod.PATCH, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Retrieve a given node
     * Retrieve the details of the node with the given identifier.
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @return Node
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Node retrieveNode(String nodeId) throws RestClientException {
        return retrieveNodeWithHttpInfo(nodeId).getBody();
    }

    /**
     * Retrieve a given node
     * Retrieve the details of the node with the given identifier.
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @return ResponseEntity&lt;Node&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Node> retrieveNodeWithHttpInfo(String nodeId) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'nodeId' is set
        if (nodeId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'nodeId' when calling retrieveNode");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("nodeId", nodeId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Node> localReturnType = new ParameterizedTypeReference<Node>() {};
        return apiClient.invokeAPI("/nodes/{nodeId}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Search nodes of the Organizational Chart matching specific criteria.
     * Search nodes of the Organizational Chart matching specific criteria. Some example of criteria are business identifier (SSIN, agent number for example) or the nodeId(s) that you&#39;re seeking information for or even the nodeId of the parent node(s) (memeberOfGroup) you want. You must always specify a searchType but other parameters are optionnal.
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param searchType Search criterium that determines the type of search query. The full search type will show all the available information of the node, the summary search type will only show essential information about the node. (required)
     * @param nodeId Search criterium on the nodeId(s) identifier(s) (max 100). A nodeId is a unique node identifier. (optional)
     * @param name Search criterium on the name of the node. This can be the name of an employee or the name of a department (in dutch or french). (optional)
     * @param businessIdentifier Search criterium on the business identifier(s) of the node. A business identifier is specific information about the node, this can be a identifier for the department, an agent number of an employee or a SSIN of an employee. (optional)
     * @param attribution Search criterium on the attributions of the node. (optional)
     * @param attributionEnable Determine if we return the  attributions of the node or not. (optional, default to false)
     * @param type Search criterium on type of the node (employee or group). (optional)
     * @param technicalStatus Search criterium on the technical status of the node (open, closed, deleted or obsolete). (optional)
     * @param memberOfGroup Search criterium for nodes being part of the given group(s). The search is made by the group nodeId. (optional)
     * @param rootNode Search query to return a node with or without a parent node. The value as false will return nodes with parent(s) nodes, true will return nodes without a parent. Return both by default. (optional)
     * @param depth The value of the node and it&#39;s parent node(s) in the Organizational Chart have to be returned here. For instance, if &#39;depth&#39; is &#39;1&#39;, then the value of the immediate superior node (parent node) will be returned along with the value of the given node. Maximum value is 4. If asked for more depth than available, only available depth is given. (optional, default to 0)
     * @param page Page number to be returned. Minimum is 1. (optional, default to 1)
     * @param pageSize Page size used for pagination. (optional, default to 20)
     * @param linkStatus Search criterium on the technical status of the node&#39;s links to other nodes   (open or closed). (optional)
     * @return Nodes
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Nodes searchNode(String searchType, List<String> nodeId, String name, List<String> businessIdentifier, List<String> attribution, Boolean attributionEnable, String type, List<String> technicalStatus, List<String> memberOfGroup, Boolean rootNode, Integer depth, Integer page, Integer pageSize, List<String> linkStatus) throws RestClientException {
        return searchNodeWithHttpInfo(searchType, nodeId, name, businessIdentifier, attribution, attributionEnable, type, technicalStatus, memberOfGroup, rootNode, depth, page, pageSize, linkStatus).getBody();
    }

    /**
     * Search nodes of the Organizational Chart matching specific criteria.
     * Search nodes of the Organizational Chart matching specific criteria. Some example of criteria are business identifier (SSIN, agent number for example) or the nodeId(s) that you&#39;re seeking information for or even the nodeId of the parent node(s) (memeberOfGroup) you want. You must always specify a searchType but other parameters are optionnal.
     * <p><b>200</b> - OK
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param searchType Search criterium that determines the type of search query. The full search type will show all the available information of the node, the summary search type will only show essential information about the node. (required)
     * @param nodeId Search criterium on the nodeId(s) identifier(s) (max 100). A nodeId is a unique node identifier. (optional)
     * @param name Search criterium on the name of the node. This can be the name of an employee or the name of a department (in dutch or french). (optional)
     * @param businessIdentifier Search criterium on the business identifier(s) of the node. A business identifier is specific information about the node, this can be a identifier for the department, an agent number of an employee or a SSIN of an employee. (optional)
     * @param attribution Search criterium on the attributions of the node. (optional)
     * @param attributionEnable Determine if we return the  attributions of the node or not. (optional, default to false)
     * @param type Search criterium on type of the node (employee or group). (optional)
     * @param technicalStatus Search criterium on the technical status of the node (open, closed, deleted or obsolete). (optional)
     * @param memberOfGroup Search criterium for nodes being part of the given group(s). The search is made by the group nodeId. (optional)
     * @param rootNode Search query to return a node with or without a parent node. The value as false will return nodes with parent(s) nodes, true will return nodes without a parent. Return both by default. (optional)
     * @param depth The value of the node and it&#39;s parent node(s) in the Organizational Chart have to be returned here. For instance, if &#39;depth&#39; is &#39;1&#39;, then the value of the immediate superior node (parent node) will be returned along with the value of the given node. Maximum value is 4. If asked for more depth than available, only available depth is given. (optional, default to 0)
     * @param page Page number to be returned. Minimum is 1. (optional, default to 1)
     * @param pageSize Page size used for pagination. (optional, default to 20)
     * @param linkStatus Search criterium on the technical status of the node&#39;s links to other nodes   (open or closed). (optional)
     * @return ResponseEntity&lt;Nodes&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Nodes> searchNodeWithHttpInfo(String searchType, List<String> nodeId, String name, List<String> businessIdentifier, List<String> attribution, Boolean attributionEnable, String type, List<String> technicalStatus, List<String> memberOfGroup, Boolean rootNode, Integer depth, Integer page, Integer pageSize, List<String> linkStatus) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'searchType' is set
        if (searchType == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'searchType' when calling searchNode");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "searchType", searchType));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "nodeId", nodeId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "name", name));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "businessIdentifier", businessIdentifier));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "attribution", attribution));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "attribution_enable", attributionEnable));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "type", type));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "technicalStatus", technicalStatus));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "memberOfGroup", memberOfGroup));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "rootNode", rootNode));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "depth", depth));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "page", page));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "linkStatus", linkStatus));
        

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Nodes> localReturnType = new ParameterizedTypeReference<Nodes>() {};
        return apiClient.invokeAPI("/nodes", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * Update a given node
     * Update the details of a given node
     * <p><b>200</b> - OK
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @param body The full body of the node to update (optional)
     * @return Node
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public Node updateNode(String nodeId, Node body) throws RestClientException {
        return updateNodeWithHttpInfo(nodeId, body).getBody();
    }

    /**
     * Update a given node
     * Update the details of a given node
     * <p><b>200</b> - OK
     * <p><b>204</b> - No Content
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>409</b> - Conflict
     * <p><b>500</b> - Unexpected error
     * <p><b>503</b> - Service unavailable
     * @param nodeId The node identifier (required)
     * @param body The full body of the node to update (optional)
     * @return ResponseEntity&lt;Node&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Node> updateNodeWithHttpInfo(String nodeId, Node body) throws RestClientException {
        Object localVarPostBody = body;
        
        // verify the required parameter 'nodeId' is set
        if (nodeId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'nodeId' when calling updateNode");
        }
        
        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("nodeId", nodeId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json;charset=utf-8"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<Node> localReturnType = new ParameterizedTypeReference<Node>() {};
        return apiClient.invokeAPI("/nodes/{nodeId}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json;charset=utf-8", "application/problem+json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = { 
            "application/json;charset=utf-8"
         };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
