/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CitizenInfoV2DTO
 * @deprecated
 */
@Deprecated
@JsonPropertyOrder({
  CitizenInfoV2DTO.JSON_PROPERTY_DATE_MCPTE,
  CitizenInfoV2DTO.JSON_PROPERTY_COMMUNE_DATE_VALID,
  CitizenInfoV2DTO.JSON_PROPERTY_NATION_DATE_VALID,
  CitizenInfoV2DTO.JSON_PROPERTY_NATION_BCSS,
  CitizenInfoV2DTO.JSON_PROPERTY_ID,
  CitizenInfoV2DTO.JSON_PROPERTY_SSIN,
  CitizenInfoV2DTO.JSON_PROPERTY_NUM_PENS,
  CitizenInfoV2DTO.JSON_PROPERTY_LAST_NAME,
  CitizenInfoV2DTO.JSON_PROPERTY_FIRST_NAME,
  CitizenInfoV2DTO.JSON_PROPERTY_ADDRESS,
  CitizenInfoV2DTO.JSON_PROPERTY_POSTAL_CODE,
  CitizenInfoV2DTO.JSON_PROPERTY_RVA_COUNTRY_CODE,
  CitizenInfoV2DTO.JSON_PROPERTY_NUM_BOX,
  CitizenInfoV2DTO.JSON_PROPERTY_O_P,
  CitizenInfoV2DTO.JSON_PROPERTY_UNEMPLOYMENT_OFFICE,
  CitizenInfoV2DTO.JSON_PROPERTY_IBAN,
  CitizenInfoV2DTO.JSON_PROPERTY_LANGUAGE,
  CitizenInfoV2DTO.JSON_PROPERTY_SEX,
  CitizenInfoV2DTO.JSON_PROPERTY_FLAG_PURGE,
  CitizenInfoV2DTO.JSON_PROPERTY_FLAG_NATION,
  CitizenInfoV2DTO.JSON_PROPERTY_FLAG_V_CPTE,
  CitizenInfoV2DTO.JSON_PROPERTY_EMAIL,
  CitizenInfoV2DTO.JSON_PROPERTY_TELEPHONE_ONEM,
  CitizenInfoV2DTO.JSON_PROPERTY_GSM_ONEM,
  CitizenInfoV2DTO.JSON_PROPERTY_TELEPHONE_REG,
  CitizenInfoV2DTO.JSON_PROPERTY_GSM_REG,
  CitizenInfoV2DTO.JSON_PROPERTY_DECEASED_DATE,
  CitizenInfoV2DTO.JSON_PROPERTY_BIS_NUMBER
})
@JsonTypeName("CitizenInfoV2")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenInfoV2DTO {
  public static final String JSON_PROPERTY_DATE_MCPTE = "dateMcpte";
  private LocalDate dateMcpte;

  public static final String JSON_PROPERTY_COMMUNE_DATE_VALID = "communeDateValid";
  private LocalDate communeDateValid;

  public static final String JSON_PROPERTY_NATION_DATE_VALID = "nationDateValid";
  private LocalDate nationDateValid;

  public static final String JSON_PROPERTY_NATION_BCSS = "nationBcss";
  private BigDecimal nationBcss;

  public static final String JSON_PROPERTY_ID = "id";
  private BigDecimal id;

  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_NUM_PENS = "numPens";
  private BigDecimal numPens;

  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public static final String JSON_PROPERTY_ADDRESS = "address";
  private String address;

  public static final String JSON_PROPERTY_POSTAL_CODE = "postalCode";
  private String postalCode;

  public static final String JSON_PROPERTY_RVA_COUNTRY_CODE = "rvaCountryCode";
  private Integer rvaCountryCode;

  public static final String JSON_PROPERTY_NUM_BOX = "numBox";
  private BigDecimal numBox;

  public static final String JSON_PROPERTY_O_P = "OP";
  private BigDecimal OP;

  public static final String JSON_PROPERTY_UNEMPLOYMENT_OFFICE = "unemploymentOffice";
  private BigDecimal unemploymentOffice;

  public static final String JSON_PROPERTY_IBAN = "iban";
  private String iban;

  public static final String JSON_PROPERTY_LANGUAGE = "language";
  private String language;

  public static final String JSON_PROPERTY_SEX = "sex";
  private String sex;

  public static final String JSON_PROPERTY_FLAG_PURGE = "flagPurge";
  private String flagPurge;

  public static final String JSON_PROPERTY_FLAG_NATION = "flagNation";
  private BigDecimal flagNation;

  public static final String JSON_PROPERTY_FLAG_V_CPTE = "flagVCpte";
  private BigDecimal flagVCpte;

  public static final String JSON_PROPERTY_EMAIL = "email";
  private String email;

  public static final String JSON_PROPERTY_TELEPHONE_ONEM = "telephoneOnem";
  private String telephoneOnem;

  public static final String JSON_PROPERTY_GSM_ONEM = "gsmOnem";
  private String gsmOnem;

  public static final String JSON_PROPERTY_TELEPHONE_REG = "telephoneReg";
  private String telephoneReg;

  public static final String JSON_PROPERTY_GSM_REG = "gsmReg";
  private String gsmReg;

  public static final String JSON_PROPERTY_DECEASED_DATE = "deceasedDate";
  private LocalDate deceasedDate;

  public static final String JSON_PROPERTY_BIS_NUMBER = "bisNumber";
  private List<String> bisNumber;

  public CitizenInfoV2DTO() {
  }

  public CitizenInfoV2DTO dateMcpte(LocalDate dateMcpte) {
    
    this.dateMcpte = dateMcpte;
    return this;
  }

  /**
   * Get dateMcpte
   * @return dateMcpte
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_MCPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateMcpte() {
    return dateMcpte;
  }


  @JsonProperty(JSON_PROPERTY_DATE_MCPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateMcpte(LocalDate dateMcpte) {
    this.dateMcpte = dateMcpte;
  }

  public CitizenInfoV2DTO communeDateValid(LocalDate communeDateValid) {
    
    this.communeDateValid = communeDateValid;
    return this;
  }

  /**
   * Get communeDateValid
   * @return communeDateValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMUNE_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getCommuneDateValid() {
    return communeDateValid;
  }


  @JsonProperty(JSON_PROPERTY_COMMUNE_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCommuneDateValid(LocalDate communeDateValid) {
    this.communeDateValid = communeDateValid;
  }

  public CitizenInfoV2DTO nationDateValid(LocalDate nationDateValid) {
    
    this.nationDateValid = nationDateValid;
    return this;
  }

  /**
   * Get nationDateValid
   * @return nationDateValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATION_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getNationDateValid() {
    return nationDateValid;
  }


  @JsonProperty(JSON_PROPERTY_NATION_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationDateValid(LocalDate nationDateValid) {
    this.nationDateValid = nationDateValid;
  }

  public CitizenInfoV2DTO nationBcss(BigDecimal nationBcss) {
    
    this.nationBcss = nationBcss;
    return this;
  }

  /**
   * Get nationBcss
   * @return nationBcss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATION_BCSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNationBcss() {
    return nationBcss;
  }


  @JsonProperty(JSON_PROPERTY_NATION_BCSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNationBcss(BigDecimal nationBcss) {
    this.nationBcss = nationBcss;
  }

  public CitizenInfoV2DTO id(BigDecimal id) {
    
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(BigDecimal id) {
    this.id = id;
  }

  public CitizenInfoV2DTO ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public CitizenInfoV2DTO numPens(BigDecimal numPens) {
    
    this.numPens = numPens;
    return this;
  }

  /**
   * Get numPens
   * @return numPens
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNumPens() {
    return numPens;
  }


  @JsonProperty(JSON_PROPERTY_NUM_PENS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumPens(BigDecimal numPens) {
    this.numPens = numPens;
  }

  public CitizenInfoV2DTO lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public CitizenInfoV2DTO firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public CitizenInfoV2DTO address(String address) {
    
    this.address = address;
    return this;
  }

  /**
   * Get address
   * @return address
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAddress() {
    return address;
  }


  @JsonProperty(JSON_PROPERTY_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAddress(String address) {
    this.address = address;
  }

  public CitizenInfoV2DTO postalCode(String postalCode) {
    
    this.postalCode = postalCode;
    return this;
  }

  /**
   * Get postalCode
   * @return postalCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPostalCode() {
    return postalCode;
  }


  @JsonProperty(JSON_PROPERTY_POSTAL_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPostalCode(String postalCode) {
    this.postalCode = postalCode;
  }

  public CitizenInfoV2DTO rvaCountryCode(Integer rvaCountryCode) {
    
    this.rvaCountryCode = rvaCountryCode;
    return this;
  }

  /**
   * Get rvaCountryCode
   * @return rvaCountryCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RVA_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getRvaCountryCode() {
    return rvaCountryCode;
  }


  @JsonProperty(JSON_PROPERTY_RVA_COUNTRY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRvaCountryCode(Integer rvaCountryCode) {
    this.rvaCountryCode = rvaCountryCode;
  }

  public CitizenInfoV2DTO numBox(BigDecimal numBox) {
    
    this.numBox = numBox;
    return this;
  }

  /**
   * Get numBox
   * @return numBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NUM_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getNumBox() {
    return numBox;
  }


  @JsonProperty(JSON_PROPERTY_NUM_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNumBox(BigDecimal numBox) {
    this.numBox = numBox;
  }

  public CitizenInfoV2DTO OP(BigDecimal OP) {
    
    this.OP = OP;
    return this;
  }

  /**
   * Get OP
   * @return OP
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_O_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getOP() {
    return OP;
  }


  @JsonProperty(JSON_PROPERTY_O_P)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOP(BigDecimal OP) {
    this.OP = OP;
  }

  public CitizenInfoV2DTO unemploymentOffice(BigDecimal unemploymentOffice) {
    
    this.unemploymentOffice = unemploymentOffice;
    return this;
  }

  /**
   * Get unemploymentOffice
   * @return unemploymentOffice
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getUnemploymentOffice() {
    return unemploymentOffice;
  }


  @JsonProperty(JSON_PROPERTY_UNEMPLOYMENT_OFFICE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUnemploymentOffice(BigDecimal unemploymentOffice) {
    this.unemploymentOffice = unemploymentOffice;
  }

  public CitizenInfoV2DTO iban(String iban) {
    
    this.iban = iban;
    return this;
  }

  /**
   * Get iban
   * @return iban
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getIban() {
    return iban;
  }


  @JsonProperty(JSON_PROPERTY_IBAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIban(String iban) {
    this.iban = iban;
  }

  public CitizenInfoV2DTO language(String language) {
    
    this.language = language;
    return this;
  }

  /**
   * Get language
   * @return language
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLanguage() {
    return language;
  }


  @JsonProperty(JSON_PROPERTY_LANGUAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLanguage(String language) {
    this.language = language;
  }

  public CitizenInfoV2DTO sex(String sex) {
    
    this.sex = sex;
    return this;
  }

  /**
   * Get sex
   * @return sex
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSex() {
    return sex;
  }


  @JsonProperty(JSON_PROPERTY_SEX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSex(String sex) {
    this.sex = sex;
  }

  public CitizenInfoV2DTO flagPurge(String flagPurge) {
    
    this.flagPurge = flagPurge;
    return this;
  }

  /**
   * Get flagPurge
   * @return flagPurge
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_PURGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFlagPurge() {
    return flagPurge;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_PURGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagPurge(String flagPurge) {
    this.flagPurge = flagPurge;
  }

  public CitizenInfoV2DTO flagNation(BigDecimal flagNation) {
    
    this.flagNation = flagNation;
    return this;
  }

  /**
   * Get flagNation
   * @return flagNation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getFlagNation() {
    return flagNation;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_NATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagNation(BigDecimal flagNation) {
    this.flagNation = flagNation;
  }

  public CitizenInfoV2DTO flagVCpte(BigDecimal flagVCpte) {
    
    this.flagVCpte = flagVCpte;
    return this;
  }

  /**
   * Get flagVCpte
   * @return flagVCpte
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLAG_V_CPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public BigDecimal getFlagVCpte() {
    return flagVCpte;
  }


  @JsonProperty(JSON_PROPERTY_FLAG_V_CPTE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlagVCpte(BigDecimal flagVCpte) {
    this.flagVCpte = flagVCpte;
  }

  public CitizenInfoV2DTO email(String email) {
    
    this.email = email;
    return this;
  }

  /**
   * Get email
   * @return email
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmail() {
    return email;
  }


  @JsonProperty(JSON_PROPERTY_EMAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmail(String email) {
    this.email = email;
  }

  public CitizenInfoV2DTO telephoneOnem(String telephoneOnem) {
    
    this.telephoneOnem = telephoneOnem;
    return this;
  }

  /**
   * Get telephoneOnem
   * @return telephoneOnem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TELEPHONE_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTelephoneOnem() {
    return telephoneOnem;
  }


  @JsonProperty(JSON_PROPERTY_TELEPHONE_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTelephoneOnem(String telephoneOnem) {
    this.telephoneOnem = telephoneOnem;
  }

  public CitizenInfoV2DTO gsmOnem(String gsmOnem) {
    
    this.gsmOnem = gsmOnem;
    return this;
  }

  /**
   * Get gsmOnem
   * @return gsmOnem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GSM_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGsmOnem() {
    return gsmOnem;
  }


  @JsonProperty(JSON_PROPERTY_GSM_ONEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGsmOnem(String gsmOnem) {
    this.gsmOnem = gsmOnem;
  }

  public CitizenInfoV2DTO telephoneReg(String telephoneReg) {
    
    this.telephoneReg = telephoneReg;
    return this;
  }

  /**
   * Get telephoneReg
   * @return telephoneReg
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TELEPHONE_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTelephoneReg() {
    return telephoneReg;
  }


  @JsonProperty(JSON_PROPERTY_TELEPHONE_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTelephoneReg(String telephoneReg) {
    this.telephoneReg = telephoneReg;
  }

  public CitizenInfoV2DTO gsmReg(String gsmReg) {
    
    this.gsmReg = gsmReg;
    return this;
  }

  /**
   * Get gsmReg
   * @return gsmReg
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_GSM_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getGsmReg() {
    return gsmReg;
  }


  @JsonProperty(JSON_PROPERTY_GSM_REG)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setGsmReg(String gsmReg) {
    this.gsmReg = gsmReg;
  }

  public CitizenInfoV2DTO deceasedDate(LocalDate deceasedDate) {
    
    this.deceasedDate = deceasedDate;
    return this;
  }

  /**
   * Get deceasedDate
   * @return deceasedDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECEASED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDeceasedDate() {
    return deceasedDate;
  }


  @JsonProperty(JSON_PROPERTY_DECEASED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeceasedDate(LocalDate deceasedDate) {
    this.deceasedDate = deceasedDate;
  }

  public CitizenInfoV2DTO bisNumber(List<String> bisNumber) {
    
    this.bisNumber = bisNumber;
    return this;
  }

  public CitizenInfoV2DTO addBisNumberItem(String bisNumberItem) {
    if (this.bisNumber == null) {
      this.bisNumber = new ArrayList<>();
    }
    this.bisNumber.add(bisNumberItem);
    return this;
  }

  /**
   * Get bisNumber
   * @return bisNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BIS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getBisNumber() {
    return bisNumber;
  }


  @JsonProperty(JSON_PROPERTY_BIS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBisNumber(List<String> bisNumber) {
    this.bisNumber = bisNumber;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CitizenInfoV2DTO citizenInfoV2 = (CitizenInfoV2DTO) o;
    return Objects.equals(this.dateMcpte, citizenInfoV2.dateMcpte) &&
        Objects.equals(this.communeDateValid, citizenInfoV2.communeDateValid) &&
        Objects.equals(this.nationDateValid, citizenInfoV2.nationDateValid) &&
        Objects.equals(this.nationBcss, citizenInfoV2.nationBcss) &&
        Objects.equals(this.id, citizenInfoV2.id) &&
        Objects.equals(this.ssin, citizenInfoV2.ssin) &&
        Objects.equals(this.numPens, citizenInfoV2.numPens) &&
        Objects.equals(this.lastName, citizenInfoV2.lastName) &&
        Objects.equals(this.firstName, citizenInfoV2.firstName) &&
        Objects.equals(this.address, citizenInfoV2.address) &&
        Objects.equals(this.postalCode, citizenInfoV2.postalCode) &&
        Objects.equals(this.rvaCountryCode, citizenInfoV2.rvaCountryCode) &&
        Objects.equals(this.numBox, citizenInfoV2.numBox) &&
        Objects.equals(this.OP, citizenInfoV2.OP) &&
        Objects.equals(this.unemploymentOffice, citizenInfoV2.unemploymentOffice) &&
        Objects.equals(this.iban, citizenInfoV2.iban) &&
        Objects.equals(this.language, citizenInfoV2.language) &&
        Objects.equals(this.sex, citizenInfoV2.sex) &&
        Objects.equals(this.flagPurge, citizenInfoV2.flagPurge) &&
        Objects.equals(this.flagNation, citizenInfoV2.flagNation) &&
        Objects.equals(this.flagVCpte, citizenInfoV2.flagVCpte) &&
        Objects.equals(this.email, citizenInfoV2.email) &&
        Objects.equals(this.telephoneOnem, citizenInfoV2.telephoneOnem) &&
        Objects.equals(this.gsmOnem, citizenInfoV2.gsmOnem) &&
        Objects.equals(this.telephoneReg, citizenInfoV2.telephoneReg) &&
        Objects.equals(this.gsmReg, citizenInfoV2.gsmReg) &&
        Objects.equals(this.deceasedDate, citizenInfoV2.deceasedDate) &&
        Objects.equals(this.bisNumber, citizenInfoV2.bisNumber);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dateMcpte, communeDateValid, nationDateValid, nationBcss, id, ssin, numPens, lastName, firstName, address, postalCode, rvaCountryCode, numBox, OP, unemploymentOffice, iban, language, sex, flagPurge, flagNation, flagVCpte, email, telephoneOnem, gsmOnem, telephoneReg, gsmReg, deceasedDate, bisNumber);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CitizenInfoV2DTO {\n");
    sb.append("    dateMcpte: ").append(toIndentedString(dateMcpte)).append("\n");
    sb.append("    communeDateValid: ").append(toIndentedString(communeDateValid)).append("\n");
    sb.append("    nationDateValid: ").append(toIndentedString(nationDateValid)).append("\n");
    sb.append("    nationBcss: ").append(toIndentedString(nationBcss)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    numPens: ").append(toIndentedString(numPens)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    address: ").append(toIndentedString(address)).append("\n");
    sb.append("    postalCode: ").append(toIndentedString(postalCode)).append("\n");
    sb.append("    rvaCountryCode: ").append(toIndentedString(rvaCountryCode)).append("\n");
    sb.append("    numBox: ").append(toIndentedString(numBox)).append("\n");
    sb.append("    OP: ").append(toIndentedString(OP)).append("\n");
    sb.append("    unemploymentOffice: ").append(toIndentedString(unemploymentOffice)).append("\n");
    sb.append("    iban: ").append(toIndentedString(iban)).append("\n");
    sb.append("    language: ").append(toIndentedString(language)).append("\n");
    sb.append("    sex: ").append(toIndentedString(sex)).append("\n");
    sb.append("    flagPurge: ").append(toIndentedString(flagPurge)).append("\n");
    sb.append("    flagNation: ").append(toIndentedString(flagNation)).append("\n");
    sb.append("    flagVCpte: ").append(toIndentedString(flagVCpte)).append("\n");
    sb.append("    email: ").append(toIndentedString(email)).append("\n");
    sb.append("    telephoneOnem: ").append(toIndentedString(telephoneOnem)).append("\n");
    sb.append("    gsmOnem: ").append(toIndentedString(gsmOnem)).append("\n");
    sb.append("    telephoneReg: ").append(toIndentedString(telephoneReg)).append("\n");
    sb.append("    gsmReg: ").append(toIndentedString(gsmReg)).append("\n");
    sb.append("    deceasedDate: ").append(toIndentedString(deceasedDate)).append("\n");
    sb.append("    bisNumber: ").append(toIndentedString(bisNumber)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

