/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UpdateUnionContributionRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class UnionContributionApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun getUnionContribution(requestId: java.util.UUID): UnionContributionDetailResponse {
        val result = getUnionContributionWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getUnionContributionWithHttpInfo(requestId: java.util.UUID): ResponseEntity<UnionContributionDetailResponse> {
        val localVariableConfig = getUnionContributionRequestConfig(requestId = requestId)
        return request<Unit, UnionContributionDetailResponse>(
            localVariableConfig
        )
    }

    fun getUnionContributionRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/union-contribution",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun selectUnionContributionSources(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): Unit {
        selectUnionContributionSourcesWithHttpInfo(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
    }

    @Throws(RestClientResponseException::class)
    fun selectUnionContributionSourcesWithHttpInfo(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): ResponseEntity<Unit> {
        val localVariableConfig = selectUnionContributionSourcesRequestConfig(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
        return request<SelectFieldSourcesRequest, Unit>(
            localVariableConfig
        )
    }

    fun selectUnionContributionSourcesRequestConfig(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest) : RequestConfig<SelectFieldSourcesRequest> {
        val localVariableBody = selectFieldSourcesRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/union-contribution/select",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateUnionContribution(requestId: java.util.UUID, updateUnionContributionRequest: UpdateUnionContributionRequest): Unit {
        updateUnionContributionWithHttpInfo(requestId = requestId, updateUnionContributionRequest = updateUnionContributionRequest)
    }

    @Throws(RestClientResponseException::class)
    fun updateUnionContributionWithHttpInfo(requestId: java.util.UUID, updateUnionContributionRequest: UpdateUnionContributionRequest): ResponseEntity<Unit> {
        val localVariableConfig = updateUnionContributionRequestConfig(requestId = requestId, updateUnionContributionRequest = updateUnionContributionRequest)
        return request<UpdateUnionContributionRequest, Unit>(
            localVariableConfig
        )
    }

    fun updateUnionContributionRequestConfig(requestId: java.util.UUID, updateUnionContributionRequest: UpdateUnionContributionRequest) : RequestConfig<UpdateUnionContributionRequest> {
        val localVariableBody = updateUnionContributionRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/union-contribution",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
