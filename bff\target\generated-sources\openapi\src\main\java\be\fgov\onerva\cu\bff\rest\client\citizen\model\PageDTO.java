/*
 * Person API
 * Person API
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.citizen.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PageDTO
 */
@JsonPropertyOrder({
  PageDTO.JSON_PROPERTY_CONTENT,
  PageDTO.JSON_PROPERTY_PAGE_NUMBER,
  PageDTO.JSON_PROPERTY_PAGE_SIZE,
  PageDTO.JSON_PROPERTY_TOTAL_PAGE,
  PageDTO.JSON_PROPERTY_TOTAL_ELEMENTS,
  PageDTO.JSON_PROPERTY_IS_FIRST,
  PageDTO.JSON_PROPERTY_IS_LAST
})
@JsonTypeName("Page")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PageDTO {
  public static final String JSON_PROPERTY_CONTENT = "content";
  private List<Object> content = new ArrayList<>();

  public static final String JSON_PROPERTY_PAGE_NUMBER = "pageNumber";
  private Integer pageNumber;

  public static final String JSON_PROPERTY_PAGE_SIZE = "pageSize";
  private Integer pageSize;

  public static final String JSON_PROPERTY_TOTAL_PAGE = "totalPage";
  private Integer totalPage;

  public static final String JSON_PROPERTY_TOTAL_ELEMENTS = "totalElements";
  private Integer totalElements;

  public static final String JSON_PROPERTY_IS_FIRST = "isFirst";
  private Boolean isFirst;

  public static final String JSON_PROPERTY_IS_LAST = "isLast";
  private Boolean isLast;

  public PageDTO() {
  }

  public PageDTO content(List<Object> content) {
    
    this.content = content;
    return this;
  }

  public PageDTO addContentItem(Object contentItem) {
    if (this.content == null) {
      this.content = new ArrayList<>();
    }
    this.content.add(contentItem);
    return this;
  }

  /**
   * Get content
   * @return content
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<Object> getContent() {
    return content;
  }


  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContent(List<Object> content) {
    this.content = content;
  }

  public PageDTO pageNumber(Integer pageNumber) {
    
    this.pageNumber = pageNumber;
    return this;
  }

  /**
   * Get pageNumber
   * @return pageNumber
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPageNumber() {
    return pageNumber;
  }


  @JsonProperty(JSON_PROPERTY_PAGE_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPageNumber(Integer pageNumber) {
    this.pageNumber = pageNumber;
  }

  public PageDTO pageSize(Integer pageSize) {
    
    this.pageSize = pageSize;
    return this;
  }

  /**
   * Get pageSize
   * @return pageSize
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getPageSize() {
    return pageSize;
  }


  @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPageSize(Integer pageSize) {
    this.pageSize = pageSize;
  }

  public PageDTO totalPage(Integer totalPage) {
    
    this.totalPage = totalPage;
    return this;
  }

  /**
   * Get totalPage
   * @return totalPage
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_PAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalPage() {
    return totalPage;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_PAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalPage(Integer totalPage) {
    this.totalPage = totalPage;
  }

  public PageDTO totalElements(Integer totalElements) {
    
    this.totalElements = totalElements;
    return this;
  }

  /**
   * Get totalElements
   * @return totalElements
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TOTAL_ELEMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getTotalElements() {
    return totalElements;
  }


  @JsonProperty(JSON_PROPERTY_TOTAL_ELEMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTotalElements(Integer totalElements) {
    this.totalElements = totalElements;
  }

  public PageDTO isFirst(Boolean isFirst) {
    
    this.isFirst = isFirst;
    return this;
  }

  /**
   * Get isFirst
   * @return isFirst
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_FIRST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsFirst() {
    return isFirst;
  }


  @JsonProperty(JSON_PROPERTY_IS_FIRST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsFirst(Boolean isFirst) {
    this.isFirst = isFirst;
  }

  public PageDTO isLast(Boolean isLast) {
    
    this.isLast = isLast;
    return this;
  }

  /**
   * Get isLast
   * @return isLast
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_LAST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsLast() {
    return isLast;
  }


  @JsonProperty(JSON_PROPERTY_IS_LAST)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsLast(Boolean isLast) {
    this.isLast = isLast;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PageDTO page = (PageDTO) o;
    return Objects.equals(this.content, page.content) &&
        Objects.equals(this.pageNumber, page.pageNumber) &&
        Objects.equals(this.pageSize, page.pageSize) &&
        Objects.equals(this.totalPage, page.totalPage) &&
        Objects.equals(this.totalElements, page.totalElements) &&
        Objects.equals(this.isFirst, page.isFirst) &&
        Objects.equals(this.isLast, page.isLast);
  }

  @Override
  public int hashCode() {
    return Objects.hash(content, pageNumber, pageSize, totalPage, totalElements, isFirst, isLast);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PageDTO {\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    pageNumber: ").append(toIndentedString(pageNumber)).append("\n");
    sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
    sb.append("    totalPage: ").append(toIndentedString(totalPage)).append("\n");
    sb.append("    totalElements: ").append(toIndentedString(totalElements)).append("\n");
    sb.append("    isFirst: ").append(toIndentedString(isFirst)).append("\n");
    sb.append("    isLast: ").append(toIndentedString(isLast)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

