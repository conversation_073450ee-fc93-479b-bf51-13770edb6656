package be.fgov.onerva.cu.backend.adapter.`in`.controller

import jakarta.validation.ConstraintViolation
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Path
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.http.HttpStatus
import org.springframework.web.context.request.WebRequest
import be.fgov.onerva.cu.backend.application.exception.BaremaNotFoundException
import be.fgov.onerva.cu.backend.application.exception.CitizenNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import be.fgov.onerva.cu.backend.application.exception.RequestInvalidStateException
import be.fgov.onerva.cu.backend.application.exception.WaveException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.mockk
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class GlobalExceptionHandlerTest {

    @InjectMockKs
    private lateinit var globalExceptionHandler: GlobalExceptionHandler

    @MockK
    private lateinit var webRequest: WebRequest

    @Test
    fun `should return bad request with problem details when invalid input exception occurs`() {
        // Given
        val errorMessage = "Invalid input data"
        val exception = InvalidInputException(errorMessage)

        // When
        val response = globalExceptionHandler.handleInvalidInputException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Invalid Input")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
    }

    @Test
    fun `should return not found with problem details when request id not found`() {
        // Given
        val errorMessage = "Request ID 123 not found"
        val exception = RequestIdNotFoundException(errorMessage)

        // When
        val response = globalExceptionHandler.handleRequestIdNotFoundException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.NOT_FOUND)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Request Not Found")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.NOT_FOUND.value())
    }

    @Test
    fun `should handle constraint violation with problem details`() {
        // Given
        val constraintViolations = mutableSetOf<ConstraintViolation<*>>()
        val violation1 = mockk<ConstraintViolation<*>>()
        val violation2 = mockk<ConstraintViolation<*>>()
        val path1 = mockk<Path>()
        val path2 = mockk<Path>()

        every { path1.toString() } returns "field1"
        every { path2.toString() } returns "field2"
        every { violation1.propertyPath } returns path1
        every { violation2.propertyPath } returns path2
        every { violation1.message } returns "error message 1"
        every { violation2.message } returns "error message 2"

        constraintViolations.add(violation1)
        constraintViolations.add(violation2)

        val exception = ConstraintViolationException("Validation failed", constraintViolations)

        // When
        val response = globalExceptionHandler.handleValidationExceptions(exception)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo("Validation failed")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.BAD_REQUEST.value())

        @Suppress("UNCHECKED_CAST")
        val errors = problemDetail?.properties?.get("errors") as Map<String, String>
        assertThat(errors)
            .hasSize(2)
            .containsEntry("field1", "error message 1")
            .containsEntry("field2", "error message 2")

        verify(exactly = 1) {
            violation1.propertyPath
            violation2.propertyPath
            violation1.message
            violation2.message
            path1.toString()
            path2.toString()
        }
    }

    @Test
    fun `should handle constraint violation with empty violations set`() {
        // Given
        val constraintViolations = mutableSetOf<ConstraintViolation<*>>()
        val exception = ConstraintViolationException("Validation failed", constraintViolations)

        // When
        val response = globalExceptionHandler.handleValidationExceptions(exception)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo("Validation failed")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.BAD_REQUEST.value())

        @Suppress("UNCHECKED_CAST")
        val errors = problemDetail?.properties?.get("errors") as Map<String, String>
        assertThat(errors).isEmpty()
    }

    @Test
    fun `should return bad request with problem details when request is in invalid state`() {
        // Given
        val errorMessage = "Request is in invalid state"
        val exception = RequestInvalidStateException(errorMessage)

        // When
        val response = globalExceptionHandler.handleRequestInvalidStateException(exception)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Request is in invalid state")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
    }

    @Test
    fun `should return internal server error with problem details when wave exception occurs`() {
        // Given
        val errorMessage = "Wave service unavailable"
        val exception = WaveException(errorMessage)

        // When
        val response = globalExceptionHandler.handleInternalServerError(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Wave Exception")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.INTERNAL_SERVER_ERROR.value())
    }

    @Test
    fun `should return not found with problem details when barema not found`() {
        // Given
        val errorMessage = "Barema ID 123 not found"
        val exception = BaremaNotFoundException(errorMessage)

        // When
        val response = globalExceptionHandler.handleBaremaNotFoundException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.NOT_FOUND)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Barema Not Found")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.NOT_FOUND.value())
    }

    @Test
    fun `should return bad request with problem details when invalid request id exception occurs`() {
        // Given
        val errorMessage = "Invalid request ID format"
        val exception = InvalidRequestIdException(errorMessage)

        // When
        val response = globalExceptionHandler.handleInvalidRequestIdException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.BAD_REQUEST)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Invalid Request ID")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.BAD_REQUEST.value())
    }

    @Test
    fun `should return not found with problem details when information not found`() {
        // Given
        val errorMessage = "Required information not found"
        val exception = InformationNotFoundException(errorMessage)

        // When
        val response = globalExceptionHandler.handleInformationNotFoundException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.NOT_FOUND)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail?.detail).isEqualTo(errorMessage)
        assertThat(problemDetail?.title).isEqualTo("Information Not Found")
        assertThat(problemDetail?.status).isEqualTo(HttpStatus.NOT_FOUND.value())
    }

    @Test
    fun `handleCitizenNotFoundException should return NOT_FOUND with correct problem details`() {
        // Given
        val citizenId = "12345678901"
        val exceptionMessage = "Citizen with ID $citizenId not found"
        val exception = CitizenNotFoundException(exceptionMessage)

        // When
        val response = globalExceptionHandler.handleCitizenNotFoundException(exception, webRequest)

        // Then
        assertThat(response.statusCode).isEqualTo(HttpStatus.NOT_FOUND)

        val problemDetail = response.body
        assertThat(problemDetail).isNotNull
        assertThat(problemDetail!!.detail).isEqualTo(exceptionMessage)
        assertThat(problemDetail.title).isEqualTo("Request Not Found")
        assertThat(problemDetail.status).isEqualTo(HttpStatus.NOT_FOUND.value())
    }
}
