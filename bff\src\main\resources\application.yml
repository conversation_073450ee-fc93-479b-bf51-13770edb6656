spring:
  jackson:
    default-property-inclusion: non_null
  application:
    name: cu-bff
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}
      client:
        registration:
          keycloak:
            client-id: cu-bff
            client-secret:
            scope: openid
            authorization-grant-type: client_credentials
        provider:
          keycloak:
            issuer-uri: ${keycloak.auth-server-url}/realms/${keycloak.realm}

onerva:
  observability:
    prometheus:
      domain: "UNEMP"
      system: "cu"
      component: "bff"
management:
  endpoints:
    web:
      exposure:
        include: "*"
        exclude: "heapdump"
logging:
  level:
    org.springframework.cloud.gateway.server.mvc: INFO

backend:
  base-url: ""

app:
  jobs:
    lookup:
      cron: 0 0 * * * *
