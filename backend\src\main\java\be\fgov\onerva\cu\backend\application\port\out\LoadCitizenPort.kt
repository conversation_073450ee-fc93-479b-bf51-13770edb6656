package be.fgov.onerva.cu.backend.application.port.out

import be.fgov.onerva.cu.backend.application.domain.Citizen
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem

/**
 * Port interface for loading citizen information from external systems.
 *
 * This interface is part of the hexagonal architecture's ports and adapters pattern,
 * serving as an output port for retrieving citizen data. It defines methods to fetch
 * citizen information using their Social Security Identification Number (SSIN).
 *
 * @see Citizen
 * @see CitizenAdapter The implementation class for this port
 */
interface LoadCitizenPort {
    /**
     * Retrieves the numbox value for a citizen identified by their SSIN.
     *
     * @param ssin The Social Security Identification Number (SSIN) of the citizen
     * @return The numbox value associated with the citizen
     * @throws CitizenNotFoundException if no citizen is found with the given SSIN
     */
    fun getCitizenNumbox(ssin: String): Int

    /**
     * Retrieves complete citizen information based on their SSIN.
     *
     * @param ssin The Social Security Identification Number (SSIN) of the citizen
     * @return [Citizen] object containing the citizen's information
     * @throws CitizenNotFoundException if no citizen is found with the given SSIN
     */
    fun getCitizenBySsin(ssin: String): Citizen

    /**
     * Retrieves complete citizen information including address based on their SSIN.
     *
     * @param ssin The Social Security Identification Number (SSIN) of the citizen
     * @return citizenInfo object containing the citizen's information and address
     * @throws CitizenNotFoundException if no citizen is found with the given SSIN
     */
    fun getCitizenWithAddress(ssin: String): HistoricalCitizenOnem?
}