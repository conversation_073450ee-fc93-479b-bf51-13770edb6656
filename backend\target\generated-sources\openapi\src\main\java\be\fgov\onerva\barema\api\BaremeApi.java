package be.fgov.onerva.barema.api;

import be.fgov.onerva.barema.invoker.ApiClient;
import be.fgov.onerva.barema.invoker.BaseApi;

import be.fgov.onerva.barema.rest.model.BaremeResponseDTO;
import be.fgov.onerva.barema.rest.model.BaremeSearchTypeDTO;
import be.fgov.onerva.barema.rest.model.BaremeV2ResponseDTO;
import java.math.BigDecimal;
import be.fgov.onerva.barema.rest.model.DateCompareOperatorDTO;
import java.time.LocalDate;
import be.fgov.onerva.barema.rest.model.SortDirectionDTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:06.182361900+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class BaremeApi extends BaseApi {

    public BaremeApi() {
        super(new ApiClient());
    }

    public BaremeApi(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * 
     * <p><b>200</b> - Return a list of \&quot;Bareme\&quot; based on ssin list
     * <p><b>404</b> - no bareme found
     * @param ssin  (optional)
     * @param pageSize  (optional, default to 10)
     * @param pageNumber  (optional, default to 0)
     * @param orderBy  (optional, default to num_pens)
     * @param orderDirection  (optional, default to asc)
     * @return BaremeResponseDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public BaremeResponseDTO getBaremeBySsinList(List<String> ssin, BigDecimal pageSize, BigDecimal pageNumber, String orderBy, String orderDirection) throws RestClientException {
        return getBaremeBySsinListWithHttpInfo(ssin, pageSize, pageNumber, orderBy, orderDirection).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - Return a list of \&quot;Bareme\&quot; based on ssin list
     * <p><b>404</b> - no bareme found
     * @param ssin  (optional)
     * @param pageSize  (optional, default to 10)
     * @param pageNumber  (optional, default to 0)
     * @param orderBy  (optional, default to num_pens)
     * @param orderDirection  (optional, default to asc)
     * @return ResponseEntity&lt;BaremeResponseDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<BaremeResponseDTO> getBaremeBySsinListWithHttpInfo(List<String> ssin, BigDecimal pageSize, BigDecimal pageNumber, String orderBy, String orderDirection) throws RestClientException {
        Object localVarPostBody = null;
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("multi".toUpperCase(Locale.ROOT)), "ssin", ssin));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageNumber", pageNumber));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "orderBy", orderBy));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "orderDirection", orderDirection));
        

        final String[] localVarAccepts = { 
            "application/json", "text/html"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "bearerAuth" };

        ParameterizedTypeReference<BaremeResponseDTO> localReturnType = new ParameterizedTypeReference<BaremeResponseDTO>() {};
        return apiClient.invokeAPI("/bareme", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
    /**
     * 
     * 
     * <p><b>200</b> - Return a list of \&quot;Baremes\&quot; for the citizen id
     * @param citizenId  (required)
     * @param type  (optional, default to ALL)
     * @param date  (optional)
     * @param dateOrderDirection  (optional, default to ASC)
     * @param dateCompareOperator  (optional, default to LESS_OR_EQUAL)
     * @return BaremeV2ResponseDTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public BaremeV2ResponseDTO getBaremesByCitizenId(Integer citizenId, BaremeSearchTypeDTO type, LocalDate date, SortDirectionDTO dateOrderDirection, DateCompareOperatorDTO dateCompareOperator) throws RestClientException {
        return getBaremesByCitizenIdWithHttpInfo(citizenId, type, date, dateOrderDirection, dateCompareOperator).getBody();
    }

    /**
     * 
     * 
     * <p><b>200</b> - Return a list of \&quot;Baremes\&quot; for the citizen id
     * @param citizenId  (required)
     * @param type  (optional, default to ALL)
     * @param date  (optional)
     * @param dateOrderDirection  (optional, default to ASC)
     * @param dateCompareOperator  (optional, default to LESS_OR_EQUAL)
     * @return ResponseEntity&lt;BaremeV2ResponseDTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<BaremeV2ResponseDTO> getBaremesByCitizenIdWithHttpInfo(Integer citizenId, BaremeSearchTypeDTO type, LocalDate date, SortDirectionDTO dateOrderDirection, DateCompareOperatorDTO dateCompareOperator) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'citizenId' is set
        if (citizenId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'citizenId' when calling getBaremesByCitizenId");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "citizenId", citizenId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "type", type));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "date", date));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "dateOrderDirection", dateOrderDirection));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "dateCompareOperator", dateCompareOperator));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "bearerAuth" };

        ParameterizedTypeReference<BaremeV2ResponseDTO> localReturnType = new ParameterizedTypeReference<BaremeV2ResponseDTO>() {};
        return apiClient.invokeAPI("/v2/bareme", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] { "bearerAuth" };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
