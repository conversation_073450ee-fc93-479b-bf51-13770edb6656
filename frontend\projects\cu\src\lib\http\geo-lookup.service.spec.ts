import { TestBed, fakeAsync, tick } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { FormControl } from '@angular/forms';
import { GeoLookupService } from './geo-lookup.service';
import { ConfigService } from '../config/config.service';
import { NationalityResponse } from '@rest-client/cu-bff';

describe('GeoLookupService', () => {
  let service: GeoLookupService;
  let httpMock: HttpTestingController;

  const mockEntities: NationalityResponse[] = [
    { code: 'BE', descFr: 'Belgique', descNl: 'België' },
    { code: 'FR', descFr: 'France', descNl: '<PERSON>rijk' },
    { code: 'DE', descFr: 'Allemagne', descNl: 'Duitsland' }
  ];

  const mockConfigService = {
    getEnvironmentVariable: jest.fn().mockImplementation((key: string) => {
      if (key === 'bffBaseUrl') {
        return 'http://bff';
      }
      return '';
    }),
    isOnWO: jest.fn().mockReturnValue(false)
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        GeoLookupService,
        { provide: ConfigService, useValue: mockConfigService },
      ],
    });

    service = TestBed.inject(GeoLookupService);
    httpMock = TestBed.inject(HttpTestingController);

    const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=');
    req.flush(mockEntities);
  });

  afterEach(() => {
    httpMock.verify();
    jest.clearAllMocks();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('initializeService', () => {
    it('should set default headers if token is provided', () => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      service.initializeService('my-token');

      const initReq = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=');
      expect(initReq.request.method).toBe('GET');
      expect(initReq.request.headers.get('Authorization')).toBe('Bearer my-token');
      initReq.flush(mockEntities);
    });

    it('should NOT set authorization header if no token is provided', () => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      service.initializeService();

      const initReq = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=');
      expect(initReq.request.method).toBe('GET');
      expect(initReq.request.headers.has('Authorization')).toBe(false);
      initReq.flush(mockEntities);
    });

    it('should load all geo entities on initialization', () => {
      expect(service.getEntityByCode('BE')).toEqual(mockEntities[0]);
      expect(service.getEntityByCode('FR')).toEqual(mockEntities[1]);
    });

    it('should not load entities again if already loaded', () => {
      service.initializeService();
    });
  });

  describe('displayLookup', () => {
    it('should return empty string if no lookup provided', () => {
      const result = service.displayLookup(undefined, 'fr');
      expect(result).toBe('');
    });

    it('should return descFr if language is FR', () => {
      const mockCountry: NationalityResponse = {
        code: 'FR',
        descFr: 'France',
        descNl: 'Frankrijk',
      };
      const result = service.displayLookup(mockCountry, 'fr');
      expect(result).toBe('France');
    });

    it('should return descNl if language is NL', () => {
      const mockCountry: NationalityResponse = {
        code: 'NL',
        descFr: 'Pays-Bas',
        descNl: 'Nederland',
      };
      const result = service.displayLookup(mockCountry, 'nl');
      expect(result).toBe('Nederland');
    });

    it('should be case insensitive for language selection', () => {
      const mockCountry: NationalityResponse = {
        code: 'BE',
        descFr: 'Belgique',
        descNl: 'België'
      };
      const result = service.displayLookup(mockCountry, 'Fr');
      expect(result).toBe('Belgique');
    });

    it('should use descNl for non-FR languages', () => {
      const mockCountry: NationalityResponse = {
        code: 'BE',
        descFr: 'Belgique',
        descNl: 'België'
      };
      const result = service.displayLookup(mockCountry, 'en');
      expect(result).toBe('België');
    });
  });

  describe('getEntityByCode', () => {
    it('should return entity by code from cached data', () => {
      const entity = service.getEntityByCode('BE');
      expect(entity).toEqual(mockEntities[0]);
    });

    it('should return undefined if entity not found', () => {
      const entity = service.getEntityByCode('XX');
      expect(entity).toBeUndefined();
    });
  });

  describe('getEntityDescription', () => {
    it('should return French description for FR language', () => {
      const desc = service.getEntityDescription('BE', 'FR');
      expect(desc).toBe('Belgique');
    });

    it('should return Dutch description for non-FR language', () => {
      const desc = service.getEntityDescription('BE', 'NL');
      expect(desc).toBe('België');
    });

    it('should return code if entity not found', () => {
      const desc = service.getEntityDescription('XX', 'FR');
      expect(desc).toBe('XX');
    });
  });

  describe('searchEntityByCode', () => {
    it('should use cache if data is ready', () => {
      service.searchCountryByCode('BE').subscribe((country: NationalityResponse | null) => {
        expect(country).toEqual(mockEntities[0]);
      });
    });

    it('should make HTTP request if cache not ready', () => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      service.searchCountryByCode('BE').subscribe((country: NationalityResponse | null) => {
        expect(country).toEqual(mockEntities[0]);
      });

      const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=BE');
      expect(req.request.method).toBe('GET');
      req.flush(mockEntities);
    });

    it('should return null if entity not found in cache', () => {
      service.searchCountryByCode('XX').subscribe((country: NationalityResponse | null) => {
        expect(country).toBeNull();
      });
    });
  });

  describe('searchCountryByCode', () => {
    it('should delegate to searchEntityByCode', () => {
      // @ts-ignore - accessing private method for testing
      const spy = jest.spyOn(service, 'searchEntityByCode');

      service.searchCountryByCode('BE').subscribe();

      expect(spy).toHaveBeenCalledWith('BE');
    });
  });

  describe('searchNationalityByCode', () => {
    it('should delegate to searchEntityByCode', () => {
      // @ts-ignore - accessing private method for testing
      const spy = jest.spyOn(service, 'searchEntityByCode');

      service.searchNationalityByCode('FR').subscribe();

      expect(spy).toHaveBeenCalledWith('FR');
    });
  });

  describe('setupSearchControl', () => {
    it('should return empty array for object values', fakeAsync(() => {
      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue({ code: 'BE' });
      tick(300);

      expect(results).toEqual([]);
    }));

    it('should filter cached data for string values when cache is ready', fakeAsync(() => {
      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue('bel');
      tick(300);

      expect(results.length).toBe(1);
      expect(results[0].code).toBe('BE');
    }));

    it('should call API when cache is not ready', fakeAsync(() => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue('bel');
      tick(300);

      const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=bel');
      req.flush([mockEntities[0]]);

      expect(results.length).toBe(1);
      expect(results[0].code).toBe('BE');
    }));

    it('should return empty array for empty value', fakeAsync(() => {
      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue('');
      tick(300);

      expect(results).toEqual([]);
    }));

    it('should return empty array for null value', fakeAsync(() => {
      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue(null);
      tick(300);

      expect(results).toEqual([]);
    }));

    it('should match text without accents to text with accents', fakeAsync(() => {
      let results: NationalityResponse[] = [];
      const control = new FormControl();

      service.setupSearchControl(control).subscribe((res: NationalityResponse[]) => {
        results = res;
      });

      control.setValue('belgie');
      tick(300); // Wait for debounceTime

      expect(results.length).toBe(1);
      expect(results[0].code).toBe('BE');
      expect(results[0].descNl).toBe('België');
    }));
  });

  describe('Error handling', () => {
    it('should handle HTTP errors in initial load', () => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      service.initializeService();

      const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=');
      req.flush('Not found', { status: 404, statusText: 'Not Found' });

      expect(consoleSpy).toHaveBeenCalledWith(
          'Error loading geo entities:',
          expect.anything()
      );

      consoleSpy.mockRestore();
    });

    it('should propagate HTTP errors in searchCountryByCode when not cached', (done) => {
      // @ts-ignore - accessing private property for testing
      service['dataReady$'].next(false);

      service.searchCountryByCode('BE').subscribe({
        next: () => done.fail('should have failed'),
        error: (error: any) => {
          expect(error.status).toBe(500);
          done();
        }
      });

      const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=BE');
      req.flush('Server error', { status: 500, statusText: 'Internal Server Error' });
    });
  });


  it('should not make API call in WO environment without force', () => {
    service['dataReady$'].next(false);
    mockConfigService.isOnWO.mockReturnValue(true);
    service.initializeService();
    mockConfigService.isOnWO.mockReturnValue(false);
  });

  it('should make API call in WO environment with force (token)', () => {
    service['dataReady$'].next(false);

    mockConfigService.isOnWO.mockReturnValue(true);

    service.initializeService('test-token');

    const req = httpMock.expectOne('http://bff/api/lookup/nationality?searchQuery=');
    expect(req.request.headers.get('Authorization')).toBe('Bearer test-token');
    req.flush(mockEntities);

    mockConfigService.isOnWO.mockReturnValue(false);
  });

  describe('normalizeText', () => {
    it('should remove diacritical marks from text', () => {
      // @ts-ignore - accessing private method for testing
      const result = service['normalizeText']('België');
      expect(result).toBe('Belgie');
    });

    it('should handle multiple accented characters', () => {
      // @ts-ignore - accessing private method for testing
      const result = service['normalizeText']('Česká Republika');
      expect(result).toBe('Ceska Republika');
    });

    it('should preserve case', () => {
      // @ts-ignore - accessing private method for testing
      const result = service['normalizeText']('ESPAÑA');
      expect(result).toBe('ESPANA');
    });

    it('should handle non-accented text without changes', () => {
      // @ts-ignore - accessing private method for testing
      const result = service['normalizeText']('Netherlands');
      expect(result).toBe('Netherlands');
    });
  });
});