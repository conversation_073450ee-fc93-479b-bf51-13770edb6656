/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.TranslationDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param code 
 * @param `value` 
 * @param valueTranslation 
 */


data class MetaDataDTO (

    @get:JsonProperty("code")
    val code: kotlin.String,

    @get:JsonProperty("value")
    val `value`: kotlin.String? = null,

    @get:JsonProperty("valueTranslation")
    val valueTranslation: TranslationDTO? = null

) {


}

