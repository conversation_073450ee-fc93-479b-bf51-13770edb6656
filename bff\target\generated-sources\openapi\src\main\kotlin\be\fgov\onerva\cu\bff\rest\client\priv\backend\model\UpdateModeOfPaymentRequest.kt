/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param otherPersonName 
 * @param iban 
 * @param bic 
 */


data class UpdateModeOfPaymentRequest (

    @get:JsonProperty("otherPersonName")
    val otherPersonName: kotlin.String? = null,

    @get:JsonProperty("iban")
    val iban: kotlin.String? = null,

    @get:JsonProperty("bic")
    val bic: kotlin.String? = null

) {


}

