/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.registerproxyservice.rest.model.ForeignAddress;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ForeignAddressMap
 */
@JsonPropertyOrder({
  ForeignAddressMap.JSON_PROPERTY_D_E,
  ForeignAddressMap.JSON_PROPERTY_F_R,
  ForeignAddressMap.JSON_PROPERTY_N_L
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ForeignAddressMap {
  public static final String JSON_PROPERTY_D_E = "DE";
  private ForeignAddress DE;

  public static final String JSON_PROPERTY_F_R = "FR";
  private ForeignAddress FR;

  public static final String JSON_PROPERTY_N_L = "NL";
  private ForeignAddress NL;

  public ForeignAddressMap() {
  }

  public ForeignAddressMap DE(ForeignAddress DE) {
    
    this.DE = DE;
    return this;
  }

  /**
   * Get DE
   * @return DE
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_D_E)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ForeignAddress getDE() {
    return DE;
  }


  @JsonProperty(JSON_PROPERTY_D_E)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDE(ForeignAddress DE) {
    this.DE = DE;
  }

  public ForeignAddressMap FR(ForeignAddress FR) {
    
    this.FR = FR;
    return this;
  }

  /**
   * Get FR
   * @return FR
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_F_R)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ForeignAddress getFR() {
    return FR;
  }


  @JsonProperty(JSON_PROPERTY_F_R)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFR(ForeignAddress FR) {
    this.FR = FR;
  }

  public ForeignAddressMap NL(ForeignAddress NL) {
    
    this.NL = NL;
    return this;
  }

  /**
   * Get NL
   * @return NL
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_N_L)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ForeignAddress getNL() {
    return NL;
  }


  @JsonProperty(JSON_PROPERTY_N_L)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNL(ForeignAddress NL) {
    this.NL = NL;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ForeignAddressMap foreignAddressMap = (ForeignAddressMap) o;
    return Objects.equals(this.DE, foreignAddressMap.DE) &&
        Objects.equals(this.FR, foreignAddressMap.FR) &&
        Objects.equals(this.NL, foreignAddressMap.NL);
  }

  @Override
  public int hashCode() {
    return Objects.hash(DE, FR, NL);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ForeignAddressMap {\n");
    sb.append("    DE: ").append(toIndentedString(DE)).append("\n");
    sb.append("    FR: ").append(toIndentedString(FR)).append("\n");
    sb.append("    NL: ").append(toIndentedString(NL)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

