/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Describe how to request
 *
 * @param niss 
 * @param firstname 
 * @param lastname 
 * @param fallbackUrl Deprecated. A POST will be done on this endpoint with the following request body {\"status \":  \"SUCCESS  FAILED\"}
 * @param correlationId An optional ID of your choice to correlate with.
 */


data class CitizenCreationRequestDTO (

    @get:JsonProperty("niss")
    val niss: kotlin.String? = null,

    @get:JsonProperty("firstname")
    val firstname: kotlin.String? = null,

    @get:JsonProperty("lastname")
    val lastname: kotlin.String? = null,

    /* Deprecated. A POST will be done on this endpoint with the following request body {\"status \":  \"SUCCESS  FAILED\"} */
    @get:JsonProperty("fallbackUrl")
    @Deprecated(message = "This property is deprecated.")
    val fallbackUrl: kotlin.String? = null,

    /* An optional ID of your choice to correlate with. */
    @get:JsonProperty("correlationId")
    val correlationId: kotlin.String? = null

) {


}

