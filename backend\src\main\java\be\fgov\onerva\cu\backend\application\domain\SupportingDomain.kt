package be.fgov.onerva.cu.backend.application.domain

import java.time.LocalDate

/**
 * Represents a physical address.
 *
 * @property street Street name
 * @property houseNumber House number
 * @property boxNumber Optional box or apartment number
 * @property country Country name
 * @property city City name
 * @property zipCode Postal code
 * @see be.fgov.onerva.cu.backend.application.port.out.LoadCitizenPort
 */
data class AddressNullable(
    val street: String,
    val houseNumber: String?,
    val boxNumber: String?,
    val country: String?,
    val city: String?,
    val zipCode: String,
    val valueDate: LocalDate?,
)

data class HistoricalCitizenSnapshot(
    val firstName: String,
    val lastName: String,
    val numbox: Int?,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenOnem(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenC1(
    val firstName: String,
    val lastName: String,
    val numbox: Int,
    val nationality: String,
    val address: AddressNullable,
    val iban: String?,
    val bic: String?,
    val otherPersonName: String?,
    val birthDate: LocalDate?,
    val bankAccountValueDate: LocalDate?,
    val paymentMode: Int?,
    val authorized: Boolean?,
    val effectiveDate: LocalDate?,
)

data class HistoricalCitizenAuthenticSources(
    val firstName: String,
    val lastName: String,
    val nationality: String,
    val address: AddressNullable,
    val birthDate: LocalDate,
)

data class UpdateCitizen(
    val ssin: String,
    val address: Address,
    val birthDate: LocalDate?,
    val nationality: String,
    val correlationId: String,
    val userName: String,
    val authorized: Boolean?,
    val valueDate: LocalDate,
)

data class PersonUpdated(
    val id: Long,
    val correlationId: String,
    val ssin: String,
    val success: Boolean,
    val names: String,
)