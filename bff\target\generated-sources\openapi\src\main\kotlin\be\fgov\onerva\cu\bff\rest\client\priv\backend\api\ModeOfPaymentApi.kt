/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.UpdateModeOfPaymentRequest
import be.fgov.onerva.cu.bff.rest.client.priv.backend.infrastructure.*

class ModeOfPaymentApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun getModeOfPayment(requestId: java.util.UUID): ModeOfPaymentDetailResponse {
        val result = getModeOfPaymentWithHttpInfo(requestId = requestId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getModeOfPaymentWithHttpInfo(requestId: java.util.UUID): ResponseEntity<ModeOfPaymentDetailResponse> {
        val localVariableConfig = getModeOfPaymentRequestConfig(requestId = requestId)
        return request<Unit, ModeOfPaymentDetailResponse>(
            localVariableConfig
        )
    }

    fun getModeOfPaymentRequestConfig(requestId: java.util.UUID) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/api/requests/{requestId}/mode-of-payment",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun selectModeOfPaymentSources(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): Unit {
        selectModeOfPaymentSourcesWithHttpInfo(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
    }

    @Throws(RestClientResponseException::class)
    fun selectModeOfPaymentSourcesWithHttpInfo(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest): ResponseEntity<Unit> {
        val localVariableConfig = selectModeOfPaymentSourcesRequestConfig(requestId = requestId, selectFieldSourcesRequest = selectFieldSourcesRequest)
        return request<SelectFieldSourcesRequest, Unit>(
            localVariableConfig
        )
    }

    fun selectModeOfPaymentSourcesRequestConfig(requestId: java.util.UUID, selectFieldSourcesRequest: SelectFieldSourcesRequest) : RequestConfig<SelectFieldSourcesRequest> {
        val localVariableBody = selectFieldSourcesRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/mode-of-payment/select",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateModeOfPayment(requestId: java.util.UUID, updateModeOfPaymentRequest: UpdateModeOfPaymentRequest): Unit {
        updateModeOfPaymentWithHttpInfo(requestId = requestId, updateModeOfPaymentRequest = updateModeOfPaymentRequest)
    }

    @Throws(RestClientResponseException::class)
    fun updateModeOfPaymentWithHttpInfo(requestId: java.util.UUID, updateModeOfPaymentRequest: UpdateModeOfPaymentRequest): ResponseEntity<Unit> {
        val localVariableConfig = updateModeOfPaymentRequestConfig(requestId = requestId, updateModeOfPaymentRequest = updateModeOfPaymentRequest)
        return request<UpdateModeOfPaymentRequest, Unit>(
            localVariableConfig
        )
    }

    fun updateModeOfPaymentRequestConfig(requestId: java.util.UUID, updateModeOfPaymentRequest: UpdateModeOfPaymentRequest) : RequestConfig<UpdateModeOfPaymentRequest> {
        val localVariableBody = updateModeOfPaymentRequest
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "requestId" to requestId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/api/requests/{requestId}/mode-of-payment",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
