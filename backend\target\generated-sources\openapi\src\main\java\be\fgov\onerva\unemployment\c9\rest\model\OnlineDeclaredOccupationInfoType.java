/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * OnlineDeclaredOccupationInfoType
 */
@JsonPropertyOrder({
  OnlineDeclaredOccupationInfoType.JSON_PROPERTY_OCCUPATION_STARTING_DATE,
  OnlineDeclaredOccupationInfoType.JSON_PROPERTY_OCCUPATION_ENDING_DATE,
  OnlineDeclaredOccupationInfoType.JSON_PROPERTY_MEAN_WORKING_HOURS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class OnlineDeclaredOccupationInfoType {
  public static final String JSON_PROPERTY_OCCUPATION_STARTING_DATE = "occupationStartingDate";
  private LocalDate occupationStartingDate;

  public static final String JSON_PROPERTY_OCCUPATION_ENDING_DATE = "occupationEndingDate";
  private LocalDate occupationEndingDate;

  public static final String JSON_PROPERTY_MEAN_WORKING_HOURS = "meanWorkingHours";
  private Integer meanWorkingHours;

  public OnlineDeclaredOccupationInfoType() {
  }

  public OnlineDeclaredOccupationInfoType occupationStartingDate(LocalDate occupationStartingDate) {
    
    this.occupationStartingDate = occupationStartingDate;
    return this;
  }

  /**
   * Get occupationStartingDate
   * @return occupationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getOccupationStartingDate() {
    return occupationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationStartingDate(LocalDate occupationStartingDate) {
    this.occupationStartingDate = occupationStartingDate;
  }

  public OnlineDeclaredOccupationInfoType occupationEndingDate(LocalDate occupationEndingDate) {
    
    this.occupationEndingDate = occupationEndingDate;
    return this;
  }

  /**
   * Get occupationEndingDate
   * @return occupationEndingDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getOccupationEndingDate() {
    return occupationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationEndingDate(LocalDate occupationEndingDate) {
    this.occupationEndingDate = occupationEndingDate;
  }

  public OnlineDeclaredOccupationInfoType meanWorkingHours(Integer meanWorkingHours) {
    
    this.meanWorkingHours = meanWorkingHours;
    return this;
  }

  /**
   * Get meanWorkingHours
   * @return meanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getMeanWorkingHours() {
    return meanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMeanWorkingHours(Integer meanWorkingHours) {
    this.meanWorkingHours = meanWorkingHours;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OnlineDeclaredOccupationInfoType onlineDeclaredOccupationInfoType = (OnlineDeclaredOccupationInfoType) o;
    return Objects.equals(this.occupationStartingDate, onlineDeclaredOccupationInfoType.occupationStartingDate) &&
        Objects.equals(this.occupationEndingDate, onlineDeclaredOccupationInfoType.occupationEndingDate) &&
        Objects.equals(this.meanWorkingHours, onlineDeclaredOccupationInfoType.meanWorkingHours);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationStartingDate, occupationEndingDate, meanWorkingHours);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OnlineDeclaredOccupationInfoType {\n");
    sb.append("    occupationStartingDate: ").append(toIndentedString(occupationStartingDate)).append("\n");
    sb.append("    occupationEndingDate: ").append(toIndentedString(occupationEndingDate)).append("\n");
    sb.append("    meanWorkingHours: ").append(toIndentedString(meanWorkingHours)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

