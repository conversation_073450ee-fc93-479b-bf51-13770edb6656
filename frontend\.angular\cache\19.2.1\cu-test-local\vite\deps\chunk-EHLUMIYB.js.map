{"version": 3, "sources": ["../../../../../../node_modules/@angular/cdk/fesm2022/clipboard.mjs", "../../../../../../node_modules/ibantools/jsnext/ibantools.js", "../../../../../../node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-shared.mjs"], "sourcesContent": ["import { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, Injectable, InjectionToken, NgZone, EventEmitter, Directive, Input, Output, NgModule } from '@angular/core';\n\n/**\n * A pending copy-to-clipboard operation.\n *\n * The implementation of copying text to the clipboard modifies the DOM and\n * forces a re-layout. This re-layout can take too long if the string is large,\n * causing the execCommand('copy') to happen too long after the user clicked.\n * This results in the browser refusing to copy. This object lets the\n * re-layout happen in a separate tick from copying by providing a copy function\n * that can be called later.\n *\n * Destroy must be called when no longer in use, regardless of whether `copy` is\n * called.\n */\nclass PendingCopy {\n  _document;\n  _textarea;\n  constructor(text, _document) {\n    this._document = _document;\n    const textarea = this._textarea = this._document.createElement('textarea');\n    const styles = textarea.style;\n    // Hide the element for display and accessibility. Set a fixed position so the page layout\n    // isn't affected. We use `fixed` with `top: 0`, because focus is moved into the textarea\n    // for a split second and if it's off-screen, some browsers will attempt to scroll it into view.\n    styles.position = 'fixed';\n    styles.top = styles.opacity = '0';\n    styles.left = '-999em';\n    textarea.setAttribute('aria-hidden', 'true');\n    textarea.value = text;\n    // Making the textarea `readonly` prevents the screen from jumping on iOS Safari (see #25169).\n    textarea.readOnly = true;\n    // The element needs to be inserted into the fullscreen container, if the page\n    // is in fullscreen mode, otherwise the browser won't execute the copy command.\n    (this._document.fullscreenElement || this._document.body).appendChild(textarea);\n  }\n  /** Finishes copying the text. */\n  copy() {\n    const textarea = this._textarea;\n    let successful = false;\n    try {\n      // Older browsers could throw if copy is not supported.\n      if (textarea) {\n        const currentFocus = this._document.activeElement;\n        textarea.select();\n        textarea.setSelectionRange(0, textarea.value.length);\n        successful = this._document.execCommand('copy');\n        if (currentFocus) {\n          currentFocus.focus();\n        }\n      }\n    } catch {\n      // Discard error.\n      // Initial setting of {@code successful} will represent failure here.\n    }\n    return successful;\n  }\n  /** Cleans up DOM changes used to perform the copy operation. */\n  destroy() {\n    const textarea = this._textarea;\n    if (textarea) {\n      textarea.remove();\n      this._textarea = undefined;\n    }\n  }\n}\n\n/**\n * A service for copying text to the clipboard.\n */\nclass Clipboard {\n  _document = inject(DOCUMENT);\n  constructor() {}\n  /**\n   * Copies the provided text into the user's clipboard.\n   *\n   * @param text The string to copy.\n   * @returns Whether the operation was successful.\n   */\n  copy(text) {\n    const pendingCopy = this.beginCopy(text);\n    const successful = pendingCopy.copy();\n    pendingCopy.destroy();\n    return successful;\n  }\n  /**\n   * Prepares a string to be copied later. This is useful for large strings\n   * which take too long to successfully render and be copied in the same tick.\n   *\n   * The caller must call `destroy` on the returned `PendingCopy`.\n   *\n   * @param text The string to copy.\n   * @returns the pending copy operation.\n   */\n  beginCopy(text) {\n    return new PendingCopy(text, this._document);\n  }\n  static ɵfac = function Clipboard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || Clipboard)();\n  };\n  static ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n    token: Clipboard,\n    factory: Clipboard.ɵfac,\n    providedIn: 'root'\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Clipboard, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [], null);\n})();\n\n/** Injection token that can be used to provide the default options to `CdkCopyToClipboard`. */\nconst CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken('CDK_COPY_TO_CLIPBOARD_CONFIG');\n/**\n * Provides behavior for a button that when clicked copies content into user's\n * clipboard.\n */\nclass CdkCopyToClipboard {\n  _clipboard = inject(Clipboard);\n  _ngZone = inject(NgZone);\n  /** Content to be copied. */\n  text = '';\n  /**\n   * How many times to attempt to copy the text. This may be necessary for longer text, because\n   * the browser needs time to fill an intermediate textarea element and copy the content.\n   */\n  attempts = 1;\n  /**\n   * Emits when some text is copied to the clipboard. The\n   * emitted value indicates whether copying was successful.\n   */\n  copied = new EventEmitter();\n  /** Copies that are currently being attempted. */\n  _pending = new Set();\n  /** Whether the directive has been destroyed. */\n  _destroyed;\n  /** Timeout for the current copy attempt. */\n  _currentTimeout;\n  constructor() {\n    const config = inject(CDK_COPY_TO_CLIPBOARD_CONFIG, {\n      optional: true\n    });\n    if (config && config.attempts != null) {\n      this.attempts = config.attempts;\n    }\n  }\n  /** Copies the current text to the clipboard. */\n  copy(attempts = this.attempts) {\n    if (attempts > 1) {\n      let remainingAttempts = attempts;\n      const pending = this._clipboard.beginCopy(this.text);\n      this._pending.add(pending);\n      const attempt = () => {\n        const successful = pending.copy();\n        if (!successful && --remainingAttempts && !this._destroyed) {\n          // We use 1 for the timeout since it's more predictable when flushing in unit tests.\n          this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));\n        } else {\n          this._currentTimeout = null;\n          this._pending.delete(pending);\n          pending.destroy();\n          this.copied.emit(successful);\n        }\n      };\n      attempt();\n    } else {\n      this.copied.emit(this._clipboard.copy(this.text));\n    }\n  }\n  ngOnDestroy() {\n    if (this._currentTimeout) {\n      clearTimeout(this._currentTimeout);\n    }\n    this._pending.forEach(copy => copy.destroy());\n    this._pending.clear();\n    this._destroyed = true;\n  }\n  static ɵfac = function CdkCopyToClipboard_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || CdkCopyToClipboard)();\n  };\n  static ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n    type: CdkCopyToClipboard,\n    selectors: [[\"\", \"cdkCopyToClipboard\", \"\"]],\n    hostBindings: function CdkCopyToClipboard_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function CdkCopyToClipboard_click_HostBindingHandler() {\n          return ctx.copy();\n        });\n      }\n    },\n    inputs: {\n      text: [0, \"cdkCopyToClipboard\", \"text\"],\n      attempts: [0, \"cdkCopyToClipboardAttempts\", \"attempts\"]\n    },\n    outputs: {\n      copied: \"cdkCopyToClipboardCopied\"\n    }\n  });\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkCopyToClipboard, [{\n    type: Directive,\n    args: [{\n      selector: '[cdkCopyToClipboard]',\n      host: {\n        '(click)': 'copy()'\n      }\n    }]\n  }], () => [], {\n    text: [{\n      type: Input,\n      args: ['cdkCopyToClipboard']\n    }],\n    attempts: [{\n      type: Input,\n      args: ['cdkCopyToClipboardAttempts']\n    }],\n    copied: [{\n      type: Output,\n      args: ['cdkCopyToClipboardCopied']\n    }]\n  });\n})();\nclass ClipboardModule {\n  static ɵfac = function ClipboardModule_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ClipboardModule)();\n  };\n  static ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n    type: ClipboardModule,\n    imports: [CdkCopyToClipboard],\n    exports: [CdkCopyToClipboard]\n  });\n  static ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [CdkCopyToClipboard],\n      exports: [CdkCopyToClipboard]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDK_COPY_TO_CLIPBOARD_CONFIG, CdkCopyToClipboard, Clipboard, ClipboardModule, PendingCopy };\n", "/*!\n * @license\n * Copyright <PERSON><PERSON>\n * Licensed under the Mozilla Public License, Version 2.0 or the MIT license,\n * at your option. This file may not be copied, modified, or distributed\n * except according to those terms.\n * SPDX-FileCopyrightText: <PERSON><PERSON>\n * SPDX-License-Identifier: MIT or MPL/2.0\n */\n/**\n * Validation, extraction and creation of IBAN, BBAN, BIC/SWIFT numbers plus some other helpful stuff\n * @package Documentation\n * <AUTHOR>\n * @module ibantools\n * @version 4.5.1\n * @license MIT or MPL-2.0\n * @preferred\n */\n'use strict';\n\n/**\n * Validate IBAN\n * ```\n * // returns true\n * ibantools.isValidIBAN(\"******************\");\n * ```\n * ```\n * // returns false\n * ibantools.isValidIBAN(\"******************\");\n * ```\n * ```\n * // returns true\n * ibantools.isValidIBAN('*********************');\n * ```\n * ```\n * // returns false\n * ibantools.isValidIBAN('*********************', { allowQRIBAN: false });\n * ```\n */\nexport function isValidIBAN(iban, validationOptions) {\n  if (validationOptions === void 0) {\n    validationOptions = {\n      allowQRIBAN: true\n    };\n  }\n  if (iban === undefined || iban === null) return false;\n  var reg = new RegExp('^[0-9]{2}$', '');\n  var countryCode = iban.slice(0, 2);\n  var spec = countrySpecs[countryCode];\n  if (spec === undefined || spec.bban_regexp === undefined || spec.bban_regexp === null || spec.chars === undefined) return false;\n  return spec.chars === iban.length && reg.test(iban.slice(2, 4)) && isValidBBAN(iban.slice(4), countryCode) && isValidIBANChecksum(iban) && (validationOptions.allowQRIBAN || !isQRIBAN(iban));\n}\n/**\n * IBAM validation errors\n */\nexport var ValidationErrorsIBAN;\n(function (ValidationErrorsIBAN) {\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"NoIBANProvided\"] = 0] = \"NoIBANProvided\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"NoIBANCountry\"] = 1] = \"NoIBANCountry\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"WrongBBANLength\"] = 2] = \"WrongBBANLength\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"WrongBBANFormat\"] = 3] = \"WrongBBANFormat\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"ChecksumNotNumber\"] = 4] = \"ChecksumNotNumber\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"WrongIBANChecksum\"] = 5] = \"WrongIBANChecksum\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"WrongAccountBankBranchChecksum\"] = 6] = \"WrongAccountBankBranchChecksum\";\n  ValidationErrorsIBAN[ValidationErrorsIBAN[\"QRIBANNotAllowed\"] = 7] = \"QRIBANNotAllowed\";\n})(ValidationErrorsIBAN || (ValidationErrorsIBAN = {}));\n/**\n * validateIBAN\n * ```\n * // returns {errorCodes: [], valid: true}\n * ibantools.validateIBAN(\"******************\");\n * ```\n * ```\n * ```\n * // returns {errorCodes: [], valid: true}\n * ibantools.validateIBAN('*********************');\n * ```\n * ```\n * // returns {errorCodes: [7], valid: false}\n * ibantools.validateIBAN('*********************', { allowQRIBAN: false });\n * ```\n */\nexport function validateIBAN(iban, validationOptions) {\n  if (validationOptions === void 0) {\n    validationOptions = {\n      allowQRIBAN: true\n    };\n  }\n  var result = {\n    errorCodes: [],\n    valid: true\n  };\n  if (iban !== undefined && iban !== null && iban !== '') {\n    var spec = countrySpecs[iban.slice(0, 2)];\n    if (!spec || !(spec.bban_regexp || spec.chars)) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.NoIBANCountry);\n      return result;\n    }\n    if (spec && spec.chars && spec.chars !== iban.length) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.WrongBBANLength);\n    }\n    if (spec && spec.bban_regexp && !checkFormatBBAN(iban.slice(4), spec.bban_regexp)) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.WrongBBANFormat);\n    }\n    if (spec && spec.bban_validation_func && !spec.bban_validation_func(iban.slice(4))) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.WrongAccountBankBranchChecksum);\n    }\n    var reg = new RegExp('^[0-9]{2}$', '');\n    if (!reg.test(iban.slice(2, 4))) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.ChecksumNotNumber);\n    }\n    if (result.errorCodes.indexOf(ValidationErrorsIBAN.WrongBBANFormat) !== -1 || !isValidIBANChecksum(iban)) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.WrongIBANChecksum);\n    }\n    if (!validationOptions.allowQRIBAN && isQRIBAN(iban)) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsIBAN.QRIBANNotAllowed);\n    }\n  } else {\n    result.valid = false;\n    result.errorCodes.push(ValidationErrorsIBAN.NoIBANProvided);\n  }\n  return result;\n}\n/**\n * Validate BBAN\n *\n * ```\n * // returns true\n * ibantools.isValidBBAN(\"ABNA**********\", \"NL\");\n * ```\n * ```\n * // returns false\n * ibantools.isValidBBAN(\"A7NA0517164300\", \"NL\");\n * ```\n */\nexport function isValidBBAN(bban, countryCode) {\n  if (bban === undefined || bban === null || countryCode === undefined || countryCode === null) return false;\n  var spec = countrySpecs[countryCode];\n  if (spec === undefined || spec === null || spec.bban_regexp === undefined || spec.bban_regexp === null || spec.chars === undefined || spec.chars === null) return false;\n  if (spec.chars - 4 === bban.length && checkFormatBBAN(bban, spec.bban_regexp)) {\n    if (spec.bban_validation_func) {\n      return spec.bban_validation_func(bban.replace(/[\\s.]+/g, ''));\n    }\n    return true;\n  }\n  return false;\n}\n/**\n * Validate if country code is from a SEPA country\n * ```\n * // returns true\n * ibantools.isSEPACountry(\"NL\");\n * ```\n * ```\n * // returns false\n * ibantools.isSEPACountry(\"PK\");\n * ```\n */\nexport function isSEPACountry(countryCode) {\n  if (countryCode !== undefined && countryCode !== null) {\n    var spec = countrySpecs[countryCode];\n    if (spec !== undefined) {\n      return spec.SEPA ? spec.SEPA : false;\n    }\n  }\n  return false;\n}\n/**\n * Check if IBAN is QR-IBAN\n * ```\n * // returns true\n * ibantools.isQRIBAN(\"*********************\");\n * ```\n * ```\n * // returns false\n * ibantools.isQRIBAN(\"******************\");\n * ```\n */\nexport function isQRIBAN(iban) {\n  if (iban === undefined || iban === null) return false;\n  var countryCode = iban.slice(0, 2);\n  var QRIBANCountries = ['LI', 'CH'];\n  if (!QRIBANCountries.includes(countryCode)) return false;\n  var reg = new RegExp('^3[0-1]{1}[0-9]{3}$', '');\n  return reg.test(iban.slice(4, 9));\n}\n/**\n * composeIBAN\n *\n * ```\n * // returns ******************\n * ibantools.composeIBAN({ countryCode: \"NL\", bban: \"ABNA**********\" });\n * ```\n */\nexport function composeIBAN(params) {\n  var formated_bban = electronicFormatIBAN(params.bban) || '';\n  if (params.countryCode === null || params.countryCode === undefined) {\n    return null;\n  }\n  var spec = countrySpecs[params.countryCode];\n  if (formated_bban !== '' && spec !== undefined && spec.chars && spec.chars !== null && spec.chars === formated_bban.length + 4 && spec.bban_regexp && spec.bban_regexp !== null && checkFormatBBAN(formated_bban, spec.bban_regexp)) {\n    var checksom = mod9710Iban(params.countryCode + '00' + formated_bban);\n    return params.countryCode + ('0' + (98 - checksom)).slice(-2) + formated_bban;\n  }\n  return null;\n}\n/**\n * extractIBAN\n * ```\n * // returns {iban: \"******************\", bban: \"ABNA**********\", countryCode: \"NL\", valid: true, accountNumber: '**********', bankIdentifier: 'ABNA'}\n * ibantools.extractIBAN(\"NL91 ABNA 0417 1643 00\");\n * ```\n */\nexport function extractIBAN(iban) {\n  var result = {};\n  var eFormatIBAN = electronicFormatIBAN(iban);\n  result.iban = eFormatIBAN || iban;\n  if (!!eFormatIBAN && isValidIBAN(eFormatIBAN)) {\n    result.bban = eFormatIBAN.slice(4);\n    result.countryCode = eFormatIBAN.slice(0, 2);\n    result.valid = true;\n    var spec = countrySpecs[result.countryCode];\n    if (spec.account_indentifier) {\n      var ac = spec.account_indentifier.split('-');\n      var starting = parseInt(ac[0]);\n      var ending = parseInt(ac[1]);\n      result.accountNumber = result.iban.slice(starting, ending + 1);\n    }\n    if (spec.bank_identifier) {\n      var ac = spec.bank_identifier.split('-');\n      var starting = parseInt(ac[0]);\n      var ending = parseInt(ac[1]);\n      result.bankIdentifier = result.bban.slice(starting, ending + 1);\n    }\n    if (spec.branch_indentifier) {\n      var ac = spec.branch_indentifier.split('-');\n      var starting = parseInt(ac[0]);\n      var ending = parseInt(ac[1]);\n      result.branchIdentifier = result.bban.slice(starting, ending + 1);\n    }\n  } else {\n    result.valid = false;\n  }\n  return result;\n}\n/**\n * Check BBAN format\n *\n * @ignore\n */\nfunction checkFormatBBAN(bban, bformat) {\n  var reg = new RegExp(bformat, '');\n  return reg.test(bban);\n}\n/**\n * Get IBAN in electronic format (no spaces)\n * IBAN validation is not performed.\n * When non-string value for IBAN is provided, returns null.\n * ```\n * // returns \"******************\"\n * ibantools.electronicFormatIBAN(\"NL91 ABNA 0417 1643 00\");\n * ```\n */\nexport function electronicFormatIBAN(iban) {\n  if (typeof iban !== 'string') {\n    return null;\n  }\n  return iban.replace(/[-\\ ]/g, '').toUpperCase();\n}\n/**\n * Get IBAN in friendly format (separated after every 4 characters)\n * IBAN validation is not performed.\n * When non-string value for IBAN is provided, returns null.\n * ```\n * // returns \"NL91 ABNA 0417 1643 00\"\n * ibantools.friendlyFormatIBAN(\"******************\");\n * ```\n * ```\n * // returns \"NL91-ABNA-0417-1643-00\"\n * ibantools.friendlyFormatIBAN(\"******************\",\"-\");\n * ```\n */\nexport function friendlyFormatIBAN(iban, separator) {\n  if (typeof iban !== 'string') {\n    return null;\n  }\n  if (separator === undefined || separator === null) {\n    separator = ' ';\n  }\n  var electronic_iban = electronicFormatIBAN(iban);\n  /* istanbul ignore if */\n  if (electronic_iban === null) {\n    return null;\n  }\n  return electronic_iban.replace(/(.{4})(?!$)/g, '$1' + separator);\n}\n/**\n * Calculate checksum of IBAN and compares it with checksum provided in IBAN Registry\n *\n * @ignore\n */\nfunction isValidIBANChecksum(iban) {\n  var countryCode = iban.slice(0, 2);\n  var providedChecksum = parseInt(iban.slice(2, 4), 10);\n  var bban = iban.slice(4);\n  // Wikipedia[validating_iban] says there are a specif way to check if a IBAN is valid but\n  // it. It says 'If the remainder is 1, the check digit test is passed and the\n  // IBAN might be valid.'. might, MIGHT!\n  // We don't want might but want yes or no. Since every BBAN is IBAN from the fifth\n  // (slice(4)) we can generate the IBAN from BBAN and country code(two first characters)\n  // from in the IBAN.\n  // To generate the (generate the iban check digits)[generating-iban-check]\n  //   Move the country code to the end\n  //   remove the checksum from the begging\n  //   Add \"00\" to the end\n  //   modulo 97 on the amount\n  //   subtract remainder from 98, (98 - remainder)\n  //   Add a leading 0 if the remainder is less then 10 (padStart(2, \"0\")) (we skip this\n  //     since we compare int, not string)\n  //\n  // [validating_iban][https://en.wikipedia.org/wiki/International_Bank_Account_Number#Validating_the_IBAN]\n  // [generating-iban-check][https://en.wikipedia.org/wiki/International_Bank_Account_Number#Generating_IBAN_check_digits]\n  var validationString = replaceCharaterWithCode(\"\".concat(bban).concat(countryCode, \"00\"));\n  var rest = mod9710(validationString);\n  return 98 - rest === providedChecksum;\n}\n/**\n * Iban contain characters and should be converted to intereger by 55 substracted\n * from there ascii value\n *\n * @ignore\n */\nfunction replaceCharaterWithCode(str) {\n  // It is slower but alot more readable\n  // https://jsbench.me/ttkzgsekae/1\n  return str.split('').map(function (c) {\n    var code = c.charCodeAt(0);\n    return code >= 65 ? (code - 55).toString() : c;\n  }).join('');\n}\n/**\n * MOD-97-10\n *\n * @ignore\n */\nfunction mod9710Iban(iban) {\n  return mod9710(replaceCharaterWithCode(iban.slice(4) + iban.slice(0, 4)));\n}\n/**\n * Returns specifications for all countries, even those who are not\n * members of IBAN registry. `IBANRegistry` field indicates if country\n * is member of not.\n *\n * ```\n * // Validating IBAN form field after user selects his country\n * // <select id=\"countries\">\n * //   ...\n * //   <option value=\"NL\">Netherlands</option>\n * //   ...\n * // </select>\n * $(\"#countries\").select(function() {\n *   // Find country\n *   let country = ibantools.getCountrySpecifications()[$(this).val()];\n *   // Add country code letters to IBAN form field\n *   $(\"input#iban\").value($(this).val());\n *   // Add New value to \"pattern\" attribute to #iban input text field\n *   $(\"input#iban\").attr(\"pattern\", $(this).val() + \"[0-9]{2}\" + country.bban_regexp.slice(1).replace(\"$\",\"\"));\n * });\n * ```\n */\nexport function getCountrySpecifications() {\n  var countyMap = {};\n  for (var countyCode in countrySpecs) {\n    var county = countrySpecs[countyCode];\n    countyMap[countyCode] = {\n      chars: county.chars || null,\n      bban_regexp: county.bban_regexp || null,\n      IBANRegistry: county.IBANRegistry || false,\n      SEPA: county.SEPA || false\n    };\n  }\n  return countyMap;\n}\n/**\n * Validate BIC/SWIFT\n *\n * ```\n * // returns true\n * ibantools.isValidBIC(\"ABNANL2A\");\n *\n * // returns true\n * ibantools.isValidBIC(\"NEDSZAJJXXX\");\n *\n * // returns false\n * ibantools.isValidBIC(\"ABN4NL2A\");\n *\n * // returns false\n * ibantools.isValidBIC(\"ABNA NL 2A\");\n * ```\n */\nexport function isValidBIC(bic) {\n  if (!bic) {\n    return false;\n  }\n  var reg = new RegExp('^[a-zA-Z]{6}[a-zA-Z0-9]{2}([a-zA-Z0-9]{3})?$', '');\n  var spec = countrySpecs[bic.toUpperCase().slice(4, 6)];\n  return reg.test(bic) && spec !== undefined;\n}\n/**\n * BIC validation errors\n */\nexport var ValidationErrorsBIC;\n(function (ValidationErrorsBIC) {\n  ValidationErrorsBIC[ValidationErrorsBIC[\"NoBICProvided\"] = 0] = \"NoBICProvided\";\n  ValidationErrorsBIC[ValidationErrorsBIC[\"NoBICCountry\"] = 1] = \"NoBICCountry\";\n  ValidationErrorsBIC[ValidationErrorsBIC[\"WrongBICFormat\"] = 2] = \"WrongBICFormat\";\n})(ValidationErrorsBIC || (ValidationErrorsBIC = {}));\n/**\n * validateBIC\n * ```\n * // returns {errorCodes: [], valid: true}\n * ibantools.validateBIC(\"NEDSZAJJXXX\");\n * ```\n */\nexport function validateBIC(bic) {\n  var result = {\n    errorCodes: [],\n    valid: true\n  };\n  if (bic !== undefined && bic !== null && bic !== '') {\n    var spec = countrySpecs[bic.toUpperCase().slice(4, 6)];\n    if (spec === undefined) {\n      result.valid = false;\n      result.errorCodes.push(ValidationErrorsBIC.NoBICCountry);\n    } else {\n      var reg = new RegExp('^[a-zA-Z]{6}[a-zA-Z0-9]{2}([a-zA-Z0-9]{3})?$', '');\n      if (!reg.test(bic)) {\n        result.valid = false;\n        result.errorCodes.push(ValidationErrorsBIC.WrongBICFormat);\n      }\n    }\n  } else {\n    result.valid = false;\n    result.errorCodes.push(ValidationErrorsBIC.NoBICProvided);\n  }\n  return result;\n}\n/**\n * extractBIC\n * ```\n * // returns {bankCode: \"ABNA\", countryCode: \"NL\", locationCode: \"2A\", branchCode: null, testBIC: false, valid: true}\n * ibantools.extractBIC(\"ABNANL2A\");\n * ```\n */\nexport function extractBIC(inputBic) {\n  var result = {};\n  var bic = inputBic.toUpperCase();\n  if (isValidBIC(bic)) {\n    result.bankCode = bic.slice(0, 4);\n    result.countryCode = bic.slice(4, 6);\n    result.locationCode = bic.slice(6, 8);\n    result.testBIC = result.locationCode[1] === '0' ? true : false;\n    result.branchCode = bic.length > 8 ? bic.slice(8) : null;\n    result.valid = true;\n  } else {\n    result.valid = false;\n  }\n  return result;\n}\n/**\n * Used for Norway BBAN check\n *\n * @ignore\n */\nvar checkNorwayBBAN = function (bban) {\n  var weights = [5, 4, 3, 2, 7, 6, 5, 4, 3, 2];\n  var bbanWithoutSpacesAndPeriods = bban.replace(/[\\s.]+/g, '');\n  var controlDigit = parseInt(bbanWithoutSpacesAndPeriods.charAt(10), 10);\n  var bbanWithoutControlDigit = bbanWithoutSpacesAndPeriods.substring(0, 10);\n  var sum = 0;\n  for (var index = 0; index < 10; index++) {\n    sum += parseInt(bbanWithoutControlDigit.charAt(index), 10) * weights[index];\n  }\n  var remainder = sum % 11;\n  return controlDigit === (remainder === 0 ? 0 : 11 - remainder);\n};\n/**\n * Used for Belgian BBAN check\n *\n * @ignore\n */\nvar checkBelgianBBAN = function (bban) {\n  var stripped = bban.replace(/[\\s.]+/g, '');\n  var checkingPart = parseInt(stripped.substring(0, stripped.length - 2), 10);\n  var checksum = parseInt(stripped.substring(stripped.length - 2, stripped.length), 10);\n  var remainder = checkingPart % 97 === 0 ? 97 : checkingPart % 97;\n  return remainder === checksum;\n};\n/**\n * Mod 97/10 calculation\n *\n * @ignore\n */\nvar mod9710 = function (validationString) {\n  while (validationString.length > 2) {\n    // > Any computer programming language or software package that is used to compute D\n    // > mod 97 directly must have the ability to handle integers of more than 30 digits.\n    // > In practice, this can only be done by software that either supports\n    // > arbitrary-precision arithmetic or that can handle 219-bit (unsigned) integers\n    // https://en.wikipedia.org/wiki/International_Bank_Account_Number#Modulo_operation_on_IBAN\n    var part = validationString.slice(0, 6);\n    var partInt = parseInt(part, 10);\n    if (isNaN(partInt)) {\n      return NaN;\n    }\n    validationString = partInt % 97 + validationString.slice(part.length);\n  }\n  return parseInt(validationString, 10) % 97;\n};\n/**\n * Check BBAN based on Mod97/10 calculation for countries that support it:\n * BA, ME, MK, PT, RS, SI\n *\n * @ignore\n */\nvar checkMod9710BBAN = function (bban) {\n  var stripped = bban.replace(/[\\s.]+/g, '');\n  var reminder = mod9710(stripped);\n  return reminder === 1;\n};\n/**\n * Used for Poland BBAN check\n *\n * @ignore\n */\nvar checkPolandBBAN = function (bban) {\n  var weights = [3, 9, 7, 1, 3, 9, 7];\n  var controlDigit = parseInt(bban.charAt(7), 10);\n  var toCheck = bban.substring(0, 7);\n  var sum = 0;\n  for (var index = 0; index < 7; index++) {\n    sum += parseInt(toCheck.charAt(index), 10) * weights[index];\n  }\n  var remainder = sum % 10;\n  return controlDigit === (remainder === 0 ? 0 : 10 - remainder);\n};\n/**\n * Spain (ES) BBAN check\n *\n * @ignore\n */\nvar checkSpainBBAN = function (bban) {\n  var weightsBankBranch = [4, 8, 5, 10, 9, 7, 3, 6];\n  var weightsAccount = [1, 2, 4, 8, 5, 10, 9, 7, 3, 6];\n  var controlBankBranch = parseInt(bban.charAt(8), 10);\n  var controlAccount = parseInt(bban.charAt(9), 10);\n  var bankBranch = bban.substring(0, 8);\n  var account = bban.substring(10, 20);\n  var sum = 0;\n  for (var index = 0; index < 8; index++) {\n    sum += parseInt(bankBranch.charAt(index), 10) * weightsBankBranch[index];\n  }\n  var remainder = sum % 11;\n  if (controlBankBranch !== (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder)) {\n    return false;\n  }\n  sum = 0;\n  for (var index = 0; index < 10; index++) {\n    sum += parseInt(account.charAt(index), 10) * weightsAccount[index];\n  }\n  remainder = sum % 11;\n  return controlAccount === (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder);\n};\n/**\n * Mod 11/10 check\n *\n * @ignore\n */\nvar checkMod1110 = function (toCheck, control) {\n  var nr = 10;\n  for (var index = 0; index < toCheck.length; index++) {\n    nr += parseInt(toCheck.charAt(index), 10);\n    if (nr % 10 !== 0) {\n      nr = nr % 10;\n    }\n    nr = nr * 2;\n    nr = nr % 11;\n  }\n  return control === (11 - nr === 10 ? 0 : 11 - nr);\n};\n/**\n * Croatian (HR) BBAN check\n *\n * @ignore\n */\nvar checkCroatianBBAN = function (bban) {\n  var controlBankBranch = parseInt(bban.charAt(6), 10);\n  var controlAccount = parseInt(bban.charAt(16), 10);\n  var bankBranch = bban.substring(0, 6);\n  var account = bban.substring(7, 16);\n  return checkMod1110(bankBranch, controlBankBranch) && checkMod1110(account, controlAccount);\n};\n/**\n * Czech (CZ) and Slowak (SK) BBAN check\n *\n * @ignore\n */\nvar checkCzechAndSlovakBBAN = function (bban) {\n  var weightsPrefix = [10, 5, 8, 4, 2, 1];\n  var weightsSuffix = [6, 3, 7, 9, 10, 5, 8, 4, 2, 1];\n  var controlPrefix = parseInt(bban.charAt(9), 10);\n  var controlSuffix = parseInt(bban.charAt(19), 10);\n  var prefix = bban.substring(4, 9);\n  var suffix = bban.substring(10, 19);\n  var sum = 0;\n  for (var index = 0; index < prefix.length; index++) {\n    sum += parseInt(prefix.charAt(index), 10) * weightsPrefix[index];\n  }\n  var remainder = sum % 11;\n  if (controlPrefix !== (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder)) {\n    return false;\n  }\n  sum = 0;\n  for (var index = 0; index < suffix.length; index++) {\n    sum += parseInt(suffix.charAt(index), 10) * weightsSuffix[index];\n  }\n  remainder = sum % 11;\n  return controlSuffix === (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder);\n};\n/**\n * Estonian (EE) BBAN check\n *\n * @ignore\n */\nvar checkEstonianBBAN = function (bban) {\n  var weights = [7, 1, 3, 7, 1, 3, 7, 1, 3, 7, 1, 3, 7];\n  var controlDigit = parseInt(bban.charAt(15), 10);\n  var toCheck = bban.substring(2, 15);\n  var sum = 0;\n  for (var index = 0; index < toCheck.length; index++) {\n    sum += parseInt(toCheck.charAt(index), 10) * weights[index];\n  }\n  var remainder = sum % 10;\n  return controlDigit === (remainder === 0 ? 0 : 10 - remainder);\n};\n/**\n * Check French (FR) BBAN\n * Also for Monaco (MC)\n *\n * @ignore\n */\nvar checkFrenchBBAN = function (bban) {\n  var stripped = bban.replace(/[\\s.]+/g, '');\n  var normalized = Array.from(stripped);\n  for (var index = 0; index < stripped.length; index++) {\n    var c = normalized[index].charCodeAt(0);\n    if (c >= 65) {\n      switch (c) {\n        case 65:\n        case 74:\n          normalized[index] = '1';\n          break;\n        case 66:\n        case 75:\n        case 83:\n          normalized[index] = '2';\n          break;\n        case 67:\n        case 76:\n        case 84:\n          normalized[index] = '3';\n          break;\n        case 68:\n        case 77:\n        case 85:\n          normalized[index] = '4';\n          break;\n        case 69:\n        case 78:\n        case 86:\n          normalized[index] = '5';\n          break;\n        case 70:\n        case 79:\n        case 87:\n          normalized[index] = '6';\n          break;\n        case 71:\n        case 80:\n        case 88:\n          normalized[index] = '7';\n          break;\n        case 72:\n        case 81:\n        case 89:\n          normalized[index] = '8';\n          break;\n        case 73:\n        case 82:\n        case 90:\n          normalized[index] = '9';\n          break;\n      }\n    }\n  }\n  var remainder = mod9710(normalized.join(''));\n  return remainder === 0;\n};\n/**\n * Hungarian (HU) BBAN check\n *\n * @ignore\n */\nvar checkHungarianBBAN = function (bban) {\n  var weights = [9, 7, 3, 1, 9, 7, 3, 1, 9, 7, 3, 1, 9, 7, 3];\n  var controlDigitBankBranch = parseInt(bban.charAt(7), 10);\n  var toCheckBankBranch = bban.substring(0, 7);\n  var sum = 0;\n  for (var index = 0; index < toCheckBankBranch.length; index++) {\n    sum += parseInt(toCheckBankBranch.charAt(index), 10) * weights[index];\n  }\n  var remainder = sum % 10;\n  if (controlDigitBankBranch !== (remainder === 0 ? 0 : 10 - remainder)) {\n    return false;\n  }\n  sum = 0;\n  if (bban.endsWith('********')) {\n    var toCheckAccount = bban.substring(8, 15);\n    var controlDigitAccount = parseInt(bban.charAt(15), 10);\n    for (var index = 0; index < toCheckAccount.length; index++) {\n      sum += parseInt(toCheckAccount.charAt(index), 10) * weights[index];\n    }\n    var remainder_1 = sum % 10;\n    return controlDigitAccount === (remainder_1 === 0 ? 0 : 10 - remainder_1);\n  } else {\n    var toCheckAccount = bban.substring(8, 23);\n    var controlDigitAccount = parseInt(bban.charAt(23), 10);\n    for (var index = 0; index < toCheckAccount.length; index++) {\n      sum += parseInt(toCheckAccount.charAt(index), 10) * weights[index];\n    }\n    var remainder_2 = sum % 10;\n    return controlDigitAccount === (remainder_2 === 0 ? 0 : 10 - remainder_2);\n  }\n};\n/**\n * Set custom BBAN validation function for country.\n *\n * If `bban_validation_func` already exists for the corresponding country,\n * it will be overwritten.\n */\nexport var setCountryBBANValidation = function (country, func) {\n  if (typeof countrySpecs[country] === 'undefined') {\n    return false;\n  }\n  countrySpecs[country].bban_validation_func = func;\n  return true;\n};\n/**\n * Country specifications\n */\nexport var countrySpecs = {\n  AD: {\n    chars: 24,\n    bban_regexp: '^[0-9]{8}[A-Z0-9]{12}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-7',\n    bank_identifier: '0-3',\n    account_indentifier: '8-24'\n  },\n  AE: {\n    chars: 23,\n    bban_regexp: '^[0-9]{3}[0-9]{16}$',\n    IBANRegistry: true,\n    bank_identifier: '0-2',\n    account_indentifier: '7-23'\n  },\n  AF: {},\n  AG: {},\n  AI: {},\n  AL: {\n    chars: 28,\n    bban_regexp: '^[0-9]{8}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    branch_indentifier: '3-7',\n    bank_identifier: '0-2',\n    account_indentifier: '12-28'\n  },\n  AM: {},\n  AO: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$'\n  },\n  AQ: {},\n  AR: {},\n  AS: {},\n  AT: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-4'\n  },\n  AU: {},\n  AW: {},\n  AX: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true\n  },\n  AZ: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{20}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '4-28'\n  },\n  BA: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    branch_indentifier: '3-5',\n    bank_identifier: '0-2'\n  },\n  BB: {},\n  BD: {},\n  BE: {\n    chars: 16,\n    bban_regexp: '^[0-9]{12}$',\n    bban_validation_func: checkBelgianBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-2',\n    account_indentifier: '0-16'\n  },\n  BF: {\n    chars: 28,\n    bban_regexp: '^[A-Z0-9]{2}[0-9]{22}$'\n  },\n  BG: {\n    chars: 22,\n    bban_regexp: '^[A-Z]{4}[0-9]{6}[A-Z0-9]{8}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-7',\n    bank_identifier: '0-3'\n  },\n  BH: {\n    chars: 22,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{14}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-22'\n  },\n  BI: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$',\n    branch_indentifier: '5-9',\n    bank_identifier: '0-4',\n    account_indentifier: '14-27'\n  },\n  BJ: {\n    chars: 28,\n    bban_regexp: '^[A-Z0-9]{2}[0-9]{22}$'\n  },\n  BL: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$'\n  },\n  BM: {},\n  BN: {},\n  BO: {},\n  BQ: {},\n  BR: {\n    chars: 29,\n    bban_regexp: '^[0-9]{23}[A-Z]{1}[A-Z0-9]{1}$',\n    IBANRegistry: true,\n    branch_indentifier: '8-12',\n    bank_identifier: '0-7',\n    account_indentifier: '17-29'\n  },\n  BS: {},\n  BT: {},\n  BV: {},\n  BW: {},\n  BY: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[0-9]{4}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3'\n  },\n  BZ: {},\n  CA: {},\n  CC: {},\n  CD: {},\n  CF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  CG: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  CH: {\n    chars: 21,\n    bban_regexp: '^[0-9]{5}[A-Z0-9]{12}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-4'\n  },\n  CI: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{1}[0-9]{23}$'\n  },\n  CK: {},\n  CL: {},\n  CM: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  CN: {},\n  CO: {},\n  CR: {\n    chars: 22,\n    bban_regexp: '^[0-9]{18}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-22'\n  },\n  CU: {},\n  CV: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$'\n  },\n  CW: {},\n  CX: {},\n  CY: {\n    chars: 28,\n    bban_regexp: '^[0-9]{8}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '3-7',\n    bank_identifier: '0-2',\n    account_indentifier: '12-28'\n  },\n  CZ: {\n    chars: 24,\n    bban_regexp: '^[0-9]{20}$',\n    bban_validation_func: checkCzechAndSlovakBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3'\n  },\n  DE: {\n    chars: 22,\n    bban_regexp: '^[0-9]{18}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-7',\n    account_indentifier: '13-22'\n  },\n  DJ: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$',\n    branch_indentifier: '5-9',\n    bank_identifier: '0-4',\n    account_indentifier: '14-27'\n  },\n  DK: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '4-18'\n  },\n  DM: {},\n  DO: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[0-9]{20}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-28'\n  },\n  DZ: {\n    chars: 26,\n    bban_regexp: '^[0-9]{22}$'\n  },\n  EC: {},\n  EE: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    bban_validation_func: checkEstonianBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-1',\n    account_indentifier: '8-20'\n  },\n  EG: {\n    chars: 29,\n    bban_regexp: '^[0-9]{25}',\n    IBANRegistry: true,\n    branch_indentifier: '4-7',\n    bank_identifier: '0-3',\n    account_indentifier: '17-29'\n  },\n  EH: {},\n  ER: {},\n  ES: {\n    chars: 24,\n    bban_validation_func: checkSpainBBAN,\n    bban_regexp: '^[0-9]{20}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-7',\n    bank_identifier: '0-3',\n    account_indentifier: '14-24'\n  },\n  ET: {},\n  FI: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-2',\n    account_indentifier: '0-0'\n  },\n  FJ: {},\n  FK: {\n    chars: 18,\n    bban_regexp: '^[A-Z]{2}[0-9]{12}$',\n    bank_identifier: '0-1',\n    account_indentifier: '6-18'\n  },\n  FM: {},\n  FO: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '4-18'\n  },\n  FR: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    bban_validation_func: checkFrenchBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-4',\n    branch_indentifier: '5-9',\n    account_indentifier: '14-24'\n  },\n  GA: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  GB: {\n    chars: 22,\n    bban_regexp: '^[A-Z]{4}[0-9]{14}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-9',\n    bank_identifier: '0-3'\n  },\n  GD: {},\n  GE: {\n    chars: 22,\n    bban_regexp: '^[A-Z0-9]{2}[0-9]{16}$',\n    IBANRegistry: true,\n    bank_identifier: '0-1',\n    account_indentifier: '6-22'\n  },\n  GF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  GG: {},\n  GH: {},\n  GI: {\n    chars: 23,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{15}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-23'\n  },\n  GL: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '4-18'\n  },\n  GM: {},\n  GN: {},\n  GP: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  GQ: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  GR: {\n    chars: 27,\n    bban_regexp: '^[0-9]{7}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '3-6',\n    bank_identifier: '0-2',\n    account_indentifier: '7-27'\n  },\n  GS: {},\n  GT: {\n    chars: 28,\n    bban_regexp: '^[A-Z0-9]{24}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-28'\n  },\n  GU: {},\n  GW: {\n    chars: 25,\n    bban_regexp: '^[A-Z]{2}[0-9]{19}$'\n  },\n  GY: {},\n  HK: {},\n  HM: {},\n  HN: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[0-9]{20}$'\n  },\n  HR: {\n    chars: 21,\n    bban_regexp: '^[0-9]{17}$',\n    bban_validation_func: checkCroatianBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-6'\n  },\n  HT: {},\n  HU: {\n    chars: 28,\n    bban_regexp: '^[0-9]{24}$',\n    bban_validation_func: checkHungarianBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '3-6',\n    bank_identifier: '0-2'\n  },\n  ID: {},\n  IE: {\n    chars: 22,\n    bban_regexp: '^[A-Z0-9]{4}[0-9]{14}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-9',\n    bank_identifier: '0-3'\n  },\n  IL: {\n    chars: 23,\n    bban_regexp: '^[0-9]{19}$',\n    IBANRegistry: true,\n    branch_indentifier: '3-5',\n    bank_identifier: '0-2'\n  },\n  IM: {},\n  IN: {},\n  IO: {},\n  IQ: {\n    chars: 23,\n    bban_regexp: '^[A-Z]{4}[0-9]{15}$',\n    IBANRegistry: true,\n    branch_indentifier: '4-6',\n    bank_identifier: '0-3',\n    account_indentifier: '11-23'\n  },\n  IR: {\n    chars: 26,\n    bban_regexp: '^[0-9]{22}$'\n  },\n  IS: {\n    chars: 26,\n    bban_regexp: '^[0-9]{22}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '2-3',\n    bank_identifier: '0-1'\n  },\n  IT: {\n    chars: 27,\n    bban_regexp: '^[A-Z]{1}[0-9]{10}[A-Z0-9]{12}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '6-10',\n    bank_identifier: '1-5',\n    account_indentifier: '4-27'\n  },\n  JE: {},\n  JM: {},\n  JO: {\n    chars: 30,\n    bban_regexp: '^[A-Z]{4}[0-9]{4}[A-Z0-9]{18}$',\n    IBANRegistry: true,\n    branch_indentifier: '4-7',\n    bank_identifier: '4-7'\n  },\n  JP: {},\n  KE: {},\n  KG: {},\n  KH: {},\n  KI: {},\n  KM: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  KN: {},\n  KP: {},\n  KR: {},\n  KW: {\n    chars: 30,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{22}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '20-30'\n  },\n  KY: {},\n  KZ: {\n    chars: 20,\n    bban_regexp: '^[0-9]{3}[A-Z0-9]{13}$',\n    IBANRegistry: true,\n    bank_identifier: '0-2',\n    account_indentifier: '0-20'\n  },\n  LA: {},\n  LB: {\n    chars: 28,\n    bban_regexp: '^[0-9]{4}[A-Z0-9]{20}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '14-28'\n  },\n  LC: {\n    chars: 32,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{24}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-32'\n  },\n  LI: {\n    chars: 21,\n    bban_regexp: '^[0-9]{5}[A-Z0-9]{12}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-4'\n  },\n  LK: {},\n  LR: {},\n  LS: {},\n  LT: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-4'\n  },\n  LU: {\n    chars: 20,\n    bban_regexp: '^[0-9]{3}[A-Z0-9]{13}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-2'\n  },\n  LV: {\n    chars: 21,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{13}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '0-21'\n  },\n  LY: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$',\n    IBANRegistry: true,\n    branch_indentifier: '3-5',\n    bank_identifier: '0-2',\n    account_indentifier: '10-25'\n  },\n  MA: {\n    chars: 28,\n    bban_regexp: '^[0-9]{24}$'\n  },\n  MC: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    bban_validation_func: checkFrenchBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '5-9',\n    bank_identifier: '0-4'\n  },\n  MD: {\n    chars: 24,\n    bban_regexp: '^[A-Z0-9]{2}[A-Z0-9]{18}$',\n    IBANRegistry: true,\n    bank_identifier: '0-1',\n    account_indentifier: '6-24'\n  },\n  ME: {\n    chars: 22,\n    bban_regexp: '^[0-9]{18}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    bank_identifier: '0-2',\n    account_indentifier: '4-22'\n  },\n  MF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  MG: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  MH: {},\n  MK: {\n    chars: 19,\n    bban_regexp: '^[0-9]{3}[A-Z0-9]{10}[0-9]{2}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    bank_identifier: '0-2'\n  },\n  ML: {\n    chars: 28,\n    bban_regexp: '^[A-Z0-9]{2}[0-9]{22}$'\n  },\n  MM: {},\n  MN: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-20'\n  },\n  MO: {},\n  MP: {},\n  MQ: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  MR: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$',\n    IBANRegistry: true,\n    branch_indentifier: '5-9',\n    bank_identifier: '0-4',\n    account_indentifier: '4-27'\n  },\n  MS: {},\n  MT: {\n    chars: 31,\n    bban_regexp: '^[A-Z]{4}[0-9]{5}[A-Z0-9]{18}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '4-8',\n    bank_identifier: '0-3',\n    account_indentifier: '15-31'\n  },\n  MU: {\n    chars: 30,\n    bban_regexp: '^[A-Z]{4}[0-9]{19}[A-Z]{3}$',\n    IBANRegistry: true,\n    branch_indentifier: '6-7',\n    bank_identifier: '0-5',\n    account_indentifier: '0-30'\n  },\n  MV: {},\n  MW: {},\n  MX: {},\n  MY: {},\n  MZ: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$'\n  },\n  NA: {},\n  NC: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  NE: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{2}[0-9]{22}$'\n  },\n  NF: {},\n  NG: {},\n  NI: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[0-9]{20}$',\n    bank_identifier: '0-3',\n    IBANRegistry: true,\n    account_indentifier: '8-28'\n  },\n  NL: {\n    chars: 18,\n    bban_regexp: '^[A-Z]{4}[0-9]{10}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-18'\n  },\n  NO: {\n    chars: 15,\n    bban_regexp: '^[0-9]{11}$',\n    bban_validation_func: checkNorwayBBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '4-15'\n  },\n  NP: {},\n  NR: {},\n  NU: {},\n  NZ: {},\n  OM: {\n    chars: 23,\n    bban_regexp: '^[0-9]{3}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: false,\n    bank_identifier: '0-2'\n  },\n  PA: {},\n  PE: {},\n  PF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  PG: {},\n  PH: {},\n  PK: {\n    chars: 24,\n    bban_regexp: '^[A-Z0-9]{4}[0-9]{16}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3'\n  },\n  PL: {\n    chars: 28,\n    bban_validation_func: checkPolandBBAN,\n    bban_regexp: '^[0-9]{24}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '0-7',\n    account_indentifier: '2-28'\n  },\n  PM: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  PN: {},\n  PR: {},\n  PS: {\n    chars: 29,\n    bban_regexp: '^[A-Z0-9]{4}[0-9]{21}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '17-29'\n  },\n  PT: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3'\n  },\n  PW: {},\n  PY: {},\n  QA: {\n    chars: 29,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{21}$',\n    IBANRegistry: true,\n    bank_identifier: '0-3',\n    account_indentifier: '8-29'\n  },\n  RE: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  RO: {\n    chars: 24,\n    bban_regexp: '^[A-Z]{4}[A-Z0-9]{16}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-3',\n    account_indentifier: '0-24'\n  },\n  RS: {\n    chars: 22,\n    bban_regexp: '^[0-9]{18}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    bank_identifier: '0-2'\n  },\n  RU: {\n    chars: 33,\n    bban_regexp: '^[0-9]{14}[A-Z0-9]{15}$',\n    IBANRegistry: true,\n    branch_indentifier: '9-13',\n    bank_identifier: '0-8',\n    account_indentifier: '13-33'\n  },\n  RW: {},\n  SA: {\n    chars: 24,\n    bban_regexp: '^[0-9]{2}[A-Z0-9]{18}$',\n    IBANRegistry: true,\n    bank_identifier: '0-1',\n    account_indentifier: '12-24'\n  },\n  SB: {},\n  SC: {\n    chars: 31,\n    bban_regexp: '^[A-Z]{4}[0-9]{20}[A-Z]{3}$',\n    IBANRegistry: true,\n    branch_indentifier: '6-7',\n    bank_identifier: '0-5',\n    account_indentifier: '12-28'\n  },\n  SD: {\n    chars: 18,\n    bban_regexp: '^[0-9]{14}$',\n    IBANRegistry: true,\n    bank_identifier: '0-1',\n    account_indentifier: '6-18'\n  },\n  SE: {\n    chars: 24,\n    bban_regexp: '^[0-9]{20}$',\n    IBANRegistry: true,\n    SEPA: true,\n    bank_identifier: '0-2'\n  },\n  SG: {},\n  SH: {},\n  SI: {\n    chars: 19,\n    bban_regexp: '^[0-9]{15}$',\n    bban_validation_func: checkMod9710BBAN,\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '2-4',\n    bank_identifier: '0-1',\n    account_indentifier: '9-16'\n  },\n  SJ: {},\n  SK: {\n    chars: 24,\n    bban_regexp: '^[0-9]{20}$',\n    bban_validation_func: checkCzechAndSlovakBBAN,\n    IBANRegistry: true,\n    SEPA: true\n  },\n  SL: {},\n  SM: {\n    chars: 27,\n    bban_regexp: '^[A-Z]{1}[0-9]{10}[A-Z0-9]{12}$',\n    IBANRegistry: true,\n    SEPA: true,\n    branch_indentifier: '6-10'\n  },\n  SN: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{2}[0-9]{22}$'\n  },\n  SO: {\n    chars: 23,\n    bban_regexp: '^[0-9]{19}$',\n    IBANRegistry: true,\n    branch_indentifier: '4-6',\n    account_indentifier: '11-23'\n  },\n  SR: {},\n  SS: {},\n  ST: {\n    chars: 25,\n    bban_regexp: '^[0-9]{21}$',\n    IBANRegistry: true,\n    branch_indentifier: '4-7'\n  },\n  SV: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{4}[0-9]{20}$',\n    IBANRegistry: true,\n    account_indentifier: '8-28'\n  },\n  SX: {},\n  SY: {},\n  SZ: {},\n  TC: {},\n  TD: {\n    chars: 27,\n    bban_regexp: '^[0-9]{23}$'\n  },\n  TF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  TG: {\n    chars: 28,\n    bban_regexp: '^[A-Z]{2}[0-9]{22}$'\n  },\n  TH: {},\n  TJ: {},\n  TK: {},\n  TL: {\n    chars: 23,\n    bban_regexp: '^[0-9]{19}$',\n    IBANRegistry: true,\n    account_indentifier: '4-23'\n  },\n  TM: {},\n  TN: {\n    chars: 24,\n    bban_regexp: '^[0-9]{20}$',\n    IBANRegistry: true,\n    branch_indentifier: '2-4',\n    account_indentifier: '4-24'\n  },\n  TO: {},\n  TR: {\n    chars: 26,\n    bban_regexp: '^[0-9]{5}[A-Z0-9]{17}$',\n    IBANRegistry: true\n  },\n  TT: {},\n  TV: {},\n  TW: {},\n  TZ: {},\n  UA: {\n    chars: 29,\n    bban_regexp: '^[0-9]{6}[A-Z0-9]{19}$',\n    IBANRegistry: true,\n    account_indentifier: '15-29'\n  },\n  UG: {},\n  UM: {},\n  US: {},\n  UY: {},\n  UZ: {},\n  VA: {\n    chars: 22,\n    bban_regexp: '^[0-9]{18}',\n    IBANRegistry: true,\n    SEPA: true,\n    account_indentifier: '7-22'\n  },\n  VC: {},\n  VE: {},\n  VG: {\n    chars: 24,\n    bban_regexp: '^[A-Z0-9]{4}[0-9]{16}$',\n    IBANRegistry: true,\n    account_indentifier: '8-24'\n  },\n  VI: {},\n  VN: {},\n  VU: {},\n  WF: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  WS: {},\n  XK: {\n    chars: 20,\n    bban_regexp: '^[0-9]{16}$',\n    IBANRegistry: true,\n    branch_indentifier: '2-3',\n    account_indentifier: '4-20'\n  },\n  YE: {},\n  YT: {\n    chars: 27,\n    bban_regexp: '^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$',\n    IBANRegistry: true\n  },\n  ZA: {},\n  ZM: {},\n  ZW: {}\n};", "import * as i0 from '@angular/core';\nimport { Component, InjectionToken, Inject, Injectable, HostListener, Directive, EventEmitter, HostBinding, Input, Output, Pipe, Optional, NgModule, inject, ErrorHandler } from '@angular/core';\nimport * as i2 from '@angular/material/icon';\nimport { MatIconModule } from '@angular/material/icon';\nimport * as i3 from '@angular/material/tooltip';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport * as i1 from '@angular/cdk/clipboard';\nimport { DOCUMENT } from '@angular/common';\nimport * as i1$1 from '@angular/cdk/platform';\nimport { _isTestEnvironment } from '@angular/cdk/platform';\nimport { Subject, merge, NEVER, take, tap, catchError, startWith, switchMap, shareReplay, filter, finalize, of, forkJoin, ReplaySubject, skip, distinctUntilChanged, map as map$1, combineLatestWith, from, takeUntil } from 'rxjs';\nimport { OnemrvaMatColor } from '@onemrvapublic/design-system/utils';\nimport * as i1$2 from '@angular/cdk/layout';\nimport { catchError as catchError$1, map, startWith as startWith$1 } from 'rxjs/operators';\nimport * as i1$3 from '@angular/common/http';\nimport { HttpParams } from '@angular/common/http';\nimport { __decorate } from 'tslib';\nimport * as i1$4 from '@ngx-translate/core';\nimport * as i1$5 from '@angular/material/snack-bar';\nimport { countrySpecs, validateIBAN, electronicFormatIBAN, ValidationErrorsIBAN } from 'ibantools';\nimport { NativeDateAdapter, DateAdapter, MAT_DATE_LOCALE, MAT_DATE_FORMATS } from '@angular/material/core';\nimport { DateTime, Info } from 'luxon';\nimport { takeUntilDestroyed } from '@angular/core/rxjs-interop';\nimport { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';\nimport { OverlayContainer } from '@angular/cdk/overlay';\nclass ClipboardIconComponent {\n  constructor(_elementRef, clipboardService) {\n    this._elementRef = _elementRef;\n    this.clipboardService = clipboardService;\n  }\n  static {\n    this.ɵfac = function ClipboardIconComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || ClipboardIconComponent)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Clipboard));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: ClipboardIconComponent,\n      selectors: [[\"lib-clipboard-icon\"]],\n      decls: 2,\n      vars: 1,\n      consts: [[3, \"matTooltip\"]],\n      template: function ClipboardIconComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"mat-icon\", 0);\n          i0.ɵɵtext(1, \"content_copy\");\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"matTooltip\", \"TEST\");\n        }\n      },\n      dependencies: [MatIconModule, i2.MatIcon, MatTooltipModule, i3.MatTooltip],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ClipboardIconComponent, [{\n    type: Component,\n    args: [{\n      selector: 'lib-clipboard-icon',\n      standalone: true,\n      imports: [MatIconModule, MatTooltipModule],\n      template: \"<mat-icon [matTooltip]=\\\"'TEST'\\\">content_copy</mat-icon>\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Clipboard\n  }], null);\n})();\nvar CDNUrlModeOptions;\n(function (CDNUrlModeOptions) {\n  CDNUrlModeOptions[\"PROD\"] = \"production\";\n  CDNUrlModeOptions[\"VAL\"] = \"validation\";\n})(CDNUrlModeOptions || (CDNUrlModeOptions = {}));\nconst CDN_URLS = {\n  prod: 'https://cdn.services.rvaonem.fgov.be',\n  val: 'https://cdn.servicesval.rvaonem.fgov.be'\n};\nconst NISS_MASK = '000000/000-00';\nconst LOOKUP_COUNTRY_URL = 'https://services/lookupwpptservice/rest/lookup/getLookups?class=be.fgov.onerva.lookup.wppt.persistence.model.common.Country';\n// export const LOOKUP_POSTAL_CODE_URL =\n//   'https://services/lookupwpptservice/rest/lookup/getBelgianCommunityByPostalCode?postalCode=';\n\nconst parseNativeDateFormats = ['ddMMyyyy', 'dd/MM/yyyy', 'd/M/yyyy', 'dd/M/yyyy', 'd/MM/yyyy', 'd.M.yyyy', 'dd.M.yyyy', 'd.MM.yyyy', 'd/M/yy', 'dd/M/yy', 'd/MM/yy', 'd.M.yy', 'dd.M.yy', 'd.MM.yy'];\nconst parseNativeYearMonthFormats = ['MMyyyy', 'M/yyyy', 'M/yy', 'MM/yyyy', 'MM/yy', 'M.yyyy', 'MM.yyyy'];\nconst parseLuxonYearMonthFormats = ['LLyyyy', 'L/yyyy', 'L/yy', 'LL/yyyy', 'LL/yy', 'L.yyyy', 'LL.yyyy'];\nconst parseLuxonDateFormat = ['ddLLyyyy', 'd/LL/yyyy', 'd/L/yyyy', 'dd/L/yyyy', 'd/LL/yyyy', 'd.L.yyyy', 'dd.L.yyyy', 'd.LL.yyyy', 'd/L/yy', 'dd/L/yy', 'd/LL/yy', 'd.L.yy', 'dd.L.yy', 'd.LL.yy'];\nconst ONEMRVA_MAT_NATIVE_DATE_FORMAT = {\n  parse: {\n    dateInput: parseNativeDateFormats\n  },\n  display: {\n    dateInput: 'dd/MM/yyyy',\n    monthYearLabel: 'LL / yyyy',\n    dateA11yLabel: 'LL',\n    monthYearA11yLabel: 'MMMM yyyy'\n  }\n};\nconst ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT = {\n  parse: {\n    dateInput: parseNativeYearMonthFormats\n  },\n  display: {\n    dateInput: 'dd/MM/yyyy',\n    monthYearLabel: 'LL / yyyy',\n    dateA11yLabel: 'LL',\n    monthYearA11yLabel: 'MMM yyyy'\n  }\n};\nconst ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS = {\n  parse: {\n    dateInput: parseLuxonYearMonthFormats\n  },\n  display: {\n    dateInput: 'LL/yyyy',\n    monthYearLabel: 'LLL yyyy',\n    dateA11yLabel: 'DDD',\n    monthYearA11yLabel: 'LLLL yyyy'\n  }\n};\nconst ONEMRVA_MAT_LUXON_DATE_FORMATS = {\n  parse: {\n    dateInput: parseLuxonDateFormat\n  },\n  display: {\n    dateInput: 'dd/LL/yyyy',\n    monthYearLabel: 'LLL yyyy',\n    dateA11yLabel: 'DDD',\n    monthYearA11yLabel: 'LLL yyyy'\n  }\n};\nconst CDN_URL_MODE = new InjectionToken('This is cdn url that will be used (val/prod)');\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n/** Container inside which all overlays will render. */\nclass WebComponentOverlayContainer {\n  constructor(document, _platform) {\n    this._platform = _platform;\n    this._containerElement = null;\n    this._document = document;\n  }\n  ngOnDestroy() {\n    this._containerElement?.remove();\n  }\n  /**\n   * This method returns the overlay container element. It will lazily\n   * create the element the first time it is called to facilitate using\n   * the container in non-browser environments.\n   * @returns the container element\n   */\n  getContainerElement() {\n    if (this._containerElement === null) {\n      this._createContainer();\n    }\n    return this._containerElement;\n  }\n  /**\n   * Create the overlay container element, which is simply a div\n   * with the 'cdk-overlay-container' class on the document body.\n   */\n  _createContainer() {\n    const containerClass = 'cdk-overlay-container';\n    // module or Angular starts tearing down the testing `NgModule`. See:\n    // https://github.com/angular/angular/issues/18831\n    if (this._platform.isBrowser || _isTestEnvironment()) {\n      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform=\"server\"], ` + `.${containerClass}[platform=\"test\"]`);\n      // Remove any old containers from the opposite platform.\n      // This can happen when transitioning from the server to the client.\n      oppositePlatformContainers.forEach(platform => {\n        platform.remove();\n      });\n    }\n    const containerwrap = this._document.createElement('div');\n    containerwrap.classList.add('onemrva-theme');\n    const container = this._document.createElement('div');\n    container.classList.add(containerClass);\n    // A long time ago we kept adding new overlay containers whenever a new app was instantiated,\n    // but at some point we added logic which clears the duplicate ones in order to avoid leaks.\n    // The new logic was a little too aggressive since it was breaking some legitimate use cases.\n    // To mitigate the problem we made it so that only containers from a different platform are\n    // cleared, but the side-effect was that people started depending on the overly-aggressive\n    // logic to clean up their tests for them. Until we can introduce an overlay-specific testing\n    // module which does the cleanup, we try to detect that we're in a test environment and we\n    // always clear the container. See #17006.\n    if (_isTestEnvironment()) {\n      container.setAttribute('platform', 'test');\n    } else if (!this._platform.isBrowser) {\n      container.setAttribute('platform', 'server');\n    }\n    containerwrap.appendChild(container);\n    this._document.body.appendChild(containerwrap);\n    this._containerElement = container;\n  }\n  static {\n    this.ɵfac = function WebComponentOverlayContainer_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || WebComponentOverlayContainer)(i0.ɵɵinject(DOCUMENT), i0.ɵɵinject(i1$1.Platform));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: WebComponentOverlayContainer,\n      factory: WebComponentOverlayContainer.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(WebComponentOverlayContainer, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i1$1.Platform\n  }], null);\n})();\nclass DefaultStorage {\n  constructor() {\n    this.storage = new Map();\n  }\n  getItem(key) {\n    return this.storage.get(key);\n  }\n  setItem(key, item) {\n    this.storage.set(key, item);\n  }\n  deleteItem(key) {\n    this.storage.delete(key);\n  }\n}\nclass RequestTimes {\n  constructor() {\n    this.storage = new Map();\n  }\n  getItem(key) {\n    return this.storage.get(key);\n  }\n  setItem(key, item) {\n    this.storage.set(key, item);\n  }\n  deleteItem(key) {\n    this.storage.delete(key);\n  }\n}\nconst HttpRequestCache = optionsHandler => {\n  return (target, methodName, descriptor) => {\n    if (!(descriptor?.value instanceof Function)) {\n      throw Error(`'@HttpRequestCache' can be applied only to the class method which returns an Observable`);\n    }\n    const cacheKeyPrefix = `${target.constructor.name}_${methodName}`;\n    const originalMethod = descriptor.value;\n    const working = {};\n    let subscribers = 0;\n    descriptor.value = function (...args) {\n      const options = optionsHandler?.call(this, this, ...args);\n      if (!options?.storage && !target._____storage_____) {\n        target._____storage_____ = new DefaultStorage();\n      }\n      if (options?.ttl && !target._____ttl_storage_____) {\n        target._____ttl_storage_____ = new RequestTimes();\n      }\n      const storage = options?.storage ?? target._____storage_____;\n      const key = `${cacheKeyPrefix}_${JSON.stringify(args)}`;\n      let ttl = undefined;\n      if (options?.ttl) {\n        ttl = target._____ttl_storage_____.getItem(key);\n        if (!ttl) {\n          ttl = {\n            requestTime: Date.now(),\n            subject: new Subject()\n          };\n        } else if (ttl.requestTime + options.ttl <= Date.now()) {\n          working[key] = true;\n          ttl.requestTime = Date.now();\n          ttl.subject.next();\n        }\n        target._____ttl_storage_____.setItem(key, ttl);\n      }\n      const refreshOn = merge(options?.refreshOn ?? NEVER, ttl?.subject ?? NEVER);\n      return storage.getItem(key).pipe(take(1), tap(value => {\n        subscribers++;\n        if (value === null) throw '';\n      }), catchError(() => {\n        const observable = refreshOn.pipe(startWith(true), switchMap(() => originalMethod.apply(this, [...args])), tap(() => {\n          delete working[key];\n        }), shareReplay({\n          bufferSize: 1,\n          refCount: options?.refCount ?? false,\n          windowTime: options?.windowTime ?? Infinity\n        }), filter(() => {\n          return !working[key];\n        }), finalize(() => {\n          subscribers--;\n          if (subscribers === 0 && options?.refCount) {\n            storage.deleteItem(key);\n            target._____ttl_storage_____?.deleteItem(key);\n          }\n        }));\n        storage.setItem(key, observable);\n        return observable;\n      }));\n      // let observable = storage.getItem(key);\n      //\n      // if (!observable) {\n      //\n      //   observable = refreshOn.pipe(\n      //     startWith(true),\n      //     switchMap(() => originalMethod.apply(this, [...args])),\n      //     tap(() => {\n      //       delete working[key];\n      //     }),\n      //     shareReplay({\n      //       bufferSize: 1,\n      //       refCount: options?.refCount ?? false,\n      //       windowTime: options?.windowTime ?? Infinity,\n      //     }),\n      //     filter(() => {\n      //       return !working[key];\n      //     }),\n      //     finalize(() => {\n      //       subscribers--;\n      //       if (subscribers === 0 && options?.refCount) {\n      //         storage.deleteItem(key);\n      //         (target as any)._____ttl_storage_____?.deleteItem(key);\n      //       }\n      //     })\n      //   );\n      //   storage.setItem(key, observable);\n      // }\n      //      subscribers++;\n      //      return observable;\n    };\n    return descriptor;\n  };\n};\nclass DigitOnlyDirective {\n  onKeyDown(event) {\n    const e = event;\n    const allowedKey = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', ',', ' ', 'Backspace', 'Delete', 'Tab', '-', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];\n    if (allowedKey.indexOf(e.key) >= 0) return;\n    // HWKTODO manage ctrl Z and ctrl U\n    const allowedCtrlShortcuts = ['a', 'c', 'x', 'v'];\n    if (e.ctrlKey && allowedCtrlShortcuts.indexOf(e.key) >= 0) return;\n    e.preventDefault();\n  }\n  static {\n    this.ɵfac = function DigitOnlyDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || DigitOnlyDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: DigitOnlyDirective,\n      selectors: [[\"\", \"digitOnly\", \"\"]],\n      hostBindings: function DigitOnlyDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function DigitOnlyDirective_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          });\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DigitOnlyDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[digitOnly]',\n      standalone: true\n    }]\n  }], null, {\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }]\n  });\n})();\nclass MatRowClickableDirective {\n  constructor() {\n    this.matRowClickable = new EventEmitter();\n    this.color = '';\n    this.cssClass = true;\n  }\n  click() {\n    this.matRowClickable.emit();\n  }\n  /** @hidden @internal */\n  get _isClickable() {\n    return this.matRowClickable.observed;\n  }\n  /** @hidden @internal */\n  get _isPrimary() {\n    return this.color === OnemrvaMatColor.PRIMARY;\n  }\n  /** @hidden @internal */\n  get _isAccent() {\n    return this.color === OnemrvaMatColor.ACCENT;\n  }\n  /** @hidden @internal */\n  get _isError() {\n    return this.color === OnemrvaMatColor.ERROR;\n  }\n  /** @hidden @internal */\n  get _isWarn() {\n    return this.color === OnemrvaMatColor.WARN;\n  }\n  /** @hidden @internal */\n  get _isSuccess() {\n    return this.color === OnemrvaMatColor.SUCCESS;\n  }\n  /** @hidden @internal */\n  get _isInfo() {\n    return this.color === OnemrvaMatColor.INFO;\n  }\n  static {\n    this.ɵfac = function MatRowClickableDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || MatRowClickableDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatRowClickableDirective,\n      selectors: [[\"tr\", \"matRowClickable\", \"\"]],\n      hostVars: 16,\n      hostBindings: function MatRowClickableDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function MatRowClickableDirective_click_HostBindingHandler($event) {\n            return ctx.click($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"clickable\", ctx._isClickable)(\"onemrva-clickable-row\", ctx.cssClass)(\"mat-primary\", ctx._isPrimary)(\"mat-accent\", ctx._isAccent)(\"mat-error\", ctx._isError)(\"mat-warn\", ctx._isWarn)(\"mat-success\", ctx._isSuccess)(\"mat-info\", ctx._isInfo);\n        }\n      },\n      inputs: {\n        color: \"color\"\n      },\n      outputs: {\n        matRowClickable: \"matRowClickable\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatRowClickableDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'tr[matRowClickable]',\n      standalone: true\n    }]\n  }], null, {\n    matRowClickable: [{\n      type: Output\n    }],\n    color: [{\n      type: Input\n    }],\n    click: [{\n      type: HostListener,\n      args: ['click', ['$event']]\n    }],\n    _isClickable: [{\n      type: HostBinding,\n      args: ['class.clickable']\n    }],\n    cssClass: [{\n      type: HostBinding,\n      args: ['class.onemrva-clickable-row']\n    }],\n    _isPrimary: [{\n      type: HostBinding,\n      args: ['class.mat-primary']\n    }],\n    _isAccent: [{\n      type: HostBinding,\n      args: ['class.mat-accent']\n    }],\n    _isError: [{\n      type: HostBinding,\n      args: ['class.mat-error']\n    }],\n    _isWarn: [{\n      type: HostBinding,\n      args: ['class.mat-warn']\n    }],\n    _isSuccess: [{\n      type: HostBinding,\n      args: ['class.mat-success']\n    }],\n    _isInfo: [{\n      type: HostBinding,\n      args: ['class.mat-info']\n    }]\n  });\n})();\n\n/**\n * Conditionally adds component to the tree if screen width matches at least one size in input\n */\nclass IfWidthIsDirective {\n  constructor(breakpointObserver, _templateRef, _viewContainer) {\n    this.breakpointObserver = breakpointObserver;\n    this._templateRef = _templateRef;\n    this._viewContainer = _viewContainer;\n  }\n  ngOnInit() {\n    this.breakpointObserver.observe([...this.ifWidthIs]).subscribe(state => {\n      this._viewContainer.clear();\n      if (state.matches) {\n        this._viewContainer.createEmbeddedView(this._templateRef);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function IfWidthIsDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || IfWidthIsDirective)(i0.ɵɵdirectiveInject(i1$2.BreakpointObserver), i0.ɵɵdirectiveInject(i0.TemplateRef), i0.ɵɵdirectiveInject(i0.ViewContainerRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: IfWidthIsDirective,\n      selectors: [[\"\", \"ifWidthIs\", \"\"]],\n      inputs: {\n        ifWidthIs: \"ifWidthIs\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(IfWidthIsDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[ifWidthIs]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i1$2.BreakpointObserver\n  }, {\n    type: i0.TemplateRef\n  }, {\n    type: i0.ViewContainerRef\n  }], {\n    ifWidthIs: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Conditionally adds component to the tree if screen width matches at least one size in input\n */\nclass OnemRvaColorDirective {\n  constructor() {\n    this.color = '';\n  }\n  /** @hidden @internal */\n  get _isPrimary() {\n    return this.color === OnemrvaMatColor.PRIMARY;\n  }\n  /** @hidden @internal */\n  get _isAccent() {\n    return this.color === OnemrvaMatColor.ACCENT;\n  }\n  /** @hidden @internal */\n  get _isError() {\n    return this.color === OnemrvaMatColor.ERROR;\n  }\n  /** @hidden @internal */\n  get _isWarn() {\n    return this.color === OnemrvaMatColor.WARN;\n  }\n  /** @hidden @internal */\n  get _isSuccess() {\n    return this.color === OnemrvaMatColor.SUCCESS;\n  }\n  /** @hidden @internal */\n  get _isInfo() {\n    return this.color === OnemrvaMatColor.INFO;\n  }\n  static {\n    this.ɵfac = function OnemRvaColorDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaColorDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemRvaColorDirective,\n      selectors: [[\"mat-card\", \"color\", \"\"], [\"mat-chip\", \"color\", \"\"]],\n      hostVars: 12,\n      hostBindings: function OnemRvaColorDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-primary\", ctx._isPrimary)(\"mat-accent\", ctx._isAccent)(\"mat-error\", ctx._isError)(\"mat-warn\", ctx._isWarn)(\"mat-success\", ctx._isSuccess)(\"mat-info\", ctx._isInfo);\n        }\n      },\n      inputs: {\n        color: \"color\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaColorDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card[color],mat-chip[color]',\n      standalone: true\n    }]\n  }], null, {\n    color: [{\n      type: Input\n    }],\n    _isPrimary: [{\n      type: HostBinding,\n      args: ['class.mat-primary']\n    }],\n    _isAccent: [{\n      type: HostBinding,\n      args: ['class.mat-accent']\n    }],\n    _isError: [{\n      type: HostBinding,\n      args: ['class.mat-error']\n    }],\n    _isWarn: [{\n      type: HostBinding,\n      args: ['class.mat-warn']\n    }],\n    _isSuccess: [{\n      type: HostBinding,\n      args: ['class.mat-success']\n    }],\n    _isInfo: [{\n      type: HostBinding,\n      args: ['class.mat-info']\n    }]\n  });\n})();\nconst UNDO_STACK_MAX_LENGTH = 50;\n/**\n * 0: digits\n * A: letters (uppercase or lowercase) and digits\n * S: only letters (uppercase or lowercase)\n * U: only letters uppercase\n * L: only letters lowercase\n */\nclass OnemrvaMaskDirective {\n  onKeyDown(event) {\n    // Allow functional keystrokes\n    const e = event;\n    let specialKeys = ['Tab', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End'];\n    if (specialKeys.indexOf(e.key) >= 0) return;\n    specialKeys = ['Backspace', 'Delete'];\n    if (specialKeys.indexOf(e.key) >= 0) {\n      this.markForDelete = true;\n      return;\n    }\n    // Undo\n    if (e.ctrlKey && 'z' === e.key && this.stackIdx > 0) {\n      this.stackIdx--;\n      this.el.nativeElement.value = this.inputStack[this.stackIdx];\n      event.preventDefault();\n    }\n    // Redo\n    if (e.ctrlKey && 'u' === e.key && this.stackIdx < this.inputStack.length - 1) {\n      this.stackIdx++;\n      this.el.nativeElement.value = this.inputStack[this.stackIdx];\n      event.preventDefault();\n    }\n    const allowedCtrlShortcuts = ['a', 'c', 'x', 'v'];\n    if (e.ctrlKey && allowedCtrlShortcuts.indexOf(e.key) >= 0) return;\n  }\n  onInput(event) {\n    const selectionStart = event.target?.selectionStart;\n    /*\n     * Step 1\n     * This block lets a delete execution if the new value is compliant.\n     * There are 2 deletion cases, after which the new value is not compliant\n     *  - dd/MX/yyyy: X is deleted, then the date becomes dd/M/yyyy\n     *                This case is resolved by step 2 and the date will become: dd/My/yyy\n     *  - dd/MMXyyyy: the last '/' is deleted, then the date becomes dd/MMyyyy\n     *                In this particular case, step 2 will automatically add the missing slash, so the date will remain dd/MM/yyyy\n     *                The issue is that the caret position will remain stuck where it was, so if the user keeps hitting the delete key, nothing will change at all. This is resolved in step 4 by moving the caret to the left\n     */\n    if (this.markForDelete) {\n      const isCompliantAfterDeletion = [...this.el.nativeElement.value].findIndex((char, idx) => {\n        const rule = this.onemrvamask[idx];\n        if (rule === null || rule === undefined) return true;\n        if (isNaN(char) && char.toLowerCase() === char.toUpperCase()) return char !== rule;\n        return !isAllowed(rule, char);\n      }) < 0;\n      if (isCompliantAfterDeletion) {\n        this.markForDelete = false;\n        return;\n      }\n    }\n    // Step 2\n    const compliantValue = [...this.el.nativeElement.value].filter(char => isAllowed('A', char)) // Necessary when several special char in a row\n    .reduce((newValue, char) => {\n      let idx = newValue.length;\n      const rule = this.onemrvamask[idx];\n      if (rule === null || rule === undefined) return newValue;\n      newValue = !isAllowed(rule, char) ? newValue : newValue + char;\n      // Add all trailing special characters\n      let nextRule = this.onemrvamask[++idx];\n      let i = 0;\n      while (nextRule !== null && nextRule !== undefined && ['0', 'A', 'S', 'U', 'L'].indexOf(nextRule) < 0) {\n        i++;\n        newValue += nextRule;\n        nextRule = this.onemrvamask[++idx];\n        if (i > 50) break;\n      }\n      return newValue;\n    }, '');\n    this.el.nativeElement.value = compliantValue;\n    // Step 3 - Find the new cursor position. If the last new character is just before special character, move the caret after\n    let idx = selectionStart;\n    let nextRule = this.onemrvamask[idx];\n    while (nextRule !== null && nextRule !== undefined && ['0', 'A', 'S', 'U', 'L'].indexOf(nextRule) < 0) {\n      idx++;\n      nextRule = this.onemrvamask[idx];\n      if (nextRule !== null && nextRule && ['0', 'A', 'S', 'U', 'L'].indexOf(nextRule) < 0) {\n        /* empty */\n      } else break;\n    }\n    // Step 4 - Update inputStack only when all characters are processed\n    if (compliantValue !== this.inputStack[this.stackIdx]) {\n      this.stackIdx++;\n      const stack = this.stackIdx > UNDO_STACK_MAX_LENGTH - 1 ? this.inputStack.slice(1) : this.inputStack.splice(0, this.stackIdx);\n      this.stackIdx = this.stackIdx > UNDO_STACK_MAX_LENGTH - 1 ? UNDO_STACK_MAX_LENGTH - 1 : this.stackIdx;\n      this.inputStack = [...stack, compliantValue];\n      this.el.nativeElement.setSelectionRange(idx, idx);\n    } else {\n      if (this.markForDelete) {\n        let i = idx - 1;\n        /* Finds the last rule character before the special character.\n         *    e.g: (nnn)/nnnn\n         *         If the user tries to delete '/', the caret will move just before ')'\n         */\n        while (i > 0) {\n          const previousRule = this.onemrvamask[i - 1];\n          if (['0', 'A', 'S', 'U', 'L'].indexOf(previousRule) < 0) {\n            i--;\n          } else break;\n        }\n        this.el.nativeElement.setSelectionRange(i, i);\n      }\n    }\n    this.markForDelete = false;\n  }\n  constructor(el) {\n    this.el = el;\n    this.inputStack = ['']; // Stack for undo/redo\n    this.stackIdx = 0; // Current index in undo/redo stack\n    this.markForDelete = false;\n  }\n  static {\n    this.ɵfac = function OnemrvaMaskDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaMaskDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemrvaMaskDirective,\n      selectors: [[\"\", \"onemrvamask\", \"\"]],\n      hostBindings: function OnemrvaMaskDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"keydown\", function OnemrvaMaskDirective_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          })(\"input\", function OnemrvaMaskDirective_input_HostBindingHandler($event) {\n            return ctx.onInput($event);\n          });\n        }\n      },\n      inputs: {\n        onemrvamask: \"onemrvamask\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaMaskDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[onemrvamask]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    onemrvamask: [{\n      type: Input\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }],\n    onInput: [{\n      type: HostListener,\n      args: ['input', ['$event']]\n    }]\n  });\n})();\nfunction isAllowed(rule, character) {\n  switch (rule) {\n    // Any digit\n    case '0':\n      if (character === ' ') return false;\n      return !isNaN(+character);\n    // A: letters (uppercase or lowercase) and digits\n    case 'A':\n      if (character === ' ') return false;\n      return !isNaN(+character) || character.toLowerCase() != character.toUpperCase();\n    // only letters (uppercase or lowercase)\n    case 'S':\n      return character.toLowerCase() != character.toUpperCase();\n    //  only uppercase letters\n    case 'U':\n      return character.toLowerCase() != character.toUpperCase() && character === character.toUpperCase();\n    //  only lowercase letters\n    case 'L':\n      return character.toLowerCase() != character.toUpperCase() && character === character.toLowerCase();\n  }\n  return false;\n}\n\n/**\n * Conditionally adds component to the tree if screen width matches at least one size in input\n */\nclass OnemRvaClipboardDirective {\n  constructor(elementRef, renderer, factory, vcRef, clipboardService) {\n    this.elementRef = elementRef;\n    this.renderer = renderer;\n    this.factory = factory;\n    this.vcRef = vcRef;\n    this.clipboardService = clipboardService;\n    this.class = '';\n    this.clipboard = '';\n    const miFactory = this.factory.resolveComponentFactory(ClipboardIconComponent);\n    this.icon = this.vcRef.createComponent(miFactory);\n    this.iconEl = this.icon.injector.get(ClipboardIconComponent)._elementRef.nativeElement;\n  }\n  ngOnInit() {\n    this.vcRef.clear();\n    this.renderer.appendChild(this.elementRef.nativeElement, this.iconEl);\n  }\n  static {\n    this.ɵfac = function OnemRvaClipboardDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaClipboardDirective)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.Renderer2), i0.ɵɵdirectiveInject(i0.ComponentFactoryResolver), i0.ɵɵdirectiveInject(i0.ViewContainerRef), i0.ɵɵdirectiveInject(i1.Clipboard));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemRvaClipboardDirective,\n      selectors: [[\"span\", \"clipboard\", \"\"]],\n      hostVars: 2,\n      hostBindings: function OnemRvaClipboardDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassMap(ctx.class);\n        }\n      },\n      inputs: {\n        clipboard: \"clipboard\"\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaClipboardDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'span[clipboard]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.Renderer2\n  }, {\n    type: i0.ComponentFactoryResolver\n  }, {\n    type: i0.ViewContainerRef\n  }, {\n    type: i1.Clipboard\n  }], {\n    class: [{\n      type: HostBinding,\n      args: ['class']\n    }],\n    clipboard: [{\n      type: Input\n    }]\n  });\n})();\n\n/**\n * Conditionally adds component to the tree if screen width matches at least one size in input\n */\nclass OnemRvaIconRightDirective {\n  constructor() {\n    this.class = true;\n  }\n  static {\n    this.ɵfac = function OnemRvaIconRightDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaIconRightDirective)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemRvaIconRightDirective,\n      selectors: [[\"button\", \"iconRight\", \"\"]],\n      hostVars: 2,\n      hostBindings: function OnemRvaIconRightDirective_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"onemrva-icon-right\", ctx.class);\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaIconRightDirective, [{\n    type: Directive,\n    args: [{\n      selector: 'button[iconRight]',\n      standalone: true\n    }]\n  }], null, {\n    class: [{\n      type: HostBinding,\n      args: ['class.onemrva-icon-right']\n    }]\n  });\n})();\nclass OnemrvaDateFormatDirective {\n  constructor(el) {\n    this.el = el;\n    this.template = '__/__/____';\n  }\n  onPaste(event) {\n    const input = this.el.nativeElement;\n    // Get pasted content\n    const pasted = event.clipboardData?.getData('text/plain') || '';\n    const digitsOnly = pasted.replace(/\\D/g, '');\n    if (!digitsOnly) {\n      event.preventDefault(); // Ignore invalid paste\n      return;\n    }\n    event.preventDefault(); // Stop default paste\n    // Start at current caret position\n    let pos = input.selectionStart ?? 0;\n    const chars = input.value.split('');\n    let digitIndex = 0;\n    while (pos < this.template.length && digitIndex < digitsOnly.length) {\n      if (this.template[pos] === '/') {\n        pos++;\n        continue;\n      }\n      chars[pos] = digitsOnly[digitIndex++];\n      pos++;\n    }\n    input.value = chars.join('');\n    this.emitInputEvent(input);\n    // Set caret after the last replaced character\n    this.setCaretToNextEditable(pos);\n  }\n  onCut(event) {\n    const input = this.el.nativeElement;\n    const start = input.selectionStart ?? 0;\n    const end = input.selectionEnd ?? 0;\n    // Capture the selected value (optional: log or store it)\n    const cutText = input.value.substring(start, end);\n    // Replace the selected portion with underscores (skip slashes)\n    const chars = input.value.split('');\n    for (let i = start; i < end; i++) {\n      if (this.template[i] !== '/') {\n        chars[i] = '_';\n      }\n    }\n    // Update input value\n    input.value = chars.join('');\n    this.emitInputEvent(input);\n    // Move caret to next editable position\n    this.setCaretToNextEditable(start);\n    // Prevent default cut behavior from modifying content further\n    event.preventDefault();\n    // ✅ Optionally copy to clipboard manually (optional)\n    if (event.clipboardData) {\n      event.clipboardData.setData('text/plain', cutText.replace(/\\//g, ''));\n    }\n  }\n  onFocus() {\n    const input = this.el.nativeElement;\n    if (!input.value || input.value === this.template) {\n      input.value = this.template;\n    }\n    this.setCaretToNextEditable(0);\n  }\n  onClick() {\n    const input = this.el.nativeElement;\n    // Only reposition the caret if there's no selection (start === end)\n    const selectionStart = input.selectionStart ?? 0;\n    const selectionEnd = input.selectionEnd ?? 0;\n    if (selectionStart === selectionEnd) {\n      if (input.value === this.template) {\n        this.setCaretToNextEditable(0);\n      } else {\n        this.setCaretToNextEditable(selectionStart);\n      }\n    }\n  }\n  onKeyDown(event) {\n    const input = this.el.nativeElement;\n    let pos = input.selectionStart ?? 0;\n    const allowedCtrlShortcuts = ['a', 'c', 'x', 'v'];\n    if (event.ctrlKey && allowedCtrlShortcuts.indexOf(event.key) >= 0) {\n      console.log(input.value);\n      if (event.key === 'x' && input.value === '') {\n        console.log(event.key);\n        input.value = this.template;\n        this.emitInputEvent(input);\n      }\n      return;\n    }\n    // Allow navigation\n    if (['ArrowLeft', 'ArrowRight', 'Tab'].includes(event.key)) return;\n    // Handle Backspace or Delete with selection\n    if (event.key === 'Backspace' || event.key === 'Delete') {\n      event.preventDefault();\n      const start = input.selectionStart ?? 0;\n      const end = input.selectionEnd ?? start;\n      // If there's a selection\n      if (end > start) {\n        const chars = input.value.split('');\n        for (let i = start; i < end; i++) {\n          if (this.template[i] !== '/') {\n            chars[i] = '_';\n          }\n        }\n        input.value = chars.join('');\n        this.emitInputEvent(input);\n        this.setCaretToNextEditable(start);\n        return;\n      }\n      // If no selection, handle as usual (delete previous or next char)\n      let pos = start;\n      if (event.key === 'Backspace' && pos > 0) {\n        do {\n          pos--;\n        } while (this.template[pos] === '/' && pos > 0);\n        this.replaceCharAt(input, pos, '_');\n        this.emitInputEvent(input);\n        this.setCaretToNextEditable(pos);\n        return;\n      }\n      if (event.key === 'Delete' && pos < this.template.length) {\n        while (this.template[pos] === '/' && pos < this.template.length) {\n          pos++;\n        }\n        this.replaceCharAt(input, pos, '_');\n        this.emitInputEvent(input);\n        this.setCaretToNextEditable(pos);\n        return;\n      }\n    }\n    // Handle Slash `/` key\n    if (event.key === '/' || event.key === '.' || event.key === '-' || event.key === ' ') {\n      event.preventDefault();\n      if (pos === 1 || pos === 4) {\n        // Typing `/` at end of day field\n        const currentVal = input.value.substring(pos - 1, pos);\n        if (!currentVal.includes('_')) {\n          this.replaceCharAt(input, pos - 1, '0');\n          this.replaceCharAt(input, pos, currentVal);\n          this.setCaretToNextEditable(pos + 2);\n          return;\n        }\n      }\n      // Default: just skip to next editable section if allowed\n      this.setCaretToNextEditable(pos + 1);\n      return;\n    }\n    // Handle Backspace\n    if (event.key === 'Backspace') {\n      event.preventDefault();\n      if (pos === 0) return;\n      do {\n        pos--;\n      } while (this.template[pos] === '/' && pos > 0);\n      this.replaceCharAt(input, pos, '_');\n      this.setCaretToNextEditable(pos);\n      this.emitInputEvent(input);\n      return;\n    }\n    // Allow only digits\n    if (!/^\\d$/.test(event.key)) {\n      event.preventDefault();\n      return;\n    }\n    // Prevent typing beyond mask\n    if (pos >= this.template.length) {\n      event.preventDefault();\n      return;\n    }\n    // Skip slashes forward\n    while (this.template[pos] === '/' && pos < this.template.length) {\n      pos++;\n    }\n    this.replaceCharAt(input, pos, event.key);\n    event.preventDefault();\n    this.setCaretToNextEditable(pos + 1);\n    this.emitInputEvent(input);\n  }\n  emitInputEvent(input) {\n    input.dispatchEvent(new Event('input', {\n      bubbles: true\n    }));\n  }\n  onBlur() {\n    // Optionally keep the mask visible on blur — do nothing\n  }\n  replaceCharAt(input, index, char) {\n    const value = input.value.split('');\n    value[index] = char;\n    input.value = value.join('');\n  }\n  setCaretToNextEditable(pos) {\n    const input = this.el.nativeElement;\n    while (this.template[pos] === '/' && pos < this.template.length) {\n      pos++;\n    }\n    input.setSelectionRange(pos, pos);\n  }\n  sanitizeDateInput(input) {\n    if (typeof input !== 'string') return '';\n    return input.replace(/[.\\s-]/g, '/') // Replace dot, space, dash with slash\n    .replace(/[^0-9/]/g, '').replace('//', ''); // Remove everything except digits and slash\n  }\n  static {\n    this.ɵfac = function OnemrvaDateFormatDirective_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaDateFormatDirective)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: OnemrvaDateFormatDirective,\n      selectors: [[\"\", \"onemrvaDateFormat\", \"\"]],\n      hostBindings: function OnemrvaDateFormatDirective_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"paste\", function OnemrvaDateFormatDirective_paste_HostBindingHandler($event) {\n            return ctx.onPaste($event);\n          })(\"cut\", function OnemrvaDateFormatDirective_cut_HostBindingHandler($event) {\n            return ctx.onCut($event);\n          })(\"focus\", function OnemrvaDateFormatDirective_focus_HostBindingHandler() {\n            return ctx.onFocus();\n          })(\"click\", function OnemrvaDateFormatDirective_click_HostBindingHandler() {\n            return ctx.onClick();\n          })(\"keydown\", function OnemrvaDateFormatDirective_keydown_HostBindingHandler($event) {\n            return ctx.onKeyDown($event);\n          })(\"blur\", function OnemrvaDateFormatDirective_blur_HostBindingHandler() {\n            return ctx.onBlur();\n          });\n        }\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaDateFormatDirective, [{\n    type: Directive,\n    args: [{\n      selector: '[onemrvaDateFormat]',\n      standalone: true\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    onPaste: [{\n      type: HostListener,\n      args: ['paste', ['$event']]\n    }],\n    onCut: [{\n      type: HostListener,\n      args: ['cut', ['$event']]\n    }],\n    onFocus: [{\n      type: HostListener,\n      args: ['focus']\n    }],\n    onClick: [{\n      type: HostListener,\n      args: ['click']\n    }],\n    onKeyDown: [{\n      type: HostListener,\n      args: ['keydown', ['$event']]\n    }],\n    onBlur: [{\n      type: HostListener,\n      args: ['blur']\n    }]\n  });\n})();\nconst directives = [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective];\n\n/**\n * Get language from local storage or browser.ééééééééééééé\n * é\n * @param translateService\n * @param languages\n * @param default_language\n */\nfunction setTranslationLanguage(translateService, languages = ['en', 'fr', 'nl', 'de'], default_language = 'en') {\n  return async () => {\n    translateService.onLangChange.subscribe(value => {\n      localStorage.setItem('language', value.lang);\n    });\n    translateService.addLangs(languages);\n    let language = localStorage.getItem('language');\n    if (language === null || languages.indexOf(language) < 0) {\n      // getting language from browser\n      if (languages.indexOf(navigator.language) >= 0) {\n        language = navigator.language;\n      } else {\n        // getting language from one of the browser's languages\n        for (const lng of navigator.languages) {\n          if (languages.indexOf(lng) >= 0) {\n            language = lng;\n            break;\n          }\n        }\n        // getting default language\n        if (language === null || languages.indexOf(language) < 0) {\n          language = default_language;\n        }\n      }\n    }\n    await translateService.use(language).toPromise();\n  };\n}\n/**\n * Get language from WO\n *\n * @param translateService\n * @param languages\n * @param default_language\n */\nfunction setTranslationLanguageFromWO(translateService, languages = ['fr', 'nl'], default_language = 'fr') {\n  return async () => {\n    translateService.addLangs(languages);\n    let language = localStorage.getItem('be_social_security_workenv_language');\n    if (language !== null) {\n      language = language.toLowerCase();\n      if (languages.indexOf(language) >= 0) {\n        await translateService.use(language).toPromise();\n        return;\n      }\n    }\n    await translateService.use(default_language).toPromise();\n  };\n}\nclass OnemrvaTranslateCDNLoader {\n  constructor(cdn, projects = [], prefix = '/i18n/', suffix = '.json') {\n    this.cdn = cdn;\n    this.projects = projects;\n    this.prefix = prefix;\n    this.suffix = suffix;\n  }\n  /**\n   * Gets the translations from the server\n   */\n  getTranslation(lang) {\n    return this.cdn.getTranslations(this.projects, lang, this.prefix, this.suffix);\n  }\n}\nclass OnemrvaTranslateHttpLoader {\n  constructor(http, prefix = '/assets/i18n/', suffix = '.json', modules = []) {\n    this.http = http;\n    this.prefix = prefix;\n    this.suffix = suffix;\n    this.modules = modules;\n  }\n  /**\n   * Gets the translations from the server\n   */\n  getTranslation(lang) {\n    const observables = [this.http.get(`${this.prefix}${lang}${this.suffix}`).pipe(catchError$1(() => of(null))), ...this.modules.map(m => {\n      this.http.get(`${this.prefix}${m}/${lang}${this.suffix}`).pipe(catchError$1(() => of(null)));\n      //console.log(`${this.prefix}${m}/${lang}${this.suffix}`);\n    })];\n    return forkJoin(observables).pipe(map(all => {\n      return all.filter(v => !!v).reduce((s, c) => ({\n        ...s,\n        ...c\n      }), {});\n    }));\n  }\n}\nclass OnemrvaBcePipe {\n  transform(value) {\n    const strOut = value.trim().replace(/\\/|\\.|-/g, '');\n    if (strOut.length !== 10) return '?01?.???.???';\n    return `${strOut.substring(0, 4)}.${strOut.substring(4, 7)}.${strOut.substring(7, 10)}`;\n  }\n  static {\n    this.ɵfac = function OnemrvaBcePipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaBcePipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"onemrvaBce\",\n      type: OnemrvaBcePipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaBcePipe, [{\n    type: Pipe,\n    args: [{\n      name: 'onemrvaBce',\n      standalone: true\n    }]\n  }], null, null);\n})();\nclass OnemrvaNissPipe {\n  transform(value) {\n    const strOut = value.trim().replace(/\\/|\\.|-/g, '');\n    if (strOut.length !== 11) return '??01??/???-??';\n    return `${strOut.substring(0, 6)}/${strOut.substring(6, 9)}-${strOut.substring(9, 11)}`;\n  }\n  static {\n    this.ɵfac = function OnemrvaNissPipe_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaNissPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /* @__PURE__ */i0.ɵɵdefinePipe({\n      name: \"onemrvaNiss\",\n      type: OnemrvaNissPipe,\n      pure: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaNissPipe, [{\n    type: Pipe,\n    args: [{\n      name: 'onemrvaNiss',\n      standalone: true\n    }]\n  }], null, null);\n})();\nconst LOOKUP_COUNTRY_SERVICE_URL = new InjectionToken('LOOKUP_COUNTRY_SERVICE_URL', {\n  providedIn: 'root',\n  factory: () => LOOKUP_COUNTRY_URL\n});\nclass CommonCountryLookupService {\n  constructor(http, commonCountryServiceURL) {\n    this.http = http;\n    this.commonCountryServiceURL = commonCountryServiceURL;\n    this._countries$ = new ReplaySubject(1);\n    this._customersInitialized = false;\n    //console.log(this.commonCountryServiceURL);\n  }\n  getCountries(refresh = false) {\n    if (refresh || !this._customersInitialized) {\n      this._customersInitialized = true;\n      this.http.get(this.commonCountryServiceURL).subscribe(countries => {\n        this._countries$.next(countries);\n      });\n    }\n    return this._countries$.pipe(skip(+refresh), distinctUntilChanged());\n  }\n  getCountryByCode(code) {\n    return this._countries$.pipe(map$1(countries => {\n      const country = countries.find(country => country.code === code);\n      return country || null;\n    }));\n  }\n  static {\n    this.ɵfac = function CommonCountryLookupService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CommonCountryLookupService)(i0.ɵɵinject(i1$3.HttpClient), i0.ɵɵinject(LOOKUP_COUNTRY_SERVICE_URL));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CommonCountryLookupService,\n      factory: CommonCountryLookupService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CommonCountryLookupService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$3.HttpClient\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [LOOKUP_COUNTRY_SERVICE_URL]\n    }]\n  }], null);\n})();\nclass CacheService {\n  setItem(key, item) {\n    //console.log(\"SET \" + key);\n    item.subscribe(value => {\n      localStorage.setItem(key, JSON.stringify(value));\n    });\n  }\n  getItem(key) {\n    const r = localStorage.getItem(key);\n    if (r === null) {\n      return of(null);\n    }\n    return of(JSON.parse(r));\n  }\n  deleteItem(key) {\n    //console.log(\"DELETE \" + key);\n    localStorage.removeItem(key);\n  }\n  static {\n    this.ɵfac = function CacheService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || CacheService)();\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: CacheService,\n      factory: CacheService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CacheService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], null, null);\n})();\nclass OnemRvaCDNService {\n  constructor(rendererFactory, cacheService, http, cdnUrlMode) {\n    this.rendererFactory = rendererFactory;\n    this.cacheService = cacheService;\n    this.http = http;\n    this.refresh$ = new Subject();\n    this.renderer = this.rendererFactory.createRenderer(null, null);\n    switch (cdnUrlMode) {\n      case CDNUrlModeOptions.PROD:\n        this.cdnUrl = CDN_URLS.prod;\n        break;\n      case CDNUrlModeOptions.VAL:\n        this.cdnUrl = CDN_URLS.val;\n        break;\n      default:\n        this.cdnUrl = CDN_URLS.prod;\n    }\n    const linkElement = this.renderer.createElement('link');\n    // Set the attributes\n    this.renderer.setAttribute(linkElement, 'rel', 'preconnect');\n    this.renderer.setAttribute(linkElement, 'href', this.cdnUrl);\n    // Append the <link> element to the <head>\n    this.renderer.appendChild(document.head, linkElement);\n  }\n  getUrl(path = '') {\n    return this.cdnUrl + path;\n  }\n  getImg(path = '') {\n    return this.getUrl('/img/' + path);\n  }\n  getOri(path = '') {\n    return this.getImg('ori/' + path);\n  }\n  fetch(endpoint) {\n    return this.http.get(this.cdnUrl + endpoint);\n  }\n  fetchNoCache(endpoint) {\n    return this.http.get(this.cdnUrl + endpoint);\n  }\n  getTranslations(projects, lang, prefix = '', suffix = '') {\n    const observables = [this.http.get(`${this.cdnUrl}${prefix}design-system/design-system/${lang}${suffix}`).pipe(catchError$1(() => of({}))), ...projects.map(m => {\n      return this.http.get(`${this.cdnUrl}${prefix}${m}/${lang}${suffix}`).pipe(catchError$1(() => {\n        return of({});\n      }));\n    })];\n    return forkJoin(observables).pipe(map(all => {\n      return all.filter(v => !!v).reduce((s, c) => ({\n        ...s,\n        ...c\n      }), {});\n    }));\n  }\n  /**\n   * Loads a CSS file dynamically into the document head.\n   * @param href The URL of the CSS file.\n   * @returns A promise that resolves when the CSS file is loaded.\n   * //\n   */\n  loadCss(href) {\n    return new Promise((resolve, reject) => {\n      const existingLink = document.querySelector(`link[href=\"${href}\"]`);\n      if (existingLink) {\n        resolve(); // CSS is already loaded\n        return;\n      }\n      const link = this.renderer.createElement('link');\n      this.renderer.setAttribute(link, 'rel', 'stylesheet');\n      this.renderer.setAttribute(link, 'type', 'text/css');\n      this.renderer.setAttribute(link, 'href', href);\n      link.onload = () => resolve();\n      link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));\n      this.renderer.appendChild(document.head, link);\n    });\n  }\n  static {\n    this.ɵfac = function OnemRvaCDNService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaCDNService)(i0.ɵɵinject(i0.RendererFactory2), i0.ɵɵinject(CacheService), i0.ɵɵinject(i1$3.HttpClient), i0.ɵɵinject(CDN_URL_MODE, 8));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemRvaCDNService,\n      factory: OnemRvaCDNService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n__decorate([HttpRequestCache(cdnService => ({\n  refreshOn: cdnService.refresh$,\n  storage: cdnService.cacheService,\n  ttl: 3600000\n}))], OnemRvaCDNService.prototype, \"fetch\", null);\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaCDNService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i0.RendererFactory2\n  }, {\n    type: CacheService\n  }, {\n    type: i1$3.HttpClient\n  }, {\n    type: CDNUrlModeOptions,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [CDN_URL_MODE]\n    }]\n  }], {\n    fetch: []\n  });\n})();\nclass OnemRvaCDNCountryService {\n  constructor(cdnService, translateService) {\n    this.cdnService = cdnService;\n    this.translateService = translateService;\n  }\n  getCountries() {\n    return this.cdnService.fetch('/json/countries.json').pipe(map(response => response),\n    // ✅ Explicitly cast response\n    combineLatestWith(from(this.translateService.onLangChange).pipe(startWith$1(this.translateService.currentLang), map(event => typeof event === 'string' ? event : event.lang))), map(([response, currentLang]) => {\n      // ✅ Explicit type definition\n      return response.map(country => ({\n        ...country,\n        name: this.getTranslatedCountryName(country, currentLang)\n      })).sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));\n    }));\n  }\n  getCountriesByZone(zone) {\n    return this.getCountries().pipe(map(countries => {\n      return countries.filter(country => country.zone === zone);\n    }));\n  }\n  getTranslatedCountryName(country, languageCode = this.translateService.currentLang) {\n    if (languageCode === 'fr' || languageCode === 'nl' || languageCode === 'de') {\n      if (country.translations[languageCode] !== undefined) return country.translations[languageCode];\n    }\n    return country.name;\n  }\n  findCountryFromCode(code) {\n    return this.getCountries().pipe(map(countries => {\n      return countries.find(country => country.code.toLowerCase() === code.toLowerCase());\n    }));\n  }\n  findCountryFromVIESCode(code) {\n    return this.getCountriesByZone('EU').pipe(map(countries => {\n      return countries.find(country => country.pattern.substring(0, 2) === code.substring(0, 2));\n    }));\n  }\n  findCountryFromPrefix(number) {\n    return this.getCountries().pipe(map(countries => {\n      return countries.find(country => {\n        return country.dial_code === number.substring(0, country.dial_code.length);\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function OnemRvaCDNCountryService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaCDNCountryService)(i0.ɵɵinject(OnemRvaCDNService), i0.ɵɵinject(i1$4.TranslateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemRvaCDNCountryService,\n      factory: OnemRvaCDNCountryService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaCDNCountryService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: OnemRvaCDNService\n  }, {\n    type: i1$4.TranslateService\n  }], null);\n})();\nclass OnemRvaCDNMimeService {\n  static {\n    this.mimetypeFile = '/json/mimetypes.json';\n  }\n  constructor(cdnService) {\n    this.cdnService = cdnService;\n  }\n  getMimesForExtension(extensions) {\n    return this.cdnService.fetch(OnemRvaCDNMimeService.mimetypeFile).pipe(catchError(() => {\n      return [];\n    }), map$1(response => {\n      return response.filter(mime => {\n        return extensions.includes(mime.extension);\n      }).map(mime => {\n        return mime.mimeType;\n      });\n    }));\n  }\n  getExtensionsForMime(mimTypes) {\n    return this.cdnService.fetch(OnemRvaCDNMimeService.mimetypeFile).pipe(catchError(() => {\n      return [];\n    }), map$1(response => {\n      return response.filter(mime => {\n        return mimTypes.includes(mime.mimeType);\n      }).map(mime => {\n        return mime.extension;\n      });\n    }));\n  }\n  static {\n    this.ɵfac = function OnemRvaCDNMimeService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaCDNMimeService)(i0.ɵɵinject(OnemRvaCDNService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemRvaCDNMimeService,\n      factory: OnemRvaCDNMimeService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaCDNMimeService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: OnemRvaCDNService\n  }], null);\n})();\nclass OnemRvaOSMService {\n  constructor(http) {\n    this.http = http;\n    this.nominatimUrl = 'https://nominatim.openstreetmap.org/search';\n  }\n  searchAddress(query, format = 'json', addressdetails = 1, limit = 10) {\n    const params = new HttpParams().set('q', query).set('format', format).set('addressdetails', addressdetails).set('limit', limit);\n    return this.http.get(this.nominatimUrl, {\n      params\n    });\n  }\n  static {\n    this.ɵfac = function OnemRvaOSMService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemRvaOSMService)(i0.ɵɵinject(i1$3.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemRvaOSMService,\n      factory: OnemRvaOSMService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemRvaOSMService, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$3.HttpClient\n  }], null);\n})();\nclass OnemrvaErrorHandler {\n  constructor(_snackBar) {\n    this._snackBar = _snackBar;\n    this.horizontalPosition = 'end';\n    this.verticalPosition = 'bottom';\n  }\n  handleError(error) {\n    console.error(error);\n    //alert(\"Error: \" + error.message);\n    this._snackBar.open(`Error: ${error.message}`, '', {\n      duration: 5000,\n      panelClass: 'mat-primary',\n      horizontalPosition: this.horizontalPosition,\n      verticalPosition: this.verticalPosition\n    });\n  }\n  static {\n    this.ɵfac = function OnemrvaErrorHandler_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaErrorHandler)(i0.ɵɵinject(i1$5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemrvaErrorHandler,\n      factory: OnemrvaErrorHandler.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaErrorHandler, [{\n    type: Injectable\n  }], () => [{\n    type: i1$5.MatSnackBar\n  }], null);\n})();\nclass OnemrvaMissingTranslationHandler {\n  handle(params) {\n    return `???${params.key}???`;\n  }\n}\nclass OnemrvaValidators {\n  static bceValidator(required = false, control) {\n    if (control.value === null || control.value.trim() === '') {\n      return !required ? null : {\n        bceNull: true\n      };\n    }\n    const bceCandidate = control.value.trim().replace(/\\/|\\.|-/g, '');\n    if (bceCandidate.length !== 10) return {\n      bceLengthError: {\n        value: bceCandidate\n      }\n    };\n    if (Number.isNaN(+bceCandidate)) return {\n      bceNan: {\n        value: bceCandidate\n      }\n    };\n    return null;\n  }\n  static bce(required = false) {\n    return control => {\n      return this.bceValidator(required, control);\n    };\n  }\n  static nissValidator(required = false, control) {\n    if (control.value === null || control.value.trim() === '') {\n      return !required ? null : {\n        nissNull: true\n      };\n    }\n    const nissCandidate = control.value.trim().replace(/\\/|\\.|-/g, '');\n    if (nissCandidate.length !== 11) {\n      return {\n        nissLengthError: {\n          value: nissCandidate\n        }\n      };\n    }\n    if (Number.isNaN(+nissCandidate)) {\n      return {\n        nissNan: {\n          value: nissCandidate\n        }\n      };\n    }\n    // Niss day and year are actual values, the month can be different according to the logic below\n    // [20-32] & [40-52] => foreigners & temporary NISS values\n    // it is not necessary to check for the actual date to be valid\n    const checksumValue = nissCandidate.substring(9);\n    const nissNumbersToCheck = nissCandidate.substring(0, 9);\n    const isValidChecksumYear1900 = 97 - +nissNumbersToCheck % 97 === +checksumValue;\n    const isValidChecksumYear2000 = 97 - +`2${nissNumbersToCheck}` % 97 === +checksumValue;\n    if (!isValidChecksumYear1900 && !isValidChecksumYear2000) {\n      return {\n        nissCheckDigitError: {\n          value: nissCandidate\n        }\n      };\n    }\n    /*\n      Below checks aren't really necessary but are here to exclude even more potential errors\n      We could still improve this to include february days & 30 day months + the real potential ranges\n    */\n    const monthString = nissCandidate.substring(2, 4);\n    const month = parseInt(monthString) - 1;\n    const real_month = parseInt(monthString) % 20 - 1;\n    if (real_month > 11 || month > 51) {\n      return {\n        nissInvalidMonth: {\n          value: nissCandidate\n        }\n      };\n    }\n    const day = +nissCandidate.substring(4, 6);\n    if (day > 31) {\n      return {\n        nissInvalidDate: {\n          value: nissCandidate\n        }\n      };\n    }\n    return null;\n  }\n  static niss(required = false) {\n    return control => {\n      return this.nissValidator(required, control);\n    };\n  }\n  static nissOrBce(required = false) {\n    return control => {\n      if (control.value === null || control.value.trim() === '') return !required ? null : {\n        nissNull: true\n      };\n      const nissCandidate = control.value.trim().replace(/\\/|\\.|-/g, '');\n      if (control.value.length === 11) {\n        return this.nissValidator(required, control);\n      } else if (control.value.length === 10) {\n        return this.bceValidator(required, control);\n      }\n      return {\n        lengthError: {\n          value: nissCandidate\n        }\n      };\n    };\n  }\n}\nconst IBAN_SUPPORTED_COUNTRIES = Object.entries(countrySpecs).filter(value => {\n  return value[1].IBANRegistry;\n}).map(value => {\n  return value[0];\n});\nconst SEPA_ONLY_SUPPORTED_COUNTRIES = Object.entries(countrySpecs).filter(value => {\n  return value[1].SEPA;\n}).map(value => {\n  return value[0];\n});\nconst internValidateIban = (iban = '') => {\n  return validateIBAN(electronicFormatIBAN(iban) || undefined);\n};\n// const getMappedCodes = (errorCodes: ValidationErrorsIBAN[], value?: any) => {\n//   const mappedCodes = errorCodes.map(code => ValidationErrorsIBAN[code]);\n//\n//   if (mappedCodes.length > 0) {\n//     const listCodes: Record<string, any> = {};\n//\n//     mappedCodes.forEach((code: string) => {\n//       listCodes[code] = { value };\n//     });\n//\n//     return listCodes as ValidationErrors;\n//   }\n//\n//   return null;\n// };\nconst bankAccountValidator = () => {\n  return control => {\n    let finalErrorCodes = [];\n    if (typeof control.getRawValue() === 'string') {\n      const regex = /^[a-zA-Z]{2}/;\n      if (regex.test(control.getRawValue().substring(0, 2))) {\n        const {\n          valid: _valid,\n          errorCodes\n        } = internValidateIban(control.value);\n        finalErrorCodes = errorCodes;\n      }\n    } else {\n      const countryCodeValue = control.value !== null ? control.value['countryCode'] : '';\n      const bankNumber = control.value !== null ? control.value['bban'] : '';\n      if (countryCodeValue !== '') {\n        const {\n          valid: _valid,\n          errorCodes\n        } = internValidateIban(`${countryCodeValue}${bankNumber}`);\n        finalErrorCodes = errorCodes;\n      }\n    }\n    if (finalErrorCodes.length === 0) {\n      return null;\n    }\n    const validationErrors = {};\n    finalErrorCodes.forEach(errCode => {\n      const key = ValidationErrorsIBAN[errCode];\n      validationErrors[key] = {\n        value: control.getRawValue()\n      };\n    });\n    return validationErrors;\n  };\n};\nclass OnemrvaSharedModule {\n  static {\n    this.ɵfac = function OnemrvaSharedModule_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaSharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: OnemrvaSharedModule,\n      imports: [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective],\n      exports: [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective]\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaSharedModule, [{\n    type: NgModule,\n    args: [{\n      imports: [...directives],\n      exports: [...directives]\n    }]\n  }], null, null);\n})();\nclass OnemrvaNativeDateAdapter extends NativeDateAdapter {\n  constructor(translateService) {\n    super();\n    this.translateService = translateService;\n    this.destroyNotifier$ = new Subject();\n    this.localeChanges = new Subject();\n    this.setLocale(this.translateService.currentLang);\n    this.localeChanges.next(this);\n    if (this.translateService !== undefined) {\n      this.translateService.onLangChange.pipe(takeUntil(this.destroyNotifier$)).subscribe(({\n        lang\n      }) => {\n        this.setLocale(lang);\n        this.localeChanges.next(this);\n      });\n    }\n  }\n  format(date, formats) {\n    const dt = DateTime.fromJSDate(date);\n    return dt.toFormat(formats);\n  }\n  parse(value) {\n    const formats = ONEMRVA_MAT_NATIVE_DATE_FORMAT;\n    if (value && typeof value === 'string') {\n      value = value.replace(/\\D/g, '/');\n    }\n    if (value === null || value === '') {\n      return null;\n    }\n    const dt = DateTime.fromFormat(value, formats.display.dateInput, {\n      locale: this.locale\n    });\n    if (dt.isValid) {\n      return dt.toJSDate();\n    }\n    for (const format of formats.parse.dateInput) {\n      const parsed = DateTime.fromFormat(value, format, {\n        locale: this.locale\n      });\n      if (parsed.isValid) {\n        return parsed.toJSDate();\n      }\n    }\n    return this.invalid();\n  }\n  getFirstDayOfWeek() {\n    return 1;\n  }\n  ngOnDestroy() {\n    this.destroyNotifier$.next();\n    this.destroyNotifier$.complete();\n  }\n  static {\n    this.ɵfac = function OnemrvaNativeDateAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaNativeDateAdapter)(i0.ɵɵinject(i1$4.TranslateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemrvaNativeDateAdapter,\n      factory: OnemrvaNativeDateAdapter.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaNativeDateAdapter, [{\n    type: Injectable\n  }], () => [{\n    type: i1$4.TranslateService\n  }], null);\n})();\n\n/** InjectionToken for LuxonDateAdapter to configure options. */\nconst MAT_LUXON_DATE_ADAPTER_OPTIONS = new InjectionToken('MAT_LUXON_DATE_ADAPTER_OPTIONS', {\n  providedIn: 'root',\n  factory: MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY\n});\n/** @docs-private */\nfunction MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY() {\n  return {\n    useUtc: false,\n    firstDayOfWeek: 0,\n    defaultOutputCalendar: 'gregory'\n  };\n}\n/** Creates an array and fills it with values. */\nfunction luxonRange(length, valueFunction) {\n  const valuesArray = Array(length);\n  for (let i = 0; i < length; i++) {\n    valuesArray[i] = valueFunction(i);\n  }\n  return valuesArray;\n}\n\n/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n/** Adapts Luxon Dates for use with Angular Material. */\nclass OnemrvaLuxonDateAdapter extends DateAdapter {\n  constructor(translateService) {\n    super();\n    this.translateService = translateService;\n    const dateLocale = inject(MAT_DATE_LOCALE, {\n      optional: true\n    });\n    const options = inject(MAT_LUXON_DATE_ADAPTER_OPTIONS, {\n      optional: true\n    });\n    this._useUTC = !!options?.useUtc;\n    this._firstDayOfWeek = options?.firstDayOfWeek || 0;\n    this._defaultOutputCalendar = options?.defaultOutputCalendar || 'gregory';\n    this.setLocale(dateLocale || DateTime.local().locale);\n    this.setLocale(this.translateService.currentLang);\n    if (this.translateService !== undefined) {\n      this.translateService.onLangChange.pipe(takeUntilDestroyed()).subscribe(({\n        lang\n      }) => {\n        this.setLocale(lang);\n      });\n    }\n  }\n  getYear(date) {\n    return date.year;\n  }\n  getMonth(date) {\n    // Luxon works with 1-indexed months whereas our code expects 0-indexed.\n    return date.month - 1;\n  }\n  getDate(date) {\n    return date.day;\n  }\n  getDayOfWeek(date) {\n    return date.weekday;\n  }\n  getMonthNames(style) {\n    // Adding outputCalendar option, because LuxonInfo doesn't get effected by LuxonSettings\n    return Info.months(style, {\n      locale: this.locale,\n      outputCalendar: this._defaultOutputCalendar\n    });\n  }\n  getDateNames() {\n    // At the time of writing, Luxon doesn't offer similar\n    // functionality so we have to fall back to the Intl API.\n    const dtf = new Intl.DateTimeFormat(this.locale, {\n      day: 'numeric',\n      timeZone: 'utc'\n    });\n    // Format a UTC date in order to avoid DST issues.\n    return luxonRange(31, i => dtf.format(DateTime.utc(2017, 1, i + 1).toJSDate()));\n  }\n  getDayOfWeekNames(style) {\n    // Note that we shift the array once, because Luxon returns Monday as the\n    // first day of the week, whereas our logic assumes that it's Sunday. See:\n    // https://moment.github.io/luxon/api-docs/index.html#infoweekdays\n    const days = Info.weekdays(style, {\n      locale: this.locale\n    });\n    days.unshift(days.pop());\n    return days;\n  }\n  getYearName(date) {\n    return date.toFormat('yyyy', this._getOptions());\n  }\n  getFirstDayOfWeek() {\n    return this._firstDayOfWeek;\n  }\n  getNumDaysInMonth(date) {\n    return date.daysInMonth;\n  }\n  clone(date) {\n    return DateTime.fromObject(date.toObject(), this._getOptions());\n  }\n  createDate(year, month, date) {\n    const options = this._getOptions();\n    if (month < 0 || month > 11) {\n      throw Error(`Invalid month index \"${month}\". Month index has to be between 0 and 11.`);\n    }\n    if (date < 1) {\n      throw Error(`Invalid date \"${date}\". Date has to be greater than 0.`);\n    }\n    // Luxon uses 1-indexed months so we need to add one to the month.\n    const result = this._useUTC ? DateTime.utc(year, month + 1, date, options) : DateTime.local(year, month + 1, date, options);\n    if (!this.isValid(result)) {\n      throw Error(`Invalid date \"${date}\". Reason: \"${result.invalidReason}\".`);\n    }\n    return result;\n  }\n  today() {\n    const options = this._getOptions();\n    return this._useUTC ? DateTime.utc(options) : DateTime.local(options);\n  }\n  parse(value, parseFormat) {\n    const options = this._getOptions();\n    if (typeof value == 'string' && value.length > 0) {\n      // This is screwing things up as 062005 would become 05 0602\n      // const iso8601Date = LuxonDateTime.fromISO(value, options);\n      //\n      // if (this.isValid(iso8601Date)) {\n      //   console.log(iso8601Date);\n      //   return iso8601Date;\n      // }\n      const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];\n      if (!parseFormat.length) {\n        throw Error('Formats array must not be empty.');\n      }\n      for (const format of formats) {\n        const fromFormat = DateTime.fromFormat(value, format, options);\n        if (this.isValid(fromFormat)) {\n          return fromFormat;\n        }\n      }\n      return this.invalid();\n    } else if (typeof value === 'number') {\n      return DateTime.fromMillis(value, options);\n    } else if (value instanceof Date) {\n      return DateTime.fromJSDate(value, options);\n    } else if (value instanceof DateTime) {\n      return DateTime.fromMillis(value.toMillis(), options);\n    }\n    return null;\n  }\n  format(date, displayFormat) {\n    if (!this.isValid(date)) {\n      throw Error('LuxonDateAdapter: Cannot format invalid date.');\n    }\n    if (this._useUTC) {\n      return date.setLocale(this.locale).setZone('utc').toFormat(displayFormat);\n    } else {\n      return date.setLocale(this.locale).toFormat(displayFormat);\n    }\n  }\n  addCalendarYears(date, years) {\n    return date.reconfigure(this._getOptions()).plus({\n      years\n    });\n  }\n  addCalendarMonths(date, months) {\n    return date.reconfigure(this._getOptions()).plus({\n      months\n    });\n  }\n  addCalendarDays(date, days) {\n    return date.reconfigure(this._getOptions()).plus({\n      days\n    });\n  }\n  toIso8601(date) {\n    return date.toISO();\n  }\n  /**\n   * Returns the given value if given a valid Luxon or null. Deserializes valid ISO 8601 strings\n   * (https://www.ietf.org/rfc/rfc3339.txt) and valid Date objects into valid DateTime and empty\n   * string into null. Returns an invalid date for all other values.\n   */\n  deserialize(value) {\n    const options = this._getOptions();\n    let date;\n    if (value instanceof Date) {\n      date = DateTime.fromJSDate(value, options);\n    }\n    if (typeof value === 'string') {\n      if (!value) {\n        return null;\n      }\n      date = DateTime.fromISO(value, options);\n    }\n    if (date && this.isValid(date)) {\n      return date;\n    }\n    return super.deserialize(value);\n  }\n  isDateInstance(obj) {\n    return obj instanceof DateTime;\n  }\n  isValid(date) {\n    return date.isValid;\n  }\n  invalid() {\n    return DateTime.invalid('Invalid Luxon DateTime object.');\n  }\n  setTime(target, hours, minutes, seconds) {\n    //if (typeof ngDevMode === 'undefined' || ngDevMode) {\n    if (hours < 0 || hours > 23) {\n      throw Error(`Invalid hours \"${hours}\". Hours value must be between 0 and 23.`);\n    }\n    if (minutes < 0 || minutes > 59) {\n      throw Error(`Invalid minutes \"${minutes}\". Minutes value must be between 0 and 59.`);\n    }\n    if (seconds < 0 || seconds > 59) {\n      throw Error(`Invalid seconds \"${seconds}\". Seconds value must be between 0 and 59.`);\n    }\n    //}\n    return this.clone(target).set({\n      hour: hours,\n      minute: minutes,\n      second: seconds,\n      millisecond: 0\n    });\n  }\n  getHours(date) {\n    return date.hour;\n  }\n  getMinutes(date) {\n    return date.minute;\n  }\n  getSeconds(date) {\n    return date.second;\n  }\n  parseTime(value, parseFormat) {\n    const result = this.parse(value, parseFormat);\n    if ((!result || !this.isValid(result)) && typeof value === 'string') {\n      // It seems like Luxon doesn't work well cross-browser for strings that have\n      // additional characters around the time. Try parsing without those characters.\n      return this.parse(value.replace(/[^0-9:(AM|PM)]/gi, ''), parseFormat) || result;\n    }\n    return result;\n  }\n  addSeconds(date, amount) {\n    return date.reconfigure(this._getOptions()).plus({\n      seconds: amount\n    });\n  }\n  /** Gets the options that should be used when constructing a new `DateTime` object. */\n  _getOptions() {\n    return {\n      zone: this._useUTC ? 'utc' : undefined,\n      locale: this.locale,\n      outputCalendar: this._defaultOutputCalendar\n    };\n  }\n  static {\n    this.ɵfac = function OnemrvaLuxonDateAdapter_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || OnemrvaLuxonDateAdapter)(i0.ɵɵinject(i1$4.TranslateService));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: OnemrvaLuxonDateAdapter,\n      factory: OnemrvaLuxonDateAdapter.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(OnemrvaLuxonDateAdapter, [{\n    type: Injectable\n  }], () => [{\n    type: i1$4.TranslateService\n  }], null);\n})();\nfunction onemrvaDateNativeYearMonthProvider() {\n  return [{\n    provide: MAT_DATE_FORMATS,\n    useValue: ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT\n  }, {\n    provide: DateAdapter,\n    useClass: OnemrvaNativeDateAdapter\n  }];\n}\nfunction onemrvaDateNativeProvider() {\n  return [{\n    provide: MAT_DATE_FORMATS,\n    useValue: ONEMRVA_MAT_NATIVE_DATE_FORMAT\n  }, {\n    provide: DateAdapter,\n    useClass: OnemrvaNativeDateAdapter\n  }];\n}\nfunction onemrvaDateLuxonProvider() {\n  return [{\n    provide: MAT_DATE_FORMATS,\n    useValue: ONEMRVA_MAT_LUXON_DATE_FORMATS\n  }, {\n    provide: DateAdapter,\n    useClass: OnemrvaLuxonDateAdapter\n  }];\n}\nfunction onemrvaDateLuxonYearMonthProvider() {\n  return [{\n    provide: MAT_DATE_FORMATS,\n    useValue: ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS\n  }, {\n    provide: DateAdapter,\n    useClass: OnemrvaLuxonDateAdapter\n  }];\n}\nfunction onemrvaThemeProvider() {\n  return [{\n    provide: ErrorHandler,\n    useClass: OnemrvaErrorHandler\n  }, {\n    provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,\n    useValue: {\n      appearance: 'outline',\n      floatLabel: 'always'\n    }\n  }, {\n    provide: OverlayContainer,\n    useClass: WebComponentOverlayContainer\n  }];\n}\n\n/*\n * Public API Surface of shared\n */\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CDNUrlModeOptions, CDN_URLS, CDN_URL_MODE, CacheService, ClipboardIconComponent, CommonCountryLookupService, DefaultStorage, DigitOnlyDirective, HttpRequestCache, IBAN_SUPPORTED_COUNTRIES, IfWidthIsDirective, LOOKUP_COUNTRY_SERVICE_URL, LOOKUP_COUNTRY_URL, MatRowClickableDirective, NISS_MASK, ONEMRVA_MAT_LUXON_DATE_FORMATS, ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS, ONEMRVA_MAT_NATIVE_DATE_FORMAT, ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT, OnemRvaCDNCountryService, OnemRvaCDNMimeService, OnemRvaCDNService, OnemRvaClipboardDirective, OnemRvaColorDirective, OnemRvaIconRightDirective, OnemRvaOSMService, OnemrvaBcePipe, OnemrvaDateFormatDirective, OnemrvaErrorHandler, OnemrvaLuxonDateAdapter, OnemrvaMaskDirective, OnemrvaMissingTranslationHandler, OnemrvaNativeDateAdapter, OnemrvaNissPipe, OnemrvaSharedModule, OnemrvaTranslateCDNLoader, OnemrvaTranslateHttpLoader, OnemrvaValidators, RequestTimes, SEPA_ONLY_SUPPORTED_COUNTRIES, WebComponentOverlayContainer, bankAccountValidator, directives, onemrvaDateLuxonProvider, onemrvaDateLuxonYearMonthProvider, onemrvaDateNativeProvider, onemrvaDateNativeYearMonthProvider, onemrvaThemeProvider, setTranslationLanguage, setTranslationLanguageFromWO };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBA,IAAM,cAAN,MAAkB;AAAA,EAChB;AAAA,EACA;AAAA,EACA,YAAY,MAAM,WAAW;AAC3B,SAAK,YAAY;AACjB,UAAM,WAAW,KAAK,YAAY,KAAK,UAAU,cAAc,UAAU;AACzE,UAAM,SAAS,SAAS;AAIxB,WAAO,WAAW;AAClB,WAAO,MAAM,OAAO,UAAU;AAC9B,WAAO,OAAO;AACd,aAAS,aAAa,eAAe,MAAM;AAC3C,aAAS,QAAQ;AAEjB,aAAS,WAAW;AAGpB,KAAC,KAAK,UAAU,qBAAqB,KAAK,UAAU,MAAM,YAAY,QAAQ;AAAA,EAChF;AAAA;AAAA,EAEA,OAAO;AACL,UAAM,WAAW,KAAK;AACtB,QAAI,aAAa;AACjB,QAAI;AAEF,UAAI,UAAU;AACZ,cAAM,eAAe,KAAK,UAAU;AACpC,iBAAS,OAAO;AAChB,iBAAS,kBAAkB,GAAG,SAAS,MAAM,MAAM;AACnD,qBAAa,KAAK,UAAU,YAAY,MAAM;AAC9C,YAAI,cAAc;AAChB,uBAAa,MAAM;AAAA,QACrB;AAAA,MACF;AAAA,IACF,QAAQ;AAAA,IAGR;AACA,WAAO;AAAA,EACT;AAAA;AAAA,EAEA,UAAU;AACR,UAAM,WAAW,KAAK;AACtB,QAAI,UAAU;AACZ,eAAS,OAAO;AAChB,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACF;AAKA,IAAM,YAAN,MAAM,WAAU;AAAA,EACd,YAAY,OAAO,QAAQ;AAAA,EAC3B,cAAc;AAAA,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOf,KAAK,MAAM;AACT,UAAM,cAAc,KAAK,UAAU,IAAI;AACvC,UAAM,aAAa,YAAY,KAAK;AACpC,gBAAY,QAAQ;AACpB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,MAAM;AACd,WAAO,IAAI,YAAY,MAAM,KAAK,SAAS;AAAA,EAC7C;AAAA,EACA,OAAO,OAAO,SAAS,kBAAkB,mBAAmB;AAC1D,WAAO,KAAK,qBAAqB,YAAW;AAAA,EAC9C;AAAA,EACA,OAAO,QAA0B,mBAAmB;AAAA,IAClD,OAAO;AAAA,IACP,SAAS,WAAU;AAAA,IACnB,YAAY;AAAA,EACd,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,WAAW,CAAC;AAAA,IAClF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG,IAAI;AACpB,GAAG;AAGH,IAAM,+BAA+B,IAAI,eAAe,8BAA8B;AAKtF,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,aAAa,OAAO,SAAS;AAAA,EAC7B,UAAU,OAAO,MAAM;AAAA;AAAA,EAEvB,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKP,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,SAAS,IAAI,aAAa;AAAA;AAAA,EAE1B,WAAW,oBAAI,IAAI;AAAA;AAAA,EAEnB;AAAA;AAAA,EAEA;AAAA,EACA,cAAc;AACZ,UAAM,SAAS,OAAO,8BAA8B;AAAA,MAClD,UAAU;AAAA,IACZ,CAAC;AACD,QAAI,UAAU,OAAO,YAAY,MAAM;AACrC,WAAK,WAAW,OAAO;AAAA,IACzB;AAAA,EACF;AAAA;AAAA,EAEA,KAAK,WAAW,KAAK,UAAU;AAC7B,QAAI,WAAW,GAAG;AAChB,UAAI,oBAAoB;AACxB,YAAM,UAAU,KAAK,WAAW,UAAU,KAAK,IAAI;AACnD,WAAK,SAAS,IAAI,OAAO;AACzB,YAAM,UAAU,MAAM;AACpB,cAAM,aAAa,QAAQ,KAAK;AAChC,YAAI,CAAC,cAAc,EAAE,qBAAqB,CAAC,KAAK,YAAY;AAE1D,eAAK,kBAAkB,KAAK,QAAQ,kBAAkB,MAAM,WAAW,SAAS,CAAC,CAAC;AAAA,QACpF,OAAO;AACL,eAAK,kBAAkB;AACvB,eAAK,SAAS,OAAO,OAAO;AAC5B,kBAAQ,QAAQ;AAChB,eAAK,OAAO,KAAK,UAAU;AAAA,QAC7B;AAAA,MACF;AACA,cAAQ;AAAA,IACV,OAAO;AACL,WAAK,OAAO,KAAK,KAAK,WAAW,KAAK,KAAK,IAAI,CAAC;AAAA,IAClD;AAAA,EACF;AAAA,EACA,cAAc;AACZ,QAAI,KAAK,iBAAiB;AACxB,mBAAa,KAAK,eAAe;AAAA,IACnC;AACA,SAAK,SAAS,QAAQ,UAAQ,KAAK,QAAQ,CAAC;AAC5C,SAAK,SAAS,MAAM;AACpB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,OAAO,OAAO,SAAS,2BAA2B,mBAAmB;AACnE,WAAO,KAAK,qBAAqB,qBAAoB;AAAA,EACvD;AAAA,EACA,OAAO,OAAyB,kBAAkB;AAAA,IAChD,MAAM;AAAA,IACN,WAAW,CAAC,CAAC,IAAI,sBAAsB,EAAE,CAAC;AAAA,IAC1C,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,UAAI,KAAK,GAAG;AACV,QAAG,WAAW,SAAS,SAAS,8CAA8C;AAC5E,iBAAO,IAAI,KAAK;AAAA,QAClB,CAAC;AAAA,MACH;AAAA,IACF;AAAA,IACA,QAAQ;AAAA,MACN,MAAM,CAAC,GAAG,sBAAsB,MAAM;AAAA,MACtC,UAAU,CAAC,GAAG,8BAA8B,UAAU;AAAA,IACxD;AAAA,IACA,SAAS;AAAA,MACP,QAAQ;AAAA,IACV;AAAA,EACF,CAAC;AACH;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,MAAM;AAAA,QACJ,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC,GAAG;AAAA,IACZ,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,MAAM,CAAC,oBAAoB;AAAA,IAC7B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,4BAA4B;AAAA,IACrC,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,OAAO,OAAO,SAAS,wBAAwB,mBAAmB;AAChE,WAAO,KAAK,qBAAqB,kBAAiB;AAAA,EACpD;AAAA,EACA,OAAO,OAAyB,iBAAiB;AAAA,IAC/C,MAAM;AAAA,IACN,SAAS,CAAC,kBAAkB;AAAA,IAC5B,SAAS,CAAC,kBAAkB;AAAA,EAC9B,CAAC;AAAA,EACD,OAAO,OAAyB,iBAAiB,CAAC,CAAC;AACrD;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,kBAAkB;AAAA,MAC5B,SAAS,CAAC,kBAAkB;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;;;ACjNI,SAAS,YAAY,MAAM,mBAAmB;AACnD,MAAI,sBAAsB,QAAQ;AAChC,wBAAoB;AAAA,MAClB,aAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,MAAI,MAAM,IAAI,OAAO,cAAc,EAAE;AACrC,MAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,MAAI,OAAO,aAAa,WAAW;AACnC,MAAI,SAAS,UAAa,KAAK,gBAAgB,UAAa,KAAK,gBAAgB,QAAQ,KAAK,UAAU,OAAW,QAAO;AAC1H,SAAO,KAAK,UAAU,KAAK,UAAU,IAAI,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,KAAK,YAAY,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,oBAAoB,IAAI,MAAM,kBAAkB,eAAe,CAAC,SAAS,IAAI;AAC7L;AAIO,IAAI;AAAA,CACV,SAAUA,uBAAsB;AAC/B,EAAAA,sBAAqBA,sBAAqB,gBAAgB,IAAI,CAAC,IAAI;AACnE,EAAAA,sBAAqBA,sBAAqB,eAAe,IAAI,CAAC,IAAI;AAClE,EAAAA,sBAAqBA,sBAAqB,iBAAiB,IAAI,CAAC,IAAI;AACpE,EAAAA,sBAAqBA,sBAAqB,iBAAiB,IAAI,CAAC,IAAI;AACpE,EAAAA,sBAAqBA,sBAAqB,mBAAmB,IAAI,CAAC,IAAI;AACtE,EAAAA,sBAAqBA,sBAAqB,mBAAmB,IAAI,CAAC,IAAI;AACtE,EAAAA,sBAAqBA,sBAAqB,gCAAgC,IAAI,CAAC,IAAI;AACnF,EAAAA,sBAAqBA,sBAAqB,kBAAkB,IAAI,CAAC,IAAI;AACvE,GAAG,yBAAyB,uBAAuB,CAAC,EAAE;AAiB/C,SAAS,aAAa,MAAM,mBAAmB;AACpD,MAAI,sBAAsB,QAAQ;AAChC,wBAAoB;AAAA,MAClB,aAAa;AAAA,IACf;AAAA,EACF;AACA,MAAI,SAAS;AAAA,IACX,YAAY,CAAC;AAAA,IACb,OAAO;AAAA,EACT;AACA,MAAI,SAAS,UAAa,SAAS,QAAQ,SAAS,IAAI;AACtD,QAAI,OAAO,aAAa,KAAK,MAAM,GAAG,CAAC,CAAC;AACxC,QAAI,CAAC,QAAQ,EAAE,KAAK,eAAe,KAAK,QAAQ;AAC9C,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,aAAa;AACzD,aAAO;AAAA,IACT;AACA,QAAI,QAAQ,KAAK,SAAS,KAAK,UAAU,KAAK,QAAQ;AACpD,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,eAAe;AAAA,IAC7D;AACA,QAAI,QAAQ,KAAK,eAAe,CAAC,gBAAgB,KAAK,MAAM,CAAC,GAAG,KAAK,WAAW,GAAG;AACjF,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,eAAe;AAAA,IAC7D;AACA,QAAI,QAAQ,KAAK,wBAAwB,CAAC,KAAK,qBAAqB,KAAK,MAAM,CAAC,CAAC,GAAG;AAClF,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,8BAA8B;AAAA,IAC5E;AACA,QAAI,MAAM,IAAI,OAAO,cAAc,EAAE;AACrC,QAAI,CAAC,IAAI,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC,GAAG;AAC/B,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,iBAAiB;AAAA,IAC/D;AACA,QAAI,OAAO,WAAW,QAAQ,qBAAqB,eAAe,MAAM,MAAM,CAAC,oBAAoB,IAAI,GAAG;AACxG,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,iBAAiB;AAAA,IAC/D;AACA,QAAI,CAAC,kBAAkB,eAAe,SAAS,IAAI,GAAG;AACpD,aAAO,QAAQ;AACf,aAAO,WAAW,KAAK,qBAAqB,gBAAgB;AAAA,IAC9D;AAAA,EACF,OAAO;AACL,WAAO,QAAQ;AACf,WAAO,WAAW,KAAK,qBAAqB,cAAc;AAAA,EAC5D;AACA,SAAO;AACT;AAaO,SAAS,YAAY,MAAM,aAAa;AAC7C,MAAI,SAAS,UAAa,SAAS,QAAQ,gBAAgB,UAAa,gBAAgB,KAAM,QAAO;AACrG,MAAI,OAAO,aAAa,WAAW;AACnC,MAAI,SAAS,UAAa,SAAS,QAAQ,KAAK,gBAAgB,UAAa,KAAK,gBAAgB,QAAQ,KAAK,UAAU,UAAa,KAAK,UAAU,KAAM,QAAO;AAClK,MAAI,KAAK,QAAQ,MAAM,KAAK,UAAU,gBAAgB,MAAM,KAAK,WAAW,GAAG;AAC7E,QAAI,KAAK,sBAAsB;AAC7B,aAAO,KAAK,qBAAqB,KAAK,QAAQ,WAAW,EAAE,CAAC;AAAA,IAC9D;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAgCO,SAAS,SAAS,MAAM;AAC7B,MAAI,SAAS,UAAa,SAAS,KAAM,QAAO;AAChD,MAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,MAAI,kBAAkB,CAAC,MAAM,IAAI;AACjC,MAAI,CAAC,gBAAgB,SAAS,WAAW,EAAG,QAAO;AACnD,MAAI,MAAM,IAAI,OAAO,uBAAuB,EAAE;AAC9C,SAAO,IAAI,KAAK,KAAK,MAAM,GAAG,CAAC,CAAC;AAClC;AA4BO,SAAS,YAAY,MAAM;AAChC,MAAI,SAAS,CAAC;AACd,MAAI,cAAc,qBAAqB,IAAI;AAC3C,SAAO,OAAO,eAAe;AAC7B,MAAI,CAAC,CAAC,eAAe,YAAY,WAAW,GAAG;AAC7C,WAAO,OAAO,YAAY,MAAM,CAAC;AACjC,WAAO,cAAc,YAAY,MAAM,GAAG,CAAC;AAC3C,WAAO,QAAQ;AACf,QAAI,OAAO,aAAa,OAAO,WAAW;AAC1C,QAAI,KAAK,qBAAqB;AAC5B,UAAI,KAAK,KAAK,oBAAoB,MAAM,GAAG;AAC3C,UAAI,WAAW,SAAS,GAAG,CAAC,CAAC;AAC7B,UAAI,SAAS,SAAS,GAAG,CAAC,CAAC;AAC3B,aAAO,gBAAgB,OAAO,KAAK,MAAM,UAAU,SAAS,CAAC;AAAA,IAC/D;AACA,QAAI,KAAK,iBAAiB;AACxB,UAAI,KAAK,KAAK,gBAAgB,MAAM,GAAG;AACvC,UAAI,WAAW,SAAS,GAAG,CAAC,CAAC;AAC7B,UAAI,SAAS,SAAS,GAAG,CAAC,CAAC;AAC3B,aAAO,iBAAiB,OAAO,KAAK,MAAM,UAAU,SAAS,CAAC;AAAA,IAChE;AACA,QAAI,KAAK,oBAAoB;AAC3B,UAAI,KAAK,KAAK,mBAAmB,MAAM,GAAG;AAC1C,UAAI,WAAW,SAAS,GAAG,CAAC,CAAC;AAC7B,UAAI,SAAS,SAAS,GAAG,CAAC,CAAC;AAC3B,aAAO,mBAAmB,OAAO,KAAK,MAAM,UAAU,SAAS,CAAC;AAAA,IAClE;AAAA,EACF,OAAO;AACL,WAAO,QAAQ;AAAA,EACjB;AACA,SAAO;AACT;AAMA,SAAS,gBAAgB,MAAM,SAAS;AACtC,MAAI,MAAM,IAAI,OAAO,SAAS,EAAE;AAChC,SAAO,IAAI,KAAK,IAAI;AACtB;AAUO,SAAS,qBAAqB,MAAM;AACzC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,KAAK,QAAQ,UAAU,EAAE,EAAE,YAAY;AAChD;AAcO,SAAS,mBAAmB,MAAM,WAAW;AAClD,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO;AAAA,EACT;AACA,MAAI,cAAc,UAAa,cAAc,MAAM;AACjD,gBAAY;AAAA,EACd;AACA,MAAI,kBAAkB,qBAAqB,IAAI;AAE/C,MAAI,oBAAoB,MAAM;AAC5B,WAAO;AAAA,EACT;AACA,SAAO,gBAAgB,QAAQ,gBAAgB,OAAO,SAAS;AACjE;AAMA,SAAS,oBAAoB,MAAM;AACjC,MAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,MAAI,mBAAmB,SAAS,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE;AACpD,MAAI,OAAO,KAAK,MAAM,CAAC;AAkBvB,MAAI,mBAAmB,wBAAwB,GAAG,OAAO,IAAI,EAAE,OAAO,aAAa,IAAI,CAAC;AACxF,MAAI,OAAO,QAAQ,gBAAgB;AACnC,SAAO,KAAK,SAAS;AACvB;AAOA,SAAS,wBAAwB,KAAK;AAGpC,SAAO,IAAI,MAAM,EAAE,EAAE,IAAI,SAAU,GAAG;AACpC,QAAI,OAAO,EAAE,WAAW,CAAC;AACzB,WAAO,QAAQ,MAAM,OAAO,IAAI,SAAS,IAAI;AAAA,EAC/C,CAAC,EAAE,KAAK,EAAE;AACZ;AAwEO,IAAI;AAAA,CACV,SAAUC,sBAAqB;AAC9B,EAAAA,qBAAoBA,qBAAoB,eAAe,IAAI,CAAC,IAAI;AAChE,EAAAA,qBAAoBA,qBAAoB,cAAc,IAAI,CAAC,IAAI;AAC/D,EAAAA,qBAAoBA,qBAAoB,gBAAgB,IAAI,CAAC,IAAI;AACnE,GAAG,wBAAwB,sBAAsB,CAAC,EAAE;AA0DpD,IAAI,kBAAkB,SAAU,MAAM;AACpC,MAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3C,MAAI,8BAA8B,KAAK,QAAQ,WAAW,EAAE;AAC5D,MAAI,eAAe,SAAS,4BAA4B,OAAO,EAAE,GAAG,EAAE;AACtE,MAAI,0BAA0B,4BAA4B,UAAU,GAAG,EAAE;AACzE,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,IAAI,SAAS;AACvC,WAAO,SAAS,wBAAwB,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,EAC5E;AACA,MAAI,YAAY,MAAM;AACtB,SAAO,kBAAkB,cAAc,IAAI,IAAI,KAAK;AACtD;AAMA,IAAI,mBAAmB,SAAU,MAAM;AACrC,MAAI,WAAW,KAAK,QAAQ,WAAW,EAAE;AACzC,MAAI,eAAe,SAAS,SAAS,UAAU,GAAG,SAAS,SAAS,CAAC,GAAG,EAAE;AAC1E,MAAI,WAAW,SAAS,SAAS,UAAU,SAAS,SAAS,GAAG,SAAS,MAAM,GAAG,EAAE;AACpF,MAAI,YAAY,eAAe,OAAO,IAAI,KAAK,eAAe;AAC9D,SAAO,cAAc;AACvB;AAMA,IAAI,UAAU,SAAU,kBAAkB;AACxC,SAAO,iBAAiB,SAAS,GAAG;AAMlC,QAAI,OAAO,iBAAiB,MAAM,GAAG,CAAC;AACtC,QAAI,UAAU,SAAS,MAAM,EAAE;AAC/B,QAAI,MAAM,OAAO,GAAG;AAClB,aAAO;AAAA,IACT;AACA,uBAAmB,UAAU,KAAK,iBAAiB,MAAM,KAAK,MAAM;AAAA,EACtE;AACA,SAAO,SAAS,kBAAkB,EAAE,IAAI;AAC1C;AAOA,IAAI,mBAAmB,SAAU,MAAM;AACrC,MAAI,WAAW,KAAK,QAAQ,WAAW,EAAE;AACzC,MAAI,WAAW,QAAQ,QAAQ;AAC/B,SAAO,aAAa;AACtB;AAMA,IAAI,kBAAkB,SAAU,MAAM;AACpC,MAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAClC,MAAI,eAAe,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AAC9C,MAAI,UAAU,KAAK,UAAU,GAAG,CAAC;AACjC,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,WAAO,SAAS,QAAQ,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,EAC5D;AACA,MAAI,YAAY,MAAM;AACtB,SAAO,kBAAkB,cAAc,IAAI,IAAI,KAAK;AACtD;AAMA,IAAI,iBAAiB,SAAU,MAAM;AACnC,MAAI,oBAAoB,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AAChD,MAAI,iBAAiB,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,CAAC;AACnD,MAAI,oBAAoB,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AACnD,MAAI,iBAAiB,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AAChD,MAAI,aAAa,KAAK,UAAU,GAAG,CAAC;AACpC,MAAI,UAAU,KAAK,UAAU,IAAI,EAAE;AACnC,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,GAAG,SAAS;AACtC,WAAO,SAAS,WAAW,OAAO,KAAK,GAAG,EAAE,IAAI,kBAAkB,KAAK;AAAA,EACzE;AACA,MAAI,YAAY,MAAM;AACtB,MAAI,uBAAuB,cAAc,IAAI,IAAI,cAAc,IAAI,IAAI,KAAK,YAAY;AACtF,WAAO;AAAA,EACT;AACA,QAAM;AACN,WAAS,QAAQ,GAAG,QAAQ,IAAI,SAAS;AACvC,WAAO,SAAS,QAAQ,OAAO,KAAK,GAAG,EAAE,IAAI,eAAe,KAAK;AAAA,EACnE;AACA,cAAY,MAAM;AAClB,SAAO,oBAAoB,cAAc,IAAI,IAAI,cAAc,IAAI,IAAI,KAAK;AAC9E;AAMA,IAAI,eAAe,SAAU,SAAS,SAAS;AAC7C,MAAI,KAAK;AACT,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACnD,UAAM,SAAS,QAAQ,OAAO,KAAK,GAAG,EAAE;AACxC,QAAI,KAAK,OAAO,GAAG;AACjB,WAAK,KAAK;AAAA,IACZ;AACA,SAAK,KAAK;AACV,SAAK,KAAK;AAAA,EACZ;AACA,SAAO,aAAa,KAAK,OAAO,KAAK,IAAI,KAAK;AAChD;AAMA,IAAI,oBAAoB,SAAU,MAAM;AACtC,MAAI,oBAAoB,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AACnD,MAAI,iBAAiB,SAAS,KAAK,OAAO,EAAE,GAAG,EAAE;AACjD,MAAI,aAAa,KAAK,UAAU,GAAG,CAAC;AACpC,MAAI,UAAU,KAAK,UAAU,GAAG,EAAE;AAClC,SAAO,aAAa,YAAY,iBAAiB,KAAK,aAAa,SAAS,cAAc;AAC5F;AAMA,IAAI,0BAA0B,SAAU,MAAM;AAC5C,MAAI,gBAAgB,CAAC,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AACtC,MAAI,gBAAgB,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAClD,MAAI,gBAAgB,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AAC/C,MAAI,gBAAgB,SAAS,KAAK,OAAO,EAAE,GAAG,EAAE;AAChD,MAAI,SAAS,KAAK,UAAU,GAAG,CAAC;AAChC,MAAI,SAAS,KAAK,UAAU,IAAI,EAAE;AAClC,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,WAAO,SAAS,OAAO,OAAO,KAAK,GAAG,EAAE,IAAI,cAAc,KAAK;AAAA,EACjE;AACA,MAAI,YAAY,MAAM;AACtB,MAAI,mBAAmB,cAAc,IAAI,IAAI,cAAc,IAAI,IAAI,KAAK,YAAY;AAClF,WAAO;AAAA,EACT;AACA,QAAM;AACN,WAAS,QAAQ,GAAG,QAAQ,OAAO,QAAQ,SAAS;AAClD,WAAO,SAAS,OAAO,OAAO,KAAK,GAAG,EAAE,IAAI,cAAc,KAAK;AAAA,EACjE;AACA,cAAY,MAAM;AAClB,SAAO,mBAAmB,cAAc,IAAI,IAAI,cAAc,IAAI,IAAI,KAAK;AAC7E;AAMA,IAAI,oBAAoB,SAAU,MAAM;AACtC,MAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AACpD,MAAI,eAAe,SAAS,KAAK,OAAO,EAAE,GAAG,EAAE;AAC/C,MAAI,UAAU,KAAK,UAAU,GAAG,EAAE;AAClC,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,QAAQ,QAAQ,SAAS;AACnD,WAAO,SAAS,QAAQ,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,EAC5D;AACA,MAAI,YAAY,MAAM;AACtB,SAAO,kBAAkB,cAAc,IAAI,IAAI,KAAK;AACtD;AAOA,IAAI,kBAAkB,SAAU,MAAM;AACpC,MAAI,WAAW,KAAK,QAAQ,WAAW,EAAE;AACzC,MAAI,aAAa,MAAM,KAAK,QAAQ;AACpC,WAAS,QAAQ,GAAG,QAAQ,SAAS,QAAQ,SAAS;AACpD,QAAI,IAAI,WAAW,KAAK,EAAE,WAAW,CAAC;AACtC,QAAI,KAAK,IAAI;AACX,cAAQ,GAAG;AAAA,QACT,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,qBAAW,KAAK,IAAI;AACpB;AAAA,MACJ;AAAA,IACF;AAAA,EACF;AACA,MAAI,YAAY,QAAQ,WAAW,KAAK,EAAE,CAAC;AAC3C,SAAO,cAAc;AACvB;AAMA,IAAI,qBAAqB,SAAU,MAAM;AACvC,MAAI,UAAU,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC1D,MAAI,yBAAyB,SAAS,KAAK,OAAO,CAAC,GAAG,EAAE;AACxD,MAAI,oBAAoB,KAAK,UAAU,GAAG,CAAC;AAC3C,MAAI,MAAM;AACV,WAAS,QAAQ,GAAG,QAAQ,kBAAkB,QAAQ,SAAS;AAC7D,WAAO,SAAS,kBAAkB,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,EACtE;AACA,MAAI,YAAY,MAAM;AACtB,MAAI,4BAA4B,cAAc,IAAI,IAAI,KAAK,YAAY;AACrE,WAAO;AAAA,EACT;AACA,QAAM;AACN,MAAI,KAAK,SAAS,UAAU,GAAG;AAC7B,QAAI,iBAAiB,KAAK,UAAU,GAAG,EAAE;AACzC,QAAI,sBAAsB,SAAS,KAAK,OAAO,EAAE,GAAG,EAAE;AACtD,aAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,aAAO,SAAS,eAAe,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,IACnE;AACA,QAAI,cAAc,MAAM;AACxB,WAAO,yBAAyB,gBAAgB,IAAI,IAAI,KAAK;AAAA,EAC/D,OAAO;AACL,QAAI,iBAAiB,KAAK,UAAU,GAAG,EAAE;AACzC,QAAI,sBAAsB,SAAS,KAAK,OAAO,EAAE,GAAG,EAAE;AACtD,aAAS,QAAQ,GAAG,QAAQ,eAAe,QAAQ,SAAS;AAC1D,aAAO,SAAS,eAAe,OAAO,KAAK,GAAG,EAAE,IAAI,QAAQ,KAAK;AAAA,IACnE;AACA,QAAI,cAAc,MAAM;AACxB,WAAO,yBAAyB,gBAAgB,IAAI,IAAI,KAAK;AAAA,EAC/D;AACF;AAiBO,IAAI,eAAe;AAAA,EACxB,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,sBAAsB;AAAA,IACtB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,iBAAiB;AAAA,IACjB,cAAc;AAAA,IACd,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,sBAAsB;AAAA,IACtB,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,IACpB,iBAAiB;AAAA,IACjB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,cAAc;AAAA,IACd,MAAM;AAAA,EACR;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,oBAAoB;AAAA,EACtB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,EACtB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,EACf;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,MAAM;AAAA,IACN,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,IACd,oBAAoB;AAAA,IACpB,qBAAqB;AAAA,EACvB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI;AAAA,IACF,OAAO;AAAA,IACP,aAAa;AAAA,IACb,cAAc;AAAA,EAChB;AAAA,EACA,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AAAA,EACL,IAAI,CAAC;AACP;;;ACjpDA,IAAM,yBAAN,MAAM,wBAAuB;AAAA,EAC3B,YAAY,aAAa,kBAAkB;AACzC,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,+BAA+B,mBAAmB;AACrE,aAAO,KAAK,qBAAqB,yBAA2B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,CAAC;AAAA,IAClI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,oBAAoB,CAAC;AAAA,MAClC,OAAO;AAAA,MACP,MAAM;AAAA,MACN,QAAQ,CAAC,CAAC,GAAG,YAAY,CAAC;AAAA,MAC1B,UAAU,SAAS,gCAAgC,IAAI,KAAK;AAC1D,YAAI,KAAK,GAAG;AACV,UAAG,eAAe,GAAG,YAAY,CAAC;AAClC,UAAG,OAAO,GAAG,cAAc;AAC3B,UAAG,aAAa;AAAA,QAClB;AACA,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,cAAc,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,MACA,cAAc,CAAC,eAAkB,SAAS,kBAAqB,UAAU;AAAA,MACzE,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,wBAAwB,CAAC;AAAA,IAC/F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,SAAS,CAAC,eAAe,gBAAgB;AAAA,MACzC,UAAU;AAAA;AAAA,IACZ,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAI;AAAA,CACH,SAAUC,oBAAmB;AAC5B,EAAAA,mBAAkB,MAAM,IAAI;AAC5B,EAAAA,mBAAkB,KAAK,IAAI;AAC7B,GAAG,sBAAsB,oBAAoB,CAAC,EAAE;AAChD,IAAM,WAAW;AAAA,EACf,MAAM;AAAA,EACN,KAAK;AACP;AACA,IAAM,YAAY;AAClB,IAAM,qBAAqB;AAI3B,IAAM,yBAAyB,CAAC,YAAY,cAAc,YAAY,aAAa,aAAa,YAAY,aAAa,aAAa,UAAU,WAAW,WAAW,UAAU,WAAW,SAAS;AACpM,IAAM,8BAA8B,CAAC,UAAU,UAAU,QAAQ,WAAW,SAAS,UAAU,SAAS;AACxG,IAAM,6BAA6B,CAAC,UAAU,UAAU,QAAQ,WAAW,SAAS,UAAU,SAAS;AACvG,IAAM,uBAAuB,CAAC,YAAY,aAAa,YAAY,aAAa,aAAa,YAAY,aAAa,aAAa,UAAU,WAAW,WAAW,UAAU,WAAW,SAAS;AACjM,IAAM,iCAAiC;AAAA,EACrC,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AACF;AACA,IAAM,uCAAuC;AAAA,EAC3C,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AACF;AACA,IAAM,uCAAuC;AAAA,EAC3C,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AACF;AACA,IAAM,iCAAiC;AAAA,EACrC,OAAO;AAAA,IACL,WAAW;AAAA,EACb;AAAA,EACA,SAAS;AAAA,IACP,WAAW;AAAA,IACX,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,oBAAoB;AAAA,EACtB;AACF;AACA,IAAM,eAAe,IAAI,eAAe,8CAA8C;AAUtF,IAAM,+BAAN,MAAM,8BAA6B;AAAA,EACjC,YAAYC,WAAU,WAAW;AAC/B,SAAK,YAAY;AACjB,SAAK,oBAAoB;AACzB,SAAK,YAAYA;AAAA,EACnB;AAAA,EACA,cAAc;AACZ,SAAK,mBAAmB,OAAO;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,sBAAsB;AACpB,QAAI,KAAK,sBAAsB,MAAM;AACnC,WAAK,iBAAiB;AAAA,IACxB;AACA,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB;AACjB,UAAM,iBAAiB;AAGvB,QAAI,KAAK,UAAU,aAAa,mBAAmB,GAAG;AACpD,YAAM,6BAA6B,KAAK,UAAU,iBAAiB,IAAI,cAAc,yBAA8B,cAAc,mBAAmB;AAGpJ,iCAA2B,QAAQ,cAAY;AAC7C,iBAAS,OAAO;AAAA,MAClB,CAAC;AAAA,IACH;AACA,UAAM,gBAAgB,KAAK,UAAU,cAAc,KAAK;AACxD,kBAAc,UAAU,IAAI,eAAe;AAC3C,UAAM,YAAY,KAAK,UAAU,cAAc,KAAK;AACpD,cAAU,UAAU,IAAI,cAAc;AAStC,QAAI,mBAAmB,GAAG;AACxB,gBAAU,aAAa,YAAY,MAAM;AAAA,IAC3C,WAAW,CAAC,KAAK,UAAU,WAAW;AACpC,gBAAU,aAAa,YAAY,QAAQ;AAAA,IAC7C;AACA,kBAAc,YAAY,SAAS;AACnC,SAAK,UAAU,KAAK,YAAY,aAAa;AAC7C,SAAK,oBAAoB;AAAA,EAC3B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qCAAqC,mBAAmB;AAC3E,aAAO,KAAK,qBAAqB,+BAAiC,SAAS,QAAQ,GAAM,SAAc,QAAQ,CAAC;AAAA,IAClH;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,8BAA6B;AAAA,MACtC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,8BAA8B,CAAC;AAAA,IACrG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,QAAQ;AAAA,IACjB,CAAC;AAAA,EACH,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,iBAAN,MAAqB;AAAA,EACnB,cAAc;AACZ,SAAK,UAAU,oBAAI,IAAI;AAAA,EACzB;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,KAAK,QAAQ,IAAI,GAAG;AAAA,EAC7B;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,SAAK,QAAQ,IAAI,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,WAAW,KAAK;AACd,SAAK,QAAQ,OAAO,GAAG;AAAA,EACzB;AACF;AACA,IAAM,eAAN,MAAmB;AAAA,EACjB,cAAc;AACZ,SAAK,UAAU,oBAAI,IAAI;AAAA,EACzB;AAAA,EACA,QAAQ,KAAK;AACX,WAAO,KAAK,QAAQ,IAAI,GAAG;AAAA,EAC7B;AAAA,EACA,QAAQ,KAAK,MAAM;AACjB,SAAK,QAAQ,IAAI,KAAK,IAAI;AAAA,EAC5B;AAAA,EACA,WAAW,KAAK;AACd,SAAK,QAAQ,OAAO,GAAG;AAAA,EACzB;AACF;AACA,IAAM,mBAAmB,oBAAkB;AACzC,SAAO,CAAC,QAAQ,YAAY,eAAe;AACzC,QAAI,EAAE,YAAY,iBAAiB,WAAW;AAC5C,YAAM,MAAM,yFAAyF;AAAA,IACvG;AACA,UAAM,iBAAiB,GAAG,OAAO,YAAY,IAAI,IAAI,UAAU;AAC/D,UAAM,iBAAiB,WAAW;AAClC,UAAM,UAAU,CAAC;AACjB,QAAI,cAAc;AAClB,eAAW,QAAQ,YAAa,MAAM;AACpC,YAAM,UAAU,gBAAgB,KAAK,MAAM,MAAM,GAAG,IAAI;AACxD,UAAI,CAAC,SAAS,WAAW,CAAC,OAAO,mBAAmB;AAClD,eAAO,oBAAoB,IAAI,eAAe;AAAA,MAChD;AACA,UAAI,SAAS,OAAO,CAAC,OAAO,uBAAuB;AACjD,eAAO,wBAAwB,IAAI,aAAa;AAAA,MAClD;AACA,YAAM,UAAU,SAAS,WAAW,OAAO;AAC3C,YAAM,MAAM,GAAG,cAAc,IAAI,KAAK,UAAU,IAAI,CAAC;AACrD,UAAI,MAAM;AACV,UAAI,SAAS,KAAK;AAChB,cAAM,OAAO,sBAAsB,QAAQ,GAAG;AAC9C,YAAI,CAAC,KAAK;AACR,gBAAM;AAAA,YACJ,aAAa,KAAK,IAAI;AAAA,YACtB,SAAS,IAAI,QAAQ;AAAA,UACvB;AAAA,QACF,WAAW,IAAI,cAAc,QAAQ,OAAO,KAAK,IAAI,GAAG;AACtD,kBAAQ,GAAG,IAAI;AACf,cAAI,cAAc,KAAK,IAAI;AAC3B,cAAI,QAAQ,KAAK;AAAA,QACnB;AACA,eAAO,sBAAsB,QAAQ,KAAK,GAAG;AAAA,MAC/C;AACA,YAAM,YAAY,MAAM,SAAS,aAAa,OAAO,KAAK,WAAW,KAAK;AAC1E,aAAO,QAAQ,QAAQ,GAAG,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,WAAS;AACrD;AACA,YAAI,UAAU,KAAM,OAAM;AAAA,MAC5B,CAAC,GAAG,WAAW,MAAM;AACnB,cAAM,aAAa,UAAU,KAAK,UAAU,IAAI,GAAG,UAAU,MAAM,eAAe,MAAM,MAAM,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,IAAI,MAAM;AACnH,iBAAO,QAAQ,GAAG;AAAA,QACpB,CAAC,GAAG,YAAY;AAAA,UACd,YAAY;AAAA,UACZ,UAAU,SAAS,YAAY;AAAA,UAC/B,YAAY,SAAS,cAAc;AAAA,QACrC,CAAC,GAAG,OAAO,MAAM;AACf,iBAAO,CAAC,QAAQ,GAAG;AAAA,QACrB,CAAC,GAAG,SAAS,MAAM;AACjB;AACA,cAAI,gBAAgB,KAAK,SAAS,UAAU;AAC1C,oBAAQ,WAAW,GAAG;AACtB,mBAAO,uBAAuB,WAAW,GAAG;AAAA,UAC9C;AAAA,QACF,CAAC,CAAC;AACF,gBAAQ,QAAQ,KAAK,UAAU;AAC/B,eAAO;AAAA,MACT,CAAC,CAAC;AAAA,IA+BJ;AACA,WAAO;AAAA,EACT;AACF;AACA,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,UAAU,OAAO;AACf,UAAM,IAAI;AACV,UAAM,aAAa,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,aAAa,UAAU,OAAO,KAAK,SAAS,aAAa,cAAc,WAAW,aAAa,QAAQ,KAAK;AACjM,QAAI,WAAW,QAAQ,EAAE,GAAG,KAAK,EAAG;AAEpC,UAAM,uBAAuB,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD,QAAI,EAAE,WAAW,qBAAqB,QAAQ,EAAE,GAAG,KAAK,EAAG;AAC3D,MAAE,eAAe;AAAA,EACnB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAoB;AAAA,IACvD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACjC,cAAc,SAAS,gCAAgC,IAAI,KAAK;AAC9D,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,8CAA8C,QAAQ;AACtF,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,cAAc;AACZ,SAAK,kBAAkB,IAAI,aAAa;AACxC,SAAK,QAAQ;AACb,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,QAAQ;AACN,SAAK,gBAAgB,KAAK;AAAA,EAC5B;AAAA;AAAA,EAEA,IAAI,eAAe;AACjB,WAAO,KAAK,gBAAgB;AAAA,EAC9B;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA0B;AAAA,IAC7D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,MAAM,mBAAmB,EAAE,CAAC;AAAA,MACzC,UAAU;AAAA,MACV,cAAc,SAAS,sCAAsC,IAAI,KAAK;AACpE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,kDAAkD,QAAQ;AACxF,mBAAO,IAAI,MAAM,MAAM;AAAA,UACzB,CAAC;AAAA,QACH;AACA,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,aAAa,IAAI,YAAY,EAAE,yBAAyB,IAAI,QAAQ,EAAE,eAAe,IAAI,UAAU,EAAE,cAAc,IAAI,SAAS,EAAE,aAAa,IAAI,QAAQ,EAAE,YAAY,IAAI,OAAO,EAAE,eAAe,IAAI,UAAU,EAAE,YAAY,IAAI,OAAO;AAAA,QAC7P;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,MACT;AAAA,MACA,SAAS;AAAA,QACP,iBAAiB;AAAA,MACnB;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,iBAAiB,CAAC;AAAA,MAChB,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,cAAc,CAAC;AAAA,MACb,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,6BAA6B;AAAA,IACtC,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,qBAAN,MAAM,oBAAmB;AAAA,EACvB,YAAY,oBAAoB,cAAc,gBAAgB;AAC5D,SAAK,qBAAqB;AAC1B,SAAK,eAAe;AACpB,SAAK,iBAAiB;AAAA,EACxB;AAAA,EACA,WAAW;AACT,SAAK,mBAAmB,QAAQ,CAAC,GAAG,KAAK,SAAS,CAAC,EAAE,UAAU,WAAS;AACtE,WAAK,eAAe,MAAM;AAC1B,UAAI,MAAM,SAAS;AACjB,aAAK,eAAe,mBAAmB,KAAK,YAAY;AAAA,MAC1D;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,2BAA2B,mBAAmB;AACjE,aAAO,KAAK,qBAAqB,qBAAuB,kBAAuB,kBAAkB,GAAM,kBAAqB,WAAW,GAAM,kBAAqB,gBAAgB,CAAC;AAAA,IACrL;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,aAAa,EAAE,CAAC;AAAA,MACjC,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,oBAAoB,CAAC;AAAA,IAC3F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,YAAY;AACd,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,WAAW;AACb,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,aAAa;AACf,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA;AAAA,EAEA,IAAI,UAAU;AACZ,WAAO,KAAK,UAAU,gBAAgB;AAAA,EACxC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAAuB;AAAA,IAC1D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,YAAY,SAAS,EAAE,GAAG,CAAC,YAAY,SAAS,EAAE,CAAC;AAAA,MAChE,UAAU;AAAA,MACV,cAAc,SAAS,mCAAmC,IAAI,KAAK;AACjE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,eAAe,IAAI,UAAU,EAAE,cAAc,IAAI,SAAS,EAAE,aAAa,IAAI,QAAQ,EAAE,YAAY,IAAI,OAAO,EAAE,eAAe,IAAI,UAAU,EAAE,YAAY,IAAI,OAAO;AAAA,QACvL;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,kBAAkB;AAAA,IAC3B,CAAC;AAAA,IACD,UAAU,CAAC;AAAA,MACT,MAAM;AAAA,MACN,MAAM,CAAC,iBAAiB;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,IACD,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,mBAAmB;AAAA,IAC5B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,gBAAgB;AAAA,IACzB,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,wBAAwB;AAQ9B,IAAM,uBAAN,MAAM,sBAAqB;AAAA,EACzB,UAAU,OAAO;AAEf,UAAM,IAAI;AACV,QAAI,cAAc,CAAC,OAAO,SAAS,aAAa,cAAc,WAAW,aAAa,QAAQ,KAAK;AACnG,QAAI,YAAY,QAAQ,EAAE,GAAG,KAAK,EAAG;AACrC,kBAAc,CAAC,aAAa,QAAQ;AACpC,QAAI,YAAY,QAAQ,EAAE,GAAG,KAAK,GAAG;AACnC,WAAK,gBAAgB;AACrB;AAAA,IACF;AAEA,QAAI,EAAE,WAAW,QAAQ,EAAE,OAAO,KAAK,WAAW,GAAG;AACnD,WAAK;AACL,WAAK,GAAG,cAAc,QAAQ,KAAK,WAAW,KAAK,QAAQ;AAC3D,YAAM,eAAe;AAAA,IACvB;AAEA,QAAI,EAAE,WAAW,QAAQ,EAAE,OAAO,KAAK,WAAW,KAAK,WAAW,SAAS,GAAG;AAC5E,WAAK;AACL,WAAK,GAAG,cAAc,QAAQ,KAAK,WAAW,KAAK,QAAQ;AAC3D,YAAM,eAAe;AAAA,IACvB;AACA,UAAM,uBAAuB,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD,QAAI,EAAE,WAAW,qBAAqB,QAAQ,EAAE,GAAG,KAAK,EAAG;AAAA,EAC7D;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,iBAAiB,MAAM,QAAQ;AAWrC,QAAI,KAAK,eAAe;AACtB,YAAM,2BAA2B,CAAC,GAAG,KAAK,GAAG,cAAc,KAAK,EAAE,UAAU,CAAC,MAAMC,SAAQ;AACzF,cAAM,OAAO,KAAK,YAAYA,IAAG;AACjC,YAAI,SAAS,QAAQ,SAAS,OAAW,QAAO;AAChD,YAAI,MAAM,IAAI,KAAK,KAAK,YAAY,MAAM,KAAK,YAAY,EAAG,QAAO,SAAS;AAC9E,eAAO,CAAC,UAAU,MAAM,IAAI;AAAA,MAC9B,CAAC,IAAI;AACL,UAAI,0BAA0B;AAC5B,aAAK,gBAAgB;AACrB;AAAA,MACF;AAAA,IACF;AAEA,UAAM,iBAAiB,CAAC,GAAG,KAAK,GAAG,cAAc,KAAK,EAAE,OAAO,UAAQ,UAAU,KAAK,IAAI,CAAC,EAC1F,OAAO,CAAC,UAAU,SAAS;AAC1B,UAAIA,OAAM,SAAS;AACnB,YAAM,OAAO,KAAK,YAAYA,IAAG;AACjC,UAAI,SAAS,QAAQ,SAAS,OAAW,QAAO;AAChD,iBAAW,CAAC,UAAU,MAAM,IAAI,IAAI,WAAW,WAAW;AAE1D,UAAIC,YAAW,KAAK,YAAY,EAAED,IAAG;AACrC,UAAI,IAAI;AACR,aAAOC,cAAa,QAAQA,cAAa,UAAa,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQA,SAAQ,IAAI,GAAG;AACrG;AACA,oBAAYA;AACZ,QAAAA,YAAW,KAAK,YAAY,EAAED,IAAG;AACjC,YAAI,IAAI,GAAI;AAAA,MACd;AACA,aAAO;AAAA,IACT,GAAG,EAAE;AACL,SAAK,GAAG,cAAc,QAAQ;AAE9B,QAAI,MAAM;AACV,QAAI,WAAW,KAAK,YAAY,GAAG;AACnC,WAAO,aAAa,QAAQ,aAAa,UAAa,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,QAAQ,IAAI,GAAG;AACrG;AACA,iBAAW,KAAK,YAAY,GAAG;AAC/B,UAAI,aAAa,QAAQ,YAAY,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,QAAQ,IAAI,GAAG;AAAA,MAEtF,MAAO;AAAA,IACT;AAEA,QAAI,mBAAmB,KAAK,WAAW,KAAK,QAAQ,GAAG;AACrD,WAAK;AACL,YAAM,QAAQ,KAAK,WAAW,wBAAwB,IAAI,KAAK,WAAW,MAAM,CAAC,IAAI,KAAK,WAAW,OAAO,GAAG,KAAK,QAAQ;AAC5H,WAAK,WAAW,KAAK,WAAW,wBAAwB,IAAI,wBAAwB,IAAI,KAAK;AAC7F,WAAK,aAAa,CAAC,GAAG,OAAO,cAAc;AAC3C,WAAK,GAAG,cAAc,kBAAkB,KAAK,GAAG;AAAA,IAClD,OAAO;AACL,UAAI,KAAK,eAAe;AACtB,YAAI,IAAI,MAAM;AAKd,eAAO,IAAI,GAAG;AACZ,gBAAM,eAAe,KAAK,YAAY,IAAI,CAAC;AAC3C,cAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,EAAE,QAAQ,YAAY,IAAI,GAAG;AACvD;AAAA,UACF,MAAO;AAAA,QACT;AACA,aAAK,GAAG,cAAc,kBAAkB,GAAG,CAAC;AAAA,MAC9C;AAAA,IACF;AACA,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,aAAa,CAAC,EAAE;AACrB,SAAK,WAAW;AAChB,SAAK,gBAAgB;AAAA,EACvB;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,6BAA6B,mBAAmB;AACnE,aAAO,KAAK,qBAAqB,uBAAyB,kBAAqB,UAAU,CAAC;AAAA,IAC5F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,eAAe,EAAE,CAAC;AAAA,MACnC,cAAc,SAAS,kCAAkC,IAAI,KAAK;AAChE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,WAAW,SAAS,gDAAgD,QAAQ;AACxF,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,CAAC,EAAE,SAAS,SAAS,8CAA8C,QAAQ;AACzE,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,aAAa;AAAA,MACf;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,sBAAsB,CAAC;AAAA,IAC7F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,aAAa,CAAC;AAAA,MACZ,MAAM;AAAA,IACR,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,SAAS,UAAU,MAAM,WAAW;AAClC,UAAQ,MAAM;AAAA;AAAA,IAEZ,KAAK;AACH,UAAI,cAAc,IAAK,QAAO;AAC9B,aAAO,CAAC,MAAM,CAAC,SAAS;AAAA;AAAA,IAE1B,KAAK;AACH,UAAI,cAAc,IAAK,QAAO;AAC9B,aAAO,CAAC,MAAM,CAAC,SAAS,KAAK,UAAU,YAAY,KAAK,UAAU,YAAY;AAAA;AAAA,IAEhF,KAAK;AACH,aAAO,UAAU,YAAY,KAAK,UAAU,YAAY;AAAA;AAAA,IAE1D,KAAK;AACH,aAAO,UAAU,YAAY,KAAK,UAAU,YAAY,KAAK,cAAc,UAAU,YAAY;AAAA;AAAA,IAEnG,KAAK;AACH,aAAO,UAAU,YAAY,KAAK,UAAU,YAAY,KAAK,cAAc,UAAU,YAAY;AAAA,EACrG;AACA,SAAO;AACT;AAKA,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,YAAY,YAAY,UAAU,SAAS,OAAO,kBAAkB;AAClE,SAAK,aAAa;AAClB,SAAK,WAAW;AAChB,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,mBAAmB;AACxB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB,UAAM,YAAY,KAAK,QAAQ,wBAAwB,sBAAsB;AAC7E,SAAK,OAAO,KAAK,MAAM,gBAAgB,SAAS;AAChD,SAAK,SAAS,KAAK,KAAK,SAAS,IAAI,sBAAsB,EAAE,YAAY;AAAA,EAC3E;AAAA,EACA,WAAW;AACT,SAAK,MAAM,MAAM;AACjB,SAAK,SAAS,YAAY,KAAK,WAAW,eAAe,KAAK,MAAM;AAAA,EACtE;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA8B,kBAAqB,UAAU,GAAM,kBAAqB,SAAS,GAAM,kBAAqB,0BAAwB,GAAM,kBAAqB,gBAAgB,GAAM,kBAAqB,SAAS,CAAC;AAAA,IACvQ;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,QAAQ,aAAa,EAAE,CAAC;AAAA,MACrC,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,IAAI,KAAK;AAAA,QACzB;AAAA,MACF;AAAA,MACA,QAAQ;AAAA,QACN,WAAW;AAAA,MACb;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,IACR,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AAKH,IAAM,4BAAN,MAAM,2BAA0B;AAAA,EAC9B,cAAc;AACZ,SAAK,QAAQ;AAAA,EACf;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,kCAAkC,mBAAmB;AACxE,aAAO,KAAK,qBAAqB,4BAA2B;AAAA,IAC9D;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,UAAU,aAAa,EAAE,CAAC;AAAA,MACvC,UAAU;AAAA,MACV,cAAc,SAAS,uCAAuC,IAAI,KAAK;AACrE,YAAI,KAAK,GAAG;AACV,UAAG,YAAY,sBAAsB,IAAI,KAAK;AAAA,QAChD;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,2BAA2B,CAAC;AAAA,IAClG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM;AAAA,IACR,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,IAAI;AACd,SAAK,KAAK;AACV,SAAK,WAAW;AAAA,EAClB;AAAA,EACA,QAAQ,OAAO;AACb,UAAM,QAAQ,KAAK,GAAG;AAEtB,UAAM,SAAS,MAAM,eAAe,QAAQ,YAAY,KAAK;AAC7D,UAAM,aAAa,OAAO,QAAQ,OAAO,EAAE;AAC3C,QAAI,CAAC,YAAY;AACf,YAAM,eAAe;AACrB;AAAA,IACF;AACA,UAAM,eAAe;AAErB,QAAI,MAAM,MAAM,kBAAkB;AAClC,UAAM,QAAQ,MAAM,MAAM,MAAM,EAAE;AAClC,QAAI,aAAa;AACjB,WAAO,MAAM,KAAK,SAAS,UAAU,aAAa,WAAW,QAAQ;AACnE,UAAI,KAAK,SAAS,GAAG,MAAM,KAAK;AAC9B;AACA;AAAA,MACF;AACA,YAAM,GAAG,IAAI,WAAW,YAAY;AACpC;AAAA,IACF;AACA,UAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,SAAK,eAAe,KAAK;AAEzB,SAAK,uBAAuB,GAAG;AAAA,EACjC;AAAA,EACA,MAAM,OAAO;AACX,UAAM,QAAQ,KAAK,GAAG;AACtB,UAAM,QAAQ,MAAM,kBAAkB;AACtC,UAAM,MAAM,MAAM,gBAAgB;AAElC,UAAM,UAAU,MAAM,MAAM,UAAU,OAAO,GAAG;AAEhD,UAAM,QAAQ,MAAM,MAAM,MAAM,EAAE;AAClC,aAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,UAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC5B,cAAM,CAAC,IAAI;AAAA,MACb;AAAA,IACF;AAEA,UAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,SAAK,eAAe,KAAK;AAEzB,SAAK,uBAAuB,KAAK;AAEjC,UAAM,eAAe;AAErB,QAAI,MAAM,eAAe;AACvB,YAAM,cAAc,QAAQ,cAAc,QAAQ,QAAQ,OAAO,EAAE,CAAC;AAAA,IACtE;AAAA,EACF;AAAA,EACA,UAAU;AACR,UAAM,QAAQ,KAAK,GAAG;AACtB,QAAI,CAAC,MAAM,SAAS,MAAM,UAAU,KAAK,UAAU;AACjD,YAAM,QAAQ,KAAK;AAAA,IACrB;AACA,SAAK,uBAAuB,CAAC;AAAA,EAC/B;AAAA,EACA,UAAU;AACR,UAAM,QAAQ,KAAK,GAAG;AAEtB,UAAM,iBAAiB,MAAM,kBAAkB;AAC/C,UAAM,eAAe,MAAM,gBAAgB;AAC3C,QAAI,mBAAmB,cAAc;AACnC,UAAI,MAAM,UAAU,KAAK,UAAU;AACjC,aAAK,uBAAuB,CAAC;AAAA,MAC/B,OAAO;AACL,aAAK,uBAAuB,cAAc;AAAA,MAC5C;AAAA,IACF;AAAA,EACF;AAAA,EACA,UAAU,OAAO;AACf,UAAM,QAAQ,KAAK,GAAG;AACtB,QAAI,MAAM,MAAM,kBAAkB;AAClC,UAAM,uBAAuB,CAAC,KAAK,KAAK,KAAK,GAAG;AAChD,QAAI,MAAM,WAAW,qBAAqB,QAAQ,MAAM,GAAG,KAAK,GAAG;AACjE,cAAQ,IAAI,MAAM,KAAK;AACvB,UAAI,MAAM,QAAQ,OAAO,MAAM,UAAU,IAAI;AAC3C,gBAAQ,IAAI,MAAM,GAAG;AACrB,cAAM,QAAQ,KAAK;AACnB,aAAK,eAAe,KAAK;AAAA,MAC3B;AACA;AAAA,IACF;AAEA,QAAI,CAAC,aAAa,cAAc,KAAK,EAAE,SAAS,MAAM,GAAG,EAAG;AAE5D,QAAI,MAAM,QAAQ,eAAe,MAAM,QAAQ,UAAU;AACvD,YAAM,eAAe;AACrB,YAAM,QAAQ,MAAM,kBAAkB;AACtC,YAAM,MAAM,MAAM,gBAAgB;AAElC,UAAI,MAAM,OAAO;AACf,cAAM,QAAQ,MAAM,MAAM,MAAM,EAAE;AAClC,iBAAS,IAAI,OAAO,IAAI,KAAK,KAAK;AAChC,cAAI,KAAK,SAAS,CAAC,MAAM,KAAK;AAC5B,kBAAM,CAAC,IAAI;AAAA,UACb;AAAA,QACF;AACA,cAAM,QAAQ,MAAM,KAAK,EAAE;AAC3B,aAAK,eAAe,KAAK;AACzB,aAAK,uBAAuB,KAAK;AACjC;AAAA,MACF;AAEA,UAAIE,OAAM;AACV,UAAI,MAAM,QAAQ,eAAeA,OAAM,GAAG;AACxC,WAAG;AACD,UAAAA;AAAA,QACF,SAAS,KAAK,SAASA,IAAG,MAAM,OAAOA,OAAM;AAC7C,aAAK,cAAc,OAAOA,MAAK,GAAG;AAClC,aAAK,eAAe,KAAK;AACzB,aAAK,uBAAuBA,IAAG;AAC/B;AAAA,MACF;AACA,UAAI,MAAM,QAAQ,YAAYA,OAAM,KAAK,SAAS,QAAQ;AACxD,eAAO,KAAK,SAASA,IAAG,MAAM,OAAOA,OAAM,KAAK,SAAS,QAAQ;AAC/D,UAAAA;AAAA,QACF;AACA,aAAK,cAAc,OAAOA,MAAK,GAAG;AAClC,aAAK,eAAe,KAAK;AACzB,aAAK,uBAAuBA,IAAG;AAC/B;AAAA,MACF;AAAA,IACF;AAEA,QAAI,MAAM,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,QAAQ,OAAO,MAAM,QAAQ,KAAK;AACpF,YAAM,eAAe;AACrB,UAAI,QAAQ,KAAK,QAAQ,GAAG;AAE1B,cAAM,aAAa,MAAM,MAAM,UAAU,MAAM,GAAG,GAAG;AACrD,YAAI,CAAC,WAAW,SAAS,GAAG,GAAG;AAC7B,eAAK,cAAc,OAAO,MAAM,GAAG,GAAG;AACtC,eAAK,cAAc,OAAO,KAAK,UAAU;AACzC,eAAK,uBAAuB,MAAM,CAAC;AACnC;AAAA,QACF;AAAA,MACF;AAEA,WAAK,uBAAuB,MAAM,CAAC;AACnC;AAAA,IACF;AAEA,QAAI,MAAM,QAAQ,aAAa;AAC7B,YAAM,eAAe;AACrB,UAAI,QAAQ,EAAG;AACf,SAAG;AACD;AAAA,MACF,SAAS,KAAK,SAAS,GAAG,MAAM,OAAO,MAAM;AAC7C,WAAK,cAAc,OAAO,KAAK,GAAG;AAClC,WAAK,uBAAuB,GAAG;AAC/B,WAAK,eAAe,KAAK;AACzB;AAAA,IACF;AAEA,QAAI,CAAC,OAAO,KAAK,MAAM,GAAG,GAAG;AAC3B,YAAM,eAAe;AACrB;AAAA,IACF;AAEA,QAAI,OAAO,KAAK,SAAS,QAAQ;AAC/B,YAAM,eAAe;AACrB;AAAA,IACF;AAEA,WAAO,KAAK,SAAS,GAAG,MAAM,OAAO,MAAM,KAAK,SAAS,QAAQ;AAC/D;AAAA,IACF;AACA,SAAK,cAAc,OAAO,KAAK,MAAM,GAAG;AACxC,UAAM,eAAe;AACrB,SAAK,uBAAuB,MAAM,CAAC;AACnC,SAAK,eAAe,KAAK;AAAA,EAC3B;AAAA,EACA,eAAe,OAAO;AACpB,UAAM,cAAc,IAAI,MAAM,SAAS;AAAA,MACrC,SAAS;AAAA,IACX,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,EAET;AAAA,EACA,cAAc,OAAO,OAAO,MAAM;AAChC,UAAM,QAAQ,MAAM,MAAM,MAAM,EAAE;AAClC,UAAM,KAAK,IAAI;AACf,UAAM,QAAQ,MAAM,KAAK,EAAE;AAAA,EAC7B;AAAA,EACA,uBAAuB,KAAK;AAC1B,UAAM,QAAQ,KAAK,GAAG;AACtB,WAAO,KAAK,SAAS,GAAG,MAAM,OAAO,MAAM,KAAK,SAAS,QAAQ;AAC/D;AAAA,IACF;AACA,UAAM,kBAAkB,KAAK,GAAG;AAAA,EAClC;AAAA,EACA,kBAAkB,OAAO;AACvB,QAAI,OAAO,UAAU,SAAU,QAAO;AACtC,WAAO,MAAM,QAAQ,WAAW,GAAG,EAClC,QAAQ,YAAY,EAAE,EAAE,QAAQ,MAAM,EAAE;AAAA,EAC3C;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,kBAAqB,UAAU,CAAC;AAAA,IAClG;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,kBAAkB;AAAA,MAC9C,MAAM;AAAA,MACN,WAAW,CAAC,CAAC,IAAI,qBAAqB,EAAE,CAAC;AAAA,MACzC,cAAc,SAAS,wCAAwC,IAAI,KAAK;AACtE,YAAI,KAAK,GAAG;AACV,UAAG,WAAW,SAAS,SAAS,oDAAoD,QAAQ;AAC1F,mBAAO,IAAI,QAAQ,MAAM;AAAA,UAC3B,CAAC,EAAE,OAAO,SAAS,kDAAkD,QAAQ;AAC3E,mBAAO,IAAI,MAAM,MAAM;AAAA,UACzB,CAAC,EAAE,SAAS,SAAS,sDAAsD;AACzE,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,SAAS,SAAS,sDAAsD;AACzE,mBAAO,IAAI,QAAQ;AAAA,UACrB,CAAC,EAAE,WAAW,SAAS,sDAAsD,QAAQ;AACnF,mBAAO,IAAI,UAAU,MAAM;AAAA,UAC7B,CAAC,EAAE,QAAQ,SAAS,qDAAqD;AACvE,mBAAO,IAAI,OAAO;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,UAAU;AAAA,MACV,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,CAAC,GAAG;AAAA,IACF,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAAA,IAC5B,CAAC;AAAA,IACD,OAAO,CAAC;AAAA,MACN,MAAM;AAAA,MACN,MAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;AAAA,IAC1B,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,SAAS,CAAC;AAAA,MACR,MAAM;AAAA,MACN,MAAM,CAAC,OAAO;AAAA,IAChB,CAAC;AAAA,IACD,WAAW,CAAC;AAAA,MACV,MAAM;AAAA,MACN,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;AAAA,IAC9B,CAAC;AAAA,IACD,QAAQ,CAAC;AAAA,MACP,MAAM;AAAA,MACN,MAAM,CAAC,MAAM;AAAA,IACf,CAAC;AAAA,EACH,CAAC;AACH,GAAG;AACH,IAAM,aAAa,CAAC,oBAAoB,0BAA0B,2BAA2B,2BAA2B,oBAAoB,uBAAuB,sBAAsB,0BAA0B;AASnN,SAAS,uBAAuB,kBAAkB,YAAY,CAAC,MAAM,MAAM,MAAM,IAAI,GAAG,mBAAmB,MAAM;AAC/G,SAAO,MAAY;AACjB,qBAAiB,aAAa,UAAU,WAAS;AAC/C,mBAAa,QAAQ,YAAY,MAAM,IAAI;AAAA,IAC7C,CAAC;AACD,qBAAiB,SAAS,SAAS;AACnC,QAAI,WAAW,aAAa,QAAQ,UAAU;AAC9C,QAAI,aAAa,QAAQ,UAAU,QAAQ,QAAQ,IAAI,GAAG;AAExD,UAAI,UAAU,QAAQ,UAAU,QAAQ,KAAK,GAAG;AAC9C,mBAAW,UAAU;AAAA,MACvB,OAAO;AAEL,mBAAW,OAAO,UAAU,WAAW;AACrC,cAAI,UAAU,QAAQ,GAAG,KAAK,GAAG;AAC/B,uBAAW;AACX;AAAA,UACF;AAAA,QACF;AAEA,YAAI,aAAa,QAAQ,UAAU,QAAQ,QAAQ,IAAI,GAAG;AACxD,qBAAW;AAAA,QACb;AAAA,MACF;AAAA,IACF;AACA,UAAM,iBAAiB,IAAI,QAAQ,EAAE,UAAU;AAAA,EACjD;AACF;AAQA,SAAS,6BAA6B,kBAAkB,YAAY,CAAC,MAAM,IAAI,GAAG,mBAAmB,MAAM;AACzG,SAAO,MAAY;AACjB,qBAAiB,SAAS,SAAS;AACnC,QAAI,WAAW,aAAa,QAAQ,qCAAqC;AACzE,QAAI,aAAa,MAAM;AACrB,iBAAW,SAAS,YAAY;AAChC,UAAI,UAAU,QAAQ,QAAQ,KAAK,GAAG;AACpC,cAAM,iBAAiB,IAAI,QAAQ,EAAE,UAAU;AAC/C;AAAA,MACF;AAAA,IACF;AACA,UAAM,iBAAiB,IAAI,gBAAgB,EAAE,UAAU;AAAA,EACzD;AACF;AACA,IAAM,4BAAN,MAAgC;AAAA,EAC9B,YAAY,KAAK,WAAW,CAAC,GAAG,SAAS,UAAU,SAAS,SAAS;AACnE,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,SAAS;AACd,SAAK,SAAS;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,MAAM;AACnB,WAAO,KAAK,IAAI,gBAAgB,KAAK,UAAU,MAAM,KAAK,QAAQ,KAAK,MAAM;AAAA,EAC/E;AACF;AACA,IAAM,6BAAN,MAAiC;AAAA,EAC/B,YAAY,MAAM,SAAS,iBAAiB,SAAS,SAAS,UAAU,CAAC,GAAG;AAC1E,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,UAAU;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,MAAM;AACnB,UAAM,cAAc,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,IAAI,GAAG,KAAK,MAAM,EAAE,EAAE,KAAK,WAAa,MAAM,GAAG,IAAI,CAAC,CAAC,GAAG,GAAG,KAAK,QAAQ,IAAI,OAAK;AACrI,WAAK,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,KAAK,MAAM,EAAE,EAAE,KAAK,WAAa,MAAM,GAAG,IAAI,CAAC,CAAC;AAAA,IAE7F,CAAC,CAAC;AACF,WAAO,SAAS,WAAW,EAAE,KAAK,IAAI,SAAO;AAC3C,aAAO,IAAI,OAAO,OAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAO,kCACzC,IACA,IACD,CAAC,CAAC;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AACF;AACA,IAAM,iBAAN,MAAM,gBAAe;AAAA,EACnB,UAAU,OAAO;AACf,UAAM,SAAS,MAAM,KAAK,EAAE,QAAQ,YAAY,EAAE;AAClD,QAAI,OAAO,WAAW,GAAI,QAAO;AACjC,WAAO,GAAG,OAAO,UAAU,GAAG,CAAC,CAAC,IAAI,OAAO,UAAU,GAAG,CAAC,CAAC,IAAI,OAAO,UAAU,GAAG,EAAE,CAAC;AAAA,EACvF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,uBAAuB,mBAAmB;AAC7D,aAAO,KAAK,qBAAqB,iBAAgB;AAAA,IACnD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,gBAAgB,CAAC;AAAA,IACvF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,kBAAN,MAAM,iBAAgB;AAAA,EACpB,UAAU,OAAO;AACf,UAAM,SAAS,MAAM,KAAK,EAAE,QAAQ,YAAY,EAAE;AAClD,QAAI,OAAO,WAAW,GAAI,QAAO;AACjC,WAAO,GAAG,OAAO,UAAU,GAAG,CAAC,CAAC,IAAI,OAAO,UAAU,GAAG,CAAC,CAAC,IAAI,OAAO,UAAU,GAAG,EAAE,CAAC;AAAA,EACvF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,wBAAwB,mBAAmB;AAC9D,aAAO,KAAK,qBAAqB,kBAAiB;AAAA,IACpD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,aAAa;AAAA,MAC1C,MAAM;AAAA,MACN,MAAM;AAAA,MACN,MAAM;AAAA,IACR,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,iBAAiB,CAAC;AAAA,IACxF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,MAAM;AAAA,MACN,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,6BAA6B,IAAI,eAAe,8BAA8B;AAAA,EAClF,YAAY;AAAA,EACZ,SAAS,MAAM;AACjB,CAAC;AACD,IAAM,6BAAN,MAAM,4BAA2B;AAAA,EAC/B,YAAY,MAAM,yBAAyB;AACzC,SAAK,OAAO;AACZ,SAAK,0BAA0B;AAC/B,SAAK,cAAc,IAAI,cAAc,CAAC;AACtC,SAAK,wBAAwB;AAAA,EAE/B;AAAA,EACA,aAAa,UAAU,OAAO;AAC5B,QAAI,WAAW,CAAC,KAAK,uBAAuB;AAC1C,WAAK,wBAAwB;AAC7B,WAAK,KAAK,IAAI,KAAK,uBAAuB,EAAE,UAAU,eAAa;AACjE,aAAK,YAAY,KAAK,SAAS;AAAA,MACjC,CAAC;AAAA,IACH;AACA,WAAO,KAAK,YAAY,KAAK,KAAK,CAAC,OAAO,GAAG,qBAAqB,CAAC;AAAA,EACrE;AAAA,EACA,iBAAiB,MAAM;AACrB,WAAO,KAAK,YAAY,KAAK,IAAM,eAAa;AAC9C,YAAM,UAAU,UAAU,KAAK,CAAAC,aAAWA,SAAQ,SAAS,IAAI;AAC/D,aAAO,WAAW;AAAA,IACpB,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,mCAAmC,mBAAmB;AACzE,aAAO,KAAK,qBAAqB,6BAA+B,SAAc,UAAU,GAAM,SAAS,0BAA0B,CAAC;AAAA,IACpI;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,4BAA2B;AAAA,MACpC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,4BAA4B,CAAC;AAAA,IACnG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,MACN,MAAM,CAAC,0BAA0B;AAAA,IACnC,CAAC;AAAA,EACH,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,eAAN,MAAM,cAAa;AAAA,EACjB,QAAQ,KAAK,MAAM;AAEjB,SAAK,UAAU,WAAS;AACtB,mBAAa,QAAQ,KAAK,KAAK,UAAU,KAAK,CAAC;AAAA,IACjD,CAAC;AAAA,EACH;AAAA,EACA,QAAQ,KAAK;AACX,UAAM,IAAI,aAAa,QAAQ,GAAG;AAClC,QAAI,MAAM,MAAM;AACd,aAAO,GAAG,IAAI;AAAA,IAChB;AACA,WAAO,GAAG,KAAK,MAAM,CAAC,CAAC;AAAA,EACzB;AAAA,EACA,WAAW,KAAK;AAEd,iBAAa,WAAW,GAAG;AAAA,EAC7B;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,qBAAqB,mBAAmB;AAC3D,aAAO,KAAK,qBAAqB,eAAc;AAAA,IACjD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,cAAa;AAAA,MACtB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,cAAc,CAAC;AAAA,IACrF,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,iBAAiB,cAAc,MAAM,YAAY;AAC3D,SAAK,kBAAkB;AACvB,SAAK,eAAe;AACpB,SAAK,OAAO;AACZ,SAAK,WAAW,IAAI,QAAQ;AAC5B,SAAK,WAAW,KAAK,gBAAgB,eAAe,MAAM,IAAI;AAC9D,YAAQ,YAAY;AAAA,MAClB,KAAK,kBAAkB;AACrB,aAAK,SAAS,SAAS;AACvB;AAAA,MACF,KAAK,kBAAkB;AACrB,aAAK,SAAS,SAAS;AACvB;AAAA,MACF;AACE,aAAK,SAAS,SAAS;AAAA,IAC3B;AACA,UAAM,cAAc,KAAK,SAAS,cAAc,MAAM;AAEtD,SAAK,SAAS,aAAa,aAAa,OAAO,YAAY;AAC3D,SAAK,SAAS,aAAa,aAAa,QAAQ,KAAK,MAAM;AAE3D,SAAK,SAAS,YAAY,SAAS,MAAM,WAAW;AAAA,EACtD;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,WAAO,KAAK,OAAO,UAAU,IAAI;AAAA,EACnC;AAAA,EACA,OAAO,OAAO,IAAI;AAChB,WAAO,KAAK,OAAO,SAAS,IAAI;AAAA,EAClC;AAAA,EACA,MAAM,UAAU;AACd,WAAO,KAAK,KAAK,IAAI,KAAK,SAAS,QAAQ;AAAA,EAC7C;AAAA,EACA,aAAa,UAAU;AACrB,WAAO,KAAK,KAAK,IAAI,KAAK,SAAS,QAAQ;AAAA,EAC7C;AAAA,EACA,gBAAgB,UAAU,MAAM,SAAS,IAAI,SAAS,IAAI;AACxD,UAAM,cAAc,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,MAAM,+BAA+B,IAAI,GAAG,MAAM,EAAE,EAAE,KAAK,WAAa,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,SAAS,IAAI,OAAK;AAC/J,aAAO,KAAK,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,MAAM,GAAG,CAAC,IAAI,IAAI,GAAG,MAAM,EAAE,EAAE,KAAK,WAAa,MAAM;AAC3F,eAAO,GAAG,CAAC,CAAC;AAAA,MACd,CAAC,CAAC;AAAA,IACJ,CAAC,CAAC;AACF,WAAO,SAAS,WAAW,EAAE,KAAK,IAAI,SAAO;AAC3C,aAAO,IAAI,OAAO,OAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,MAAO,kCACzC,IACA,IACD,CAAC,CAAC;AAAA,IACR,CAAC,CAAC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ,MAAM;AACZ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACtC,YAAM,eAAe,SAAS,cAAc,cAAc,IAAI,IAAI;AAClE,UAAI,cAAc;AAChB,gBAAQ;AACR;AAAA,MACF;AACA,YAAM,OAAO,KAAK,SAAS,cAAc,MAAM;AAC/C,WAAK,SAAS,aAAa,MAAM,OAAO,YAAY;AACpD,WAAK,SAAS,aAAa,MAAM,QAAQ,UAAU;AACnD,WAAK,SAAS,aAAa,MAAM,QAAQ,IAAI;AAC7C,WAAK,SAAS,MAAM,QAAQ;AAC5B,WAAK,UAAU,MAAM,OAAO,IAAI,MAAM,uBAAuB,IAAI,EAAE,CAAC;AACpE,WAAK,SAAS,YAAY,SAAS,MAAM,IAAI;AAAA,IAC/C,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAY,gBAAgB,GAAM,SAAS,YAAY,GAAM,SAAc,UAAU,GAAM,SAAS,cAAc,CAAC,CAAC;AAAA,IAC7K;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AACA,WAAW,CAAC,iBAAiB,iBAAe;AAAA,EAC1C,WAAW,WAAW;AAAA,EACtB,SAAS,WAAW;AAAA,EACpB,KAAK;AACP,EAAE,CAAC,GAAG,kBAAkB,WAAW,SAAS,IAAI;AAAA,CAC/C,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAS;AAAA,EACX,GAAG;AAAA,IACD,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,GAAG;AAAA,IACD,MAAM;AAAA,IACN,YAAY,CAAC;AAAA,MACX,MAAM;AAAA,IACR,GAAG;AAAA,MACD,MAAM;AAAA,MACN,MAAM,CAAC,YAAY;AAAA,IACrB,CAAC;AAAA,EACH,CAAC,GAAG;AAAA,IACF,OAAO,CAAC;AAAA,EACV,CAAC;AACH,GAAG;AACH,IAAM,2BAAN,MAAM,0BAAyB;AAAA,EAC7B,YAAY,YAAY,kBAAkB;AACxC,SAAK,aAAa;AAClB,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,eAAe;AACb,WAAO,KAAK,WAAW,MAAM,sBAAsB,EAAE;AAAA,MAAK,IAAI,cAAY,QAAQ;AAAA;AAAA,MAElF,kBAAkB,KAAK,KAAK,iBAAiB,YAAY,EAAE,KAAK,UAAY,KAAK,iBAAiB,WAAW,GAAG,IAAI,WAAS,OAAO,UAAU,WAAW,QAAQ,MAAM,IAAI,CAAC,CAAC;AAAA,MAAG,IAAI,CAAC,CAAC,UAAU,WAAW,MAAM;AAE/M,eAAO,SAAS,IAAI,aAAY,iCAC3B,UAD2B;AAAA,UAE9B,MAAM,KAAK,yBAAyB,SAAS,WAAW;AAAA,QAC1D,EAAE,EAAE,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,YAAY,EAAE,cAAc,EAAE,KAAK,YAAY,CAAC,CAAC;AAAA,MAC7E,CAAC;AAAA,IAAC;AAAA,EACJ;AAAA,EACA,mBAAmB,MAAM;AACvB,WAAO,KAAK,aAAa,EAAE,KAAK,IAAI,eAAa;AAC/C,aAAO,UAAU,OAAO,aAAW,QAAQ,SAAS,IAAI;AAAA,IAC1D,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,yBAAyB,SAAS,eAAe,KAAK,iBAAiB,aAAa;AAClF,QAAI,iBAAiB,QAAQ,iBAAiB,QAAQ,iBAAiB,MAAM;AAC3E,UAAI,QAAQ,aAAa,YAAY,MAAM,OAAW,QAAO,QAAQ,aAAa,YAAY;AAAA,IAChG;AACA,WAAO,QAAQ;AAAA,EACjB;AAAA,EACA,oBAAoB,MAAM;AACxB,WAAO,KAAK,aAAa,EAAE,KAAK,IAAI,eAAa;AAC/C,aAAO,UAAU,KAAK,aAAW,QAAQ,KAAK,YAAY,MAAM,KAAK,YAAY,CAAC;AAAA,IACpF,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,wBAAwB,MAAM;AAC5B,WAAO,KAAK,mBAAmB,IAAI,EAAE,KAAK,IAAI,eAAa;AACzD,aAAO,UAAU,KAAK,aAAW,QAAQ,QAAQ,UAAU,GAAG,CAAC,MAAM,KAAK,UAAU,GAAG,CAAC,CAAC;AAAA,IAC3F,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,sBAAsB,QAAQ;AAC5B,WAAO,KAAK,aAAa,EAAE,KAAK,IAAI,eAAa;AAC/C,aAAO,UAAU,KAAK,aAAW;AAC/B,eAAO,QAAQ,cAAc,OAAO,UAAU,GAAG,QAAQ,UAAU,MAAM;AAAA,MAC3E,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,SAAS,iBAAiB,GAAM,SAAc,gBAAgB,CAAC;AAAA,IAC/H;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,MAClC,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,GAAG;AAAA,IACD,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,wBAAN,MAAM,uBAAsB;AAAA,EAC1B,OAAO;AACL,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,YAAY,YAAY;AACtB,SAAK,aAAa;AAAA,EACpB;AAAA,EACA,qBAAqB,YAAY;AAC/B,WAAO,KAAK,WAAW,MAAM,uBAAsB,YAAY,EAAE,KAAK,WAAW,MAAM;AACrF,aAAO,CAAC;AAAA,IACV,CAAC,GAAG,IAAM,cAAY;AACpB,aAAO,SAAS,OAAO,UAAQ;AAC7B,eAAO,WAAW,SAAS,KAAK,SAAS;AAAA,MAC3C,CAAC,EAAE,IAAI,UAAQ;AACb,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,qBAAqB,UAAU;AAC7B,WAAO,KAAK,WAAW,MAAM,uBAAsB,YAAY,EAAE,KAAK,WAAW,MAAM;AACrF,aAAO,CAAC;AAAA,IACV,CAAC,GAAG,IAAM,cAAY;AACpB,aAAO,SAAS,OAAO,UAAQ;AAC7B,eAAO,SAAS,SAAS,KAAK,QAAQ;AAAA,MACxC,CAAC,EAAE,IAAI,UAAQ;AACb,eAAO,KAAK;AAAA,MACd,CAAC;AAAA,IACH,CAAC,CAAC;AAAA,EACJ;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,8BAA8B,mBAAmB;AACpE,aAAO,KAAK,qBAAqB,wBAA0B,SAAS,iBAAiB,CAAC;AAAA,IACxF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,uBAAsB;AAAA,MAC/B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,uBAAuB,CAAC;AAAA,IAC9F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAM;AAAA,EACR,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,oBAAN,MAAM,mBAAkB;AAAA,EACtB,YAAY,MAAM;AAChB,SAAK,OAAO;AACZ,SAAK,eAAe;AAAA,EACtB;AAAA,EACA,cAAc,OAAO,SAAS,QAAQ,iBAAiB,GAAG,QAAQ,IAAI;AACpE,UAAM,SAAS,IAAI,WAAW,EAAE,IAAI,KAAK,KAAK,EAAE,IAAI,UAAU,MAAM,EAAE,IAAI,kBAAkB,cAAc,EAAE,IAAI,SAAS,KAAK;AAC9H,WAAO,KAAK,KAAK,IAAI,KAAK,cAAc;AAAA,MACtC;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,0BAA0B,mBAAmB;AAChE,aAAO,KAAK,qBAAqB,oBAAsB,SAAc,UAAU,CAAC;AAAA,IAClF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,mBAAkB;AAAA,MAC3B,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,mBAAmB,CAAC;AAAA,IAC1F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,YAAY;AAAA,IACd,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,YAAY,WAAW;AACrB,SAAK,YAAY;AACjB,SAAK,qBAAqB;AAC1B,SAAK,mBAAmB;AAAA,EAC1B;AAAA,EACA,YAAY,OAAO;AACjB,YAAQ,MAAM,KAAK;AAEnB,SAAK,UAAU,KAAK,UAAU,MAAM,OAAO,IAAI,IAAI;AAAA,MACjD,UAAU;AAAA,MACV,YAAY;AAAA,MACZ,oBAAoB,KAAK;AAAA,MACzB,kBAAkB,KAAK;AAAA,IACzB,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAwB,SAAc,WAAW,CAAC;AAAA,IACrF;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,qBAAoB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,IAAM,mCAAN,MAAuC;AAAA,EACrC,OAAO,QAAQ;AACb,WAAO,MAAM,OAAO,GAAG;AAAA,EACzB;AACF;AACA,IAAM,oBAAN,MAAwB;AAAA,EACtB,OAAO,aAAa,WAAW,OAAO,SAAS;AAC7C,QAAI,QAAQ,UAAU,QAAQ,QAAQ,MAAM,KAAK,MAAM,IAAI;AACzD,aAAO,CAAC,WAAW,OAAO;AAAA,QACxB,SAAS;AAAA,MACX;AAAA,IACF;AACA,UAAM,eAAe,QAAQ,MAAM,KAAK,EAAE,QAAQ,YAAY,EAAE;AAChE,QAAI,aAAa,WAAW,GAAI,QAAO;AAAA,MACrC,gBAAgB;AAAA,QACd,OAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,OAAO,MAAM,CAAC,YAAY,EAAG,QAAO;AAAA,MACtC,QAAQ;AAAA,QACN,OAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,IAAI,WAAW,OAAO;AAC3B,WAAO,aAAW;AAChB,aAAO,KAAK,aAAa,UAAU,OAAO;AAAA,IAC5C;AAAA,EACF;AAAA,EACA,OAAO,cAAc,WAAW,OAAO,SAAS;AAC9C,QAAI,QAAQ,UAAU,QAAQ,QAAQ,MAAM,KAAK,MAAM,IAAI;AACzD,aAAO,CAAC,WAAW,OAAO;AAAA,QACxB,UAAU;AAAA,MACZ;AAAA,IACF;AACA,UAAM,gBAAgB,QAAQ,MAAM,KAAK,EAAE,QAAQ,YAAY,EAAE;AACjE,QAAI,cAAc,WAAW,IAAI;AAC/B,aAAO;AAAA,QACL,iBAAiB;AAAA,UACf,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,MAAM,CAAC,aAAa,GAAG;AAChC,aAAO;AAAA,QACL,SAAS;AAAA,UACP,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAIA,UAAM,gBAAgB,cAAc,UAAU,CAAC;AAC/C,UAAM,qBAAqB,cAAc,UAAU,GAAG,CAAC;AACvD,UAAM,0BAA0B,KAAK,CAAC,qBAAqB,OAAO,CAAC;AACnE,UAAM,0BAA0B,KAAK,CAAC,IAAI,kBAAkB,KAAK,OAAO,CAAC;AACzE,QAAI,CAAC,2BAA2B,CAAC,yBAAyB;AACxD,aAAO;AAAA,QACL,qBAAqB;AAAA,UACnB,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAKA,UAAM,cAAc,cAAc,UAAU,GAAG,CAAC;AAChD,UAAM,QAAQ,SAAS,WAAW,IAAI;AACtC,UAAM,aAAa,SAAS,WAAW,IAAI,KAAK;AAChD,QAAI,aAAa,MAAM,QAAQ,IAAI;AACjC,aAAO;AAAA,QACL,kBAAkB;AAAA,UAChB,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,UAAM,MAAM,CAAC,cAAc,UAAU,GAAG,CAAC;AACzC,QAAI,MAAM,IAAI;AACZ,aAAO;AAAA,QACL,iBAAiB;AAAA,UACf,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,KAAK,WAAW,OAAO;AAC5B,WAAO,aAAW;AAChB,aAAO,KAAK,cAAc,UAAU,OAAO;AAAA,IAC7C;AAAA,EACF;AAAA,EACA,OAAO,UAAU,WAAW,OAAO;AACjC,WAAO,aAAW;AAChB,UAAI,QAAQ,UAAU,QAAQ,QAAQ,MAAM,KAAK,MAAM,GAAI,QAAO,CAAC,WAAW,OAAO;AAAA,QACnF,UAAU;AAAA,MACZ;AACA,YAAM,gBAAgB,QAAQ,MAAM,KAAK,EAAE,QAAQ,YAAY,EAAE;AACjE,UAAI,QAAQ,MAAM,WAAW,IAAI;AAC/B,eAAO,KAAK,cAAc,UAAU,OAAO;AAAA,MAC7C,WAAW,QAAQ,MAAM,WAAW,IAAI;AACtC,eAAO,KAAK,aAAa,UAAU,OAAO;AAAA,MAC5C;AACA,aAAO;AAAA,QACL,aAAa;AAAA,UACX,OAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACF;AACA,IAAM,2BAA2B,OAAO,QAAQ,YAAY,EAAE,OAAO,WAAS;AAC5E,SAAO,MAAM,CAAC,EAAE;AAClB,CAAC,EAAE,IAAI,WAAS;AACd,SAAO,MAAM,CAAC;AAChB,CAAC;AACD,IAAM,gCAAgC,OAAO,QAAQ,YAAY,EAAE,OAAO,WAAS;AACjF,SAAO,MAAM,CAAC,EAAE;AAClB,CAAC,EAAE,IAAI,WAAS;AACd,SAAO,MAAM,CAAC;AAChB,CAAC;AACD,IAAM,qBAAqB,CAAC,OAAO,OAAO;AACxC,SAAO,aAAa,qBAAqB,IAAI,KAAK,MAAS;AAC7D;AAgBA,IAAM,uBAAuB,MAAM;AACjC,SAAO,aAAW;AAChB,QAAI,kBAAkB,CAAC;AACvB,QAAI,OAAO,QAAQ,YAAY,MAAM,UAAU;AAC7C,YAAM,QAAQ;AACd,UAAI,MAAM,KAAK,QAAQ,YAAY,EAAE,UAAU,GAAG,CAAC,CAAC,GAAG;AACrD,cAAM;AAAA,UACJ,OAAO;AAAA,UACP;AAAA,QACF,IAAI,mBAAmB,QAAQ,KAAK;AACpC,0BAAkB;AAAA,MACpB;AAAA,IACF,OAAO;AACL,YAAM,mBAAmB,QAAQ,UAAU,OAAO,QAAQ,MAAM,aAAa,IAAI;AACjF,YAAM,aAAa,QAAQ,UAAU,OAAO,QAAQ,MAAM,MAAM,IAAI;AACpE,UAAI,qBAAqB,IAAI;AAC3B,cAAM;AAAA,UACJ,OAAO;AAAA,UACP;AAAA,QACF,IAAI,mBAAmB,GAAG,gBAAgB,GAAG,UAAU,EAAE;AACzD,0BAAkB;AAAA,MACpB;AAAA,IACF;AACA,QAAI,gBAAgB,WAAW,GAAG;AAChC,aAAO;AAAA,IACT;AACA,UAAM,mBAAmB,CAAC;AAC1B,oBAAgB,QAAQ,aAAW;AACjC,YAAM,MAAM,qBAAqB,OAAO;AACxC,uBAAiB,GAAG,IAAI;AAAA,QACtB,OAAO,QAAQ,YAAY;AAAA,MAC7B;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,IAAM,sBAAN,MAAM,qBAAoB;AAAA,EACxB,OAAO;AACL,SAAK,OAAO,SAAS,4BAA4B,mBAAmB;AAClE,aAAO,KAAK,qBAAqB,sBAAqB;AAAA,IACxD;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB;AAAA,MAC7C,MAAM;AAAA,MACN,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,2BAA2B,oBAAoB,uBAAuB,sBAAsB,0BAA0B;AAAA,MACzM,SAAS,CAAC,oBAAoB,0BAA0B,2BAA2B,2BAA2B,oBAAoB,uBAAuB,sBAAsB,0BAA0B;AAAA,IAC3M,CAAC;AAAA,EACH;AAAA,EACA,OAAO;AACL,SAAK,OAAyB,iBAAiB,CAAC,CAAC;AAAA,EACnD;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,qBAAqB,CAAC;AAAA,IAC5F,MAAM;AAAA,IACN,MAAM,CAAC;AAAA,MACL,SAAS,CAAC,GAAG,UAAU;AAAA,MACvB,SAAS,CAAC,GAAG,UAAU;AAAA,IACzB,CAAC;AAAA,EACH,CAAC,GAAG,MAAM,IAAI;AAChB,GAAG;AACH,IAAM,2BAAN,MAAM,kCAAiC,kBAAkB;AAAA,EACvD,YAAY,kBAAkB;AAC5B,UAAM;AACN,SAAK,mBAAmB;AACxB,SAAK,mBAAmB,IAAI,QAAQ;AACpC,SAAK,gBAAgB,IAAI,QAAQ;AACjC,SAAK,UAAU,KAAK,iBAAiB,WAAW;AAChD,SAAK,cAAc,KAAK,IAAI;AAC5B,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,iBAAiB,aAAa,KAAK,UAAU,KAAK,gBAAgB,CAAC,EAAE,UAAU,CAAC;AAAA,QACnF;AAAA,MACF,MAAM;AACJ,aAAK,UAAU,IAAI;AACnB,aAAK,cAAc,KAAK,IAAI;AAAA,MAC9B,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,OAAO,MAAM,SAAS;AACpB,UAAM,KAAK,SAAS,WAAW,IAAI;AACnC,WAAO,GAAG,SAAS,OAAO;AAAA,EAC5B;AAAA,EACA,MAAM,OAAO;AACX,UAAM,UAAU;AAChB,QAAI,SAAS,OAAO,UAAU,UAAU;AACtC,cAAQ,MAAM,QAAQ,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,UAAU,QAAQ,UAAU,IAAI;AAClC,aAAO;AAAA,IACT;AACA,UAAM,KAAK,SAAS,WAAW,OAAO,QAAQ,QAAQ,WAAW;AAAA,MAC/D,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,QAAI,GAAG,SAAS;AACd,aAAO,GAAG,SAAS;AAAA,IACrB;AACA,eAAW,UAAU,QAAQ,MAAM,WAAW;AAC5C,YAAM,SAAS,SAAS,WAAW,OAAO,QAAQ;AAAA,QAChD,QAAQ,KAAK;AAAA,MACf,CAAC;AACD,UAAI,OAAO,SAAS;AAClB,eAAO,OAAO,SAAS;AAAA,MACzB;AAAA,IACF;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,oBAAoB;AAClB,WAAO;AAAA,EACT;AAAA,EACA,cAAc;AACZ,SAAK,iBAAiB,KAAK;AAC3B,SAAK,iBAAiB,SAAS;AAAA,EACjC;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,iCAAiC,mBAAmB;AACvE,aAAO,KAAK,qBAAqB,2BAA6B,SAAc,gBAAgB,CAAC;AAAA,IAC/F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,0BAAyB;AAAA,IACpC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,0BAA0B,CAAC;AAAA,IACjG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AAGH,IAAM,iCAAiC,IAAI,eAAe,kCAAkC;AAAA,EAC1F,YAAY;AAAA,EACZ,SAAS;AACX,CAAC;AAED,SAAS,yCAAyC;AAChD,SAAO;AAAA,IACL,QAAQ;AAAA,IACR,gBAAgB;AAAA,IAChB,uBAAuB;AAAA,EACzB;AACF;AAEA,SAAS,WAAW,QAAQ,eAAe;AACzC,QAAM,cAAc,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC/B,gBAAY,CAAC,IAAI,cAAc,CAAC;AAAA,EAClC;AACA,SAAO;AACT;AAUA,IAAM,0BAAN,MAAM,iCAAgC,YAAY;AAAA,EAChD,YAAY,kBAAkB;AAC5B,UAAM;AACN,SAAK,mBAAmB;AACxB,UAAM,aAAa,OAAO,iBAAiB;AAAA,MACzC,UAAU;AAAA,IACZ,CAAC;AACD,UAAM,UAAU,OAAO,gCAAgC;AAAA,MACrD,UAAU;AAAA,IACZ,CAAC;AACD,SAAK,UAAU,CAAC,CAAC,SAAS;AAC1B,SAAK,kBAAkB,SAAS,kBAAkB;AAClD,SAAK,yBAAyB,SAAS,yBAAyB;AAChE,SAAK,UAAU,cAAc,SAAS,MAAM,EAAE,MAAM;AACpD,SAAK,UAAU,KAAK,iBAAiB,WAAW;AAChD,QAAI,KAAK,qBAAqB,QAAW;AACvC,WAAK,iBAAiB,aAAa,KAAK,mBAAmB,CAAC,EAAE,UAAU,CAAC;AAAA,QACvE;AAAA,MACF,MAAM;AACJ,aAAK,UAAU,IAAI;AAAA,MACrB,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,SAAS,MAAM;AAEb,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,aAAa,MAAM;AACjB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,cAAc,OAAO;AAEnB,WAAO,KAAK,OAAO,OAAO;AAAA,MACxB,QAAQ,KAAK;AAAA,MACb,gBAAgB,KAAK;AAAA,IACvB,CAAC;AAAA,EACH;AAAA,EACA,eAAe;AAGb,UAAM,MAAM,IAAI,KAAK,eAAe,KAAK,QAAQ;AAAA,MAC/C,KAAK;AAAA,MACL,UAAU;AAAA,IACZ,CAAC;AAED,WAAO,WAAW,IAAI,OAAK,IAAI,OAAO,SAAS,IAAI,MAAM,GAAG,IAAI,CAAC,EAAE,SAAS,CAAC,CAAC;AAAA,EAChF;AAAA,EACA,kBAAkB,OAAO;AAIvB,UAAM,OAAO,KAAK,SAAS,OAAO;AAAA,MAChC,QAAQ,KAAK;AAAA,IACf,CAAC;AACD,SAAK,QAAQ,KAAK,IAAI,CAAC;AACvB,WAAO;AAAA,EACT;AAAA,EACA,YAAY,MAAM;AAChB,WAAO,KAAK,SAAS,QAAQ,KAAK,YAAY,CAAC;AAAA,EACjD;AAAA,EACA,oBAAoB;AAClB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,kBAAkB,MAAM;AACtB,WAAO,KAAK;AAAA,EACd;AAAA,EACA,MAAM,MAAM;AACV,WAAO,SAAS,WAAW,KAAK,SAAS,GAAG,KAAK,YAAY,CAAC;AAAA,EAChE;AAAA,EACA,WAAW,MAAM,OAAO,MAAM;AAC5B,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,YAAM,MAAM,wBAAwB,KAAK,4CAA4C;AAAA,IACvF;AACA,QAAI,OAAO,GAAG;AACZ,YAAM,MAAM,iBAAiB,IAAI,mCAAmC;AAAA,IACtE;AAEA,UAAM,SAAS,KAAK,UAAU,SAAS,IAAI,MAAM,QAAQ,GAAG,MAAM,OAAO,IAAI,SAAS,MAAM,MAAM,QAAQ,GAAG,MAAM,OAAO;AAC1H,QAAI,CAAC,KAAK,QAAQ,MAAM,GAAG;AACzB,YAAM,MAAM,iBAAiB,IAAI,eAAe,OAAO,aAAa,IAAI;AAAA,IAC1E;AACA,WAAO;AAAA,EACT;AAAA,EACA,QAAQ;AACN,UAAM,UAAU,KAAK,YAAY;AACjC,WAAO,KAAK,UAAU,SAAS,IAAI,OAAO,IAAI,SAAS,MAAM,OAAO;AAAA,EACtE;AAAA,EACA,MAAM,OAAO,aAAa;AACxB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,OAAO,SAAS,YAAY,MAAM,SAAS,GAAG;AAQhD,YAAM,UAAU,MAAM,QAAQ,WAAW,IAAI,cAAc,CAAC,WAAW;AACvE,UAAI,CAAC,YAAY,QAAQ;AACvB,cAAM,MAAM,kCAAkC;AAAA,MAChD;AACA,iBAAW,UAAU,SAAS;AAC5B,cAAM,aAAa,SAAS,WAAW,OAAO,QAAQ,OAAO;AAC7D,YAAI,KAAK,QAAQ,UAAU,GAAG;AAC5B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO,KAAK,QAAQ;AAAA,IACtB,WAAW,OAAO,UAAU,UAAU;AACpC,aAAO,SAAS,WAAW,OAAO,OAAO;AAAA,IAC3C,WAAW,iBAAiB,MAAM;AAChC,aAAO,SAAS,WAAW,OAAO,OAAO;AAAA,IAC3C,WAAW,iBAAiB,UAAU;AACpC,aAAO,SAAS,WAAW,MAAM,SAAS,GAAG,OAAO;AAAA,IACtD;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,MAAM,eAAe;AAC1B,QAAI,CAAC,KAAK,QAAQ,IAAI,GAAG;AACvB,YAAM,MAAM,+CAA+C;AAAA,IAC7D;AACA,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,UAAU,KAAK,MAAM,EAAE,QAAQ,KAAK,EAAE,SAAS,aAAa;AAAA,IAC1E,OAAO;AACL,aAAO,KAAK,UAAU,KAAK,MAAM,EAAE,SAAS,aAAa;AAAA,IAC3D;AAAA,EACF;AAAA,EACA,iBAAiB,MAAM,OAAO;AAC5B,WAAO,KAAK,YAAY,KAAK,YAAY,CAAC,EAAE,KAAK;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,kBAAkB,MAAM,QAAQ;AAC9B,WAAO,KAAK,YAAY,KAAK,YAAY,CAAC,EAAE,KAAK;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,gBAAgB,MAAM,MAAM;AAC1B,WAAO,KAAK,YAAY,KAAK,YAAY,CAAC,EAAE,KAAK;AAAA,MAC/C;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EACA,UAAU,MAAM;AACd,WAAO,KAAK,MAAM;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACjB,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI;AACJ,QAAI,iBAAiB,MAAM;AACzB,aAAO,SAAS,WAAW,OAAO,OAAO;AAAA,IAC3C;AACA,QAAI,OAAO,UAAU,UAAU;AAC7B,UAAI,CAAC,OAAO;AACV,eAAO;AAAA,MACT;AACA,aAAO,SAAS,QAAQ,OAAO,OAAO;AAAA,IACxC;AACA,QAAI,QAAQ,KAAK,QAAQ,IAAI,GAAG;AAC9B,aAAO;AAAA,IACT;AACA,WAAO,MAAM,YAAY,KAAK;AAAA,EAChC;AAAA,EACA,eAAe,KAAK;AAClB,WAAO,eAAe;AAAA,EACxB;AAAA,EACA,QAAQ,MAAM;AACZ,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU;AACR,WAAO,SAAS,QAAQ,gCAAgC;AAAA,EAC1D;AAAA,EACA,QAAQ,QAAQ,OAAO,SAAS,SAAS;AAEvC,QAAI,QAAQ,KAAK,QAAQ,IAAI;AAC3B,YAAM,MAAM,kBAAkB,KAAK,0CAA0C;AAAA,IAC/E;AACA,QAAI,UAAU,KAAK,UAAU,IAAI;AAC/B,YAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,IACrF;AACA,QAAI,UAAU,KAAK,UAAU,IAAI;AAC/B,YAAM,MAAM,oBAAoB,OAAO,4CAA4C;AAAA,IACrF;AAEA,WAAO,KAAK,MAAM,MAAM,EAAE,IAAI;AAAA,MAC5B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,aAAa;AAAA,IACf,CAAC;AAAA,EACH;AAAA,EACA,SAAS,MAAM;AACb,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,WAAW,MAAM;AACf,WAAO,KAAK;AAAA,EACd;AAAA,EACA,UAAU,OAAO,aAAa;AAC5B,UAAM,SAAS,KAAK,MAAM,OAAO,WAAW;AAC5C,SAAK,CAAC,UAAU,CAAC,KAAK,QAAQ,MAAM,MAAM,OAAO,UAAU,UAAU;AAGnE,aAAO,KAAK,MAAM,MAAM,QAAQ,oBAAoB,EAAE,GAAG,WAAW,KAAK;AAAA,IAC3E;AACA,WAAO;AAAA,EACT;AAAA,EACA,WAAW,MAAM,QAAQ;AACvB,WAAO,KAAK,YAAY,KAAK,YAAY,CAAC,EAAE,KAAK;AAAA,MAC/C,SAAS;AAAA,IACX,CAAC;AAAA,EACH;AAAA;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,MAAM,KAAK,UAAU,QAAQ;AAAA,MAC7B,QAAQ,KAAK;AAAA,MACb,gBAAgB,KAAK;AAAA,IACvB;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,OAAO,SAAS,gCAAgC,mBAAmB;AACtE,aAAO,KAAK,qBAAqB,0BAA4B,SAAc,gBAAgB,CAAC;AAAA,IAC9F;AAAA,EACF;AAAA,EACA,OAAO;AACL,SAAK,QAA0B,mBAAmB;AAAA,MAChD,OAAO;AAAA,MACP,SAAS,yBAAwB;AAAA,IACnC,CAAC;AAAA,EACH;AACF;AAAA,CACC,MAAM;AACL,GAAC,OAAO,cAAc,eAAe,cAAiB,iBAAkB,yBAAyB,CAAC;AAAA,IAChG,MAAM;AAAA,EACR,CAAC,GAAG,MAAM,CAAC;AAAA,IACT,MAAW;AAAA,EACb,CAAC,GAAG,IAAI;AACV,GAAG;AACH,SAAS,qCAAqC;AAC5C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AACA,SAAS,4BAA4B;AACnC,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AACA,SAAS,2BAA2B;AAClC,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AACA,SAAS,oCAAoC;AAC3C,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;AACA,SAAS,uBAAuB;AAC9B,SAAO,CAAC;AAAA,IACN,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,MACR,YAAY;AAAA,MACZ,YAAY;AAAA,IACd;AAAA,EACF,GAAG;AAAA,IACD,SAAS;AAAA,IACT,UAAU;AAAA,EACZ,CAAC;AACH;", "names": ["ValidationErrorsIBAN", "ValidationErrorsBIC", "CDNUrlModeOptions", "document", "idx", "nextRule", "pos", "country"]}