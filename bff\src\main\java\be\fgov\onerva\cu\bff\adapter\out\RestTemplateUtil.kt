package be.fgov.onerva.cu.bff.adapter.out

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.stereotype.Component
import org.springframework.web.client.RestTemplate
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

@Component
class RestTemplateUtil(@Qualifier("userRestTemplate") private val restTemplate: RestTemplate) {

    fun <T, R> executeWithAuth(
        url: String,
        method: HttpMethod,
        authHeader: String?,
        responseType: Class<T>,
        requestBody: R? = null,
    ): T {
        val headers = HttpHeaders()
        if (authHeader != null) {
            headers.set("Authorization", authHeader)
        }
        val entity = if (requestBody != null) {
            HttpEntity(requestBody, headers)
        } else {
            HttpEntity<Void>(headers)
        }
        return restTemplate.exchange(url, method, entity, responseType).body
            ?: throw IllegalStateException("Response body is null")
    }

    fun <T> getForObject(url: String, responseType: Class<T>): T {
        return restTemplate.getForObject(url, responseType)
            ?: throw IllegalStateException("Response body is null")
    }

    // For PUT requests with a body
    fun <T> executeWithAuth(
        url: String,
        method: HttpMethod,
        authHeader: String?,
        requestBody: T,
    ) {
        val headers = HttpHeaders()
        if (authHeader != null) {
            headers.set("Authorization", authHeader)
        }
        val entity = HttpEntity(requestBody, headers)
        restTemplate.exchange(url, method, entity, Void::class.java)
    }

    fun captureAuthorizationHeader(): String? {
        val attributes = RequestContextHolder.getRequestAttributes() as? ServletRequestAttributes
        return attributes?.request?.getHeader("Authorization")
    }
}