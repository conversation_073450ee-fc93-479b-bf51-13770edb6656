package be.fgov.onerva.cu.common.aop

import java.util.UUID
import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.After
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.aspectj.lang.reflect.CodeSignature
import org.slf4j.MDC
import org.springframework.stereotype.Component

/**
 * Aspect to log the request ID for methods annotated with [LogWithRequestId].
 * The request ID is expected to be a [UUID] parameter named "requestId".
 * The aspect will set the request ID in the MDC context for logging purposes.
 * The aspect will clear the request ID from the MDC context after the method execution.
 * The aspect will match a parameter of the type [UUID] and name "requestId" in the method signature.
 */
@Aspect
@Component
class RequestLoggingAspect {
    /**
     * Before advice to set the request ID in the MDC context.
     *
     * @param joinPoint The join point for the method execution
     */
    @Before("@annotation(be.fgov.onerva.cu.common.aop.LogWithRequestId)") // Adjust this pointcut to target the relevant methods
    fun beforeMethod(joinPoint: JoinPoint) {
        val args: Array<Any> = joinPoint.args
        val codeSignature: CodeSignature = joinPoint.signature as CodeSignature
        val parameterNames = codeSignature.parameterNames
        val parameterTypes = codeSignature.parameterTypes

        if (parameterNames.size != parameterTypes.size) {
            return
        }

        for (i in parameterNames.indices) {
            if (REQUEST_ID_PARAM_NAME == parameterNames[i] && UUID::class.java == parameterTypes[i]) {
                val requestId = args[i] as UUID
                MDC.put(REQUEST_ID_PARAM_NAME, requestId.toString())
                break // Assume only one requestId per method
            }
        }
    }

    /**
     * After advice to clear the request ID from the MDC context.
     */
    @After("@annotation(be.fgov.onerva.cu.common.aop.LogWithRequestId)") // Match the same pointcut as in @Before
    fun afterMethod() {
        MDC.remove(REQUEST_ID_PARAM_NAME)
    }

    companion object {
        const val REQUEST_ID_PARAM_NAME: String = "requestId"
    }
}
