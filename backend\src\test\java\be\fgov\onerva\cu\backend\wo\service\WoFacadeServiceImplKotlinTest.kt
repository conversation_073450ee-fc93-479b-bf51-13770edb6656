package be.fgov.onerva.cu.backend.wo.service

import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.exception.WaveTaskNotFoundException
import be.fgov.onerva.cu.backend.wo.dto.WoMetadataDTO
import be.fgov.onerva.wo.facade.api.FacadeControllerApi
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.MetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.TaskDTO
import be.fgov.onerva.wo.organizational.chart.api.NodeApi
import be.fgov.onerva.wo_thirdparty.api.DefaultApi
import io.mockk.Runs
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.just
import io.mockk.slot
import io.mockk.verify
import io.mockk.verifySequence

@ExtendWith(MockKExtension::class)
class WoFacadeServiceImplKotlinTest {

    @MockK
    private lateinit var facadeControllerApi: FacadeControllerApi

    @MockK
    private lateinit var partyApi: DefaultApi

    @MockK
    private lateinit var nodeApi: NodeApi

    @InjectMockKs
    private lateinit var woFacadeService: WoFacadeServiceImpl

    @Nested
    inner class PatchProcessData {

        @Test
        fun `should update specific metadata fields while preserving others`() {
            // Given
            val processId = 1234L
            val taskId = 5678L

            // Existing metadata in the task
            val existingMetadata = listOf(
                MetaDataDTO(code = "field1", value = "value1"),
                MetaDataDTO(code = "field2", value = "value2"),
                MetaDataDTO(code = "field3", value = "value3")
            )

            // Task returned by the API
            val task = TaskDTO(
                concernedEntities = emptyList(),
                assignee = "test-assignee",
                taskTypeCode = "test-type",
                processId = processId,
                taskId = taskId,
                assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                processMetadata = existingMetadata
            )

            // New metadata to patch (only updating field2)
            val patchMetadata = listOf(
                WoMetadataDTO("field2", "updated-value2")
            )

            // Expected metadata after patching
            val expectedMetadata = listOf(
                InputMetaDataDTO(code = "field1", value = "value1"),
                InputMetaDataDTO(code = "field2", value = "updated-value2"),
                InputMetaDataDTO(code = "field3", value = "value3"),
            )

            // Capture the metadata passed to updateProcessData
            val metadataCaptor = slot<List<InputMetaDataDTO>>()

            // Setup mocks
            every { facadeControllerApi.getTask(taskId) } returns task
            every { facadeControllerApi.updateProcess(eq(processId), capture(metadataCaptor)) } just Runs

            // When
            woFacadeService.patchProcessData(processId, taskId, patchMetadata)

            // Then
            // Verify that the correct methods were called in sequence
            verifySequence {
                facadeControllerApi.getTask(taskId)
                facadeControllerApi.updateProcess(processId, any())
            }

            // Verify the content of the metadata passed to updateProcessData
            val capturedMetadata = metadataCaptor.captured
            assertThat(capturedMetadata).hasSize(3)

            // Check each metadata field individually
            with(capturedMetadata) {
                assertThat(find { it.code == "field1" }?.value).isEqualTo("value1")
                assertThat(find { it.code == "field2" }?.value).isEqualTo("updated-value2")
                assertThat(find { it.code == "field3" }?.value).isEqualTo("value3")
            }

            // Alternative verification that the entire list matches the expected metadata
            assertThat(capturedMetadata).containsExactlyInAnyOrderElementsOf(expectedMetadata)
        }

        @Test
        fun `should add new metadata fields if they don't exist`() {
            // Given
            val processId = 1234L
            val taskId = 5678L

            // Existing metadata in the task
            val existingMetadata = listOf(
                MetaDataDTO(code = "field1", value = "value1")
            )

            // Task returned by the API
            val task = TaskDTO(
                concernedEntities = emptyList(),
                assignee = "test-assignee",
                taskTypeCode = "test-type",
                processId = processId,
                taskId = taskId,
                assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                processMetadata = existingMetadata
            )

            // New metadata to patch (adding field2)
            val patchMetadata = listOf(
                WoMetadataDTO("field2", "value2")
            )

            // Expected metadata after patching (should NOT contain field2, as it wouldn't be in the existing metadata)
            val expectedMetadata = listOf(
                InputMetaDataDTO(code = "field1", value = "value1")
            )

            // Capture the metadata passed to updateProcessData
            val metadataCaptor = slot<List<InputMetaDataDTO>>()

            // Setup mocks
            every { facadeControllerApi.getTask(taskId) } returns task
            every { facadeControllerApi.updateProcess(eq(processId), capture(metadataCaptor)) } just Runs

            // When
            woFacadeService.patchProcessData(processId, taskId, patchMetadata)

            // Then
            // Verify the content of the metadata passed to updateProcessData
            val capturedMetadata = metadataCaptor.captured

            // The patch operation should only update existing fields, not add new ones
            assertThat(capturedMetadata).hasSize(1)
            assertThat(capturedMetadata[0].code).isEqualTo("field1")
            assertThat(capturedMetadata[0].value).isEqualTo("value1")

            // Verify we're preserving the original metadata (without adding the new field)
            assertThat(capturedMetadata).containsExactlyInAnyOrderElementsOf(expectedMetadata)
        }

//        @Test
//        fun `should throw WaveTaskNotFoundException when task is not found`() {
//            // Given
//            val processId = 1234L
//            val taskId = 5678L
//            val patchMetadata = listOf(
//                WoMetadataDTO("field1", "updated-value1")
//            )
//
//            // Setup mocks - task not found
//            every { facadeControllerApi.getTask(taskId) } returns null
//
//            // When/Then
//            assertThatThrownBy {
//                woFacadeService.patchProcessData(processId, taskId, patchMetadata)
//            }
//                .isInstanceOf(WaveTaskNotFoundException::class.java)
//                .hasMessage("Wave task not found for task $taskId")
//
//            // Verify getTask was called but updateProcess was not
//            verify(exactly = 1) { facadeControllerApi.getTask(taskId) }
//            verify(exactly = 0) { facadeControllerApi.updateProcess(any(), any()) }
//        }

        @Test
        fun `should handle task with no process metadata`() {
            // Given
            val processId = 1234L
            val taskId = 5678L

            // Task with null process metadata
            val task = TaskDTO(
                concernedEntities = emptyList(),
                assignee = "test-assignee",
                taskTypeCode = "test-type",
                processId = processId,
                taskId = taskId,
                assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                processMetadata = null
            )

            // New metadata to patch
            val patchMetadata = listOf(
                WoMetadataDTO("field1", "value1")
            )

            // Setup mocks
            every { facadeControllerApi.getTask(taskId) } returns task

            // When/Then
            assertThatThrownBy {
                woFacadeService.patchProcessData(processId, taskId, patchMetadata)
            }
                .isInstanceOf(WaveTaskNotFoundException::class.java)
                .hasMessage("Wave task not found for task $taskId")

            // Verify getTask was called but updateProcess was not
            verify(exactly = 1) { facadeControllerApi.getTask(taskId) }
            verify(exactly = 0) { facadeControllerApi.updateProcess(any(), any()) }
        }

        @Test
        fun `should update multiple metadata fields when all are specified`() {
            // Given
            val processId = 1234L
            val taskId = 5678L

            // Existing metadata in the task
            val existingMetadata = listOf(
                MetaDataDTO(code = "field1", value = "value1"),
                MetaDataDTO(code = "field2", value = "value2"),
                MetaDataDTO(code = "field3", value = "value3")
            )

            // Task returned by the API
            val task = TaskDTO(
                concernedEntities = emptyList(),
                assignee = "test-assignee",
                taskTypeCode = "test-type",
                processId = processId,
                taskId = taskId,
                assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO(),
                processMetadata = existingMetadata
            )

            // New metadata to patch (updating all fields)
            val patchMetadata = listOf(
                WoMetadataDTO("field1", "updated-value1"),
                WoMetadataDTO("field2", "updated-value2"),
                WoMetadataDTO("field3", "updated-value3")
            )

            // Expected metadata after patching
            val expectedMetadata = listOf(
                InputMetaDataDTO(code = "field1", value = "updated-value1"),
                InputMetaDataDTO(code = "field2", value = "updated-value2"),
                InputMetaDataDTO(code = "field3", value = "updated-value3")
            )

            // Capture the metadata passed to updateProcessData
            val metadataCaptor = slot<List<InputMetaDataDTO>>()

            // Setup mocks
            every { facadeControllerApi.getTask(taskId) } returns task
            every { facadeControllerApi.updateProcess(eq(processId), capture(metadataCaptor)) } just Runs

            // When
            woFacadeService.patchProcessData(processId, taskId, patchMetadata)

            // Then
            // Verify the content of the metadata passed to updateProcessData
            val capturedMetadata = metadataCaptor.captured
            assertThat(capturedMetadata).hasSize(3)

            // Verify all fields were updated
            assertThat(capturedMetadata).containsExactlyInAnyOrderElementsOf(expectedMetadata)
        }
    }

    @Nested
    inner class CloseTask {
        @Test
        fun `closeTask should call facadeControllerApi with closeProcess flag set to false`() {
            // Given
            val taskId = 5678L

            // Setup mocks - closeTask returns TaskDTO, not null
            every { facadeControllerApi.closeTask(taskId, any()) } returns TaskDTO(
                concernedEntities = emptyList(),
                assignee = "test-assignee",
                taskTypeCode = "test-type",
                processId = 1234L,
                taskId = taskId,
                assigneeInfo = be.fgov.onerva.wo.facade.rest.model.AssigneeInfoDTO()
            )

            // When
            woFacadeService.closeTask(taskId)

            // Then
            verify(exactly = 1) { facadeControllerApi.closeTask(taskId, any()) }
        }
    }
}