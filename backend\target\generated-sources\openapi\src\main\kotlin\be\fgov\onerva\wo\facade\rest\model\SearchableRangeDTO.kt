/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param from 
 * @param to 
 */


data class SearchableRangeDTO (

    @get:JsonProperty("from")
    val from: kotlin.String? = null,

    @get:JsonProperty("to")
    val to: kotlin.String? = null

) {


}

