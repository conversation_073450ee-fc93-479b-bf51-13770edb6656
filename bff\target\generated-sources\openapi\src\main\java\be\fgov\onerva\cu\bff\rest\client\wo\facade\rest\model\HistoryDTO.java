/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HistoryDTO
 */
@JsonPropertyOrder({
  HistoryDTO.JSON_PROPERTY_OPERATION,
  HistoryDTO.JSON_PROPERTY_OLD_VALUE,
  HistoryDTO.JSON_PROPERTY_NEW_VALUE,
  HistoryDTO.JSON_PROPERTY_COMMENT,
  HistoryDTO.JSON_PROPERTY_UPDATER,
  HistoryDTO.JSON_PROPERTY_UPDATE_DATE
})
@JsonTypeName("History")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HistoryDTO {
  /**
   * Gets or Sets operation
   */
  public enum OperationEnum {
    ASSIGN("assign"),
    
    DISTRIBUTE("distribute"),
    
    CREATE_TASK("createTask"),
    
    ADD_CONCERNED_ENTITIES("addConcernedEntities"),
    
    REMOVE_CONCERNED_ENTITIES("removeConcernedEntities"),
    
    UPDATE_DUE_DATE("updateDueDate"),
    
    UPDATE_PRIORITY("updatePriority"),
    
    CLOSE("close"),
    
    PRECLOSE("preclose"),
    
    REOPEN("reopen"),
    
    WAKEUP("wakeup"),
    
    WAIT("wait"),
    
    IN_REVIEW("inReview"),
    
    END_REVIEW("endReview"),
    
    OTHER("other");

    private String value;

    OperationEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static OperationEnum fromValue(String value) {
      for (OperationEnum b : OperationEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_OPERATION = "operation";
  private OperationEnum operation;

  public static final String JSON_PROPERTY_OLD_VALUE = "oldValue";
  private String oldValue;

  public static final String JSON_PROPERTY_NEW_VALUE = "newValue";
  private String newValue;

  public static final String JSON_PROPERTY_COMMENT = "comment";
  private String comment;

  public static final String JSON_PROPERTY_UPDATER = "updater";
  private String updater;

  public static final String JSON_PROPERTY_UPDATE_DATE = "updateDate";
  private LocalDateTime updateDate;

  public HistoryDTO() {
  }

  public HistoryDTO operation(OperationEnum operation) {
    
    this.operation = operation;
    return this;
  }

  /**
   * Get operation
   * @return operation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OperationEnum getOperation() {
    return operation;
  }


  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperation(OperationEnum operation) {
    this.operation = operation;
  }

  public HistoryDTO oldValue(String oldValue) {
    
    this.oldValue = oldValue;
    return this;
  }

  /**
   * Get oldValue
   * @return oldValue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OLD_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOldValue() {
    return oldValue;
  }


  @JsonProperty(JSON_PROPERTY_OLD_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOldValue(String oldValue) {
    this.oldValue = oldValue;
  }

  public HistoryDTO newValue(String newValue) {
    
    this.newValue = newValue;
    return this;
  }

  /**
   * Get newValue
   * @return newValue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NEW_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNewValue() {
    return newValue;
  }


  @JsonProperty(JSON_PROPERTY_NEW_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNewValue(String newValue) {
    this.newValue = newValue;
  }

  public HistoryDTO comment(String comment) {
    
    this.comment = comment;
    return this;
  }

  /**
   * This field will contain the comment added when doing the action (for example an assign).
   * @return comment
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getComment() {
    return comment;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComment(String comment) {
    this.comment = comment;
  }

  public HistoryDTO updater(String updater) {
    
    this.updater = updater;
    return this;
  }

  /**
   * The person who did the change
   * @return updater
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UPDATER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUpdater() {
    return updater;
  }


  @JsonProperty(JSON_PROPERTY_UPDATER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUpdater(String updater) {
    this.updater = updater;
  }

  public HistoryDTO updateDate(LocalDateTime updateDate) {
    
    this.updateDate = updateDate;
    return this;
  }

  /**
   * The person who did the change
   * @return updateDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_UPDATE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getUpdateDate() {
    return updateDate;
  }


  @JsonProperty(JSON_PROPERTY_UPDATE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUpdateDate(LocalDateTime updateDate) {
    this.updateDate = updateDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HistoryDTO history = (HistoryDTO) o;
    return Objects.equals(this.operation, history.operation) &&
        Objects.equals(this.oldValue, history.oldValue) &&
        Objects.equals(this.newValue, history.newValue) &&
        Objects.equals(this.comment, history.comment) &&
        Objects.equals(this.updater, history.updater) &&
        Objects.equals(this.updateDate, history.updateDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(operation, oldValue, newValue, comment, updater, updateDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HistoryDTO {\n");
    sb.append("    operation: ").append(toIndentedString(operation)).append("\n");
    sb.append("    oldValue: ").append(toIndentedString(oldValue)).append("\n");
    sb.append("    newValue: ").append(toIndentedString(newValue)).append("\n");
    sb.append("    comment: ").append(toIndentedString(comment)).append("\n");
    sb.append("    updater: ").append(toIndentedString(updater)).append("\n");
    sb.append("    updateDate: ").append(toIndentedString(updateDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

