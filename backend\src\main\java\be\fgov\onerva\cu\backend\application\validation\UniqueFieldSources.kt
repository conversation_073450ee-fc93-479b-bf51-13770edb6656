package be.fgov.onerva.cu.backend.application.validation

import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass
import be.fgov.onerva.cu.backend.application.domain.FieldSource

@Target(AnnotationTarget.FIELD, AnnotationTarget.VALUE_PARAMETER)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [UniqueFieldSourcesValidator::class])
annotation class UniqueFieldSources(
    val message: String = "Field sources must be unique",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class UniqueFieldSourcesValidator : ConstraintValidator<UniqueFieldSources, List<FieldSource>?> {
    override fun isValid(fieldSources: List<FieldSource>?, context: ConstraintValidatorContext): Boolean {
        if (fieldSources.isNullOrEmpty()) {
            return true
        }

        val fieldNames = fieldSources.map { it.fieldName }
        return fieldNames.size == fieldNames.distinct().size
    }
}