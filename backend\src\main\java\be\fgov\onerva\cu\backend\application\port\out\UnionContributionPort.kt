package be.fgov.onerva.cu.backend.application.port.out

import java.util.UUID
import be.fgov.onerva.cu.backend.application.domain.UnionContribution

/**
 * Port interface for managing union contribution persistence operations.
 *
 * This interface defines the contract for loading and persisting union contribution information
 * as part of the hexagonal architecture's port pattern. It serves as an abstraction layer
 * between the domain logic and the persistence infrastructure.
 *
 * @see UnionContribution Domain model containing union contribution details
 */
interface UnionContributionPort {
    /**
     * Retrieves the union contribution information associated with a specific request.
     *
     * @param requestId The unique identifier of the request to fetch union contribution information for
     * @return The [UnionContribution] if found for the given request ID, or null if no information exists
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     */
    fun getUnionContribution(requestId: UUID): UnionContribution?

    /**
     * Persists or updates union contribution information for a specific request.
     *
     * If union contribution information already exists for the given request ID, it will be updated.
     * Otherwise, new union contribution information will be created.
     *
     * @param requestId The unique identifier of the request to associate the union contribution information with
     * @param unionContribution The union contribution information to be persisted
     * @return The unique identifier of the entity representing the union contribution information
     * @throws RequestIdNotFoundException if the provided request ID doesn't exist in the system
     * @throws InvalidInputException if the provided union contribution data is invalid
     */
    fun persistUnionContribution(requestId: UUID, unionContribution: UnionContribution)

    fun getLatestRevision(requestId: UUID): Int

    fun getUnionContributionForRevision(requestId: UUID, revision: Int): UnionContribution?

    fun deleteUnionContribution(requestId: UUID)

    fun getEntityId(requestId: UUID): UUID

    fun patchCurrentDataWithRevision(requestId: UUID, revision: Int)
}