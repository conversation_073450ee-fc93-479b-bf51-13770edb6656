/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.Annex

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param requestDate The date when the request was submitted
 * @param firstName The first name of the person
 * @param lastName The last name of the person
 * @param ssin The Social Security Identification Number
 * @param c9Id The ID of the related C9 request
 * @param documentType The type of identity document used for the request
 * @param introductionDate The introduction date of the request
 * @param dateValid The valid date of the request
 * @param annexes 
 * @param decisionType The decision type of the request
 * @param decisionBarema 
 * @param pushbackStatus The pushback status of the request
 */


data class RequestBasicInfoResponse (

    /* The date when the request was submitted */
    @get:JsonProperty("requestDate")
    val requestDate: java.time.LocalDate,

    /* The first name of the person */
    @get:JsonProperty("firstName")
    val firstName: kotlin.String,

    /* The last name of the person */
    @get:JsonProperty("lastName")
    val lastName: kotlin.String,

    /* The Social Security Identification Number */
    @get:JsonProperty("ssin")
    val ssin: kotlin.String,

    /* The ID of the related C9 request */
    @get:JsonProperty("c9Id")
    val c9Id: kotlin.String,

    /* The type of identity document used for the request */
    @get:JsonProperty("documentType")
    val documentType: RequestBasicInfoResponse.DocumentType,

    /* The introduction date of the request */
    @get:JsonProperty("introductionDate")
    val introductionDate: java.time.LocalDate? = null,

    /* The valid date of the request */
    @get:JsonProperty("dateValid")
    val dateValid: java.time.LocalDate? = null,

    @get:JsonProperty("annexes")
    val annexes: kotlin.collections.List<Annex>? = null,

    /* The decision type of the request */
    @get:JsonProperty("decisionType")
    val decisionType: RequestBasicInfoResponse.DecisionType? = null,

    @get:JsonProperty("decisionBarema")
    val decisionBarema: kotlin.String? = null,

    /* The pushback status of the request */
    @get:JsonProperty("pushbackStatus")
    val pushbackStatus: RequestBasicInfoResponse.PushbackStatus? = null

) {

    /**
     * The type of identity document used for the request
     *
     * Values: ELECTRONIC,PAPER
     */
    enum class DocumentType(val value: kotlin.String) {
        @JsonProperty(value = "ELECTRONIC") ELECTRONIC("ELECTRONIC"),
        @JsonProperty(value = "PAPER") PAPER("PAPER");
    }
    /**
     * The decision type of the request
     *
     * Values: C2_Y,C2_N,C2_F,C2_P,C51,C9_B,C2,C9_NA
     */
    enum class DecisionType(val value: kotlin.String) {
        @JsonProperty(value = "C2Y") C2_Y("C2Y"),
        @JsonProperty(value = "C2N") C2_N("C2N"),
        @JsonProperty(value = "C2F") C2_F("C2F"),
        @JsonProperty(value = "C2P") C2_P("C2P"),
        @JsonProperty(value = "C51") C51("C51"),
        @JsonProperty(value = "C9B") C9_B("C9B"),
        @JsonProperty(value = "C2") C2("C2"),
        @JsonProperty(value = "C9NA") C9_NA("C9NA");
    }
    /**
     * The pushback status of the request
     *
     * Values: PENDING,OK,NOK
     */
    enum class PushbackStatus(val value: kotlin.String) {
        @JsonProperty(value = "PENDING") PENDING("PENDING"),
        @JsonProperty(value = "OK") OK("OK"),
        @JsonProperty(value = "NOK") NOK("NOK");
    }

}

