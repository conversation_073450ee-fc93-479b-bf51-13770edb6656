apiVersion: skaffold/v4beta11
kind: Config
metadata:
  name: infra
deploy:
  helm:
    releases:
      - name: cu-infra
        chartPath: helm/cu
        setValues:
          backend.enabled: "false"
          bff.enabled: "false"
          cu.enabled: "false"
          microcks.dynamock.enabled: "false"
          infra.enabled: "true"
          kcconfig.enabled: "true"
          onerva-metadata.enabled: "false"
          woconfig.configClient.enabled: "true"
          woconfig.enabled: "true"
          woconfig.wo-fake.enabled: "true"
          woconfig.wo_backend.enabled: "true"
        wait: true
        skipBuildDependencies: true
        upgradeOnChange: false
    flags:
      install:
        - --timeout=240s
      upgrade:
        - --timeout=180s
profiles:
  - name: dev
    activation:
      - command: dev
      - command: run
      - command: debug
    patches:
      - op: add
        path: /deploy/helm/releases/0/valuesFiles
        value:
          - helm/environments/values-dev.yaml
      - op: add
        path: /deploy/kubeContext
        value: default
      - op: add
        path: /deploy/helm/releases/0/createNamespace
        value: true
      - op: add
        path: /deploy/helm/releases/0/namespace
        value: cu
    build:
      local:
        push: true
    portForward:
      - resourceType: Service
        resourceName: keycloak-http
        namespace: cu
        port: 80
        address: 0.0.0.0
        localPort: 8092
      - resourceType: service
        resourceName: cu-infra-keycloak-http
        namespace: cu
        port: 80
        localPort: 8082
      - resourceType: service
        resourceName: cu-infra-mssql
        namespace: cu
        port: 1433
        localPort: 1433
      - resourceType: Service
        resourceName: cu-infra-woconfigurator
        namespace: cu
        port: 80
        localPort: 8077
      - resourceType: Service
        resourceName: cu-infra-fake-wo-api
        namespace: cu
        port: 8080
        localPort: 8070
      - resourceType: service
        resourceName: rabbitmq
        namespace: cu
        port: 5672
        localPort: 5672
      - resourceType: service
        resourceName: rabbitmq
        namespace: cu
        port: 15672
        localPort: 15672
      - resourceType: Service
        resourceName: cu-infra-microcks
        namespace: cu
        port: 80
        address: 0.0.0.0
        localPort: 9997
      - resourceType: Service
        resourceName: cu-infra-wiremock
        namespace: cu
        port: 80
        localPort: 9998
---
apiVersion: skaffold/v4beta11
kind: Config
metadata:
  name: app
build:
  artifacts:
    - image: onemrva/cu-backend
      context: .
      sync:
        auto: true
      jib:
        project: backend
        args:
          - -DsendCredentialsOverHttp=true
          - install
    - image: onemrva/cu-cu
      context: .
      sync:
        manual:
          - src: frontend/projects/cu/**/*.*
            dest: /usr/src/app
            strip: frontend
      docker:
        dockerfile: frontend/Dockerfile
        buildArgs:
          ANGULAR_IMAGE_TAG: angular_18
          APPLICATION_NAME: cu-app
    - image: onemrva/cu-bff
      context: .
      sync:
        auto: true
      jib:
        project: bff
        args:
          - -DsendCredentialsOverHttp=true
          - install
  insecureRegistries:
    - docker-proxy.onemrva.priv
    - docker-alpha.onemrva.priv
    - docker-beta.onemrva.priv
    - docker-release.onemrva.priv
  local:
    useDockerCLI: true
deploy:
  helm:
    releases:
      - name: cu
        chartPath: helm/cu
        setValues:
          backend.enabled: "true"
          bff.enabled: "true"
          cu.enabled: "true"
          infra.enabled: "false"
          kcconfig.enabled: "false"
          onerva-metadata.enabled: "true"
          woconfig.enabled: "false"
        setValueTemplates:
          backend.image.registry: '{{.IMAGE_DOMAIN_onemrva_cu_backend}}'
          backend.image.repository: '{{.IMAGE_REPO_NO_DOMAIN_onemrva_cu_backend}}'
          backend.image.tag: '{{.IMAGE_TAG_onemrva_cu_backend}}@{{.IMAGE_DIGEST_onemrva_cu_backend}}'
          bff.image.registry: '{{.IMAGE_DOMAIN_onemrva_cu_bff}}'
          bff.image.repository: '{{.IMAGE_REPO_NO_DOMAIN_onemrva_cu_bff}}'
          bff.image.tag: '{{.IMAGE_TAG_onemrva_cu_bff}}@{{.IMAGE_DIGEST_onemrva_cu_bff}}'
          cu.image.registry: '{{.IMAGE_DOMAIN_onemrva_cu_cu}}'
          cu.image.repository: '{{.IMAGE_REPO_NO_DOMAIN_onemrva_cu_cu}}'
          cu.image.tag: '{{.IMAGE_TAG_onemrva_cu_cu}}@{{.IMAGE_DIGEST_onemrva_cu_cu}}'
        wait: true
        skipBuildDependencies: true
        upgradeOnChange: false
    flags:
      install:
        - --timeout=7m
      upgrade:
        - --timeout=7m
profiles:
  - name: dev
    activation:
      - command: dev
      - command: run
      - command: debug
    patches:
      - op: add
        path: /deploy/helm/releases/0/valuesFiles
        value:
          - helm/environments/values-dev.yaml
      - op: add
        path: /deploy/kubeContext
        value: default
      - op: add
        path: /deploy/helm/releases/0/createNamespace
        value: true
      - op: add
        path: /deploy/helm/releases/0/namespace
        value: cu
      - op: replace
        path: /build/artifacts/1/docker/dockerfile
        value: frontend/local-dev.Dockerfile
    build:
      local:
        push: true
    portForward:
      - resourceType: service
        resourceName: cu-backend
        namespace: cu
        port: 80
        localPort: 9091
      - resourceType: service
        resourceName: cu-bff
        namespace: cu
        port: 80
        localPort: 9090
      - resourceType: service
        resourceName: cu
        namespace: cu
        port: 80
        localPort: 4300
  - name: ci
    patches:
      - op: add
        path: /deploy/helm/releases/0/wait
        value: true
      - op: add
        path: /deploy/helm/releases/0/version
        value: '{{.bamboo.version_prefix}}'
      - op: add
        path: /deploy/helm/releases/0/packaged
        value:
          appVersion: '{{.bamboo.version_prefix}}'
          version: '{{.bamboo.version_prefix}}'
      - op: add
        path: /build/artifacts/2
        value:
          context: .
          docker:
            buildArgs:
              NEXUS_PASSWORD: '{{.bamboo_nexus_secret}}'
              NEXUS_USER: '{{.bamboo_nexus_user}}'
              PUSH_REPORT: true
            dockerfile: frontend/e2e.Dockerfile
          image: onemrva/cu-cu-e2e
      - op: add
        path: /build/artifacts/3
        value:
          context: .
          docker:
            buildArgs:
              NEXUS_PASSWORD: '{{.bamboo_nexus_secret}}'
              NEXUS_USER: '{{.bamboo_nexus_user}}'
              PUSH_REPORT: true
            dockerfile: e2e/karate/Dockerfile
          image: onemrva/cu-e2e-karate
    build:
      tagPolicy:
        customTemplate:
          template: '{{.PRE}}-{{.GIT}}'
          components:
            - name: PRE
              envTemplate:
                template: '{{.bamboo.version_prefix}}'
            - name: GIT
              gitCommit:
                variant: AbbrevCommitSha
                ignoreChanges: true
