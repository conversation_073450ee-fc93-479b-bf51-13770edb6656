--liquibase formatted sql
--changeset bernard:add-payment-mode-citizen-information-snapshot context:ddl
--validCheckSum: 9:b024e447c1856cc3729afd4962e1dce2
--validCheckSum: 9:5e6cfc85778de91be16d98a2a04429fb
alter table citizen_information_snapshot
    add payment_mode int null;

-- Add default value constraint payment_mode = 1
alter table citizen_information_snapshot
    add constraint df_payment_mode default 1 for payment_mode;

update citizen_information_snapshot
set payment_mode = 1
where payment_mode is null;

-- make payment mode not null
alter table citizen_information_snapshot
    alter column payment_mode int not null;

