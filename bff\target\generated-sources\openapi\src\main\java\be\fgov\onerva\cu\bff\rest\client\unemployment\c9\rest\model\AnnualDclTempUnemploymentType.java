/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * AnnualDclTempUnemploymentType
 */
@JsonPropertyOrder({
  AnnualDclTempUnemploymentType.JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE,
  AnnualDclTempUnemploymentType.JSON_PROPERTY_MAJOR_FORCE_REASON,
  AnnualDclTempUnemploymentType.JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class AnnualDclTempUnemploymentType {
  public static final String JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE = "tempUnemploymentStartingDate";
  private LocalDate tempUnemploymentStartingDate;

  public static final String JSON_PROPERTY_MAJOR_FORCE_REASON = "majorForceReason";
  private String majorForceReason;

  public static final String JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D = "usingEmployerCompanyID";
  private String usingEmployerCompanyID;

  public AnnualDclTempUnemploymentType() {
  }

  public AnnualDclTempUnemploymentType tempUnemploymentStartingDate(LocalDate tempUnemploymentStartingDate) {
    
    this.tempUnemploymentStartingDate = tempUnemploymentStartingDate;
    return this;
  }

  /**
   * Get tempUnemploymentStartingDate
   * @return tempUnemploymentStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getTempUnemploymentStartingDate() {
    return tempUnemploymentStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_TEMP_UNEMPLOYMENT_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTempUnemploymentStartingDate(LocalDate tempUnemploymentStartingDate) {
    this.tempUnemploymentStartingDate = tempUnemploymentStartingDate;
  }

  public AnnualDclTempUnemploymentType majorForceReason(String majorForceReason) {
    
    this.majorForceReason = majorForceReason;
    return this;
  }

  /**
   * Get majorForceReason
   * @return majorForceReason
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getMajorForceReason() {
    return majorForceReason;
  }


  @JsonProperty(JSON_PROPERTY_MAJOR_FORCE_REASON)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMajorForceReason(String majorForceReason) {
    this.majorForceReason = majorForceReason;
  }

  public AnnualDclTempUnemploymentType usingEmployerCompanyID(String usingEmployerCompanyID) {
    
    this.usingEmployerCompanyID = usingEmployerCompanyID;
    return this;
  }

  /**
   * Get usingEmployerCompanyID
   * @return usingEmployerCompanyID
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getUsingEmployerCompanyID() {
    return usingEmployerCompanyID;
  }


  @JsonProperty(JSON_PROPERTY_USING_EMPLOYER_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setUsingEmployerCompanyID(String usingEmployerCompanyID) {
    this.usingEmployerCompanyID = usingEmployerCompanyID;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    AnnualDclTempUnemploymentType annualDclTempUnemploymentType = (AnnualDclTempUnemploymentType) o;
    return Objects.equals(this.tempUnemploymentStartingDate, annualDclTempUnemploymentType.tempUnemploymentStartingDate) &&
        Objects.equals(this.majorForceReason, annualDclTempUnemploymentType.majorForceReason) &&
        Objects.equals(this.usingEmployerCompanyID, annualDclTempUnemploymentType.usingEmployerCompanyID);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tempUnemploymentStartingDate, majorForceReason, usingEmployerCompanyID);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class AnnualDclTempUnemploymentType {\n");
    sb.append("    tempUnemploymentStartingDate: ").append(toIndentedString(tempUnemploymentStartingDate)).append("\n");
    sb.append("    majorForceReason: ").append(toIndentedString(majorForceReason)).append("\n");
    sb.append("    usingEmployerCompanyID: ").append(toIndentedString(usingEmployerCompanyID)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

