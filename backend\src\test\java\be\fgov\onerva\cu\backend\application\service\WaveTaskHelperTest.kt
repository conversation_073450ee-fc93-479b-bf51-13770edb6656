package be.fgov.onerva.cu.backend.application.service.helpers

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.application.domain.CreateChangePersonalDataTaskCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTask
import be.fgov.onerva.cu.backend.application.domain.WaveTaskRevisionNumbers
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidRequestIdException
import be.fgov.onerva.cu.backend.application.exception.WaveException
import be.fgov.onerva.cu.backend.application.port.out.CitizenInformationPort
import be.fgov.onerva.cu.backend.application.port.out.ModeOfPaymentPort
import be.fgov.onerva.cu.backend.application.port.out.RequestInformationPort
import be.fgov.onerva.cu.backend.application.port.out.UnionContributionPort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPersistencePort
import be.fgov.onerva.cu.backend.application.port.out.WaveTaskPort
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify
import io.mockk.verifyOrder

@ExtendWith(MockKExtension::class)
class WaveTaskHelperTest {

    @MockK
    private lateinit var waveTaskPersistencePort: WaveTaskPersistencePort

    @MockK
    private lateinit var waveTaskPort: WaveTaskPort

    @MockK
    private lateinit var citizenInformationPort: CitizenInformationPort

    @MockK
    private lateinit var modeOfPaymentPort: ModeOfPaymentPort

    @MockK
    private lateinit var unionContributionPort: UnionContributionPort

    @MockK
    private lateinit var requestInformationPort: RequestInformationPort

    @InjectMockKs
    private lateinit var waveTaskHelper: WaveTaskHelper

    private val requestId = UUID.randomUUID()
    private val taskId = "task-123"
    private val processId = "process-456"

    @Nested
    inner class CloseDataCaptureTaskTests {

        @Test
        fun `should close task and persist latest revisions when task closing successful`() {
            // Given
            val citizenInfoRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4
            val username = "test-user"

            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns citizenInfoRevision
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns modeOfPaymentRevision
            every { unionContributionPort.getLatestRevision(requestId) } returns unionContributionRevision
            every { requestInformationPort.getLatestRevision(requestId) } returns requestInformationRevision
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every {
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            } returns Unit

            // When
            val result = waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)

            // Then
            assertThat(result).isTrue()

            verifyOrder {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.closeTask(taskId)
                citizenInformationPort.getLatestRevision(requestId)
                modeOfPaymentPort.getLatestRevision(requestId)
                unionContributionPort.getLatestRevision(requestId)
                requestInformationPort.getLatestRevision(requestId)
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            }
        }

        @Test
        fun `should return false and not persist revisions when task closing fails`() {
            // Given
            val username = "test-user"
            every { waveTaskPort.closeTask(taskId) } returns false
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When
            val result = waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)

            // Then
            assertThat(result).isFalse()

            verify(exactly = 1) {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.closeTask(taskId)
            }

            verify(exactly = 0) {
                citizenInformationPort.getLatestRevision(any())
                modeOfPaymentPort.getLatestRevision(any())
                unionContributionPort.getLatestRevision(any())
                requestInformationPort.getLatestRevision(any())
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataCapture(any(), any(), any(), any(), any())
            }
        }

        @Test
        fun `should propagate exceptions from dependencies`() {
            // Given
            val username = "test-user"
            val expectedException = RuntimeException("Test exception")
            every { waveTaskPort.closeTask(taskId) } throws expectedException
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")
        }
    }

    @Nested
    inner class CreateChangePersonalDataValidateTaskTests {

        @Test
        fun `should create validate task and persist it`() {
            // Given
            val assignee = "testUser"
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "123456789",
                numbox = 42,
                receptionDate = LocalDate.now(),
                entityCode = "EC123",
                requestDate = LocalDate.now(),
                dossierId = "SO123-OP456",
                paymentInstitution = 789,
                sectOp = "SO123"
            )
            val createdTask = WaveTask(
                processId = "new-process-123",
                taskId = "new-task-456",
                status = WaveTaskStatus.OPEN
            )

            every {
                waveTaskPort.createChangePersonalDataValidateTask(
                    requestId, processId, assignee, command
                )
            } returns createdTask

            every {
                waveTaskPersistencePort.persistChangePersonalDataValidateWaveTask(
                    requestId, createdTask
                )
            } returns Unit

            // When
            val result = waveTaskHelper.createChangePersonalDataValidateTask(
                requestId, processId, command, assignee
            )

            // Then
            assertThat(result).isEqualTo(createdTask)

            verifyOrder {
                waveTaskPort.createChangePersonalDataValidateTask(requestId, processId, assignee, command)
                waveTaskPersistencePort.persistChangePersonalDataValidateWaveTask(requestId, createdTask)
            }
        }

        @Test
        fun `should propagate exceptions from wave task port`() {
            // Given
            val assignee = "testUser"
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "123456789",
                numbox = 42,
                receptionDate = LocalDate.now(),
                entityCode = "EC123",
                requestDate = LocalDate.now(),
                dossierId = "SO123-OP456",
                paymentInstitution = 789,
                sectOp = "SO123"
            )
            val expectedException = RuntimeException("Test exception")

            every {
                waveTaskPort.createChangePersonalDataValidateTask(
                    requestId, processId, assignee, command
                )
            } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.createChangePersonalDataValidateTask(requestId, processId, command, assignee)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")

            verify(exactly = 0) {
                waveTaskPersistencePort.persistChangePersonalDataValidateWaveTask(any(), any())
            }
        }
    }

    @Nested
    inner class CloseValidateDataTaskTests {

        @Test
        fun `should close validate task and persist latest revisions when task closing successful`() {
            // Given
            val citizenInfoRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4
            val username = "test-user"

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns citizenInfoRevision
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns modeOfPaymentRevision
            every { unionContributionPort.getLatestRevision(requestId) } returns unionContributionRevision
            every { requestInformationPort.getLatestRevision(requestId) } returns requestInformationRevision
            every {
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataValidate(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            } returns Unit

            // When
            waveTaskHelper.assignAndCloseValidateDataTask(requestId, taskId, username)

            // Then
            verifyOrder {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.closeTask(taskId)
                citizenInformationPort.getLatestRevision(requestId)
                modeOfPaymentPort.getLatestRevision(requestId)
                unionContributionPort.getLatestRevision(requestId)
                requestInformationPort.getLatestRevision(requestId)
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataValidate(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            }
        }

        @Test
        fun `should not persist revisions when task closing fails`() {
            // Given
            val username = "test-user"
            every { waveTaskPort.closeTask(taskId) } returns false
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When
            waveTaskHelper.assignAndCloseValidateDataTask(requestId, taskId, username)

            // Then
            verify(exactly = 1) {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.closeTask(taskId)
            }

            verify(exactly = 0) {
                citizenInformationPort.getLatestRevision(any())
                modeOfPaymentPort.getLatestRevision(any())
                unionContributionPort.getLatestRevision(any())
                requestInformationPort.getLatestRevision(any())
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataValidate(any(), any(), any(), any(), any())
            }
        }

        @Test
        fun `should propagate exceptions from dependencies`() {
            // Given
            val expectedException = RuntimeException("Test exception")
            val username = "test-user"
            every { waveTaskPort.closeTask(taskId) } throws expectedException
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignAndCloseValidateDataTask(requestId, taskId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")
        }
    }

    @Nested
    inner class SleepDataCaptureTaskTests {

        @Test
        fun `should sleep task and persist latest revisions when task sleeping successful`() {
            // Given
            val citizenInfoRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4
            val username = "test-user"

            every { waveTaskPort.sleepTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns citizenInfoRevision
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns modeOfPaymentRevision
            every { unionContributionPort.getLatestRevision(requestId) } returns unionContributionRevision
            every { requestInformationPort.getLatestRevision(requestId) } returns requestInformationRevision
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every {
                waveTaskPersistencePort.sleepWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            } returns Unit

            // When
            val result = waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, username)

            // Then
            assertThat(result).isTrue()

            verifyOrder {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.sleepTask(taskId)
                citizenInformationPort.getLatestRevision(requestId)
                modeOfPaymentPort.getLatestRevision(requestId)
                unionContributionPort.getLatestRevision(requestId)
                requestInformationPort.getLatestRevision(requestId)
                waveTaskPersistencePort.sleepWaveTaskChangePersonalDataCapture(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            }
        }

        @Test
        fun `should return false and not persist revisions when task sleeping fails`() {
            // Given
            val username = "test-user"
            every { waveTaskPort.sleepTask(taskId) } returns false
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When
            val result = waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, username)

            // Then
            assertThat(result).isFalse()

            verify(exactly = 1) {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.sleepTask(taskId)
            }

            verify(exactly = 0) {
                citizenInformationPort.getLatestRevision(any())
                modeOfPaymentPort.getLatestRevision(any())
                unionContributionPort.getLatestRevision(any())
                requestInformationPort.getLatestRevision(any())
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataCapture(any(), any(), any(), any(), any())
            }
        }

        @Test
        fun `should propagate exceptions from dependencies`() {
            // Given
            val username = "test-user"
            val expectedException = RuntimeException("Test exception")
            every { waveTaskPort.sleepTask(taskId) } throws expectedException
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignAndSleepDataCaptureTask(requestId, taskId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")
        }
    }

    @Nested
    inner class SleepValidateDataTaskTests {

        @Test
        fun `should sleep validate task and persist latest revisions when task sleeping successful`() {
            // Given
            val citizenInfoRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4
            val username = "test-user"

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.sleepTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns citizenInfoRevision
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns modeOfPaymentRevision
            every { unionContributionPort.getLatestRevision(requestId) } returns unionContributionRevision
            every { requestInformationPort.getLatestRevision(requestId) } returns requestInformationRevision
            every {
                waveTaskPersistencePort.sleepWaveTaskChangePersonalDataValidate(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            } returns Unit

            // When
            waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, username)

            // Then
            verifyOrder {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.sleepTask(taskId)
                citizenInformationPort.getLatestRevision(requestId)
                modeOfPaymentPort.getLatestRevision(requestId)
                unionContributionPort.getLatestRevision(requestId)
                requestInformationPort.getLatestRevision(requestId)
                waveTaskPersistencePort.sleepWaveTaskChangePersonalDataValidate(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            }
        }

        @Test
        fun `should not persist revisions when task sleeping fails`() {
            // Given
            val username = "test-user"
            every { waveTaskPort.sleepTask(taskId) } returns false
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When
            waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, username)

            // Then
            verify(exactly = 1) {
                waveTaskPort.assignTaskToUser(taskId, username)
                waveTaskPort.sleepTask(taskId)
            }

            verify(exactly = 0) {
                citizenInformationPort.getLatestRevision(any())
                modeOfPaymentPort.getLatestRevision(any())
                unionContributionPort.getLatestRevision(any())
                requestInformationPort.getLatestRevision(any())
                waveTaskPersistencePort.closeWaveTaskChangePersonalDataValidate(any(), any(), any(), any(), any())
            }
        }

        @Test
        fun `should propagate exceptions from dependencies`() {
            // Given
            val expectedException = RuntimeException("Test exception")
            val username = "test-user"
            every { waveTaskPort.sleepTask(taskId) } throws expectedException
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Test exception")
        }
    }

    @Nested
    inner class AssignTaskToUserTests {

        @Test
        fun `should get open task and assign it to user`() {
            // Given
            val username = "testUser"
            val openTask = WaveTask(
                processId = "process-123",
                taskId = taskId,
                status = WaveTaskStatus.OPEN
            )

            every { waveTaskPersistencePort.getOpenWaveTaskByRequestId(requestId) } returns openTask
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit

            // When
            waveTaskHelper.assignTaskToUser(requestId, username)

            // Then
            verifyOrder {
                waveTaskPersistencePort.getOpenWaveTaskByRequestId(requestId)
                waveTaskPort.assignTaskToUser(taskId, username)
            }
        }

        @Test
        fun `should propagate exceptions when getting open task fails`() {
            // Given
            val username = "testUser"
            val expectedException = RuntimeException("No open task found")

            every { waveTaskPersistencePort.getOpenWaveTaskByRequestId(requestId) } throws expectedException

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignTaskToUser(requestId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("No open task found")

            verify(exactly = 0) {
                waveTaskPort.assignTaskToUser(any(), any())
            }
        }

        @Test
        fun `should propagate exceptions when assigning task fails`() {
            // Given
            val username = "testUser"
            val openTask = WaveTask(
                processId = "process-123",
                taskId = taskId,
                status = WaveTaskStatus.OPEN
            )
            val expectedException = RuntimeException("Failed to assign task")

            every { waveTaskPersistencePort.getOpenWaveTaskByRequestId(requestId) } returns openTask
            every { waveTaskPort.assignTaskToUser(taskId, username) } throws expectedException

            // When/Then
            assertThatThrownBy { waveTaskHelper.assignTaskToUser(requestId, username) }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Failed to assign task")
        }
    }
    @Nested
    inner class CreateChangePersonalDataTaskTests {

        @Test
        fun `should create change personal data task and persist it successfully`() {
            // Given
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "123456789",
                numbox = 42,
                receptionDate = LocalDate.now(),
                entityCode = "EC123",
                requestDate = LocalDate.now(),
                dossierId = "SO123-OP456",
                paymentInstitution = 789,
                sectOp = "SO123"
            )
            val createdTask = WaveTask(
                processId = "process-123",
                taskId = "task-456",
                status = WaveTaskStatus.OPEN
            )

            every {
                waveTaskPort.createChangePersonalDataTask(requestId, command)
            } returns createdTask

            every {
                waveTaskPersistencePort.persistChangePersonalDataCaptureWaveTask(requestId, createdTask)
            } returns Unit

            // When
            waveTaskHelper.createChangePersonalDataTask(requestId, command)

            // Then
            verifyOrder {
                waveTaskPort.createChangePersonalDataTask(requestId, command)
                waveTaskPersistencePort.persistChangePersonalDataCaptureWaveTask(requestId, createdTask)
            }
        }

        @Test
        fun `should propagate WaveException when createChangePersonalDataTask fails`() {
            // Given
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "123456789",
                numbox = 42,
                receptionDate = LocalDate.now(),
                entityCode = "EC123",
                requestDate = LocalDate.now(),
                dossierId = "SO123-OP456",
                paymentInstitution = 789,
                sectOp = "SO123"
            )
            val expectedException = WaveException("Error creating task for c9id: 12345")

            every {
                waveTaskPort.createChangePersonalDataTask(requestId, command)
            } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.createChangePersonalDataTask(requestId, command)
            }
                .isInstanceOf(WaveException::class.java)
                .hasMessage("Error creating task for c9id: 12345")

            verify(exactly = 0) {
                waveTaskPersistencePort.persistChangePersonalDataCaptureWaveTask(any(), any())
            }
        }

        @Test
        fun `should propagate exception when persistence fails`() {
            // Given
            val command = CreateChangePersonalDataTaskCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "123456789",
                numbox = 42,
                receptionDate = LocalDate.now(),
                entityCode = "EC123",
                requestDate = LocalDate.now(),
                dossierId = "SO123-OP456",
                paymentInstitution = 789,
                sectOp = "SO123"
            )
            val createdTask = WaveTask(
                processId = "process-123",
                taskId = "task-456",
                status = WaveTaskStatus.OPEN
            )
            val expectedException = RuntimeException("Persistence failed")

            every {
                waveTaskPort.createChangePersonalDataTask(requestId, command)
            } returns createdTask

            every {
                waveTaskPersistencePort.persistChangePersonalDataCaptureWaveTask(requestId, createdTask)
            } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.createChangePersonalDataTask(requestId, command)
            }
                .isInstanceOf(RuntimeException::class.java)
                .hasMessage("Persistence failed")
        }
    }

    @Nested
    inner class PatchCurrentDataWithRevisionTests {

        @Test
        fun `should patch current data with valid revision numbers`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            every { citizenInformationPort.patchCurrentDataWithRevision(requestId, 1) } returns Unit
            every { modeOfPaymentPort.patchCurrentDataWithRevision(requestId, 2) } returns Unit
            every { unionContributionPort.patchCurrentDataWithRevision(requestId, 3) } returns Unit
            every { requestInformationPort.patchCurrentDataWithRevision(requestId, 4) } returns Unit

            // When
            waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)

            // Then
            verifyOrder {
                citizenInformationPort.patchCurrentDataWithRevision(requestId, 1)
                modeOfPaymentPort.patchCurrentDataWithRevision(requestId, 2)
                unionContributionPort.patchCurrentDataWithRevision(requestId, 3)
                requestInformationPort.patchCurrentDataWithRevision(requestId, 4)
            }
        }

        @Test
        fun `should throw IllegalArgumentException when citizenInformationRevisionNumber is null`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessage("Revision numbers cannot be null")

            verify(exactly = 0) {
                citizenInformationPort.patchCurrentDataWithRevision(any(), any())
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should throw IllegalArgumentException when modeOfPaymentRevisionNumber is null`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessage("Revision numbers cannot be null")

            verify(exactly = 0) {
                citizenInformationPort.patchCurrentDataWithRevision(any(), any())
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should throw IllegalArgumentException when unionContributionRevisionNumber is null`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = 4
            )

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessage("Revision numbers cannot be null")

            verify(exactly = 0) {
                citizenInformationPort.patchCurrentDataWithRevision(any(), any())
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should throw IllegalArgumentException when requestInformationRevisionNumber is null`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = null
            )

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessage("Revision numbers cannot be null")

            verify(exactly = 0) {
                citizenInformationPort.patchCurrentDataWithRevision(any(), any())
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should throw IllegalArgumentException when all revision numbers are null`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = null,
                modeOfPaymentRevisionNumber = null,
                unionContributionRevisionNumber = null,
                requestInformationRevisionNumber = null
            )

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(IllegalArgumentException::class.java)
                .hasMessage("Revision numbers cannot be null")

            verify(exactly = 0) {
                citizenInformationPort.patchCurrentDataWithRevision(any(), any())
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should propagate exception from citizenInformationPort`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { citizenInformationPort.patchCurrentDataWithRevision(requestId, 1) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 0) {
                modeOfPaymentPort.patchCurrentDataWithRevision(any(), any())
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should propagate exception from modeOfPaymentPort`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { citizenInformationPort.patchCurrentDataWithRevision(requestId, 1) } returns Unit
            every { modeOfPaymentPort.patchCurrentDataWithRevision(requestId, 2) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 0) {
                unionContributionPort.patchCurrentDataWithRevision(any(), any())
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should propagate exception from unionContributionPort`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { citizenInformationPort.patchCurrentDataWithRevision(requestId, 1) } returns Unit
            every { modeOfPaymentPort.patchCurrentDataWithRevision(requestId, 2) } returns Unit
            every { unionContributionPort.patchCurrentDataWithRevision(requestId, 3) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")

            verify(exactly = 0) {
                requestInformationPort.patchCurrentDataWithRevision(any(), any())
            }
        }

        @Test
        fun `should propagate exception from requestInformationPort`() {
            // Given
            val revisionNumbers = WaveTaskRevisionNumbers(
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 4
            )
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { citizenInformationPort.patchCurrentDataWithRevision(requestId, 1) } returns Unit
            every { modeOfPaymentPort.patchCurrentDataWithRevision(requestId, 2) } returns Unit
            every { unionContributionPort.patchCurrentDataWithRevision(requestId, 3) } returns Unit
            every { requestInformationPort.patchCurrentDataWithRevision(requestId, 4) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.patchCurrentDataWithRevision(requestId, revisionNumbers)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }
    }

    // Test supplémentaire pour couvrir la branche de retour manquante dans assignAndSleepValidateDataTask
    @Nested
    inner class AdditionalSleepValidateDataTaskTests {

        @Test
        fun `should return true when sleep validate task is successful`() {
            // Given
            val citizenInfoRevision = 1
            val modeOfPaymentRevision = 2
            val unionContributionRevision = 3
            val requestInformationRevision = 4
            val username = "test-user"

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.sleepTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns citizenInfoRevision
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns modeOfPaymentRevision
            every { unionContributionPort.getLatestRevision(requestId) } returns unionContributionRevision
            every { requestInformationPort.getLatestRevision(requestId) } returns requestInformationRevision
            every {
                waveTaskPersistencePort.sleepWaveTaskChangePersonalDataValidate(
                    requestId,
                    citizenInfoRevision,
                    modeOfPaymentRevision,
                    unionContributionRevision,
                    requestInformationRevision
                )
            } returns Unit

            // When
            val result = waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, username)

            // Then
            assertThat(result).isTrue()
        }

        @Test
        fun `should return false when sleep validate task fails`() {
            // Given
            val username = "test-user"
            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.sleepTask(taskId) } returns false

            // When
            val result = waveTaskHelper.assignAndSleepValidateDataTask(requestId, taskId, username)

            // Then
            assertThat(result).isFalse()
        }
    }

    // Tests pour couvrir les exceptions lors de la récupération des révisions
    @Nested
    inner class RevisionExceptionTests {

        @Test
        fun `should propagate exception from citizenInformationPort getLatestRevision in closeDataCaptureTask`() {
            // Given
            val username = "test-user"
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `should propagate exception from modeOfPaymentPort getLatestRevision in closeDataCaptureTask`() {
            // Given
            val username = "test-user"
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns 1
            every { modeOfPaymentPort.getLatestRevision(requestId) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `should propagate exception from unionContributionPort getLatestRevision in closeDataCaptureTask`() {
            // Given
            val username = "test-user"
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns 1
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns 2
            every { unionContributionPort.getLatestRevision(requestId) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }

        @Test
        fun `should propagate exception from requestInformationPort getLatestRevision in closeDataCaptureTask`() {
            // Given
            val username = "test-user"
            val expectedException = InvalidRequestIdException("Request ID not found: $requestId")

            every { waveTaskPort.assignTaskToUser(taskId, username) } returns Unit
            every { waveTaskPort.closeTask(taskId) } returns true
            every { citizenInformationPort.getLatestRevision(requestId) } returns 1
            every { modeOfPaymentPort.getLatestRevision(requestId) } returns 2
            every { unionContributionPort.getLatestRevision(requestId) } returns 3
            every { requestInformationPort.getLatestRevision(requestId) } throws expectedException

            // When/Then
            assertThatThrownBy {
                waveTaskHelper.assignAndCloseDataCaptureTask(requestId, taskId, username)
            }
                .isInstanceOf(InvalidRequestIdException::class.java)
                .hasMessage("Request ID not found: $requestId")
        }
    }
}