/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.ExceptSituationCalculBaseType;
import be.fgov.onerva.unemployment.c9.rest.model.OccupationFeaturesType;
import be.fgov.onerva.unemployment.c9.rest.model.Wech505HandledOccupationLink;
import be.fgov.onerva.unemployment.c9.rest.model.Wech505MonthlyDclTempUnemploymentHours;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505OccupationLink
 */
@JsonPropertyOrder({
  Wech505OccupationLink.JSON_PROPERTY_OCCUPATION_STARTING_DATE,
  Wech505OccupationLink.JSON_PROPERTY_OCCUPATION_ENDING_DATE,
  Wech505OccupationLink.JSON_PROPERTY_JOINT_COMMISSION_NBR,
  Wech505OccupationLink.JSON_PROPERTY_WORKING_DAYS_SYSTEM,
  Wech505OccupationLink.JSON_PROPERTY_MEAN_WORKING_HOURS,
  Wech505OccupationLink.JSON_PROPERTY_REF_MEAN_WORKING_HOURS,
  Wech505OccupationLink.JSON_PROPERTY_WORKER_STATUS,
  Wech505OccupationLink.JSON_PROPERTY_RETIRED,
  Wech505OccupationLink.JSON_PROPERTY_APPRENTICESHIP,
  Wech505OccupationLink.JSON_PROPERTY_CONTRACT_TYPE,
  Wech505OccupationLink.JSON_PROPERTY_REMUN_METHOD,
  Wech505OccupationLink.JSON_PROPERTY_HANDLED_OCCUPATION_LINK,
  Wech505OccupationLink.JSON_PROPERTY_OCCUPATION_FEATURES,
  Wech505OccupationLink.JSON_PROPERTY_EXCEPT_SITUATION_CALCUL_BASE,
  Wech505OccupationLink.JSON_PROPERTY_MONTHLY_DCL_TEMP_UNEMPLOYMENT_HOURS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505OccupationLink {
  public static final String JSON_PROPERTY_OCCUPATION_STARTING_DATE = "occupationStartingDate";
  private LocalDate occupationStartingDate;

  public static final String JSON_PROPERTY_OCCUPATION_ENDING_DATE = "occupationEndingDate";
  private LocalDate occupationEndingDate;

  public static final String JSON_PROPERTY_JOINT_COMMISSION_NBR = "jointCommissionNbr";
  private String jointCommissionNbr;

  public static final String JSON_PROPERTY_WORKING_DAYS_SYSTEM = "workingDaysSystem";
  private String workingDaysSystem;

  public static final String JSON_PROPERTY_MEAN_WORKING_HOURS = "meanWorkingHours";
  private String meanWorkingHours;

  public static final String JSON_PROPERTY_REF_MEAN_WORKING_HOURS = "refMeanWorkingHours";
  private String refMeanWorkingHours;

  public static final String JSON_PROPERTY_WORKER_STATUS = "workerStatus";
  private String workerStatus;

  public static final String JSON_PROPERTY_RETIRED = "retired";
  private String retired;

  public static final String JSON_PROPERTY_APPRENTICESHIP = "apprenticeship";
  private String apprenticeship;

  public static final String JSON_PROPERTY_CONTRACT_TYPE = "contractType";
  private String contractType;

  public static final String JSON_PROPERTY_REMUN_METHOD = "remunMethod";
  private String remunMethod;

  public static final String JSON_PROPERTY_HANDLED_OCCUPATION_LINK = "handledOccupationLink";
  private Wech505HandledOccupationLink handledOccupationLink;

  public static final String JSON_PROPERTY_OCCUPATION_FEATURES = "occupationFeatures";
  private OccupationFeaturesType occupationFeatures;

  public static final String JSON_PROPERTY_EXCEPT_SITUATION_CALCUL_BASE = "exceptSituationCalculBase";
  private ExceptSituationCalculBaseType exceptSituationCalculBase;

  public static final String JSON_PROPERTY_MONTHLY_DCL_TEMP_UNEMPLOYMENT_HOURS = "monthlyDclTempUnemploymentHours";
  private Wech505MonthlyDclTempUnemploymentHours monthlyDclTempUnemploymentHours;

  public Wech505OccupationLink() {
  }

  public Wech505OccupationLink occupationStartingDate(LocalDate occupationStartingDate) {
    
    this.occupationStartingDate = occupationStartingDate;
    return this;
  }

  /**
   * Get occupationStartingDate
   * @return occupationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getOccupationStartingDate() {
    return occupationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationStartingDate(LocalDate occupationStartingDate) {
    this.occupationStartingDate = occupationStartingDate;
  }

  public Wech505OccupationLink occupationEndingDate(LocalDate occupationEndingDate) {
    
    this.occupationEndingDate = occupationEndingDate;
    return this;
  }

  /**
   * Get occupationEndingDate
   * @return occupationEndingDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getOccupationEndingDate() {
    return occupationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationEndingDate(LocalDate occupationEndingDate) {
    this.occupationEndingDate = occupationEndingDate;
  }

  public Wech505OccupationLink jointCommissionNbr(String jointCommissionNbr) {
    
    this.jointCommissionNbr = jointCommissionNbr;
    return this;
  }

  /**
   * Get jointCommissionNbr
   * @return jointCommissionNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getJointCommissionNbr() {
    return jointCommissionNbr;
  }


  @JsonProperty(JSON_PROPERTY_JOINT_COMMISSION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setJointCommissionNbr(String jointCommissionNbr) {
    this.jointCommissionNbr = jointCommissionNbr;
  }

  public Wech505OccupationLink workingDaysSystem(String workingDaysSystem) {
    
    this.workingDaysSystem = workingDaysSystem;
    return this;
  }

  /**
   * Get workingDaysSystem
   * @return workingDaysSystem
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkingDaysSystem() {
    return workingDaysSystem;
  }


  @JsonProperty(JSON_PROPERTY_WORKING_DAYS_SYSTEM)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkingDaysSystem(String workingDaysSystem) {
    this.workingDaysSystem = workingDaysSystem;
  }

  public Wech505OccupationLink meanWorkingHours(String meanWorkingHours) {
    
    this.meanWorkingHours = meanWorkingHours;
    return this;
  }

  /**
   * Get meanWorkingHours
   * @return meanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getMeanWorkingHours() {
    return meanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMeanWorkingHours(String meanWorkingHours) {
    this.meanWorkingHours = meanWorkingHours;
  }

  public Wech505OccupationLink refMeanWorkingHours(String refMeanWorkingHours) {
    
    this.refMeanWorkingHours = refMeanWorkingHours;
    return this;
  }

  /**
   * Get refMeanWorkingHours
   * @return refMeanWorkingHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getRefMeanWorkingHours() {
    return refMeanWorkingHours;
  }


  @JsonProperty(JSON_PROPERTY_REF_MEAN_WORKING_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRefMeanWorkingHours(String refMeanWorkingHours) {
    this.refMeanWorkingHours = refMeanWorkingHours;
  }

  public Wech505OccupationLink workerStatus(String workerStatus) {
    
    this.workerStatus = workerStatus;
    return this;
  }

  /**
   * Get workerStatus
   * @return workerStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStatus() {
    return workerStatus;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStatus(String workerStatus) {
    this.workerStatus = workerStatus;
  }

  public Wech505OccupationLink retired(String retired) {
    
    this.retired = retired;
    return this;
  }

  /**
   * Get retired
   * @return retired
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRetired() {
    return retired;
  }


  @JsonProperty(JSON_PROPERTY_RETIRED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRetired(String retired) {
    this.retired = retired;
  }

  public Wech505OccupationLink apprenticeship(String apprenticeship) {
    
    this.apprenticeship = apprenticeship;
    return this;
  }

  /**
   * Get apprenticeship
   * @return apprenticeship
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getApprenticeship() {
    return apprenticeship;
  }


  @JsonProperty(JSON_PROPERTY_APPRENTICESHIP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setApprenticeship(String apprenticeship) {
    this.apprenticeship = apprenticeship;
  }

  public Wech505OccupationLink contractType(String contractType) {
    
    this.contractType = contractType;
    return this;
  }

  /**
   * Get contractType
   * @return contractType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContractType() {
    return contractType;
  }


  @JsonProperty(JSON_PROPERTY_CONTRACT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContractType(String contractType) {
    this.contractType = contractType;
  }

  public Wech505OccupationLink remunMethod(String remunMethod) {
    
    this.remunMethod = remunMethod;
    return this;
  }

  /**
   * Get remunMethod
   * @return remunMethod
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemunMethod() {
    return remunMethod;
  }


  @JsonProperty(JSON_PROPERTY_REMUN_METHOD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemunMethod(String remunMethod) {
    this.remunMethod = remunMethod;
  }

  public Wech505OccupationLink handledOccupationLink(Wech505HandledOccupationLink handledOccupationLink) {
    
    this.handledOccupationLink = handledOccupationLink;
    return this;
  }

  /**
   * Get handledOccupationLink
   * @return handledOccupationLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505HandledOccupationLink getHandledOccupationLink() {
    return handledOccupationLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_OCCUPATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledOccupationLink(Wech505HandledOccupationLink handledOccupationLink) {
    this.handledOccupationLink = handledOccupationLink;
  }

  public Wech505OccupationLink occupationFeatures(OccupationFeaturesType occupationFeatures) {
    
    this.occupationFeatures = occupationFeatures;
    return this;
  }

  /**
   * Get occupationFeatures
   * @return occupationFeatures
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATION_FEATURES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OccupationFeaturesType getOccupationFeatures() {
    return occupationFeatures;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_FEATURES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationFeatures(OccupationFeaturesType occupationFeatures) {
    this.occupationFeatures = occupationFeatures;
  }

  public Wech505OccupationLink exceptSituationCalculBase(ExceptSituationCalculBaseType exceptSituationCalculBase) {
    
    this.exceptSituationCalculBase = exceptSituationCalculBase;
    return this;
  }

  /**
   * Get exceptSituationCalculBase
   * @return exceptSituationCalculBase
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXCEPT_SITUATION_CALCUL_BASE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ExceptSituationCalculBaseType getExceptSituationCalculBase() {
    return exceptSituationCalculBase;
  }


  @JsonProperty(JSON_PROPERTY_EXCEPT_SITUATION_CALCUL_BASE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExceptSituationCalculBase(ExceptSituationCalculBaseType exceptSituationCalculBase) {
    this.exceptSituationCalculBase = exceptSituationCalculBase;
  }

  public Wech505OccupationLink monthlyDclTempUnemploymentHours(Wech505MonthlyDclTempUnemploymentHours monthlyDclTempUnemploymentHours) {
    
    this.monthlyDclTempUnemploymentHours = monthlyDclTempUnemploymentHours;
    return this;
  }

  /**
   * Get monthlyDclTempUnemploymentHours
   * @return monthlyDclTempUnemploymentHours
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_MONTHLY_DCL_TEMP_UNEMPLOYMENT_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505MonthlyDclTempUnemploymentHours getMonthlyDclTempUnemploymentHours() {
    return monthlyDclTempUnemploymentHours;
  }


  @JsonProperty(JSON_PROPERTY_MONTHLY_DCL_TEMP_UNEMPLOYMENT_HOURS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setMonthlyDclTempUnemploymentHours(Wech505MonthlyDclTempUnemploymentHours monthlyDclTempUnemploymentHours) {
    this.monthlyDclTempUnemploymentHours = monthlyDclTempUnemploymentHours;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505OccupationLink wech505OccupationLink = (Wech505OccupationLink) o;
    return Objects.equals(this.occupationStartingDate, wech505OccupationLink.occupationStartingDate) &&
        Objects.equals(this.occupationEndingDate, wech505OccupationLink.occupationEndingDate) &&
        Objects.equals(this.jointCommissionNbr, wech505OccupationLink.jointCommissionNbr) &&
        Objects.equals(this.workingDaysSystem, wech505OccupationLink.workingDaysSystem) &&
        Objects.equals(this.meanWorkingHours, wech505OccupationLink.meanWorkingHours) &&
        Objects.equals(this.refMeanWorkingHours, wech505OccupationLink.refMeanWorkingHours) &&
        Objects.equals(this.workerStatus, wech505OccupationLink.workerStatus) &&
        Objects.equals(this.retired, wech505OccupationLink.retired) &&
        Objects.equals(this.apprenticeship, wech505OccupationLink.apprenticeship) &&
        Objects.equals(this.contractType, wech505OccupationLink.contractType) &&
        Objects.equals(this.remunMethod, wech505OccupationLink.remunMethod) &&
        Objects.equals(this.handledOccupationLink, wech505OccupationLink.handledOccupationLink) &&
        Objects.equals(this.occupationFeatures, wech505OccupationLink.occupationFeatures) &&
        Objects.equals(this.exceptSituationCalculBase, wech505OccupationLink.exceptSituationCalculBase) &&
        Objects.equals(this.monthlyDclTempUnemploymentHours, wech505OccupationLink.monthlyDclTempUnemploymentHours);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationStartingDate, occupationEndingDate, jointCommissionNbr, workingDaysSystem, meanWorkingHours, refMeanWorkingHours, workerStatus, retired, apprenticeship, contractType, remunMethod, handledOccupationLink, occupationFeatures, exceptSituationCalculBase, monthlyDclTempUnemploymentHours);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505OccupationLink {\n");
    sb.append("    occupationStartingDate: ").append(toIndentedString(occupationStartingDate)).append("\n");
    sb.append("    occupationEndingDate: ").append(toIndentedString(occupationEndingDate)).append("\n");
    sb.append("    jointCommissionNbr: ").append(toIndentedString(jointCommissionNbr)).append("\n");
    sb.append("    workingDaysSystem: ").append(toIndentedString(workingDaysSystem)).append("\n");
    sb.append("    meanWorkingHours: ").append(toIndentedString(meanWorkingHours)).append("\n");
    sb.append("    refMeanWorkingHours: ").append(toIndentedString(refMeanWorkingHours)).append("\n");
    sb.append("    workerStatus: ").append(toIndentedString(workerStatus)).append("\n");
    sb.append("    retired: ").append(toIndentedString(retired)).append("\n");
    sb.append("    apprenticeship: ").append(toIndentedString(apprenticeship)).append("\n");
    sb.append("    contractType: ").append(toIndentedString(contractType)).append("\n");
    sb.append("    remunMethod: ").append(toIndentedString(remunMethod)).append("\n");
    sb.append("    handledOccupationLink: ").append(toIndentedString(handledOccupationLink)).append("\n");
    sb.append("    occupationFeatures: ").append(toIndentedString(occupationFeatures)).append("\n");
    sb.append("    exceptSituationCalculBase: ").append(toIndentedString(exceptSituationCalculBase)).append("\n");
    sb.append("    monthlyDclTempUnemploymentHours: ").append(toIndentedString(monthlyDclTempUnemploymentHours)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

