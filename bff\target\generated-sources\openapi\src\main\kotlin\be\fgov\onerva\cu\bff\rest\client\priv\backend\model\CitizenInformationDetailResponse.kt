/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.Address
import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.FieldSource

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param nationality The nationality of the employee
 * @param address 
 * @param fieldSources Sources for individual fields
 */


data class CitizenInformationDetailResponse (

    /* The birth date of the employee (format YYYY-MM-DD) */
    @get:JsonProperty("birthDate")
    val birthDate: java.time.LocalDate? = null,

    /* The nationality of the employee */
    @get:JsonProperty("nationality")
    val nationality: kotlin.String? = null,

    @get:JsonProperty("address")
    val address: Address? = null,

    /* Sources for individual fields */
    @get:JsonProperty("fieldSources")
    val fieldSources: kotlin.collections.List<FieldSource>? = null

) {


}

