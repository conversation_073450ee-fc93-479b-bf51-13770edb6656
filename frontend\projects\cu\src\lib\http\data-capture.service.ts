import {Injectable} from '@angular/core';
import {HttpClient, HttpHeaders} from '@angular/common/http';
import {Observable} from 'rxjs';
import {
  AggregatedChangePersonalDataCaptureResponse,
  AggregateRequestInformationService,
  Configuration as ConfigurationBff,
  RequestInformationService,
  UpdateAggregatedChangePersonalDataRequest
} from '@rest-client/cu-bff';
import {ConfigService} from './../config/config.service';

@Injectable({
  providedIn: 'root'
})
export class DataCaptureService {
  private requestInformationService!: RequestInformationService;
  private aggregateRequestInformationService!: AggregateRequestInformationService;

  constructor(
    readonly http: HttpClient,
    readonly configService: ConfigService
  ) {
    this.initializeServices();
  }

  initializeServices(token?: string) {
    const configBff = new ConfigurationBff({
      basePath: this.configService.getEnvironmentVariable('bffBaseUrl', true),
      credentials: token ? { 'Bearer': token } : undefined
    });

    const defaultHeaders = token ?
      new HttpHeaders()
        .set('Authorization', `Bearer ${token}`)
        .set('Content-Type', 'application/json') :
      new HttpHeaders().set('Content-Type', 'application/json');

    this.aggregateRequestInformationService = new AggregateRequestInformationService(
      this.http,
      this.configService.getEnvironmentVariable('bffBaseUrl', true),
      configBff
    );
    this.requestInformationService = new RequestInformationService(
      this.http,
      this.configService.getEnvironmentVariable('bffBaseUrl', true),
        configBff
    );

    if (token) {
      [
        this.aggregateRequestInformationService,
        this.requestInformationService
      ].forEach(service => {
        service.defaultHeaders = defaultHeaders;
      });
    }
  }

  getAggregatedData(requestId: string): Observable<AggregatedChangePersonalDataCaptureResponse> {
    return this.aggregateRequestInformationService.getAggregatedChangePersonalDataCaptureRequest(requestId);
  }

  updateRequest(requestId: string, data: UpdateAggregatedChangePersonalDataRequest): Observable<any>{
    return this.aggregateRequestInformationService.updateAggregatedChangePersonalDataRequest(requestId, data);
  }

  reopenTask(requestId: string, taskType: string): Observable<any>{
    return this.requestInformationService.reopenTask(taskType, requestId)
  }

  closeTask(requestId: string, taskType: string = "CHANGE_PERSONAL_DATA_CAPTURE"): Observable<any>{
    return this.requestInformationService.closeRequestTask(requestId, taskType);
  }
}
