package be.fgov.onerva.cu.bff.adapter.out

import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpServerErrorException
import be.fgov.onerva.cu.bff.exceptions.C9NotFoundException
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.C9Api

@Service
class C9Service(private val c9Api: C9Api) {
    private val logger = LoggerFactory.getLogger(javaClass)

    fun getOPKey(c9Id: String): String {
        logger.info("Fetching OPKEY for request ID: {}", c9Id)
        try {
            val requestDetails = c9Api.getC9s(c9Id.toInt())
            return requestDetails.opKey ?: throw C9NotFoundException("OPKEY not found for C9 ID: $c9Id")
        } catch (e: HttpClientErrorException) {
            logger.error("Client error when fetching OPKEY for C9 ID {}: {}", c9Id, e.message)
            throw C9NotFoundException("Failed to retrieve OPKEY: ${e.message}", e)
        } catch (e: HttpServerErrorException) {
            logger.error("Server error when fetching OPKEY for C9 ID {}: {}", c9Id, e.message)
            throw C9NotFoundException("C9 service unavailable when retrieving OPKEY: ${e.message}", e)
        } catch (e: Exception) {
            logger.error("Unexpected error when fetching OPKEY for C9 ID {}: {}", c9Id, e.message)
            throw C9NotFoundException("Unexpected error retrieving OPKEY: ${e.message}", e)
        }
    }

    fun getUnemploymentOffice(requestId: String): String {
        logger.info("Fetching unemployment office for request ID: {}", requestId)
        try {
            val requestDetails = c9Api.getC9s(requestId.toInt())
            return requestDetails.unemploymentOffice?.toString() ?:
            throw C9NotFoundException("Unemployment office not found for request ID: $requestId")
        } catch (e: HttpClientErrorException) {
            logger.error("Client error when fetching unemployment office for request ID {}: {}", requestId, e.message)
            throw C9NotFoundException("Failed to retrieve unemployment office: ${e.message}", e)
        } catch (e: HttpServerErrorException) {
            logger.error("Server error when fetching unemployment office for request ID {}: {}", requestId, e.message)
            throw C9NotFoundException("C9 service unavailable when retrieving unemployment office: ${e.message}", e)
        } catch (e: Exception) {
            logger.error("Unexpected error when fetching unemployment office for request ID {}: {}", requestId, e.message)
            throw C9NotFoundException("Unexpected error retrieving unemployment office: ${e.message}", e)
        }
    }

    fun getC9Details(c9Id: String): C9Details {
        val c9Data = c9Api.getC9s(c9Id.toInt())

        val unemploymentOfficeCode = c9Data.unemploymentOffice?.toString()
            ?: throw C9NotFoundException("Unemployment office code not found for C9 ID: $c9Id")

        val opKey = c9Data.opKey
            ?: throw C9NotFoundException("OP key not found for C9 ID: $c9Id")

        return C9Details(
            unemploymentOfficeCode = unemploymentOfficeCode.padStart(3, '0'),
            opKey = opKey
        )
    }

    data class C9Details(
        val unemploymentOfficeCode: String,
        val opKey: String
    )

}