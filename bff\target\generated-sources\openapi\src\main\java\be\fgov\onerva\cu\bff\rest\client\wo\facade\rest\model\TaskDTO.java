/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.AssigneeInfoDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CommentDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.CorrelationDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.HistoryDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.MetaDataDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.StatusDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * TaskDTO
 */
@JsonPropertyOrder({
  TaskDTO.JSON_PROPERTY_METADATA,
  TaskDTO.JSON_PROPERTY_CONCERNED_ENTITIES,
  TaskDTO.JSON_PROPERTY_ASSIGNEE,
  TaskDTO.JSON_PROPERTY_DUE_DATE,
  TaskDTO.JSON_PROPERTY_CORRELATION,
  TaskDTO.JSON_PROPERTY_TASK_TYPE_CODE,
  TaskDTO.JSON_PROPERTY_PROCESS_METADATA,
  TaskDTO.JSON_PROPERTY_PROCESS_ID,
  TaskDTO.JSON_PROPERTY_PERMISSION_GROUP_ID,
  TaskDTO.JSON_PROPERTY_BUSINESS_ID,
  TaskDTO.JSON_PROPERTY_TASK_ID,
  TaskDTO.JSON_PROPERTY_CREATION_DATE,
  TaskDTO.JSON_PROPERTY_CLOSURE_DATE,
  TaskDTO.JSON_PROPERTY_COMMENTS,
  TaskDTO.JSON_PROPERTY_STATUS,
  TaskDTO.JSON_PROPERTY_ASSIGNEE_INFO,
  TaskDTO.JSON_PROPERTY_HISTORY
})
@JsonTypeName("Task")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class TaskDTO {
  public static final String JSON_PROPERTY_METADATA = "metadata";
  private List<MetaDataDTO> metadata;

  public static final String JSON_PROPERTY_CONCERNED_ENTITIES = "concernedEntities";
  private List<ThirdPartyDTO> concernedEntities = new ArrayList<>();

  public static final String JSON_PROPERTY_ASSIGNEE = "assignee";
  private String assignee;

  public static final String JSON_PROPERTY_DUE_DATE = "dueDate";
  private LocalDate dueDate;

  public static final String JSON_PROPERTY_CORRELATION = "correlation";
  private CorrelationDTO correlation;

  public static final String JSON_PROPERTY_TASK_TYPE_CODE = "taskTypeCode";
  private String taskTypeCode;

  public static final String JSON_PROPERTY_PROCESS_METADATA = "processMetadata";
  private List<MetaDataDTO> processMetadata;

  public static final String JSON_PROPERTY_PROCESS_ID = "processId";
  private Long processId;

  public static final String JSON_PROPERTY_PERMISSION_GROUP_ID = "permissionGroupId";
  private String permissionGroupId;

  public static final String JSON_PROPERTY_BUSINESS_ID = "businessId";
  private String businessId;

  public static final String JSON_PROPERTY_TASK_ID = "taskId";
  private Long taskId;

  public static final String JSON_PROPERTY_CREATION_DATE = "creationDate";
  private LocalDateTime creationDate;

  public static final String JSON_PROPERTY_CLOSURE_DATE = "closureDate";
  private LocalDateTime closureDate;

  public static final String JSON_PROPERTY_COMMENTS = "comments";
  private List<CommentDTO> comments = new ArrayList<>();

  public static final String JSON_PROPERTY_STATUS = "status";
  private StatusDTO status;

  public static final String JSON_PROPERTY_ASSIGNEE_INFO = "assigneeInfo";
  private AssigneeInfoDTO assigneeInfo;

  public static final String JSON_PROPERTY_HISTORY = "history";
  private List<HistoryDTO> history;

  public TaskDTO() {
  }

  public TaskDTO metadata(List<MetaDataDTO> metadata) {
    
    this.metadata = metadata;
    return this;
  }

  public TaskDTO addMetadataItem(MetaDataDTO metadataItem) {
    if (this.metadata == null) {
      this.metadata = new ArrayList<>();
    }
    this.metadata.add(metadataItem);
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MetaDataDTO> getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(List<MetaDataDTO> metadata) {
    this.metadata = metadata;
  }

  public TaskDTO concernedEntities(List<ThirdPartyDTO> concernedEntities) {
    
    this.concernedEntities = concernedEntities;
    return this;
  }

  public TaskDTO addConcernedEntitiesItem(ThirdPartyDTO concernedEntitiesItem) {
    if (this.concernedEntities == null) {
      this.concernedEntities = new ArrayList<>();
    }
    this.concernedEntities.add(concernedEntitiesItem);
    return this;
  }

  /**
   * Get concernedEntities
   * @return concernedEntities
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<ThirdPartyDTO> getConcernedEntities() {
    return concernedEntities;
  }


  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setConcernedEntities(List<ThirdPartyDTO> concernedEntities) {
    this.concernedEntities = concernedEntities;
  }

  public TaskDTO assignee(String assignee) {
    
    this.assignee = assignee;
    return this;
  }

  /**
   * It should be either: - the name of the employee (the alias you use to be connected on your computer) - the name of the group. To build it, write \&quot;E\&quot; + the id of the group in the lookup. If you want to target a specific process, add the process number from the lookup
   * @return assignee
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getAssignee() {
    return assignee;
  }


  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public TaskDTO dueDate(LocalDate dueDate) {
    
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Get dueDate
   * @return dueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDueDate() {
    return dueDate;
  }


  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
  }

  public TaskDTO correlation(CorrelationDTO correlation) {
    
    this.correlation = correlation;
    return this;
  }

  /**
   * Get correlation
   * @return correlation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CORRELATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CorrelationDTO getCorrelation() {
    return correlation;
  }


  @JsonProperty(JSON_PROPERTY_CORRELATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCorrelation(CorrelationDTO correlation) {
    this.correlation = correlation;
  }

  public TaskDTO taskTypeCode(String taskTypeCode) {
    
    this.taskTypeCode = taskTypeCode;
    return this;
  }

  /**
   * Get taskTypeCode
   * @return taskTypeCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TASK_TYPE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTaskTypeCode() {
    return taskTypeCode;
  }


  @JsonProperty(JSON_PROPERTY_TASK_TYPE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTaskTypeCode(String taskTypeCode) {
    this.taskTypeCode = taskTypeCode;
  }

  public TaskDTO processMetadata(List<MetaDataDTO> processMetadata) {
    
    this.processMetadata = processMetadata;
    return this;
  }

  public TaskDTO addProcessMetadataItem(MetaDataDTO processMetadataItem) {
    if (this.processMetadata == null) {
      this.processMetadata = new ArrayList<>();
    }
    this.processMetadata.add(processMetadataItem);
    return this;
  }

  /**
   * Get processMetadata
   * @return processMetadata
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROCESS_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MetaDataDTO> getProcessMetadata() {
    return processMetadata;
  }


  @JsonProperty(JSON_PROPERTY_PROCESS_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProcessMetadata(List<MetaDataDTO> processMetadata) {
    this.processMetadata = processMetadata;
  }

  public TaskDTO processId(Long processId) {
    
    this.processId = processId;
    return this;
  }

  /**
   * Get processId
   * @return processId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROCESS_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Long getProcessId() {
    return processId;
  }


  @JsonProperty(JSON_PROPERTY_PROCESS_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setProcessId(Long processId) {
    this.processId = processId;
  }

  public TaskDTO permissionGroupId(String permissionGroupId) {
    
    this.permissionGroupId = permissionGroupId;
    return this;
  }

  /**
   * Get permissionGroupId
   * @return permissionGroupId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PERMISSION_GROUP_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPermissionGroupId() {
    return permissionGroupId;
  }


  @JsonProperty(JSON_PROPERTY_PERMISSION_GROUP_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPermissionGroupId(String permissionGroupId) {
    this.permissionGroupId = permissionGroupId;
  }

  public TaskDTO businessId(String businessId) {
    
    this.businessId = businessId;
    return this;
  }

  /**
   * Get businessId
   * @return businessId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getBusinessId() {
    return businessId;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessId(String businessId) {
    this.businessId = businessId;
  }

  public TaskDTO taskId(Long taskId) {
    
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TASK_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Long getTaskId() {
    return taskId;
  }


  @JsonProperty(JSON_PROPERTY_TASK_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTaskId(Long taskId) {
    this.taskId = taskId;
  }

  public TaskDTO creationDate(LocalDateTime creationDate) {
    
    this.creationDate = creationDate;
    return this;
  }

  /**
   * Get creationDate
   * @return creationDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreationDate() {
    return creationDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreationDate(LocalDateTime creationDate) {
    this.creationDate = creationDate;
  }

  public TaskDTO closureDate(LocalDateTime closureDate) {
    
    this.closureDate = closureDate;
    return this;
  }

  /**
   * Get closureDate
   * @return closureDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getClosureDate() {
    return closureDate;
  }


  @JsonProperty(JSON_PROPERTY_CLOSURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClosureDate(LocalDateTime closureDate) {
    this.closureDate = closureDate;
  }

  public TaskDTO comments(List<CommentDTO> comments) {
    
    this.comments = comments;
    return this;
  }

  public TaskDTO addCommentsItem(CommentDTO commentsItem) {
    if (this.comments == null) {
      this.comments = new ArrayList<>();
    }
    this.comments.add(commentsItem);
    return this;
  }

  /**
   * Get comments
   * @return comments
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<CommentDTO> getComments() {
    return comments;
  }


  @JsonProperty(JSON_PROPERTY_COMMENTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComments(List<CommentDTO> comments) {
    this.comments = comments;
  }

  public TaskDTO status(StatusDTO status) {
    
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public StatusDTO getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(StatusDTO status) {
    this.status = status;
  }

  public TaskDTO assigneeInfo(AssigneeInfoDTO assigneeInfo) {
    
    this.assigneeInfo = assigneeInfo;
    return this;
  }

  /**
   * Get assigneeInfo
   * @return assigneeInfo
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ASSIGNEE_INFO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public AssigneeInfoDTO getAssigneeInfo() {
    return assigneeInfo;
  }


  @JsonProperty(JSON_PROPERTY_ASSIGNEE_INFO)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setAssigneeInfo(AssigneeInfoDTO assigneeInfo) {
    this.assigneeInfo = assigneeInfo;
  }

  public TaskDTO history(List<HistoryDTO> history) {
    
    this.history = history;
    return this;
  }

  public TaskDTO addHistoryItem(HistoryDTO historyItem) {
    if (this.history == null) {
      this.history = new ArrayList<>();
    }
    this.history.add(historyItem);
    return this;
  }

  /**
   * Get history
   * @return history
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HISTORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<HistoryDTO> getHistory() {
    return history;
  }


  @JsonProperty(JSON_PROPERTY_HISTORY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHistory(List<HistoryDTO> history) {
    this.history = history;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TaskDTO task = (TaskDTO) o;
    return Objects.equals(this.metadata, task.metadata) &&
        Objects.equals(this.concernedEntities, task.concernedEntities) &&
        Objects.equals(this.assignee, task.assignee) &&
        Objects.equals(this.dueDate, task.dueDate) &&
        Objects.equals(this.correlation, task.correlation) &&
        Objects.equals(this.taskTypeCode, task.taskTypeCode) &&
        Objects.equals(this.processMetadata, task.processMetadata) &&
        Objects.equals(this.processId, task.processId) &&
        Objects.equals(this.permissionGroupId, task.permissionGroupId) &&
        Objects.equals(this.businessId, task.businessId) &&
        Objects.equals(this.taskId, task.taskId) &&
        Objects.equals(this.creationDate, task.creationDate) &&
        Objects.equals(this.closureDate, task.closureDate) &&
        Objects.equals(this.comments, task.comments) &&
        Objects.equals(this.status, task.status) &&
        Objects.equals(this.assigneeInfo, task.assigneeInfo) &&
        Objects.equals(this.history, task.history);
  }

  @Override
  public int hashCode() {
    return Objects.hash(metadata, concernedEntities, assignee, dueDate, correlation, taskTypeCode, processMetadata, processId, permissionGroupId, businessId, taskId, creationDate, closureDate, comments, status, assigneeInfo, history);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TaskDTO {\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    concernedEntities: ").append(toIndentedString(concernedEntities)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("    correlation: ").append(toIndentedString(correlation)).append("\n");
    sb.append("    taskTypeCode: ").append(toIndentedString(taskTypeCode)).append("\n");
    sb.append("    processMetadata: ").append(toIndentedString(processMetadata)).append("\n");
    sb.append("    processId: ").append(toIndentedString(processId)).append("\n");
    sb.append("    permissionGroupId: ").append(toIndentedString(permissionGroupId)).append("\n");
    sb.append("    businessId: ").append(toIndentedString(businessId)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    creationDate: ").append(toIndentedString(creationDate)).append("\n");
    sb.append("    closureDate: ").append(toIndentedString(closureDate)).append("\n");
    sb.append("    comments: ").append(toIndentedString(comments)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    assigneeInfo: ").append(toIndentedString(assigneeInfo)).append("\n");
    sb.append("    history: ").append(toIndentedString(history)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

