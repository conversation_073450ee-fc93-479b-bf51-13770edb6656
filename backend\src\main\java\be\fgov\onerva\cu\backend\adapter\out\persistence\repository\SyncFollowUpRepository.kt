package be.fgov.onerva.cu.backend.adapter.out.persistence.repository

import java.util.UUID
import org.springframework.data.jpa.repository.JpaRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.BaremaSnapshotEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.SyncFollowUpEntity

/**
 * Repository interface for managing [BaremaSnapshotEntity] persistence.
 * Provides methods to store and retrieve Barema snapshot data associated with requests.
 */
interface SyncFollowUpRepository : JpaRepository<SyncFollowUpEntity, UUID> {
    /**
     * Finds a Barema snapshot entity by its associated request ID.
     *
     * @param requestId The unique identifier of the request
     * @return The Barema snapshot entity if found, or null if no snapshot exists for the given request
     */
    fun findByRequestId(requestId: UUID): SyncFollowUpEntity?

    fun findByCorrelationId(correlationId: String): SyncFollowUpEntity?
}