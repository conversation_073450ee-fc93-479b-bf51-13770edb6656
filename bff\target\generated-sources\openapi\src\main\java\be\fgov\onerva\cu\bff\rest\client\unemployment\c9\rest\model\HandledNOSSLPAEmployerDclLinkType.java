/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * HandledNOSSLPAEmployerDclLinkType
 */
@JsonPropertyOrder({
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_COMPANY_I_D,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_DENOMINATION,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_STREET,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_HOUSE_NBR,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_POST_BOX,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_Z_I_P_CODE,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_CITY,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_EMPLOYER_COUNTRY,
  HandledNOSSLPAEmployerDclLinkType.JSON_PROPERTY_IMPORTANCE_CODE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class HandledNOSSLPAEmployerDclLinkType {
  public static final String JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR = "nosslpaRegistrationNbr";
  private Integer nosslpaRegistrationNbr;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_EMPLOYER_DENOMINATION = "employerDenomination";
  private String employerDenomination;

  public static final String JSON_PROPERTY_EMPLOYER_STREET = "employerStreet";
  private String employerStreet;

  public static final String JSON_PROPERTY_EMPLOYER_HOUSE_NBR = "employerHouseNbr";
  private String employerHouseNbr;

  public static final String JSON_PROPERTY_EMPLOYER_POST_BOX = "employerPostBox";
  private String employerPostBox;

  public static final String JSON_PROPERTY_EMPLOYER_Z_I_P_CODE = "employerZIPCode";
  private String employerZIPCode;

  public static final String JSON_PROPERTY_EMPLOYER_CITY = "employerCity";
  private String employerCity;

  public static final String JSON_PROPERTY_EMPLOYER_COUNTRY = "employerCountry";
  private String employerCountry;

  public static final String JSON_PROPERTY_IMPORTANCE_CODE = "importanceCode";
  private String importanceCode;

  public HandledNOSSLPAEmployerDclLinkType() {
  }

  public HandledNOSSLPAEmployerDclLinkType nosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
    return this;
  }

  /**
   * Get nosslpaRegistrationNbr
   * @return nosslpaRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNosslpaRegistrationNbr() {
    return nosslpaRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
  }

  public HandledNOSSLPAEmployerDclLinkType companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public HandledNOSSLPAEmployerDclLinkType employerDenomination(String employerDenomination) {
    
    this.employerDenomination = employerDenomination;
    return this;
  }

  /**
   * Get employerDenomination
   * @return employerDenomination
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerDenomination() {
    return employerDenomination;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_DENOMINATION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerDenomination(String employerDenomination) {
    this.employerDenomination = employerDenomination;
  }

  public HandledNOSSLPAEmployerDclLinkType employerStreet(String employerStreet) {
    
    this.employerStreet = employerStreet;
    return this;
  }

  /**
   * Get employerStreet
   * @return employerStreet
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerStreet() {
    return employerStreet;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_STREET)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerStreet(String employerStreet) {
    this.employerStreet = employerStreet;
  }

  public HandledNOSSLPAEmployerDclLinkType employerHouseNbr(String employerHouseNbr) {
    
    this.employerHouseNbr = employerHouseNbr;
    return this;
  }

  /**
   * Get employerHouseNbr
   * @return employerHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerHouseNbr() {
    return employerHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerHouseNbr(String employerHouseNbr) {
    this.employerHouseNbr = employerHouseNbr;
  }

  public HandledNOSSLPAEmployerDclLinkType employerPostBox(String employerPostBox) {
    
    this.employerPostBox = employerPostBox;
    return this;
  }

  /**
   * Get employerPostBox
   * @return employerPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerPostBox() {
    return employerPostBox;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerPostBox(String employerPostBox) {
    this.employerPostBox = employerPostBox;
  }

  public HandledNOSSLPAEmployerDclLinkType employerZIPCode(String employerZIPCode) {
    
    this.employerZIPCode = employerZIPCode;
    return this;
  }

  /**
   * Get employerZIPCode
   * @return employerZIPCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerZIPCode() {
    return employerZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerZIPCode(String employerZIPCode) {
    this.employerZIPCode = employerZIPCode;
  }

  public HandledNOSSLPAEmployerDclLinkType employerCity(String employerCity) {
    
    this.employerCity = employerCity;
    return this;
  }

  /**
   * Get employerCity
   * @return employerCity
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_EMPLOYER_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getEmployerCity() {
    return employerCity;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_CITY)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setEmployerCity(String employerCity) {
    this.employerCity = employerCity;
  }

  public HandledNOSSLPAEmployerDclLinkType employerCountry(String employerCountry) {
    
    this.employerCountry = employerCountry;
    return this;
  }

  /**
   * Get employerCountry
   * @return employerCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMPLOYER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getEmployerCountry() {
    return employerCountry;
  }


  @JsonProperty(JSON_PROPERTY_EMPLOYER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmployerCountry(String employerCountry) {
    this.employerCountry = employerCountry;
  }

  public HandledNOSSLPAEmployerDclLinkType importanceCode(String importanceCode) {
    
    this.importanceCode = importanceCode;
    return this;
  }

  /**
   * Get importanceCode
   * @return importanceCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getImportanceCode() {
    return importanceCode;
  }


  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setImportanceCode(String importanceCode) {
    this.importanceCode = importanceCode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLinkType = (HandledNOSSLPAEmployerDclLinkType) o;
    return Objects.equals(this.nosslpaRegistrationNbr, handledNOSSLPAEmployerDclLinkType.nosslpaRegistrationNbr) &&
        Objects.equals(this.companyID, handledNOSSLPAEmployerDclLinkType.companyID) &&
        Objects.equals(this.employerDenomination, handledNOSSLPAEmployerDclLinkType.employerDenomination) &&
        Objects.equals(this.employerStreet, handledNOSSLPAEmployerDclLinkType.employerStreet) &&
        Objects.equals(this.employerHouseNbr, handledNOSSLPAEmployerDclLinkType.employerHouseNbr) &&
        Objects.equals(this.employerPostBox, handledNOSSLPAEmployerDclLinkType.employerPostBox) &&
        Objects.equals(this.employerZIPCode, handledNOSSLPAEmployerDclLinkType.employerZIPCode) &&
        Objects.equals(this.employerCity, handledNOSSLPAEmployerDclLinkType.employerCity) &&
        Objects.equals(this.employerCountry, handledNOSSLPAEmployerDclLinkType.employerCountry) &&
        Objects.equals(this.importanceCode, handledNOSSLPAEmployerDclLinkType.importanceCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nosslpaRegistrationNbr, companyID, employerDenomination, employerStreet, employerHouseNbr, employerPostBox, employerZIPCode, employerCity, employerCountry, importanceCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class HandledNOSSLPAEmployerDclLinkType {\n");
    sb.append("    nosslpaRegistrationNbr: ").append(toIndentedString(nosslpaRegistrationNbr)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    employerDenomination: ").append(toIndentedString(employerDenomination)).append("\n");
    sb.append("    employerStreet: ").append(toIndentedString(employerStreet)).append("\n");
    sb.append("    employerHouseNbr: ").append(toIndentedString(employerHouseNbr)).append("\n");
    sb.append("    employerPostBox: ").append(toIndentedString(employerPostBox)).append("\n");
    sb.append("    employerZIPCode: ").append(toIndentedString(employerZIPCode)).append("\n");
    sb.append("    employerCity: ").append(toIndentedString(employerCity)).append("\n");
    sb.append("    employerCountry: ").append(toIndentedString(employerCountry)).append("\n");
    sb.append("    importanceCode: ").append(toIndentedString(importanceCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

