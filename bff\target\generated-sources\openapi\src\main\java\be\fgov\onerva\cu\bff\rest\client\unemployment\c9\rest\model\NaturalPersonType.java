/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * NaturalPersonType
 */
@JsonPropertyOrder({
  NaturalPersonType.JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR,
  NaturalPersonType.JSON_PROPERTY_INSS,
  NaturalPersonType.JSON_PROPERTY_WORKER_STREET,
  NaturalPersonType.JSON_PROPERTY_WORKER_HOUSE_NBR,
  NaturalPersonType.JSON_PROPERTY_WORKER_POST_BOX,
  NaturalPersonType.JSON_PROPERTY_WORKER_Z_I_P_CODE,
  NaturalPersonType.JSON_PROPERTY_WORKER_CITY,
  NaturalPersonType.JSON_PROPERTY_WORKER_COUNTRY,
  NaturalPersonType.JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NaturalPersonType {
  public static final String JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR = "naturalPersonSequenceNbr";
  private String naturalPersonSequenceNbr;

  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public static final String JSON_PROPERTY_WORKER_STREET = "workerStreet";
  private String workerStreet;

  public static final String JSON_PROPERTY_WORKER_HOUSE_NBR = "workerHouseNbr";
  private String workerHouseNbr;

  public static final String JSON_PROPERTY_WORKER_POST_BOX = "workerPostBox";
  private String workerPostBox;

  public static final String JSON_PROPERTY_WORKER_Z_I_P_CODE = "workerZIPCode";
  private String workerZIPCode;

  public static final String JSON_PROPERTY_WORKER_CITY = "workerCity";
  private String workerCity;

  public static final String JSON_PROPERTY_WORKER_COUNTRY = "workerCountry";
  private String workerCountry;

  public static final String JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE = "naturalPersonUserReference";
  private String naturalPersonUserReference;

  public NaturalPersonType() {
  }

  public NaturalPersonType naturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
    return this;
  }

  /**
   * Get naturalPersonSequenceNbr
   * @return naturalPersonSequenceNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNaturalPersonSequenceNbr() {
    return naturalPersonSequenceNbr;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_SEQUENCE_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPersonSequenceNbr(String naturalPersonSequenceNbr) {
    this.naturalPersonSequenceNbr = naturalPersonSequenceNbr;
  }

  public NaturalPersonType inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  public NaturalPersonType workerStreet(String workerStreet) {
    
    this.workerStreet = workerStreet;
    return this;
  }

  /**
   * Get workerStreet
   * @return workerStreet
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerStreet() {
    return workerStreet;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerStreet(String workerStreet) {
    this.workerStreet = workerStreet;
  }

  public NaturalPersonType workerHouseNbr(String workerHouseNbr) {
    
    this.workerHouseNbr = workerHouseNbr;
    return this;
  }

  /**
   * Get workerHouseNbr
   * @return workerHouseNbr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerHouseNbr() {
    return workerHouseNbr;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_HOUSE_NBR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerHouseNbr(String workerHouseNbr) {
    this.workerHouseNbr = workerHouseNbr;
  }

  public NaturalPersonType workerPostBox(String workerPostBox) {
    
    this.workerPostBox = workerPostBox;
    return this;
  }

  /**
   * Get workerPostBox
   * @return workerPostBox
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerPostBox() {
    return workerPostBox;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_POST_BOX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerPostBox(String workerPostBox) {
    this.workerPostBox = workerPostBox;
  }

  public NaturalPersonType workerZIPCode(String workerZIPCode) {
    
    this.workerZIPCode = workerZIPCode;
    return this;
  }

  /**
   * Get workerZIPCode
   * @return workerZIPCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerZIPCode() {
    return workerZIPCode;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_Z_I_P_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerZIPCode(String workerZIPCode) {
    this.workerZIPCode = workerZIPCode;
  }

  public NaturalPersonType workerCity(String workerCity) {
    
    this.workerCity = workerCity;
    return this;
  }

  /**
   * Get workerCity
   * @return workerCity
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCity() {
    return workerCity;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCity(String workerCity) {
    this.workerCity = workerCity;
  }

  public NaturalPersonType workerCountry(String workerCountry) {
    
    this.workerCountry = workerCountry;
    return this;
  }

  /**
   * Get workerCountry
   * @return workerCountry
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkerCountry() {
    return workerCountry;
  }


  @JsonProperty(JSON_PROPERTY_WORKER_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkerCountry(String workerCountry) {
    this.workerCountry = workerCountry;
  }

  public NaturalPersonType naturalPersonUserReference(String naturalPersonUserReference) {
    
    this.naturalPersonUserReference = naturalPersonUserReference;
    return this;
  }

  /**
   * Get naturalPersonUserReference
   * @return naturalPersonUserReference
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getNaturalPersonUserReference() {
    return naturalPersonUserReference;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON_USER_REFERENCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setNaturalPersonUserReference(String naturalPersonUserReference) {
    this.naturalPersonUserReference = naturalPersonUserReference;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NaturalPersonType naturalPersonType = (NaturalPersonType) o;
    return Objects.equals(this.naturalPersonSequenceNbr, naturalPersonType.naturalPersonSequenceNbr) &&
        Objects.equals(this.inss, naturalPersonType.inss) &&
        Objects.equals(this.workerStreet, naturalPersonType.workerStreet) &&
        Objects.equals(this.workerHouseNbr, naturalPersonType.workerHouseNbr) &&
        Objects.equals(this.workerPostBox, naturalPersonType.workerPostBox) &&
        Objects.equals(this.workerZIPCode, naturalPersonType.workerZIPCode) &&
        Objects.equals(this.workerCity, naturalPersonType.workerCity) &&
        Objects.equals(this.workerCountry, naturalPersonType.workerCountry) &&
        Objects.equals(this.naturalPersonUserReference, naturalPersonType.naturalPersonUserReference);
  }

  @Override
  public int hashCode() {
    return Objects.hash(naturalPersonSequenceNbr, inss, workerStreet, workerHouseNbr, workerPostBox, workerZIPCode, workerCity, workerCountry, naturalPersonUserReference);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NaturalPersonType {\n");
    sb.append("    naturalPersonSequenceNbr: ").append(toIndentedString(naturalPersonSequenceNbr)).append("\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("    workerStreet: ").append(toIndentedString(workerStreet)).append("\n");
    sb.append("    workerHouseNbr: ").append(toIndentedString(workerHouseNbr)).append("\n");
    sb.append("    workerPostBox: ").append(toIndentedString(workerPostBox)).append("\n");
    sb.append("    workerZIPCode: ").append(toIndentedString(workerZIPCode)).append("\n");
    sb.append("    workerCity: ").append(toIndentedString(workerCity)).append("\n");
    sb.append("    workerCountry: ").append(toIndentedString(workerCountry)).append("\n");
    sb.append("    naturalPersonUserReference: ").append(toIndentedString(naturalPersonUserReference)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

