/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * NationalityBCSS
 */
@JsonPropertyOrder({
  NationalityBCSS.JSON_PROPERTY_CODE,
  NationalityBCSS.JSON_PROPERTY_DESC_FR,
  NationalityBCSS.JSON_PROPERTY_DESC_NL
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NationalityBCSS {
  public static final String JSON_PROPERTY_CODE = "code";
  private String code;

  public static final String JSON_PROPERTY_DESC_FR = "descFr";
  private String descFr;

  public static final String JSON_PROPERTY_DESC_NL = "descNl";
  private String descNl;

  public NationalityBCSS() {
  }

  public NationalityBCSS code(String code) {
    
    this.code = code;
    return this;
  }

  /**
   * Get code
   * @return code
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCode() {
    return code;
  }


  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCode(String code) {
    this.code = code;
  }

  public NationalityBCSS descFr(String descFr) {
    
    this.descFr = descFr;
    return this;
  }

  /**
   * Get descFr
   * @return descFr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescFr() {
    return descFr;
  }


  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescFr(String descFr) {
    this.descFr = descFr;
  }

  public NationalityBCSS descNl(String descNl) {
    
    this.descNl = descNl;
    return this;
  }

  /**
   * Get descNl
   * @return descNl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescNl() {
    return descNl;
  }


  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescNl(String descNl) {
    this.descNl = descNl;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NationalityBCSS nationalityBCSS = (NationalityBCSS) o;
    return Objects.equals(this.code, nationalityBCSS.code) &&
        Objects.equals(this.descFr, nationalityBCSS.descFr) &&
        Objects.equals(this.descNl, nationalityBCSS.descNl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(code, descFr, descNl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NationalityBCSS {\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    descFr: ").append(toIndentedString(descFr)).append("\n");
    sb.append("    descNl: ").append(toIndentedString(descNl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

