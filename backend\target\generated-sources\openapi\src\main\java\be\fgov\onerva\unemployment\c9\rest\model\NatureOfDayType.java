/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * NatureOfDayType
 */
@JsonPropertyOrder({
  NatureOfDayType.JSON_PROPERTY_DAY_INDICATOR,
  NatureOfDayType.JSON_PROPERTY_DAY_NATURE_CODE,
  NatureOfDayType.JSON_PROPERTY_HOURS_NUMBER
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NatureOfDayType {
  public static final String JSON_PROPERTY_DAY_INDICATOR = "dayIndicator";
  private String dayIndicator;

  public static final String JSON_PROPERTY_DAY_NATURE_CODE = "dayNatureCode";
  private String dayNatureCode;

  public static final String JSON_PROPERTY_HOURS_NUMBER = "hoursNumber";
  private Integer hoursNumber;

  public NatureOfDayType() {
  }

  public NatureOfDayType dayIndicator(String dayIndicator) {
    
    this.dayIndicator = dayIndicator;
    return this;
  }

  /**
   * Get dayIndicator
   * @return dayIndicator
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DAY_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDayIndicator() {
    return dayIndicator;
  }


  @JsonProperty(JSON_PROPERTY_DAY_INDICATOR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDayIndicator(String dayIndicator) {
    this.dayIndicator = dayIndicator;
  }

  public NatureOfDayType dayNatureCode(String dayNatureCode) {
    
    this.dayNatureCode = dayNatureCode;
    return this;
  }

  /**
   * Get dayNatureCode
   * @return dayNatureCode
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_DAY_NATURE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getDayNatureCode() {
    return dayNatureCode;
  }


  @JsonProperty(JSON_PROPERTY_DAY_NATURE_CODE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setDayNatureCode(String dayNatureCode) {
    this.dayNatureCode = dayNatureCode;
  }

  public NatureOfDayType hoursNumber(Integer hoursNumber) {
    
    this.hoursNumber = hoursNumber;
    return this;
  }

  /**
   * Get hoursNumber
   * @return hoursNumber
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HOURS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getHoursNumber() {
    return hoursNumber;
  }


  @JsonProperty(JSON_PROPERTY_HOURS_NUMBER)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHoursNumber(Integer hoursNumber) {
    this.hoursNumber = hoursNumber;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NatureOfDayType natureOfDayType = (NatureOfDayType) o;
    return Objects.equals(this.dayIndicator, natureOfDayType.dayIndicator) &&
        Objects.equals(this.dayNatureCode, natureOfDayType.dayNatureCode) &&
        Objects.equals(this.hoursNumber, natureOfDayType.hoursNumber);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dayIndicator, dayNatureCode, hoursNumber);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NatureOfDayType {\n");
    sb.append("    dayIndicator: ").append(toIndentedString(dayIndicator)).append("\n");
    sb.append("    dayNatureCode: ").append(toIndentedString(dayNatureCode)).append("\n");
    sb.append("    hoursNumber: ").append(toIndentedString(hoursNumber)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

