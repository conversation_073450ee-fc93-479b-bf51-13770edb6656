/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * NavigateS24Body
 */
@JsonPropertyOrder({
  NavigateS24Body.JSON_PROPERTY_SSIN,
  NavigateS24Body.JSON_PROPERTY_DATE_VALID
})
@JsonTypeName("navigate_S24_body")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class NavigateS24Body {
  public static final String JSON_PROPERTY_SSIN = "ssin";
  private String ssin;

  public static final String JSON_PROPERTY_DATE_VALID = "dateValid";
  private LocalDate dateValid;

  public NavigateS24Body() {
  }

  public NavigateS24Body ssin(String ssin) {
    
    this.ssin = ssin;
    return this;
  }

  /**
   * Get ssin
   * @return ssin
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getSsin() {
    return ssin;
  }


  @JsonProperty(JSON_PROPERTY_SSIN)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setSsin(String ssin) {
    this.ssin = ssin;
  }

  public NavigateS24Body dateValid(LocalDate dateValid) {
    
    this.dateValid = dateValid;
    return this;
  }

  /**
   * Get dateValid
   * @return dateValid
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDateValid() {
    return dateValid;
  }


  @JsonProperty(JSON_PROPERTY_DATE_VALID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDateValid(LocalDate dateValid) {
    this.dateValid = dateValid;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    NavigateS24Body navigateS24Body = (NavigateS24Body) o;
    return Objects.equals(this.ssin, navigateS24Body.ssin) &&
        Objects.equals(this.dateValid, navigateS24Body.dateValid);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ssin, dateValid);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class NavigateS24Body {\n");
    sb.append("    ssin: ").append(toIndentedString(ssin)).append("\n");
    sb.append("    dateValid: ").append(toIndentedString(dateValid)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

