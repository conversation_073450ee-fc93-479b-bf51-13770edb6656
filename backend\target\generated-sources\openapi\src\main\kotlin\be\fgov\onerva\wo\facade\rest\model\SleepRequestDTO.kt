/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param reason Reason why you want to change the status of the task.
 * @param wakingUpDate This is the date when you want to wake the task up. At that date the task will automatically put back into treatment state.
 */


data class SleepRequestDTO (

    /* Reason why you want to change the status of the task. */
    @get:JsonProperty("reason")
    val reason: kotlin.String,

    /* This is the date when you want to wake the task up. At that date the task will automatically put back into treatment state. */
    @get:JsonProperty("wakingUpDate")
    val wakingUpDate: java.time.LocalDate? = null

) {


}

