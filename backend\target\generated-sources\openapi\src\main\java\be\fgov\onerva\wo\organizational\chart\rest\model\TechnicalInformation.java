/*
 * Organizational Chart/nsso REST service
 * RESTful API for the Organizational Chart
 *
 * The version of the OpenAPI document: 2
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo.organizational.chart.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.OffsetDateTime;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * TechnicalInformation
 */
@JsonPropertyOrder({
  TechnicalInformation.JSON_PROPERTY_CREATION_DATE,
  TechnicalInformation.JSON_PROPERTY_CREATION_SOURCE,
  TechnicalInformation.JSON_PROPERTY_CREATION_USER,
  TechnicalInformation.JSON_PROPERTY_LAST_UPDATE_DATE,
  TechnicalInformation.JSON_PROPERTY_LAST_UPDATE_SOURCE,
  TechnicalInformation.JSON_PROPERTY_LAST_UPDATE_USER,
  TechnicalInformation.JSON_PROPERTY_CLOSURE_DATE,
  TechnicalInformation.JSON_PROPERTY_CLOSURE_SOURCE,
  TechnicalInformation.JSON_PROPERTY_CLOSURE_USER,
  TechnicalInformation.JSON_PROPERTY_TECHNICAL_STATUS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:09.126799200+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class TechnicalInformation {
  public static final String JSON_PROPERTY_CREATION_DATE = "creationDate";
  private OffsetDateTime creationDate;

  public static final String JSON_PROPERTY_CREATION_SOURCE = "creationSource";
  private String creationSource;

  public static final String JSON_PROPERTY_CREATION_USER = "creationUser";
  private String creationUser;

  public static final String JSON_PROPERTY_LAST_UPDATE_DATE = "lastUpdateDate";
  private OffsetDateTime lastUpdateDate;

  public static final String JSON_PROPERTY_LAST_UPDATE_SOURCE = "lastUpdateSource";
  private String lastUpdateSource;

  public static final String JSON_PROPERTY_LAST_UPDATE_USER = "lastUpdateUser";
  private String lastUpdateUser;

  public static final String JSON_PROPERTY_CLOSURE_DATE = "closureDate";
  private OffsetDateTime closureDate;

  public static final String JSON_PROPERTY_CLOSURE_SOURCE = "closureSource";
  private String closureSource;

  public static final String JSON_PROPERTY_CLOSURE_USER = "closureUser";
  private String closureUser;

  /**
   * The technical status of the node (open, closed, deleted or obsolete).
   */
  public enum TechnicalStatusEnum {
    OPEN("open"),
    
    CLOSED("closed"),
    
    DELETED("deleted"),
    
    OBSOLETE("obsolete");

    private String value;

    TechnicalStatusEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static TechnicalStatusEnum fromValue(String value) {
      for (TechnicalStatusEnum b : TechnicalStatusEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_TECHNICAL_STATUS = "technicalStatus";
  private TechnicalStatusEnum technicalStatus;

  public TechnicalInformation() {
  }
  /**
   * Constructor with only readonly parameters
   */
  @JsonCreator
  public TechnicalInformation(
    @JsonProperty(JSON_PROPERTY_CREATION_DATE) OffsetDateTime creationDate, 
    @JsonProperty(JSON_PROPERTY_CREATION_SOURCE) String creationSource, 
    @JsonProperty(JSON_PROPERTY_CREATION_USER) String creationUser, 
    @JsonProperty(JSON_PROPERTY_LAST_UPDATE_DATE) OffsetDateTime lastUpdateDate, 
    @JsonProperty(JSON_PROPERTY_LAST_UPDATE_SOURCE) String lastUpdateSource, 
    @JsonProperty(JSON_PROPERTY_LAST_UPDATE_USER) String lastUpdateUser, 
    @JsonProperty(JSON_PROPERTY_CLOSURE_DATE) OffsetDateTime closureDate, 
    @JsonProperty(JSON_PROPERTY_CLOSURE_SOURCE) String closureSource, 
    @JsonProperty(JSON_PROPERTY_CLOSURE_USER) String closureUser
  ) {
    this();
    this.creationDate = creationDate;
    this.creationSource = creationSource;
    this.creationUser = creationUser;
    this.lastUpdateDate = lastUpdateDate;
    this.lastUpdateSource = lastUpdateSource;
    this.lastUpdateUser = lastUpdateUser;
    this.closureDate = closureDate;
    this.closureSource = closureSource;
    this.closureUser = closureUser;
  }

  /**
   * The date when the node was created.
   * @return creationDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getCreationDate() {
    return creationDate;
  }



  /**
   * Get creationSource
   * @return creationSource
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreationSource() {
    return creationSource;
  }



  /**
   * The nodeId of the user who created the node.
   * @return creationUser
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCreationUser() {
    return creationUser;
  }



  /**
   * The date when the node was last updated.
   * @return lastUpdateDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_UPDATE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getLastUpdateDate() {
    return lastUpdateDate;
  }



  /**
   * Get lastUpdateSource
   * @return lastUpdateSource
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_UPDATE_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastUpdateSource() {
    return lastUpdateSource;
  }



  /**
   * The nodeId of the user who last updated the node.
   * @return lastUpdateUser
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_UPDATE_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastUpdateUser() {
    return lastUpdateUser;
  }



  /**
   * The date when the node was closed updated.
   * @return closureDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSURE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public OffsetDateTime getClosureDate() {
    return closureDate;
  }



  /**
   * Get closureSource
   * @return closureSource
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSURE_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClosureSource() {
    return closureSource;
  }



  /**
   * The nodeId of the user who closed the node.
   * @return closureUser
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLOSURE_USER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClosureUser() {
    return closureUser;
  }



  public TechnicalInformation technicalStatus(TechnicalStatusEnum technicalStatus) {
    
    this.technicalStatus = technicalStatus;
    return this;
  }

  /**
   * The technical status of the node (open, closed, deleted or obsolete).
   * @return technicalStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TECHNICAL_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public TechnicalStatusEnum getTechnicalStatus() {
    return technicalStatus;
  }


  @JsonProperty(JSON_PROPERTY_TECHNICAL_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTechnicalStatus(TechnicalStatusEnum technicalStatus) {
    this.technicalStatus = technicalStatus;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TechnicalInformation technicalInformation = (TechnicalInformation) o;
    return Objects.equals(this.creationDate, technicalInformation.creationDate) &&
        Objects.equals(this.creationSource, technicalInformation.creationSource) &&
        Objects.equals(this.creationUser, technicalInformation.creationUser) &&
        Objects.equals(this.lastUpdateDate, technicalInformation.lastUpdateDate) &&
        Objects.equals(this.lastUpdateSource, technicalInformation.lastUpdateSource) &&
        Objects.equals(this.lastUpdateUser, technicalInformation.lastUpdateUser) &&
        Objects.equals(this.closureDate, technicalInformation.closureDate) &&
        Objects.equals(this.closureSource, technicalInformation.closureSource) &&
        Objects.equals(this.closureUser, technicalInformation.closureUser) &&
        Objects.equals(this.technicalStatus, technicalInformation.technicalStatus);
  }

  @Override
  public int hashCode() {
    return Objects.hash(creationDate, creationSource, creationUser, lastUpdateDate, lastUpdateSource, lastUpdateUser, closureDate, closureSource, closureUser, technicalStatus);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TechnicalInformation {\n");
    sb.append("    creationDate: ").append(toIndentedString(creationDate)).append("\n");
    sb.append("    creationSource: ").append(toIndentedString(creationSource)).append("\n");
    sb.append("    creationUser: ").append(toIndentedString(creationUser)).append("\n");
    sb.append("    lastUpdateDate: ").append(toIndentedString(lastUpdateDate)).append("\n");
    sb.append("    lastUpdateSource: ").append(toIndentedString(lastUpdateSource)).append("\n");
    sb.append("    lastUpdateUser: ").append(toIndentedString(lastUpdateUser)).append("\n");
    sb.append("    closureDate: ").append(toIndentedString(closureDate)).append("\n");
    sb.append("    closureSource: ").append(toIndentedString(closureSource)).append("\n");
    sb.append("    closureUser: ").append(toIndentedString(closureUser)).append("\n");
    sb.append("    technicalStatus: ").append(toIndentedString(technicalStatus)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

