/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SendEmailMeetingRequestDTO
 */
@JsonPropertyOrder({
  SendEmailMeetingRequestDTO.JSON_PROPERTY_RECIPIENTS,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_TITLE,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_START_DATE,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_END_DATE,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_CONTENT,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_RECURRING,
  SendEmailMeetingRequestDTO.JSON_PROPERTY_VISIBILITY
})
@JsonTypeName("SendEmailMeetingRequest")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SendEmailMeetingRequestDTO {
  public static final String JSON_PROPERTY_RECIPIENTS = "recipients";
  private List<String> recipients = new ArrayList<>();

  public static final String JSON_PROPERTY_TITLE = "title";
  private String title;

  public static final String JSON_PROPERTY_START_DATE = "startDate";
  private LocalDateTime startDate;

  public static final String JSON_PROPERTY_END_DATE = "endDate";
  private LocalDateTime endDate;

  public static final String JSON_PROPERTY_CONTENT = "content";
  private String content;

  public static final String JSON_PROPERTY_RECURRING = "recurring";
  private Boolean recurring = false;

  /**
   * Gets or Sets visibility
   */
  public enum VisibilityEnum {
    BUSY("busy"),
    
    FREE("free"),
    
    WORKING_ELSEWHERE("workingElsewhere");

    private String value;

    VisibilityEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static VisibilityEnum fromValue(String value) {
      for (VisibilityEnum b : VisibilityEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_VISIBILITY = "visibility";
  private VisibilityEnum visibility = VisibilityEnum.BUSY;

  public SendEmailMeetingRequestDTO() {
  }

  public SendEmailMeetingRequestDTO recipients(List<String> recipients) {
    
    this.recipients = recipients;
    return this;
  }

  public SendEmailMeetingRequestDTO addRecipientsItem(String recipientsItem) {
    if (this.recipients == null) {
      this.recipients = new ArrayList<>();
    }
    this.recipients.add(recipientsItem);
    return this;
  }

  /**
   * emails you want to send the meeting to
   * @return recipients
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_RECIPIENTS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public List<String> getRecipients() {
    return recipients;
  }


  @JsonProperty(JSON_PROPERTY_RECIPIENTS)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setRecipients(List<String> recipients) {
    this.recipients = recipients;
  }

  public SendEmailMeetingRequestDTO title(String title) {
    
    this.title = title;
    return this;
  }

  /**
   * Meeting title
   * @return title
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTitle() {
    return title;
  }


  @JsonProperty(JSON_PROPERTY_TITLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTitle(String title) {
    this.title = title;
  }

  public SendEmailMeetingRequestDTO startDate(LocalDateTime startDate) {
    
    this.startDate = startDate;
    return this;
  }

  /**
   * Start date (and optionally time) of the meeting
   * @return startDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDateTime getStartDate() {
    return startDate;
  }


  @JsonProperty(JSON_PROPERTY_START_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setStartDate(LocalDateTime startDate) {
    this.startDate = startDate;
  }

  public SendEmailMeetingRequestDTO endDate(LocalDateTime endDate) {
    
    this.endDate = endDate;
    return this;
  }

  /**
   * End date (and optionally time) of the meeting
   * @return endDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getEndDate() {
    return endDate;
  }


  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEndDate(LocalDateTime endDate) {
    this.endDate = endDate;
  }

  public SendEmailMeetingRequestDTO content(String content) {
    
    this.content = content;
    return this;
  }

  /**
   * Content of the meeting
   * @return content
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getContent() {
    return content;
  }


  @JsonProperty(JSON_PROPERTY_CONTENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContent(String content) {
    this.content = content;
  }

  public SendEmailMeetingRequestDTO recurring(Boolean recurring) {
    
    this.recurring = recurring;
    return this;
  }

  /**
   * if set to true, will create a meeting by day from the start day to the end day.
   * @return recurring
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RECURRING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRecurring() {
    return recurring;
  }


  @JsonProperty(JSON_PROPERTY_RECURRING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRecurring(Boolean recurring) {
    this.recurring = recurring;
  }

  public SendEmailMeetingRequestDTO visibility(VisibilityEnum visibility) {
    
    this.visibility = visibility;
    return this;
  }

  /**
   * Get visibility
   * @return visibility
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VISIBILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public VisibilityEnum getVisibility() {
    return visibility;
  }


  @JsonProperty(JSON_PROPERTY_VISIBILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVisibility(VisibilityEnum visibility) {
    this.visibility = visibility;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SendEmailMeetingRequestDTO sendEmailMeetingRequest = (SendEmailMeetingRequestDTO) o;
    return Objects.equals(this.recipients, sendEmailMeetingRequest.recipients) &&
        Objects.equals(this.title, sendEmailMeetingRequest.title) &&
        Objects.equals(this.startDate, sendEmailMeetingRequest.startDate) &&
        Objects.equals(this.endDate, sendEmailMeetingRequest.endDate) &&
        Objects.equals(this.content, sendEmailMeetingRequest.content) &&
        Objects.equals(this.recurring, sendEmailMeetingRequest.recurring) &&
        Objects.equals(this.visibility, sendEmailMeetingRequest.visibility);
  }

  @Override
  public int hashCode() {
    return Objects.hash(recipients, title, startDate, endDate, content, recurring, visibility);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SendEmailMeetingRequestDTO {\n");
    sb.append("    recipients: ").append(toIndentedString(recipients)).append("\n");
    sb.append("    title: ").append(toIndentedString(title)).append("\n");
    sb.append("    startDate: ").append(toIndentedString(startDate)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    content: ").append(toIndentedString(content)).append("\n");
    sb.append("    recurring: ").append(toIndentedString(recurring)).append("\n");
    sb.append("    visibility: ").append(toIndentedString(visibility)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

