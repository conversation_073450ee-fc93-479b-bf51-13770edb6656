/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech505Form;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * WECH505
 */
@JsonPropertyOrder({
  WECH505.JSON_PROPERTY_FORM
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class WECH505 {
  public static final String JSON_PROPERTY_FORM = "form";
  private Wech505Form form;

  public WECH505() {
  }

  public WECH505 form(Wech505Form form) {
    
    this.form = form;
    return this;
  }

  /**
   * Get form
   * @return form
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505Form getForm() {
    return form;
  }


  @JsonProperty(JSON_PROPERTY_FORM)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setForm(Wech505Form form) {
    this.form = form;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WECH505 WECH505 = (WECH505) o;
    return Objects.equals(this.form, WECH505.form);
  }

  @Override
  public int hashCode() {
    return Objects.hash(form);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WECH505 {\n");
    sb.append("    form: ").append(toIndentedString(form)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

