package be.fgov.onerva.cu.backend.config;

import be.fgov.onerva.barema.api.BaremeApi;
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.api.LookupApi;
import be.fgov.onerva.person.api.CitizenApi;
import be.fgov.onerva.person.api.CitizenInfoApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.boot.web.client.RestTemplateCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.web.client.RestClient;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
@Slf4j
public class HttpClientConfig {

    @Bean
    public RestTemplateBuilder restTemplateBuilder(RestTemplateBuilderConfigurer configurer) {
        return configurer.configure(new RestTemplateBuilder())
                .setReadTimeout(Duration.ofSeconds(20))
                .setConnectTimeout(Duration.ofSeconds(3))
                .additionalInterceptors(new LoggingRequestInterceptor());
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder.build();
    }

    @Bean
    @ConditionalOnProperty(name = "client.security.enabled", havingValue = "true")
    RestTemplateCustomizer globalOAuthClientInterceptor(OAuth2AuthorizedClientManager authorizedClientManager) {
        return restTemplate -> restTemplate.getInterceptors()
                .add(new OAuthClientCredentialsRestTemplateInterceptor(authorizedClientManager));
    }

    // RestClient Configuration
    @Bean
    public RestClient.Builder restClientBuilder() {
        var factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(Duration.ofSeconds(3));
        factory.setReadTimeout(Duration.ofSeconds(20));

        return RestClient.builder().requestFactory(factory).requestInterceptor(new LoggingRequestInterceptor());
    }

    @Bean
    public RestClient restClient(RestClient.Builder restClientBuilder) {
        return restClientBuilder.build();
    }

    @Bean
    @ConditionalOnProperty(name = "client.security.enabled", havingValue = "true")
    public RestClient oauthRestClient(RestClient.Builder restClientBuilder,
                                      OAuth2AuthorizedClientManager authorizedClientManager) {
        return restClientBuilder.requestInterceptor(new OAuthClientCredentialsRestTemplateInterceptor(
                authorizedClientManager)).build();
    }

    @Bean
    public BaremeApi baremeApi(RestTemplate restTemplate, @Value("${barema.url}") String baremaUrl) {
        var apiClient = new be.fgov.onerva.barema.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(baremaUrl);
        return new BaremeApi(apiClient);
    }

    @Bean
    public CitizenApi citizenApi(RestClient.Builder restClientBuilder,
                                 @Value("${citizen.url}") String citizenBasePath) {
        return new CitizenApi(restClientBuilder.baseUrl(citizenBasePath).build());
    }

    @Bean
    public CitizenInfoApi citizenInfoApi(RestClient.Builder restClientBuilder,
                                         @Value("${citizen.url}") String citizenBasePath) {
        return new CitizenInfoApi(restClientBuilder.baseUrl(citizenBasePath).build());
    }

    @Bean
    public LookupApi lookupApiClient(RestTemplate restTemplate, @Value("${lookup.url}") String lookupBasePath) {
        var apiClient = new be.fgov.onerva.cu.backend.rest.client.lookup.wppt.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(lookupBasePath);
        return new LookupApi(apiClient);
    }

    @Bean
    public be.fgov.onerva.registerproxyservice.api.CitizenApi registryApi(RestTemplate restTemplate,
                                                                          @Value("${registry.url}")
                                                                          String registryBasePath) {
        var apiClient = new be.fgov.onerva.registerproxyservice.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(registryBasePath);
        return new be.fgov.onerva.registerproxyservice.api.CitizenApi(apiClient);
    }
}
