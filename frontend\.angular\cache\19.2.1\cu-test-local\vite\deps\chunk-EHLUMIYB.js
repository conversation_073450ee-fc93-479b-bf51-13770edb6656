import {
  takeUntilDestroyed
} from "./chunk-AKSZNNDE.js";
import {
  TranslateService
} from "./chunk-VKFZTD5H.js";
import {
  OnemrvaMatColor
} from "./chunk-X7GZ3B26.js";
import {
  MatSnackBar
} from "./chunk-WCD4BJ6A.js";
import {
  MatTooltip,
  MatTooltipModule
} from "./chunk-N6U7X23W.js";
import {
  MatIcon,
  MatIconModule
} from "./chunk-JYJFRGNE.js";
import {
  HttpClient,
  HttpParams
} from "./chunk-5VYNQAP5.js";
import {
  DateTime,
  Info
} from "./chunk-OHPPLNLF.js";
import {
  OverlayContainer
} from "./chunk-VP7OCDXQ.js";
import {
  MAT_FORM_FIELD_DEFAULT_OPTIONS
} from "./chunk-T76J6HQT.js";
import {
  DateAdapter,
  MAT_DATE_FORMATS,
  MAT_DATE_LOCALE,
  NativeDateAdapter
} from "./chunk-UPOW3STX.js";
import {
  BreakpointObserver,
  Platform,
  _isTestEnvironment
} from "./chunk-IE44L42K.js";
import {
  DOCUMENT
} from "./chunk-QNFKXUK7.js";
import {
  Component,
  ComponentFactoryResolver$1,
  Directive,
  ElementRef,
  ErrorHandler,
  EventEmitter,
  HostBinding,
  HostListener,
  Inject,
  Injectable,
  InjectionToken,
  Input,
  NgModule,
  NgZone,
  Optional,
  Output,
  Pipe,
  Renderer2,
  RendererFactory2,
  TemplateRef,
  ViewContainerRef,
  inject,
  setClassMetadata,
  ɵɵclassMap,
  ɵɵclassProp,
  ɵɵdefineComponent,
  ɵɵdefineDirective,
  ɵɵdefineInjectable,
  ɵɵdefineInjector,
  ɵɵdefineNgModule,
  ɵɵdefinePipe,
  ɵɵdirectiveInject,
  ɵɵelementEnd,
  ɵɵelementStart,
  ɵɵinject,
  ɵɵlistener,
  ɵɵproperty,
  ɵɵtext
} from "./chunk-6V6BWDKV.js";
import {
  NEVER,
  forkJoin,
  merge
} from "./chunk-IC62NIWK.js";
import {
  ReplaySubject,
  Subject,
  __decorate,
  catchError,
  combineLatestWith,
  distinctUntilChanged,
  filter,
  finalize,
  from,
  map,
  of,
  shareReplay,
  skip,
  startWith,
  switchMap,
  take,
  takeUntil,
  tap
} from "./chunk-ZZ67MR3E.js";
import {
  __async,
  __spreadProps,
  __spreadValues
} from "./chunk-WDMUDEB6.js";

// node_modules/@angular/cdk/fesm2022/clipboard.mjs
var PendingCopy = class {
  _document;
  _textarea;
  constructor(text, _document) {
    this._document = _document;
    const textarea = this._textarea = this._document.createElement("textarea");
    const styles = textarea.style;
    styles.position = "fixed";
    styles.top = styles.opacity = "0";
    styles.left = "-999em";
    textarea.setAttribute("aria-hidden", "true");
    textarea.value = text;
    textarea.readOnly = true;
    (this._document.fullscreenElement || this._document.body).appendChild(textarea);
  }
  /** Finishes copying the text. */
  copy() {
    const textarea = this._textarea;
    let successful = false;
    try {
      if (textarea) {
        const currentFocus = this._document.activeElement;
        textarea.select();
        textarea.setSelectionRange(0, textarea.value.length);
        successful = this._document.execCommand("copy");
        if (currentFocus) {
          currentFocus.focus();
        }
      }
    } catch {
    }
    return successful;
  }
  /** Cleans up DOM changes used to perform the copy operation. */
  destroy() {
    const textarea = this._textarea;
    if (textarea) {
      textarea.remove();
      this._textarea = void 0;
    }
  }
};
var Clipboard = class _Clipboard {
  _document = inject(DOCUMENT);
  constructor() {
  }
  /**
   * Copies the provided text into the user's clipboard.
   *
   * @param text The string to copy.
   * @returns Whether the operation was successful.
   */
  copy(text) {
    const pendingCopy = this.beginCopy(text);
    const successful = pendingCopy.copy();
    pendingCopy.destroy();
    return successful;
  }
  /**
   * Prepares a string to be copied later. This is useful for large strings
   * which take too long to successfully render and be copied in the same tick.
   *
   * The caller must call `destroy` on the returned `PendingCopy`.
   *
   * @param text The string to copy.
   * @returns the pending copy operation.
   */
  beginCopy(text) {
    return new PendingCopy(text, this._document);
  }
  static ɵfac = function Clipboard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _Clipboard)();
  };
  static ɵprov = ɵɵdefineInjectable({
    token: _Clipboard,
    factory: _Clipboard.ɵfac,
    providedIn: "root"
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(Clipboard, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [], null);
})();
var CDK_COPY_TO_CLIPBOARD_CONFIG = new InjectionToken("CDK_COPY_TO_CLIPBOARD_CONFIG");
var CdkCopyToClipboard = class _CdkCopyToClipboard {
  _clipboard = inject(Clipboard);
  _ngZone = inject(NgZone);
  /** Content to be copied. */
  text = "";
  /**
   * How many times to attempt to copy the text. This may be necessary for longer text, because
   * the browser needs time to fill an intermediate textarea element and copy the content.
   */
  attempts = 1;
  /**
   * Emits when some text is copied to the clipboard. The
   * emitted value indicates whether copying was successful.
   */
  copied = new EventEmitter();
  /** Copies that are currently being attempted. */
  _pending = /* @__PURE__ */ new Set();
  /** Whether the directive has been destroyed. */
  _destroyed;
  /** Timeout for the current copy attempt. */
  _currentTimeout;
  constructor() {
    const config = inject(CDK_COPY_TO_CLIPBOARD_CONFIG, {
      optional: true
    });
    if (config && config.attempts != null) {
      this.attempts = config.attempts;
    }
  }
  /** Copies the current text to the clipboard. */
  copy(attempts = this.attempts) {
    if (attempts > 1) {
      let remainingAttempts = attempts;
      const pending = this._clipboard.beginCopy(this.text);
      this._pending.add(pending);
      const attempt = () => {
        const successful = pending.copy();
        if (!successful && --remainingAttempts && !this._destroyed) {
          this._currentTimeout = this._ngZone.runOutsideAngular(() => setTimeout(attempt, 1));
        } else {
          this._currentTimeout = null;
          this._pending.delete(pending);
          pending.destroy();
          this.copied.emit(successful);
        }
      };
      attempt();
    } else {
      this.copied.emit(this._clipboard.copy(this.text));
    }
  }
  ngOnDestroy() {
    if (this._currentTimeout) {
      clearTimeout(this._currentTimeout);
    }
    this._pending.forEach((copy) => copy.destroy());
    this._pending.clear();
    this._destroyed = true;
  }
  static ɵfac = function CdkCopyToClipboard_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _CdkCopyToClipboard)();
  };
  static ɵdir = ɵɵdefineDirective({
    type: _CdkCopyToClipboard,
    selectors: [["", "cdkCopyToClipboard", ""]],
    hostBindings: function CdkCopyToClipboard_HostBindings(rf, ctx) {
      if (rf & 1) {
        ɵɵlistener("click", function CdkCopyToClipboard_click_HostBindingHandler() {
          return ctx.copy();
        });
      }
    },
    inputs: {
      text: [0, "cdkCopyToClipboard", "text"],
      attempts: [0, "cdkCopyToClipboardAttempts", "attempts"]
    },
    outputs: {
      copied: "cdkCopyToClipboardCopied"
    }
  });
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CdkCopyToClipboard, [{
    type: Directive,
    args: [{
      selector: "[cdkCopyToClipboard]",
      host: {
        "(click)": "copy()"
      }
    }]
  }], () => [], {
    text: [{
      type: Input,
      args: ["cdkCopyToClipboard"]
    }],
    attempts: [{
      type: Input,
      args: ["cdkCopyToClipboardAttempts"]
    }],
    copied: [{
      type: Output,
      args: ["cdkCopyToClipboardCopied"]
    }]
  });
})();
var ClipboardModule = class _ClipboardModule {
  static ɵfac = function ClipboardModule_Factory(__ngFactoryType__) {
    return new (__ngFactoryType__ || _ClipboardModule)();
  };
  static ɵmod = ɵɵdefineNgModule({
    type: _ClipboardModule,
    imports: [CdkCopyToClipboard],
    exports: [CdkCopyToClipboard]
  });
  static ɵinj = ɵɵdefineInjector({});
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClipboardModule, [{
    type: NgModule,
    args: [{
      imports: [CdkCopyToClipboard],
      exports: [CdkCopyToClipboard]
    }]
  }], null, null);
})();

// node_modules/ibantools/jsnext/ibantools.js
function isValidIBAN(iban, validationOptions) {
  if (validationOptions === void 0) {
    validationOptions = {
      allowQRIBAN: true
    };
  }
  if (iban === void 0 || iban === null) return false;
  var reg = new RegExp("^[0-9]{2}$", "");
  var countryCode = iban.slice(0, 2);
  var spec = countrySpecs[countryCode];
  if (spec === void 0 || spec.bban_regexp === void 0 || spec.bban_regexp === null || spec.chars === void 0) return false;
  return spec.chars === iban.length && reg.test(iban.slice(2, 4)) && isValidBBAN(iban.slice(4), countryCode) && isValidIBANChecksum(iban) && (validationOptions.allowQRIBAN || !isQRIBAN(iban));
}
var ValidationErrorsIBAN;
(function(ValidationErrorsIBAN2) {
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["NoIBANProvided"] = 0] = "NoIBANProvided";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["NoIBANCountry"] = 1] = "NoIBANCountry";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["WrongBBANLength"] = 2] = "WrongBBANLength";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["WrongBBANFormat"] = 3] = "WrongBBANFormat";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["ChecksumNotNumber"] = 4] = "ChecksumNotNumber";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["WrongIBANChecksum"] = 5] = "WrongIBANChecksum";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["WrongAccountBankBranchChecksum"] = 6] = "WrongAccountBankBranchChecksum";
  ValidationErrorsIBAN2[ValidationErrorsIBAN2["QRIBANNotAllowed"] = 7] = "QRIBANNotAllowed";
})(ValidationErrorsIBAN || (ValidationErrorsIBAN = {}));
function validateIBAN(iban, validationOptions) {
  if (validationOptions === void 0) {
    validationOptions = {
      allowQRIBAN: true
    };
  }
  var result = {
    errorCodes: [],
    valid: true
  };
  if (iban !== void 0 && iban !== null && iban !== "") {
    var spec = countrySpecs[iban.slice(0, 2)];
    if (!spec || !(spec.bban_regexp || spec.chars)) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.NoIBANCountry);
      return result;
    }
    if (spec && spec.chars && spec.chars !== iban.length) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.WrongBBANLength);
    }
    if (spec && spec.bban_regexp && !checkFormatBBAN(iban.slice(4), spec.bban_regexp)) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.WrongBBANFormat);
    }
    if (spec && spec.bban_validation_func && !spec.bban_validation_func(iban.slice(4))) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.WrongAccountBankBranchChecksum);
    }
    var reg = new RegExp("^[0-9]{2}$", "");
    if (!reg.test(iban.slice(2, 4))) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.ChecksumNotNumber);
    }
    if (result.errorCodes.indexOf(ValidationErrorsIBAN.WrongBBANFormat) !== -1 || !isValidIBANChecksum(iban)) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.WrongIBANChecksum);
    }
    if (!validationOptions.allowQRIBAN && isQRIBAN(iban)) {
      result.valid = false;
      result.errorCodes.push(ValidationErrorsIBAN.QRIBANNotAllowed);
    }
  } else {
    result.valid = false;
    result.errorCodes.push(ValidationErrorsIBAN.NoIBANProvided);
  }
  return result;
}
function isValidBBAN(bban, countryCode) {
  if (bban === void 0 || bban === null || countryCode === void 0 || countryCode === null) return false;
  var spec = countrySpecs[countryCode];
  if (spec === void 0 || spec === null || spec.bban_regexp === void 0 || spec.bban_regexp === null || spec.chars === void 0 || spec.chars === null) return false;
  if (spec.chars - 4 === bban.length && checkFormatBBAN(bban, spec.bban_regexp)) {
    if (spec.bban_validation_func) {
      return spec.bban_validation_func(bban.replace(/[\s.]+/g, ""));
    }
    return true;
  }
  return false;
}
function isQRIBAN(iban) {
  if (iban === void 0 || iban === null) return false;
  var countryCode = iban.slice(0, 2);
  var QRIBANCountries = ["LI", "CH"];
  if (!QRIBANCountries.includes(countryCode)) return false;
  var reg = new RegExp("^3[0-1]{1}[0-9]{3}$", "");
  return reg.test(iban.slice(4, 9));
}
function extractIBAN(iban) {
  var result = {};
  var eFormatIBAN = electronicFormatIBAN(iban);
  result.iban = eFormatIBAN || iban;
  if (!!eFormatIBAN && isValidIBAN(eFormatIBAN)) {
    result.bban = eFormatIBAN.slice(4);
    result.countryCode = eFormatIBAN.slice(0, 2);
    result.valid = true;
    var spec = countrySpecs[result.countryCode];
    if (spec.account_indentifier) {
      var ac = spec.account_indentifier.split("-");
      var starting = parseInt(ac[0]);
      var ending = parseInt(ac[1]);
      result.accountNumber = result.iban.slice(starting, ending + 1);
    }
    if (spec.bank_identifier) {
      var ac = spec.bank_identifier.split("-");
      var starting = parseInt(ac[0]);
      var ending = parseInt(ac[1]);
      result.bankIdentifier = result.bban.slice(starting, ending + 1);
    }
    if (spec.branch_indentifier) {
      var ac = spec.branch_indentifier.split("-");
      var starting = parseInt(ac[0]);
      var ending = parseInt(ac[1]);
      result.branchIdentifier = result.bban.slice(starting, ending + 1);
    }
  } else {
    result.valid = false;
  }
  return result;
}
function checkFormatBBAN(bban, bformat) {
  var reg = new RegExp(bformat, "");
  return reg.test(bban);
}
function electronicFormatIBAN(iban) {
  if (typeof iban !== "string") {
    return null;
  }
  return iban.replace(/[-\ ]/g, "").toUpperCase();
}
function friendlyFormatIBAN(iban, separator) {
  if (typeof iban !== "string") {
    return null;
  }
  if (separator === void 0 || separator === null) {
    separator = " ";
  }
  var electronic_iban = electronicFormatIBAN(iban);
  if (electronic_iban === null) {
    return null;
  }
  return electronic_iban.replace(/(.{4})(?!$)/g, "$1" + separator);
}
function isValidIBANChecksum(iban) {
  var countryCode = iban.slice(0, 2);
  var providedChecksum = parseInt(iban.slice(2, 4), 10);
  var bban = iban.slice(4);
  var validationString = replaceCharaterWithCode("".concat(bban).concat(countryCode, "00"));
  var rest = mod9710(validationString);
  return 98 - rest === providedChecksum;
}
function replaceCharaterWithCode(str) {
  return str.split("").map(function(c) {
    var code = c.charCodeAt(0);
    return code >= 65 ? (code - 55).toString() : c;
  }).join("");
}
var ValidationErrorsBIC;
(function(ValidationErrorsBIC2) {
  ValidationErrorsBIC2[ValidationErrorsBIC2["NoBICProvided"] = 0] = "NoBICProvided";
  ValidationErrorsBIC2[ValidationErrorsBIC2["NoBICCountry"] = 1] = "NoBICCountry";
  ValidationErrorsBIC2[ValidationErrorsBIC2["WrongBICFormat"] = 2] = "WrongBICFormat";
})(ValidationErrorsBIC || (ValidationErrorsBIC = {}));
var checkNorwayBBAN = function(bban) {
  var weights = [5, 4, 3, 2, 7, 6, 5, 4, 3, 2];
  var bbanWithoutSpacesAndPeriods = bban.replace(/[\s.]+/g, "");
  var controlDigit = parseInt(bbanWithoutSpacesAndPeriods.charAt(10), 10);
  var bbanWithoutControlDigit = bbanWithoutSpacesAndPeriods.substring(0, 10);
  var sum = 0;
  for (var index = 0; index < 10; index++) {
    sum += parseInt(bbanWithoutControlDigit.charAt(index), 10) * weights[index];
  }
  var remainder = sum % 11;
  return controlDigit === (remainder === 0 ? 0 : 11 - remainder);
};
var checkBelgianBBAN = function(bban) {
  var stripped = bban.replace(/[\s.]+/g, "");
  var checkingPart = parseInt(stripped.substring(0, stripped.length - 2), 10);
  var checksum = parseInt(stripped.substring(stripped.length - 2, stripped.length), 10);
  var remainder = checkingPart % 97 === 0 ? 97 : checkingPart % 97;
  return remainder === checksum;
};
var mod9710 = function(validationString) {
  while (validationString.length > 2) {
    var part = validationString.slice(0, 6);
    var partInt = parseInt(part, 10);
    if (isNaN(partInt)) {
      return NaN;
    }
    validationString = partInt % 97 + validationString.slice(part.length);
  }
  return parseInt(validationString, 10) % 97;
};
var checkMod9710BBAN = function(bban) {
  var stripped = bban.replace(/[\s.]+/g, "");
  var reminder = mod9710(stripped);
  return reminder === 1;
};
var checkPolandBBAN = function(bban) {
  var weights = [3, 9, 7, 1, 3, 9, 7];
  var controlDigit = parseInt(bban.charAt(7), 10);
  var toCheck = bban.substring(0, 7);
  var sum = 0;
  for (var index = 0; index < 7; index++) {
    sum += parseInt(toCheck.charAt(index), 10) * weights[index];
  }
  var remainder = sum % 10;
  return controlDigit === (remainder === 0 ? 0 : 10 - remainder);
};
var checkSpainBBAN = function(bban) {
  var weightsBankBranch = [4, 8, 5, 10, 9, 7, 3, 6];
  var weightsAccount = [1, 2, 4, 8, 5, 10, 9, 7, 3, 6];
  var controlBankBranch = parseInt(bban.charAt(8), 10);
  var controlAccount = parseInt(bban.charAt(9), 10);
  var bankBranch = bban.substring(0, 8);
  var account = bban.substring(10, 20);
  var sum = 0;
  for (var index = 0; index < 8; index++) {
    sum += parseInt(bankBranch.charAt(index), 10) * weightsBankBranch[index];
  }
  var remainder = sum % 11;
  if (controlBankBranch !== (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder)) {
    return false;
  }
  sum = 0;
  for (var index = 0; index < 10; index++) {
    sum += parseInt(account.charAt(index), 10) * weightsAccount[index];
  }
  remainder = sum % 11;
  return controlAccount === (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder);
};
var checkMod1110 = function(toCheck, control) {
  var nr = 10;
  for (var index = 0; index < toCheck.length; index++) {
    nr += parseInt(toCheck.charAt(index), 10);
    if (nr % 10 !== 0) {
      nr = nr % 10;
    }
    nr = nr * 2;
    nr = nr % 11;
  }
  return control === (11 - nr === 10 ? 0 : 11 - nr);
};
var checkCroatianBBAN = function(bban) {
  var controlBankBranch = parseInt(bban.charAt(6), 10);
  var controlAccount = parseInt(bban.charAt(16), 10);
  var bankBranch = bban.substring(0, 6);
  var account = bban.substring(7, 16);
  return checkMod1110(bankBranch, controlBankBranch) && checkMod1110(account, controlAccount);
};
var checkCzechAndSlovakBBAN = function(bban) {
  var weightsPrefix = [10, 5, 8, 4, 2, 1];
  var weightsSuffix = [6, 3, 7, 9, 10, 5, 8, 4, 2, 1];
  var controlPrefix = parseInt(bban.charAt(9), 10);
  var controlSuffix = parseInt(bban.charAt(19), 10);
  var prefix = bban.substring(4, 9);
  var suffix = bban.substring(10, 19);
  var sum = 0;
  for (var index = 0; index < prefix.length; index++) {
    sum += parseInt(prefix.charAt(index), 10) * weightsPrefix[index];
  }
  var remainder = sum % 11;
  if (controlPrefix !== (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder)) {
    return false;
  }
  sum = 0;
  for (var index = 0; index < suffix.length; index++) {
    sum += parseInt(suffix.charAt(index), 10) * weightsSuffix[index];
  }
  remainder = sum % 11;
  return controlSuffix === (remainder === 0 ? 0 : remainder === 1 ? 1 : 11 - remainder);
};
var checkEstonianBBAN = function(bban) {
  var weights = [7, 1, 3, 7, 1, 3, 7, 1, 3, 7, 1, 3, 7];
  var controlDigit = parseInt(bban.charAt(15), 10);
  var toCheck = bban.substring(2, 15);
  var sum = 0;
  for (var index = 0; index < toCheck.length; index++) {
    sum += parseInt(toCheck.charAt(index), 10) * weights[index];
  }
  var remainder = sum % 10;
  return controlDigit === (remainder === 0 ? 0 : 10 - remainder);
};
var checkFrenchBBAN = function(bban) {
  var stripped = bban.replace(/[\s.]+/g, "");
  var normalized = Array.from(stripped);
  for (var index = 0; index < stripped.length; index++) {
    var c = normalized[index].charCodeAt(0);
    if (c >= 65) {
      switch (c) {
        case 65:
        case 74:
          normalized[index] = "1";
          break;
        case 66:
        case 75:
        case 83:
          normalized[index] = "2";
          break;
        case 67:
        case 76:
        case 84:
          normalized[index] = "3";
          break;
        case 68:
        case 77:
        case 85:
          normalized[index] = "4";
          break;
        case 69:
        case 78:
        case 86:
          normalized[index] = "5";
          break;
        case 70:
        case 79:
        case 87:
          normalized[index] = "6";
          break;
        case 71:
        case 80:
        case 88:
          normalized[index] = "7";
          break;
        case 72:
        case 81:
        case 89:
          normalized[index] = "8";
          break;
        case 73:
        case 82:
        case 90:
          normalized[index] = "9";
          break;
      }
    }
  }
  var remainder = mod9710(normalized.join(""));
  return remainder === 0;
};
var checkHungarianBBAN = function(bban) {
  var weights = [9, 7, 3, 1, 9, 7, 3, 1, 9, 7, 3, 1, 9, 7, 3];
  var controlDigitBankBranch = parseInt(bban.charAt(7), 10);
  var toCheckBankBranch = bban.substring(0, 7);
  var sum = 0;
  for (var index = 0; index < toCheckBankBranch.length; index++) {
    sum += parseInt(toCheckBankBranch.charAt(index), 10) * weights[index];
  }
  var remainder = sum % 10;
  if (controlDigitBankBranch !== (remainder === 0 ? 0 : 10 - remainder)) {
    return false;
  }
  sum = 0;
  if (bban.endsWith("********")) {
    var toCheckAccount = bban.substring(8, 15);
    var controlDigitAccount = parseInt(bban.charAt(15), 10);
    for (var index = 0; index < toCheckAccount.length; index++) {
      sum += parseInt(toCheckAccount.charAt(index), 10) * weights[index];
    }
    var remainder_1 = sum % 10;
    return controlDigitAccount === (remainder_1 === 0 ? 0 : 10 - remainder_1);
  } else {
    var toCheckAccount = bban.substring(8, 23);
    var controlDigitAccount = parseInt(bban.charAt(23), 10);
    for (var index = 0; index < toCheckAccount.length; index++) {
      sum += parseInt(toCheckAccount.charAt(index), 10) * weights[index];
    }
    var remainder_2 = sum % 10;
    return controlDigitAccount === (remainder_2 === 0 ? 0 : 10 - remainder_2);
  }
};
var countrySpecs = {
  AD: {
    chars: 24,
    bban_regexp: "^[0-9]{8}[A-Z0-9]{12}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-7",
    bank_identifier: "0-3",
    account_indentifier: "8-24"
  },
  AE: {
    chars: 23,
    bban_regexp: "^[0-9]{3}[0-9]{16}$",
    IBANRegistry: true,
    bank_identifier: "0-2",
    account_indentifier: "7-23"
  },
  AF: {},
  AG: {},
  AI: {},
  AL: {
    chars: 28,
    bban_regexp: "^[0-9]{8}[A-Z0-9]{16}$",
    IBANRegistry: true,
    branch_indentifier: "3-7",
    bank_identifier: "0-2",
    account_indentifier: "12-28"
  },
  AM: {},
  AO: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$"
  },
  AQ: {},
  AR: {},
  AS: {},
  AT: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-4"
  },
  AU: {},
  AW: {},
  AX: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true
  },
  AZ: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{20}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "4-28"
  },
  BA: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    branch_indentifier: "3-5",
    bank_identifier: "0-2"
  },
  BB: {},
  BD: {},
  BE: {
    chars: 16,
    bban_regexp: "^[0-9]{12}$",
    bban_validation_func: checkBelgianBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-2",
    account_indentifier: "0-16"
  },
  BF: {
    chars: 28,
    bban_regexp: "^[A-Z0-9]{2}[0-9]{22}$"
  },
  BG: {
    chars: 22,
    bban_regexp: "^[A-Z]{4}[0-9]{6}[A-Z0-9]{8}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-7",
    bank_identifier: "0-3"
  },
  BH: {
    chars: 22,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{14}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-22"
  },
  BI: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$",
    branch_indentifier: "5-9",
    bank_identifier: "0-4",
    account_indentifier: "14-27"
  },
  BJ: {
    chars: 28,
    bban_regexp: "^[A-Z0-9]{2}[0-9]{22}$"
  },
  BL: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$"
  },
  BM: {},
  BN: {},
  BO: {},
  BQ: {},
  BR: {
    chars: 29,
    bban_regexp: "^[0-9]{23}[A-Z]{1}[A-Z0-9]{1}$",
    IBANRegistry: true,
    branch_indentifier: "8-12",
    bank_identifier: "0-7",
    account_indentifier: "17-29"
  },
  BS: {},
  BT: {},
  BV: {},
  BW: {},
  BY: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[0-9]{4}[A-Z0-9]{16}$",
    IBANRegistry: true,
    bank_identifier: "0-3"
  },
  BZ: {},
  CA: {},
  CC: {},
  CD: {},
  CF: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  CG: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  CH: {
    chars: 21,
    bban_regexp: "^[0-9]{5}[A-Z0-9]{12}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-4"
  },
  CI: {
    chars: 28,
    bban_regexp: "^[A-Z]{1}[0-9]{23}$"
  },
  CK: {},
  CL: {},
  CM: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  CN: {},
  CO: {},
  CR: {
    chars: 22,
    bban_regexp: "^[0-9]{18}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-22"
  },
  CU: {},
  CV: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$"
  },
  CW: {},
  CX: {},
  CY: {
    chars: 28,
    bban_regexp: "^[0-9]{8}[A-Z0-9]{16}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "3-7",
    bank_identifier: "0-2",
    account_indentifier: "12-28"
  },
  CZ: {
    chars: 24,
    bban_regexp: "^[0-9]{20}$",
    bban_validation_func: checkCzechAndSlovakBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3"
  },
  DE: {
    chars: 22,
    bban_regexp: "^[0-9]{18}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-7",
    account_indentifier: "13-22"
  },
  DJ: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$",
    branch_indentifier: "5-9",
    bank_identifier: "0-4",
    account_indentifier: "14-27"
  },
  DK: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "4-18"
  },
  DM: {},
  DO: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[0-9]{20}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-28"
  },
  DZ: {
    chars: 26,
    bban_regexp: "^[0-9]{22}$"
  },
  EC: {},
  EE: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    bban_validation_func: checkEstonianBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-1",
    account_indentifier: "8-20"
  },
  EG: {
    chars: 29,
    bban_regexp: "^[0-9]{25}",
    IBANRegistry: true,
    branch_indentifier: "4-7",
    bank_identifier: "0-3",
    account_indentifier: "17-29"
  },
  EH: {},
  ER: {},
  ES: {
    chars: 24,
    bban_validation_func: checkSpainBBAN,
    bban_regexp: "^[0-9]{20}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-7",
    bank_identifier: "0-3",
    account_indentifier: "14-24"
  },
  ET: {},
  FI: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-2",
    account_indentifier: "0-0"
  },
  FJ: {},
  FK: {
    chars: 18,
    bban_regexp: "^[A-Z]{2}[0-9]{12}$",
    bank_identifier: "0-1",
    account_indentifier: "6-18"
  },
  FM: {},
  FO: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "4-18"
  },
  FR: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    bban_validation_func: checkFrenchBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-4",
    branch_indentifier: "5-9",
    account_indentifier: "14-24"
  },
  GA: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  GB: {
    chars: 22,
    bban_regexp: "^[A-Z]{4}[0-9]{14}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-9",
    bank_identifier: "0-3"
  },
  GD: {},
  GE: {
    chars: 22,
    bban_regexp: "^[A-Z0-9]{2}[0-9]{16}$",
    IBANRegistry: true,
    bank_identifier: "0-1",
    account_indentifier: "6-22"
  },
  GF: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  GG: {},
  GH: {},
  GI: {
    chars: 23,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{15}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "8-23"
  },
  GL: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "4-18"
  },
  GM: {},
  GN: {},
  GP: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  GQ: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  GR: {
    chars: 27,
    bban_regexp: "^[0-9]{7}[A-Z0-9]{16}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "3-6",
    bank_identifier: "0-2",
    account_indentifier: "7-27"
  },
  GS: {},
  GT: {
    chars: 28,
    bban_regexp: "^[A-Z0-9]{24}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-28"
  },
  GU: {},
  GW: {
    chars: 25,
    bban_regexp: "^[A-Z]{2}[0-9]{19}$"
  },
  GY: {},
  HK: {},
  HM: {},
  HN: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[0-9]{20}$"
  },
  HR: {
    chars: 21,
    bban_regexp: "^[0-9]{17}$",
    bban_validation_func: checkCroatianBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-6"
  },
  HT: {},
  HU: {
    chars: 28,
    bban_regexp: "^[0-9]{24}$",
    bban_validation_func: checkHungarianBBAN,
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "3-6",
    bank_identifier: "0-2"
  },
  ID: {},
  IE: {
    chars: 22,
    bban_regexp: "^[A-Z0-9]{4}[0-9]{14}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-9",
    bank_identifier: "0-3"
  },
  IL: {
    chars: 23,
    bban_regexp: "^[0-9]{19}$",
    IBANRegistry: true,
    branch_indentifier: "3-5",
    bank_identifier: "0-2"
  },
  IM: {},
  IN: {},
  IO: {},
  IQ: {
    chars: 23,
    bban_regexp: "^[A-Z]{4}[0-9]{15}$",
    IBANRegistry: true,
    branch_indentifier: "4-6",
    bank_identifier: "0-3",
    account_indentifier: "11-23"
  },
  IR: {
    chars: 26,
    bban_regexp: "^[0-9]{22}$"
  },
  IS: {
    chars: 26,
    bban_regexp: "^[0-9]{22}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "2-3",
    bank_identifier: "0-1"
  },
  IT: {
    chars: 27,
    bban_regexp: "^[A-Z]{1}[0-9]{10}[A-Z0-9]{12}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "6-10",
    bank_identifier: "1-5",
    account_indentifier: "4-27"
  },
  JE: {},
  JM: {},
  JO: {
    chars: 30,
    bban_regexp: "^[A-Z]{4}[0-9]{4}[A-Z0-9]{18}$",
    IBANRegistry: true,
    branch_indentifier: "4-7",
    bank_identifier: "4-7"
  },
  JP: {},
  KE: {},
  KG: {},
  KH: {},
  KI: {},
  KM: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  KN: {},
  KP: {},
  KR: {},
  KW: {
    chars: 30,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{22}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "20-30"
  },
  KY: {},
  KZ: {
    chars: 20,
    bban_regexp: "^[0-9]{3}[A-Z0-9]{13}$",
    IBANRegistry: true,
    bank_identifier: "0-2",
    account_indentifier: "0-20"
  },
  LA: {},
  LB: {
    chars: 28,
    bban_regexp: "^[0-9]{4}[A-Z0-9]{20}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "14-28"
  },
  LC: {
    chars: 32,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{24}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-32"
  },
  LI: {
    chars: 21,
    bban_regexp: "^[0-9]{5}[A-Z0-9]{12}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-4"
  },
  LK: {},
  LR: {},
  LS: {},
  LT: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-4"
  },
  LU: {
    chars: 20,
    bban_regexp: "^[0-9]{3}[A-Z0-9]{13}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-2"
  },
  LV: {
    chars: 21,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{13}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "0-21"
  },
  LY: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$",
    IBANRegistry: true,
    branch_indentifier: "3-5",
    bank_identifier: "0-2",
    account_indentifier: "10-25"
  },
  MA: {
    chars: 28,
    bban_regexp: "^[0-9]{24}$"
  },
  MC: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    bban_validation_func: checkFrenchBBAN,
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "5-9",
    bank_identifier: "0-4"
  },
  MD: {
    chars: 24,
    bban_regexp: "^[A-Z0-9]{2}[A-Z0-9]{18}$",
    IBANRegistry: true,
    bank_identifier: "0-1",
    account_indentifier: "6-24"
  },
  ME: {
    chars: 22,
    bban_regexp: "^[0-9]{18}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    bank_identifier: "0-2",
    account_indentifier: "4-22"
  },
  MF: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  MG: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  MH: {},
  MK: {
    chars: 19,
    bban_regexp: "^[0-9]{3}[A-Z0-9]{10}[0-9]{2}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    bank_identifier: "0-2"
  },
  ML: {
    chars: 28,
    bban_regexp: "^[A-Z0-9]{2}[0-9]{22}$"
  },
  MM: {},
  MN: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-20"
  },
  MO: {},
  MP: {},
  MQ: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  MR: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$",
    IBANRegistry: true,
    branch_indentifier: "5-9",
    bank_identifier: "0-4",
    account_indentifier: "4-27"
  },
  MS: {},
  MT: {
    chars: 31,
    bban_regexp: "^[A-Z]{4}[0-9]{5}[A-Z0-9]{18}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "4-8",
    bank_identifier: "0-3",
    account_indentifier: "15-31"
  },
  MU: {
    chars: 30,
    bban_regexp: "^[A-Z]{4}[0-9]{19}[A-Z]{3}$",
    IBANRegistry: true,
    branch_indentifier: "6-7",
    bank_identifier: "0-5",
    account_indentifier: "0-30"
  },
  MV: {},
  MW: {},
  MX: {},
  MY: {},
  MZ: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$"
  },
  NA: {},
  NC: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  NE: {
    chars: 28,
    bban_regexp: "^[A-Z]{2}[0-9]{22}$"
  },
  NF: {},
  NG: {},
  NI: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[0-9]{20}$",
    bank_identifier: "0-3",
    IBANRegistry: true,
    account_indentifier: "8-28"
  },
  NL: {
    chars: 18,
    bban_regexp: "^[A-Z]{4}[0-9]{10}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "8-18"
  },
  NO: {
    chars: 15,
    bban_regexp: "^[0-9]{11}$",
    bban_validation_func: checkNorwayBBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "4-15"
  },
  NP: {},
  NR: {},
  NU: {},
  NZ: {},
  OM: {
    chars: 23,
    bban_regexp: "^[0-9]{3}[A-Z0-9]{16}$",
    IBANRegistry: true,
    SEPA: false,
    bank_identifier: "0-2"
  },
  PA: {},
  PE: {},
  PF: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  PG: {},
  PH: {},
  PK: {
    chars: 24,
    bban_regexp: "^[A-Z0-9]{4}[0-9]{16}$",
    IBANRegistry: true,
    bank_identifier: "0-3"
  },
  PL: {
    chars: 28,
    bban_validation_func: checkPolandBBAN,
    bban_regexp: "^[0-9]{24}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "0-7",
    account_indentifier: "2-28"
  },
  PM: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  PN: {},
  PR: {},
  PS: {
    chars: 29,
    bban_regexp: "^[A-Z0-9]{4}[0-9]{21}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "17-29"
  },
  PT: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3"
  },
  PW: {},
  PY: {},
  QA: {
    chars: 29,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{21}$",
    IBANRegistry: true,
    bank_identifier: "0-3",
    account_indentifier: "8-29"
  },
  RE: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  RO: {
    chars: 24,
    bban_regexp: "^[A-Z]{4}[A-Z0-9]{16}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-3",
    account_indentifier: "0-24"
  },
  RS: {
    chars: 22,
    bban_regexp: "^[0-9]{18}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    bank_identifier: "0-2"
  },
  RU: {
    chars: 33,
    bban_regexp: "^[0-9]{14}[A-Z0-9]{15}$",
    IBANRegistry: true,
    branch_indentifier: "9-13",
    bank_identifier: "0-8",
    account_indentifier: "13-33"
  },
  RW: {},
  SA: {
    chars: 24,
    bban_regexp: "^[0-9]{2}[A-Z0-9]{18}$",
    IBANRegistry: true,
    bank_identifier: "0-1",
    account_indentifier: "12-24"
  },
  SB: {},
  SC: {
    chars: 31,
    bban_regexp: "^[A-Z]{4}[0-9]{20}[A-Z]{3}$",
    IBANRegistry: true,
    branch_indentifier: "6-7",
    bank_identifier: "0-5",
    account_indentifier: "12-28"
  },
  SD: {
    chars: 18,
    bban_regexp: "^[0-9]{14}$",
    IBANRegistry: true,
    bank_identifier: "0-1",
    account_indentifier: "6-18"
  },
  SE: {
    chars: 24,
    bban_regexp: "^[0-9]{20}$",
    IBANRegistry: true,
    SEPA: true,
    bank_identifier: "0-2"
  },
  SG: {},
  SH: {},
  SI: {
    chars: 19,
    bban_regexp: "^[0-9]{15}$",
    bban_validation_func: checkMod9710BBAN,
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "2-4",
    bank_identifier: "0-1",
    account_indentifier: "9-16"
  },
  SJ: {},
  SK: {
    chars: 24,
    bban_regexp: "^[0-9]{20}$",
    bban_validation_func: checkCzechAndSlovakBBAN,
    IBANRegistry: true,
    SEPA: true
  },
  SL: {},
  SM: {
    chars: 27,
    bban_regexp: "^[A-Z]{1}[0-9]{10}[A-Z0-9]{12}$",
    IBANRegistry: true,
    SEPA: true,
    branch_indentifier: "6-10"
  },
  SN: {
    chars: 28,
    bban_regexp: "^[A-Z]{2}[0-9]{22}$"
  },
  SO: {
    chars: 23,
    bban_regexp: "^[0-9]{19}$",
    IBANRegistry: true,
    branch_indentifier: "4-6",
    account_indentifier: "11-23"
  },
  SR: {},
  SS: {},
  ST: {
    chars: 25,
    bban_regexp: "^[0-9]{21}$",
    IBANRegistry: true,
    branch_indentifier: "4-7"
  },
  SV: {
    chars: 28,
    bban_regexp: "^[A-Z]{4}[0-9]{20}$",
    IBANRegistry: true,
    account_indentifier: "8-28"
  },
  SX: {},
  SY: {},
  SZ: {},
  TC: {},
  TD: {
    chars: 27,
    bban_regexp: "^[0-9]{23}$"
  },
  TF: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  TG: {
    chars: 28,
    bban_regexp: "^[A-Z]{2}[0-9]{22}$"
  },
  TH: {},
  TJ: {},
  TK: {},
  TL: {
    chars: 23,
    bban_regexp: "^[0-9]{19}$",
    IBANRegistry: true,
    account_indentifier: "4-23"
  },
  TM: {},
  TN: {
    chars: 24,
    bban_regexp: "^[0-9]{20}$",
    IBANRegistry: true,
    branch_indentifier: "2-4",
    account_indentifier: "4-24"
  },
  TO: {},
  TR: {
    chars: 26,
    bban_regexp: "^[0-9]{5}[A-Z0-9]{17}$",
    IBANRegistry: true
  },
  TT: {},
  TV: {},
  TW: {},
  TZ: {},
  UA: {
    chars: 29,
    bban_regexp: "^[0-9]{6}[A-Z0-9]{19}$",
    IBANRegistry: true,
    account_indentifier: "15-29"
  },
  UG: {},
  UM: {},
  US: {},
  UY: {},
  UZ: {},
  VA: {
    chars: 22,
    bban_regexp: "^[0-9]{18}",
    IBANRegistry: true,
    SEPA: true,
    account_indentifier: "7-22"
  },
  VC: {},
  VE: {},
  VG: {
    chars: 24,
    bban_regexp: "^[A-Z0-9]{4}[0-9]{16}$",
    IBANRegistry: true,
    account_indentifier: "8-24"
  },
  VI: {},
  VN: {},
  VU: {},
  WF: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  WS: {},
  XK: {
    chars: 20,
    bban_regexp: "^[0-9]{16}$",
    IBANRegistry: true,
    branch_indentifier: "2-3",
    account_indentifier: "4-20"
  },
  YE: {},
  YT: {
    chars: 27,
    bban_regexp: "^[0-9]{10}[A-Z0-9]{11}[0-9]{2}$",
    IBANRegistry: true
  },
  ZA: {},
  ZM: {},
  ZW: {}
};

// node_modules/@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-shared.mjs
var ClipboardIconComponent = class _ClipboardIconComponent {
  constructor(_elementRef, clipboardService) {
    this._elementRef = _elementRef;
    this.clipboardService = clipboardService;
  }
  static {
    this.ɵfac = function ClipboardIconComponent_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _ClipboardIconComponent)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Clipboard));
    };
  }
  static {
    this.ɵcmp = ɵɵdefineComponent({
      type: _ClipboardIconComponent,
      selectors: [["lib-clipboard-icon"]],
      decls: 2,
      vars: 1,
      consts: [[3, "matTooltip"]],
      template: function ClipboardIconComponent_Template(rf, ctx) {
        if (rf & 1) {
          ɵɵelementStart(0, "mat-icon", 0);
          ɵɵtext(1, "content_copy");
          ɵɵelementEnd();
        }
        if (rf & 2) {
          ɵɵproperty("matTooltip", "TEST");
        }
      },
      dependencies: [MatIconModule, MatIcon, MatTooltipModule, MatTooltip],
      encapsulation: 2
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(ClipboardIconComponent, [{
    type: Component,
    args: [{
      selector: "lib-clipboard-icon",
      standalone: true,
      imports: [MatIconModule, MatTooltipModule],
      template: `<mat-icon [matTooltip]="'TEST'">content_copy</mat-icon>
`
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Clipboard
  }], null);
})();
var CDNUrlModeOptions;
(function(CDNUrlModeOptions2) {
  CDNUrlModeOptions2["PROD"] = "production";
  CDNUrlModeOptions2["VAL"] = "validation";
})(CDNUrlModeOptions || (CDNUrlModeOptions = {}));
var CDN_URLS = {
  prod: "https://cdn.services.rvaonem.fgov.be",
  val: "https://cdn.servicesval.rvaonem.fgov.be"
};
var NISS_MASK = "000000/000-00";
var LOOKUP_COUNTRY_URL = "https://services/lookupwpptservice/rest/lookup/getLookups?class=be.fgov.onerva.lookup.wppt.persistence.model.common.Country";
var parseNativeDateFormats = ["ddMMyyyy", "dd/MM/yyyy", "d/M/yyyy", "dd/M/yyyy", "d/MM/yyyy", "d.M.yyyy", "dd.M.yyyy", "d.MM.yyyy", "d/M/yy", "dd/M/yy", "d/MM/yy", "d.M.yy", "dd.M.yy", "d.MM.yy"];
var parseNativeYearMonthFormats = ["MMyyyy", "M/yyyy", "M/yy", "MM/yyyy", "MM/yy", "M.yyyy", "MM.yyyy"];
var parseLuxonYearMonthFormats = ["LLyyyy", "L/yyyy", "L/yy", "LL/yyyy", "LL/yy", "L.yyyy", "LL.yyyy"];
var parseLuxonDateFormat = ["ddLLyyyy", "d/LL/yyyy", "d/L/yyyy", "dd/L/yyyy", "d/LL/yyyy", "d.L.yyyy", "dd.L.yyyy", "d.LL.yyyy", "d/L/yy", "dd/L/yy", "d/LL/yy", "d.L.yy", "dd.L.yy", "d.LL.yy"];
var ONEMRVA_MAT_NATIVE_DATE_FORMAT = {
  parse: {
    dateInput: parseNativeDateFormats
  },
  display: {
    dateInput: "dd/MM/yyyy",
    monthYearLabel: "LL / yyyy",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "MMMM yyyy"
  }
};
var ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT = {
  parse: {
    dateInput: parseNativeYearMonthFormats
  },
  display: {
    dateInput: "dd/MM/yyyy",
    monthYearLabel: "LL / yyyy",
    dateA11yLabel: "LL",
    monthYearA11yLabel: "MMM yyyy"
  }
};
var ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS = {
  parse: {
    dateInput: parseLuxonYearMonthFormats
  },
  display: {
    dateInput: "LL/yyyy",
    monthYearLabel: "LLL yyyy",
    dateA11yLabel: "DDD",
    monthYearA11yLabel: "LLLL yyyy"
  }
};
var ONEMRVA_MAT_LUXON_DATE_FORMATS = {
  parse: {
    dateInput: parseLuxonDateFormat
  },
  display: {
    dateInput: "dd/LL/yyyy",
    monthYearLabel: "LLL yyyy",
    dateA11yLabel: "DDD",
    monthYearA11yLabel: "LLL yyyy"
  }
};
var CDN_URL_MODE = new InjectionToken("This is cdn url that will be used (val/prod)");
var WebComponentOverlayContainer = class _WebComponentOverlayContainer {
  constructor(document2, _platform) {
    this._platform = _platform;
    this._containerElement = null;
    this._document = document2;
  }
  ngOnDestroy() {
    this._containerElement?.remove();
  }
  /**
   * This method returns the overlay container element. It will lazily
   * create the element the first time it is called to facilitate using
   * the container in non-browser environments.
   * @returns the container element
   */
  getContainerElement() {
    if (this._containerElement === null) {
      this._createContainer();
    }
    return this._containerElement;
  }
  /**
   * Create the overlay container element, which is simply a div
   * with the 'cdk-overlay-container' class on the document body.
   */
  _createContainer() {
    const containerClass = "cdk-overlay-container";
    if (this._platform.isBrowser || _isTestEnvironment()) {
      const oppositePlatformContainers = this._document.querySelectorAll(`.${containerClass}[platform="server"], .${containerClass}[platform="test"]`);
      oppositePlatformContainers.forEach((platform) => {
        platform.remove();
      });
    }
    const containerwrap = this._document.createElement("div");
    containerwrap.classList.add("onemrva-theme");
    const container = this._document.createElement("div");
    container.classList.add(containerClass);
    if (_isTestEnvironment()) {
      container.setAttribute("platform", "test");
    } else if (!this._platform.isBrowser) {
      container.setAttribute("platform", "server");
    }
    containerwrap.appendChild(container);
    this._document.body.appendChild(containerwrap);
    this._containerElement = container;
  }
  static {
    this.ɵfac = function WebComponentOverlayContainer_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _WebComponentOverlayContainer)(ɵɵinject(DOCUMENT), ɵɵinject(Platform));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _WebComponentOverlayContainer,
      factory: _WebComponentOverlayContainer.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(WebComponentOverlayContainer, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: void 0,
    decorators: [{
      type: Inject,
      args: [DOCUMENT]
    }]
  }, {
    type: Platform
  }], null);
})();
var DefaultStorage = class {
  constructor() {
    this.storage = /* @__PURE__ */ new Map();
  }
  getItem(key) {
    return this.storage.get(key);
  }
  setItem(key, item) {
    this.storage.set(key, item);
  }
  deleteItem(key) {
    this.storage.delete(key);
  }
};
var RequestTimes = class {
  constructor() {
    this.storage = /* @__PURE__ */ new Map();
  }
  getItem(key) {
    return this.storage.get(key);
  }
  setItem(key, item) {
    this.storage.set(key, item);
  }
  deleteItem(key) {
    this.storage.delete(key);
  }
};
var HttpRequestCache = (optionsHandler) => {
  return (target, methodName, descriptor) => {
    if (!(descriptor?.value instanceof Function)) {
      throw Error(`'@HttpRequestCache' can be applied only to the class method which returns an Observable`);
    }
    const cacheKeyPrefix = `${target.constructor.name}_${methodName}`;
    const originalMethod = descriptor.value;
    const working = {};
    let subscribers = 0;
    descriptor.value = function(...args) {
      const options = optionsHandler?.call(this, this, ...args);
      if (!options?.storage && !target._____storage_____) {
        target._____storage_____ = new DefaultStorage();
      }
      if (options?.ttl && !target._____ttl_storage_____) {
        target._____ttl_storage_____ = new RequestTimes();
      }
      const storage = options?.storage ?? target._____storage_____;
      const key = `${cacheKeyPrefix}_${JSON.stringify(args)}`;
      let ttl = void 0;
      if (options?.ttl) {
        ttl = target._____ttl_storage_____.getItem(key);
        if (!ttl) {
          ttl = {
            requestTime: Date.now(),
            subject: new Subject()
          };
        } else if (ttl.requestTime + options.ttl <= Date.now()) {
          working[key] = true;
          ttl.requestTime = Date.now();
          ttl.subject.next();
        }
        target._____ttl_storage_____.setItem(key, ttl);
      }
      const refreshOn = merge(options?.refreshOn ?? NEVER, ttl?.subject ?? NEVER);
      return storage.getItem(key).pipe(take(1), tap((value) => {
        subscribers++;
        if (value === null) throw "";
      }), catchError(() => {
        const observable = refreshOn.pipe(startWith(true), switchMap(() => originalMethod.apply(this, [...args])), tap(() => {
          delete working[key];
        }), shareReplay({
          bufferSize: 1,
          refCount: options?.refCount ?? false,
          windowTime: options?.windowTime ?? Infinity
        }), filter(() => {
          return !working[key];
        }), finalize(() => {
          subscribers--;
          if (subscribers === 0 && options?.refCount) {
            storage.deleteItem(key);
            target._____ttl_storage_____?.deleteItem(key);
          }
        }));
        storage.setItem(key, observable);
        return observable;
      }));
    };
    return descriptor;
  };
};
var DigitOnlyDirective = class _DigitOnlyDirective {
  onKeyDown(event) {
    const e = event;
    const allowedKey = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", ".", ",", " ", "Backspace", "Delete", "Tab", "-", "Enter", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "Home", "End"];
    if (allowedKey.indexOf(e.key) >= 0) return;
    const allowedCtrlShortcuts = ["a", "c", "x", "v"];
    if (e.ctrlKey && allowedCtrlShortcuts.indexOf(e.key) >= 0) return;
    e.preventDefault();
  }
  static {
    this.ɵfac = function DigitOnlyDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _DigitOnlyDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _DigitOnlyDirective,
      selectors: [["", "digitOnly", ""]],
      hostBindings: function DigitOnlyDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("keydown", function DigitOnlyDirective_keydown_HostBindingHandler($event) {
            return ctx.onKeyDown($event);
          });
        }
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(DigitOnlyDirective, [{
    type: Directive,
    args: [{
      selector: "[digitOnly]",
      standalone: true
    }]
  }], null, {
    onKeyDown: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }]
  });
})();
var MatRowClickableDirective = class _MatRowClickableDirective {
  constructor() {
    this.matRowClickable = new EventEmitter();
    this.color = "";
    this.cssClass = true;
  }
  click() {
    this.matRowClickable.emit();
  }
  /** @hidden @internal */
  get _isClickable() {
    return this.matRowClickable.observed;
  }
  /** @hidden @internal */
  get _isPrimary() {
    return this.color === OnemrvaMatColor.PRIMARY;
  }
  /** @hidden @internal */
  get _isAccent() {
    return this.color === OnemrvaMatColor.ACCENT;
  }
  /** @hidden @internal */
  get _isError() {
    return this.color === OnemrvaMatColor.ERROR;
  }
  /** @hidden @internal */
  get _isWarn() {
    return this.color === OnemrvaMatColor.WARN;
  }
  /** @hidden @internal */
  get _isSuccess() {
    return this.color === OnemrvaMatColor.SUCCESS;
  }
  /** @hidden @internal */
  get _isInfo() {
    return this.color === OnemrvaMatColor.INFO;
  }
  static {
    this.ɵfac = function MatRowClickableDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _MatRowClickableDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _MatRowClickableDirective,
      selectors: [["tr", "matRowClickable", ""]],
      hostVars: 16,
      hostBindings: function MatRowClickableDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("click", function MatRowClickableDirective_click_HostBindingHandler($event) {
            return ctx.click($event);
          });
        }
        if (rf & 2) {
          ɵɵclassProp("clickable", ctx._isClickable)("onemrva-clickable-row", ctx.cssClass)("mat-primary", ctx._isPrimary)("mat-accent", ctx._isAccent)("mat-error", ctx._isError)("mat-warn", ctx._isWarn)("mat-success", ctx._isSuccess)("mat-info", ctx._isInfo);
        }
      },
      inputs: {
        color: "color"
      },
      outputs: {
        matRowClickable: "matRowClickable"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(MatRowClickableDirective, [{
    type: Directive,
    args: [{
      selector: "tr[matRowClickable]",
      standalone: true
    }]
  }], null, {
    matRowClickable: [{
      type: Output
    }],
    color: [{
      type: Input
    }],
    click: [{
      type: HostListener,
      args: ["click", ["$event"]]
    }],
    _isClickable: [{
      type: HostBinding,
      args: ["class.clickable"]
    }],
    cssClass: [{
      type: HostBinding,
      args: ["class.onemrva-clickable-row"]
    }],
    _isPrimary: [{
      type: HostBinding,
      args: ["class.mat-primary"]
    }],
    _isAccent: [{
      type: HostBinding,
      args: ["class.mat-accent"]
    }],
    _isError: [{
      type: HostBinding,
      args: ["class.mat-error"]
    }],
    _isWarn: [{
      type: HostBinding,
      args: ["class.mat-warn"]
    }],
    _isSuccess: [{
      type: HostBinding,
      args: ["class.mat-success"]
    }],
    _isInfo: [{
      type: HostBinding,
      args: ["class.mat-info"]
    }]
  });
})();
var IfWidthIsDirective = class _IfWidthIsDirective {
  constructor(breakpointObserver, _templateRef, _viewContainer) {
    this.breakpointObserver = breakpointObserver;
    this._templateRef = _templateRef;
    this._viewContainer = _viewContainer;
  }
  ngOnInit() {
    this.breakpointObserver.observe([...this.ifWidthIs]).subscribe((state) => {
      this._viewContainer.clear();
      if (state.matches) {
        this._viewContainer.createEmbeddedView(this._templateRef);
      }
    });
  }
  static {
    this.ɵfac = function IfWidthIsDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _IfWidthIsDirective)(ɵɵdirectiveInject(BreakpointObserver), ɵɵdirectiveInject(TemplateRef), ɵɵdirectiveInject(ViewContainerRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _IfWidthIsDirective,
      selectors: [["", "ifWidthIs", ""]],
      inputs: {
        ifWidthIs: "ifWidthIs"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(IfWidthIsDirective, [{
    type: Directive,
    args: [{
      selector: "[ifWidthIs]",
      standalone: true
    }]
  }], () => [{
    type: BreakpointObserver
  }, {
    type: TemplateRef
  }, {
    type: ViewContainerRef
  }], {
    ifWidthIs: [{
      type: Input
    }]
  });
})();
var OnemRvaColorDirective = class _OnemRvaColorDirective {
  constructor() {
    this.color = "";
  }
  /** @hidden @internal */
  get _isPrimary() {
    return this.color === OnemrvaMatColor.PRIMARY;
  }
  /** @hidden @internal */
  get _isAccent() {
    return this.color === OnemrvaMatColor.ACCENT;
  }
  /** @hidden @internal */
  get _isError() {
    return this.color === OnemrvaMatColor.ERROR;
  }
  /** @hidden @internal */
  get _isWarn() {
    return this.color === OnemrvaMatColor.WARN;
  }
  /** @hidden @internal */
  get _isSuccess() {
    return this.color === OnemrvaMatColor.SUCCESS;
  }
  /** @hidden @internal */
  get _isInfo() {
    return this.color === OnemrvaMatColor.INFO;
  }
  static {
    this.ɵfac = function OnemRvaColorDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaColorDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemRvaColorDirective,
      selectors: [["mat-card", "color", ""], ["mat-chip", "color", ""]],
      hostVars: 12,
      hostBindings: function OnemRvaColorDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("mat-primary", ctx._isPrimary)("mat-accent", ctx._isAccent)("mat-error", ctx._isError)("mat-warn", ctx._isWarn)("mat-success", ctx._isSuccess)("mat-info", ctx._isInfo);
        }
      },
      inputs: {
        color: "color"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaColorDirective, [{
    type: Directive,
    args: [{
      selector: "mat-card[color],mat-chip[color]",
      standalone: true
    }]
  }], null, {
    color: [{
      type: Input
    }],
    _isPrimary: [{
      type: HostBinding,
      args: ["class.mat-primary"]
    }],
    _isAccent: [{
      type: HostBinding,
      args: ["class.mat-accent"]
    }],
    _isError: [{
      type: HostBinding,
      args: ["class.mat-error"]
    }],
    _isWarn: [{
      type: HostBinding,
      args: ["class.mat-warn"]
    }],
    _isSuccess: [{
      type: HostBinding,
      args: ["class.mat-success"]
    }],
    _isInfo: [{
      type: HostBinding,
      args: ["class.mat-info"]
    }]
  });
})();
var UNDO_STACK_MAX_LENGTH = 50;
var OnemrvaMaskDirective = class _OnemrvaMaskDirective {
  onKeyDown(event) {
    const e = event;
    let specialKeys = ["Tab", "Enter", "ArrowLeft", "ArrowRight", "ArrowUp", "ArrowDown", "Home", "End"];
    if (specialKeys.indexOf(e.key) >= 0) return;
    specialKeys = ["Backspace", "Delete"];
    if (specialKeys.indexOf(e.key) >= 0) {
      this.markForDelete = true;
      return;
    }
    if (e.ctrlKey && "z" === e.key && this.stackIdx > 0) {
      this.stackIdx--;
      this.el.nativeElement.value = this.inputStack[this.stackIdx];
      event.preventDefault();
    }
    if (e.ctrlKey && "u" === e.key && this.stackIdx < this.inputStack.length - 1) {
      this.stackIdx++;
      this.el.nativeElement.value = this.inputStack[this.stackIdx];
      event.preventDefault();
    }
    const allowedCtrlShortcuts = ["a", "c", "x", "v"];
    if (e.ctrlKey && allowedCtrlShortcuts.indexOf(e.key) >= 0) return;
  }
  onInput(event) {
    const selectionStart = event.target?.selectionStart;
    if (this.markForDelete) {
      const isCompliantAfterDeletion = [...this.el.nativeElement.value].findIndex((char, idx2) => {
        const rule = this.onemrvamask[idx2];
        if (rule === null || rule === void 0) return true;
        if (isNaN(char) && char.toLowerCase() === char.toUpperCase()) return char !== rule;
        return !isAllowed(rule, char);
      }) < 0;
      if (isCompliantAfterDeletion) {
        this.markForDelete = false;
        return;
      }
    }
    const compliantValue = [...this.el.nativeElement.value].filter((char) => isAllowed("A", char)).reduce((newValue, char) => {
      let idx2 = newValue.length;
      const rule = this.onemrvamask[idx2];
      if (rule === null || rule === void 0) return newValue;
      newValue = !isAllowed(rule, char) ? newValue : newValue + char;
      let nextRule2 = this.onemrvamask[++idx2];
      let i = 0;
      while (nextRule2 !== null && nextRule2 !== void 0 && ["0", "A", "S", "U", "L"].indexOf(nextRule2) < 0) {
        i++;
        newValue += nextRule2;
        nextRule2 = this.onemrvamask[++idx2];
        if (i > 50) break;
      }
      return newValue;
    }, "");
    this.el.nativeElement.value = compliantValue;
    let idx = selectionStart;
    let nextRule = this.onemrvamask[idx];
    while (nextRule !== null && nextRule !== void 0 && ["0", "A", "S", "U", "L"].indexOf(nextRule) < 0) {
      idx++;
      nextRule = this.onemrvamask[idx];
      if (nextRule !== null && nextRule && ["0", "A", "S", "U", "L"].indexOf(nextRule) < 0) {
      } else break;
    }
    if (compliantValue !== this.inputStack[this.stackIdx]) {
      this.stackIdx++;
      const stack = this.stackIdx > UNDO_STACK_MAX_LENGTH - 1 ? this.inputStack.slice(1) : this.inputStack.splice(0, this.stackIdx);
      this.stackIdx = this.stackIdx > UNDO_STACK_MAX_LENGTH - 1 ? UNDO_STACK_MAX_LENGTH - 1 : this.stackIdx;
      this.inputStack = [...stack, compliantValue];
      this.el.nativeElement.setSelectionRange(idx, idx);
    } else {
      if (this.markForDelete) {
        let i = idx - 1;
        while (i > 0) {
          const previousRule = this.onemrvamask[i - 1];
          if (["0", "A", "S", "U", "L"].indexOf(previousRule) < 0) {
            i--;
          } else break;
        }
        this.el.nativeElement.setSelectionRange(i, i);
      }
    }
    this.markForDelete = false;
  }
  constructor(el) {
    this.el = el;
    this.inputStack = [""];
    this.stackIdx = 0;
    this.markForDelete = false;
  }
  static {
    this.ɵfac = function OnemrvaMaskDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaMaskDirective)(ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemrvaMaskDirective,
      selectors: [["", "onemrvamask", ""]],
      hostBindings: function OnemrvaMaskDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("keydown", function OnemrvaMaskDirective_keydown_HostBindingHandler($event) {
            return ctx.onKeyDown($event);
          })("input", function OnemrvaMaskDirective_input_HostBindingHandler($event) {
            return ctx.onInput($event);
          });
        }
      },
      inputs: {
        onemrvamask: "onemrvamask"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaMaskDirective, [{
    type: Directive,
    args: [{
      selector: "[onemrvamask]",
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }], {
    onemrvamask: [{
      type: Input
    }],
    onKeyDown: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }],
    onInput: [{
      type: HostListener,
      args: ["input", ["$event"]]
    }]
  });
})();
function isAllowed(rule, character) {
  switch (rule) {
    // Any digit
    case "0":
      if (character === " ") return false;
      return !isNaN(+character);
    // A: letters (uppercase or lowercase) and digits
    case "A":
      if (character === " ") return false;
      return !isNaN(+character) || character.toLowerCase() != character.toUpperCase();
    // only letters (uppercase or lowercase)
    case "S":
      return character.toLowerCase() != character.toUpperCase();
    //  only uppercase letters
    case "U":
      return character.toLowerCase() != character.toUpperCase() && character === character.toUpperCase();
    //  only lowercase letters
    case "L":
      return character.toLowerCase() != character.toUpperCase() && character === character.toLowerCase();
  }
  return false;
}
var OnemRvaClipboardDirective = class _OnemRvaClipboardDirective {
  constructor(elementRef, renderer, factory, vcRef, clipboardService) {
    this.elementRef = elementRef;
    this.renderer = renderer;
    this.factory = factory;
    this.vcRef = vcRef;
    this.clipboardService = clipboardService;
    this.class = "";
    this.clipboard = "";
    const miFactory = this.factory.resolveComponentFactory(ClipboardIconComponent);
    this.icon = this.vcRef.createComponent(miFactory);
    this.iconEl = this.icon.injector.get(ClipboardIconComponent)._elementRef.nativeElement;
  }
  ngOnInit() {
    this.vcRef.clear();
    this.renderer.appendChild(this.elementRef.nativeElement, this.iconEl);
  }
  static {
    this.ɵfac = function OnemRvaClipboardDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaClipboardDirective)(ɵɵdirectiveInject(ElementRef), ɵɵdirectiveInject(Renderer2), ɵɵdirectiveInject(ComponentFactoryResolver$1), ɵɵdirectiveInject(ViewContainerRef), ɵɵdirectiveInject(Clipboard));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemRvaClipboardDirective,
      selectors: [["span", "clipboard", ""]],
      hostVars: 2,
      hostBindings: function OnemRvaClipboardDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassMap(ctx.class);
        }
      },
      inputs: {
        clipboard: "clipboard"
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaClipboardDirective, [{
    type: Directive,
    args: [{
      selector: "span[clipboard]",
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }, {
    type: Renderer2
  }, {
    type: ComponentFactoryResolver$1
  }, {
    type: ViewContainerRef
  }, {
    type: Clipboard
  }], {
    class: [{
      type: HostBinding,
      args: ["class"]
    }],
    clipboard: [{
      type: Input
    }]
  });
})();
var OnemRvaIconRightDirective = class _OnemRvaIconRightDirective {
  constructor() {
    this.class = true;
  }
  static {
    this.ɵfac = function OnemRvaIconRightDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaIconRightDirective)();
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemRvaIconRightDirective,
      selectors: [["button", "iconRight", ""]],
      hostVars: 2,
      hostBindings: function OnemRvaIconRightDirective_HostBindings(rf, ctx) {
        if (rf & 2) {
          ɵɵclassProp("onemrva-icon-right", ctx.class);
        }
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaIconRightDirective, [{
    type: Directive,
    args: [{
      selector: "button[iconRight]",
      standalone: true
    }]
  }], null, {
    class: [{
      type: HostBinding,
      args: ["class.onemrva-icon-right"]
    }]
  });
})();
var OnemrvaDateFormatDirective = class _OnemrvaDateFormatDirective {
  constructor(el) {
    this.el = el;
    this.template = "__/__/____";
  }
  onPaste(event) {
    const input = this.el.nativeElement;
    const pasted = event.clipboardData?.getData("text/plain") || "";
    const digitsOnly = pasted.replace(/\D/g, "");
    if (!digitsOnly) {
      event.preventDefault();
      return;
    }
    event.preventDefault();
    let pos = input.selectionStart ?? 0;
    const chars = input.value.split("");
    let digitIndex = 0;
    while (pos < this.template.length && digitIndex < digitsOnly.length) {
      if (this.template[pos] === "/") {
        pos++;
        continue;
      }
      chars[pos] = digitsOnly[digitIndex++];
      pos++;
    }
    input.value = chars.join("");
    this.emitInputEvent(input);
    this.setCaretToNextEditable(pos);
  }
  onCut(event) {
    const input = this.el.nativeElement;
    const start = input.selectionStart ?? 0;
    const end = input.selectionEnd ?? 0;
    const cutText = input.value.substring(start, end);
    const chars = input.value.split("");
    for (let i = start; i < end; i++) {
      if (this.template[i] !== "/") {
        chars[i] = "_";
      }
    }
    input.value = chars.join("");
    this.emitInputEvent(input);
    this.setCaretToNextEditable(start);
    event.preventDefault();
    if (event.clipboardData) {
      event.clipboardData.setData("text/plain", cutText.replace(/\//g, ""));
    }
  }
  onFocus() {
    const input = this.el.nativeElement;
    if (!input.value || input.value === this.template) {
      input.value = this.template;
    }
    this.setCaretToNextEditable(0);
  }
  onClick() {
    const input = this.el.nativeElement;
    const selectionStart = input.selectionStart ?? 0;
    const selectionEnd = input.selectionEnd ?? 0;
    if (selectionStart === selectionEnd) {
      if (input.value === this.template) {
        this.setCaretToNextEditable(0);
      } else {
        this.setCaretToNextEditable(selectionStart);
      }
    }
  }
  onKeyDown(event) {
    const input = this.el.nativeElement;
    let pos = input.selectionStart ?? 0;
    const allowedCtrlShortcuts = ["a", "c", "x", "v"];
    if (event.ctrlKey && allowedCtrlShortcuts.indexOf(event.key) >= 0) {
      console.log(input.value);
      if (event.key === "x" && input.value === "") {
        console.log(event.key);
        input.value = this.template;
        this.emitInputEvent(input);
      }
      return;
    }
    if (["ArrowLeft", "ArrowRight", "Tab"].includes(event.key)) return;
    if (event.key === "Backspace" || event.key === "Delete") {
      event.preventDefault();
      const start = input.selectionStart ?? 0;
      const end = input.selectionEnd ?? start;
      if (end > start) {
        const chars = input.value.split("");
        for (let i = start; i < end; i++) {
          if (this.template[i] !== "/") {
            chars[i] = "_";
          }
        }
        input.value = chars.join("");
        this.emitInputEvent(input);
        this.setCaretToNextEditable(start);
        return;
      }
      let pos2 = start;
      if (event.key === "Backspace" && pos2 > 0) {
        do {
          pos2--;
        } while (this.template[pos2] === "/" && pos2 > 0);
        this.replaceCharAt(input, pos2, "_");
        this.emitInputEvent(input);
        this.setCaretToNextEditable(pos2);
        return;
      }
      if (event.key === "Delete" && pos2 < this.template.length) {
        while (this.template[pos2] === "/" && pos2 < this.template.length) {
          pos2++;
        }
        this.replaceCharAt(input, pos2, "_");
        this.emitInputEvent(input);
        this.setCaretToNextEditable(pos2);
        return;
      }
    }
    if (event.key === "/" || event.key === "." || event.key === "-" || event.key === " ") {
      event.preventDefault();
      if (pos === 1 || pos === 4) {
        const currentVal = input.value.substring(pos - 1, pos);
        if (!currentVal.includes("_")) {
          this.replaceCharAt(input, pos - 1, "0");
          this.replaceCharAt(input, pos, currentVal);
          this.setCaretToNextEditable(pos + 2);
          return;
        }
      }
      this.setCaretToNextEditable(pos + 1);
      return;
    }
    if (event.key === "Backspace") {
      event.preventDefault();
      if (pos === 0) return;
      do {
        pos--;
      } while (this.template[pos] === "/" && pos > 0);
      this.replaceCharAt(input, pos, "_");
      this.setCaretToNextEditable(pos);
      this.emitInputEvent(input);
      return;
    }
    if (!/^\d$/.test(event.key)) {
      event.preventDefault();
      return;
    }
    if (pos >= this.template.length) {
      event.preventDefault();
      return;
    }
    while (this.template[pos] === "/" && pos < this.template.length) {
      pos++;
    }
    this.replaceCharAt(input, pos, event.key);
    event.preventDefault();
    this.setCaretToNextEditable(pos + 1);
    this.emitInputEvent(input);
  }
  emitInputEvent(input) {
    input.dispatchEvent(new Event("input", {
      bubbles: true
    }));
  }
  onBlur() {
  }
  replaceCharAt(input, index, char) {
    const value = input.value.split("");
    value[index] = char;
    input.value = value.join("");
  }
  setCaretToNextEditable(pos) {
    const input = this.el.nativeElement;
    while (this.template[pos] === "/" && pos < this.template.length) {
      pos++;
    }
    input.setSelectionRange(pos, pos);
  }
  sanitizeDateInput(input) {
    if (typeof input !== "string") return "";
    return input.replace(/[.\s-]/g, "/").replace(/[^0-9/]/g, "").replace("//", "");
  }
  static {
    this.ɵfac = function OnemrvaDateFormatDirective_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaDateFormatDirective)(ɵɵdirectiveInject(ElementRef));
    };
  }
  static {
    this.ɵdir = ɵɵdefineDirective({
      type: _OnemrvaDateFormatDirective,
      selectors: [["", "onemrvaDateFormat", ""]],
      hostBindings: function OnemrvaDateFormatDirective_HostBindings(rf, ctx) {
        if (rf & 1) {
          ɵɵlistener("paste", function OnemrvaDateFormatDirective_paste_HostBindingHandler($event) {
            return ctx.onPaste($event);
          })("cut", function OnemrvaDateFormatDirective_cut_HostBindingHandler($event) {
            return ctx.onCut($event);
          })("focus", function OnemrvaDateFormatDirective_focus_HostBindingHandler() {
            return ctx.onFocus();
          })("click", function OnemrvaDateFormatDirective_click_HostBindingHandler() {
            return ctx.onClick();
          })("keydown", function OnemrvaDateFormatDirective_keydown_HostBindingHandler($event) {
            return ctx.onKeyDown($event);
          })("blur", function OnemrvaDateFormatDirective_blur_HostBindingHandler() {
            return ctx.onBlur();
          });
        }
      }
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaDateFormatDirective, [{
    type: Directive,
    args: [{
      selector: "[onemrvaDateFormat]",
      standalone: true
    }]
  }], () => [{
    type: ElementRef
  }], {
    onPaste: [{
      type: HostListener,
      args: ["paste", ["$event"]]
    }],
    onCut: [{
      type: HostListener,
      args: ["cut", ["$event"]]
    }],
    onFocus: [{
      type: HostListener,
      args: ["focus"]
    }],
    onClick: [{
      type: HostListener,
      args: ["click"]
    }],
    onKeyDown: [{
      type: HostListener,
      args: ["keydown", ["$event"]]
    }],
    onBlur: [{
      type: HostListener,
      args: ["blur"]
    }]
  });
})();
var directives = [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective];
function setTranslationLanguage(translateService, languages = ["en", "fr", "nl", "de"], default_language = "en") {
  return () => __async(this, null, function* () {
    translateService.onLangChange.subscribe((value) => {
      localStorage.setItem("language", value.lang);
    });
    translateService.addLangs(languages);
    let language = localStorage.getItem("language");
    if (language === null || languages.indexOf(language) < 0) {
      if (languages.indexOf(navigator.language) >= 0) {
        language = navigator.language;
      } else {
        for (const lng of navigator.languages) {
          if (languages.indexOf(lng) >= 0) {
            language = lng;
            break;
          }
        }
        if (language === null || languages.indexOf(language) < 0) {
          language = default_language;
        }
      }
    }
    yield translateService.use(language).toPromise();
  });
}
function setTranslationLanguageFromWO(translateService, languages = ["fr", "nl"], default_language = "fr") {
  return () => __async(this, null, function* () {
    translateService.addLangs(languages);
    let language = localStorage.getItem("be_social_security_workenv_language");
    if (language !== null) {
      language = language.toLowerCase();
      if (languages.indexOf(language) >= 0) {
        yield translateService.use(language).toPromise();
        return;
      }
    }
    yield translateService.use(default_language).toPromise();
  });
}
var OnemrvaTranslateCDNLoader = class {
  constructor(cdn, projects = [], prefix = "/i18n/", suffix = ".json") {
    this.cdn = cdn;
    this.projects = projects;
    this.prefix = prefix;
    this.suffix = suffix;
  }
  /**
   * Gets the translations from the server
   */
  getTranslation(lang) {
    return this.cdn.getTranslations(this.projects, lang, this.prefix, this.suffix);
  }
};
var OnemrvaTranslateHttpLoader = class {
  constructor(http, prefix = "/assets/i18n/", suffix = ".json", modules = []) {
    this.http = http;
    this.prefix = prefix;
    this.suffix = suffix;
    this.modules = modules;
  }
  /**
   * Gets the translations from the server
   */
  getTranslation(lang) {
    const observables = [this.http.get(`${this.prefix}${lang}${this.suffix}`).pipe(catchError(() => of(null))), ...this.modules.map((m) => {
      this.http.get(`${this.prefix}${m}/${lang}${this.suffix}`).pipe(catchError(() => of(null)));
    })];
    return forkJoin(observables).pipe(map((all) => {
      return all.filter((v) => !!v).reduce((s, c) => __spreadValues(__spreadValues({}, s), c), {});
    }));
  }
};
var OnemrvaBcePipe = class _OnemrvaBcePipe {
  transform(value) {
    const strOut = value.trim().replace(/\/|\.|-/g, "");
    if (strOut.length !== 10) return "?01?.???.???";
    return `${strOut.substring(0, 4)}.${strOut.substring(4, 7)}.${strOut.substring(7, 10)}`;
  }
  static {
    this.ɵfac = function OnemrvaBcePipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaBcePipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "onemrvaBce",
      type: _OnemrvaBcePipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaBcePipe, [{
    type: Pipe,
    args: [{
      name: "onemrvaBce",
      standalone: true
    }]
  }], null, null);
})();
var OnemrvaNissPipe = class _OnemrvaNissPipe {
  transform(value) {
    const strOut = value.trim().replace(/\/|\.|-/g, "");
    if (strOut.length !== 11) return "??01??/???-??";
    return `${strOut.substring(0, 6)}/${strOut.substring(6, 9)}-${strOut.substring(9, 11)}`;
  }
  static {
    this.ɵfac = function OnemrvaNissPipe_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaNissPipe)();
    };
  }
  static {
    this.ɵpipe = ɵɵdefinePipe({
      name: "onemrvaNiss",
      type: _OnemrvaNissPipe,
      pure: true
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaNissPipe, [{
    type: Pipe,
    args: [{
      name: "onemrvaNiss",
      standalone: true
    }]
  }], null, null);
})();
var LOOKUP_COUNTRY_SERVICE_URL = new InjectionToken("LOOKUP_COUNTRY_SERVICE_URL", {
  providedIn: "root",
  factory: () => LOOKUP_COUNTRY_URL
});
var CommonCountryLookupService = class _CommonCountryLookupService {
  constructor(http, commonCountryServiceURL) {
    this.http = http;
    this.commonCountryServiceURL = commonCountryServiceURL;
    this._countries$ = new ReplaySubject(1);
    this._customersInitialized = false;
  }
  getCountries(refresh = false) {
    if (refresh || !this._customersInitialized) {
      this._customersInitialized = true;
      this.http.get(this.commonCountryServiceURL).subscribe((countries) => {
        this._countries$.next(countries);
      });
    }
    return this._countries$.pipe(skip(+refresh), distinctUntilChanged());
  }
  getCountryByCode(code) {
    return this._countries$.pipe(map((countries) => {
      const country = countries.find((country2) => country2.code === code);
      return country || null;
    }));
  }
  static {
    this.ɵfac = function CommonCountryLookupService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _CommonCountryLookupService)(ɵɵinject(HttpClient), ɵɵinject(LOOKUP_COUNTRY_SERVICE_URL));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _CommonCountryLookupService,
      factory: _CommonCountryLookupService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CommonCountryLookupService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: HttpClient
  }, {
    type: void 0,
    decorators: [{
      type: Inject,
      args: [LOOKUP_COUNTRY_SERVICE_URL]
    }]
  }], null);
})();
var CacheService = class _CacheService {
  setItem(key, item) {
    item.subscribe((value) => {
      localStorage.setItem(key, JSON.stringify(value));
    });
  }
  getItem(key) {
    const r = localStorage.getItem(key);
    if (r === null) {
      return of(null);
    }
    return of(JSON.parse(r));
  }
  deleteItem(key) {
    localStorage.removeItem(key);
  }
  static {
    this.ɵfac = function CacheService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _CacheService)();
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _CacheService,
      factory: _CacheService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(CacheService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], null, null);
})();
var OnemRvaCDNService = class _OnemRvaCDNService {
  constructor(rendererFactory, cacheService, http, cdnUrlMode) {
    this.rendererFactory = rendererFactory;
    this.cacheService = cacheService;
    this.http = http;
    this.refresh$ = new Subject();
    this.renderer = this.rendererFactory.createRenderer(null, null);
    switch (cdnUrlMode) {
      case CDNUrlModeOptions.PROD:
        this.cdnUrl = CDN_URLS.prod;
        break;
      case CDNUrlModeOptions.VAL:
        this.cdnUrl = CDN_URLS.val;
        break;
      default:
        this.cdnUrl = CDN_URLS.prod;
    }
    const linkElement = this.renderer.createElement("link");
    this.renderer.setAttribute(linkElement, "rel", "preconnect");
    this.renderer.setAttribute(linkElement, "href", this.cdnUrl);
    this.renderer.appendChild(document.head, linkElement);
  }
  getUrl(path = "") {
    return this.cdnUrl + path;
  }
  getImg(path = "") {
    return this.getUrl("/img/" + path);
  }
  getOri(path = "") {
    return this.getImg("ori/" + path);
  }
  fetch(endpoint) {
    return this.http.get(this.cdnUrl + endpoint);
  }
  fetchNoCache(endpoint) {
    return this.http.get(this.cdnUrl + endpoint);
  }
  getTranslations(projects, lang, prefix = "", suffix = "") {
    const observables = [this.http.get(`${this.cdnUrl}${prefix}design-system/design-system/${lang}${suffix}`).pipe(catchError(() => of({}))), ...projects.map((m) => {
      return this.http.get(`${this.cdnUrl}${prefix}${m}/${lang}${suffix}`).pipe(catchError(() => {
        return of({});
      }));
    })];
    return forkJoin(observables).pipe(map((all) => {
      return all.filter((v) => !!v).reduce((s, c) => __spreadValues(__spreadValues({}, s), c), {});
    }));
  }
  /**
   * Loads a CSS file dynamically into the document head.
   * @param href The URL of the CSS file.
   * @returns A promise that resolves when the CSS file is loaded.
   * //
   */
  loadCss(href) {
    return new Promise((resolve, reject) => {
      const existingLink = document.querySelector(`link[href="${href}"]`);
      if (existingLink) {
        resolve();
        return;
      }
      const link = this.renderer.createElement("link");
      this.renderer.setAttribute(link, "rel", "stylesheet");
      this.renderer.setAttribute(link, "type", "text/css");
      this.renderer.setAttribute(link, "href", href);
      link.onload = () => resolve();
      link.onerror = () => reject(new Error(`Failed to load CSS: ${href}`));
      this.renderer.appendChild(document.head, link);
    });
  }
  static {
    this.ɵfac = function OnemRvaCDNService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaCDNService)(ɵɵinject(RendererFactory2), ɵɵinject(CacheService), ɵɵinject(HttpClient), ɵɵinject(CDN_URL_MODE, 8));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemRvaCDNService,
      factory: _OnemRvaCDNService.ɵfac,
      providedIn: "root"
    });
  }
};
__decorate([HttpRequestCache((cdnService) => ({
  refreshOn: cdnService.refresh$,
  storage: cdnService.cacheService,
  ttl: 36e5
}))], OnemRvaCDNService.prototype, "fetch", null);
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaCDNService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: RendererFactory2
  }, {
    type: CacheService
  }, {
    type: HttpClient
  }, {
    type: CDNUrlModeOptions,
    decorators: [{
      type: Optional
    }, {
      type: Inject,
      args: [CDN_URL_MODE]
    }]
  }], {
    fetch: []
  });
})();
var OnemRvaCDNCountryService = class _OnemRvaCDNCountryService {
  constructor(cdnService, translateService) {
    this.cdnService = cdnService;
    this.translateService = translateService;
  }
  getCountries() {
    return this.cdnService.fetch("/json/countries.json").pipe(
      map((response) => response),
      // ✅ Explicitly cast response
      combineLatestWith(from(this.translateService.onLangChange).pipe(startWith(this.translateService.currentLang), map((event) => typeof event === "string" ? event : event.lang))),
      map(([response, currentLang]) => {
        return response.map((country) => __spreadProps(__spreadValues({}, country), {
          name: this.getTranslatedCountryName(country, currentLang)
        })).sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
      })
    );
  }
  getCountriesByZone(zone) {
    return this.getCountries().pipe(map((countries) => {
      return countries.filter((country) => country.zone === zone);
    }));
  }
  getTranslatedCountryName(country, languageCode = this.translateService.currentLang) {
    if (languageCode === "fr" || languageCode === "nl" || languageCode === "de") {
      if (country.translations[languageCode] !== void 0) return country.translations[languageCode];
    }
    return country.name;
  }
  findCountryFromCode(code) {
    return this.getCountries().pipe(map((countries) => {
      return countries.find((country) => country.code.toLowerCase() === code.toLowerCase());
    }));
  }
  findCountryFromVIESCode(code) {
    return this.getCountriesByZone("EU").pipe(map((countries) => {
      return countries.find((country) => country.pattern.substring(0, 2) === code.substring(0, 2));
    }));
  }
  findCountryFromPrefix(number) {
    return this.getCountries().pipe(map((countries) => {
      return countries.find((country) => {
        return country.dial_code === number.substring(0, country.dial_code.length);
      });
    }));
  }
  static {
    this.ɵfac = function OnemRvaCDNCountryService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaCDNCountryService)(ɵɵinject(OnemRvaCDNService), ɵɵinject(TranslateService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemRvaCDNCountryService,
      factory: _OnemRvaCDNCountryService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaCDNCountryService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: OnemRvaCDNService
  }, {
    type: TranslateService
  }], null);
})();
var OnemRvaCDNMimeService = class _OnemRvaCDNMimeService {
  static {
    this.mimetypeFile = "/json/mimetypes.json";
  }
  constructor(cdnService) {
    this.cdnService = cdnService;
  }
  getMimesForExtension(extensions) {
    return this.cdnService.fetch(_OnemRvaCDNMimeService.mimetypeFile).pipe(catchError(() => {
      return [];
    }), map((response) => {
      return response.filter((mime) => {
        return extensions.includes(mime.extension);
      }).map((mime) => {
        return mime.mimeType;
      });
    }));
  }
  getExtensionsForMime(mimTypes) {
    return this.cdnService.fetch(_OnemRvaCDNMimeService.mimetypeFile).pipe(catchError(() => {
      return [];
    }), map((response) => {
      return response.filter((mime) => {
        return mimTypes.includes(mime.mimeType);
      }).map((mime) => {
        return mime.extension;
      });
    }));
  }
  static {
    this.ɵfac = function OnemRvaCDNMimeService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaCDNMimeService)(ɵɵinject(OnemRvaCDNService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemRvaCDNMimeService,
      factory: _OnemRvaCDNMimeService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaCDNMimeService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: OnemRvaCDNService
  }], null);
})();
var OnemRvaOSMService = class _OnemRvaOSMService {
  constructor(http) {
    this.http = http;
    this.nominatimUrl = "https://nominatim.openstreetmap.org/search";
  }
  searchAddress(query, format = "json", addressdetails = 1, limit = 10) {
    const params = new HttpParams().set("q", query).set("format", format).set("addressdetails", addressdetails).set("limit", limit);
    return this.http.get(this.nominatimUrl, {
      params
    });
  }
  static {
    this.ɵfac = function OnemRvaOSMService_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemRvaOSMService)(ɵɵinject(HttpClient));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemRvaOSMService,
      factory: _OnemRvaOSMService.ɵfac,
      providedIn: "root"
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemRvaOSMService, [{
    type: Injectable,
    args: [{
      providedIn: "root"
    }]
  }], () => [{
    type: HttpClient
  }], null);
})();
var OnemrvaErrorHandler = class _OnemrvaErrorHandler {
  constructor(_snackBar) {
    this._snackBar = _snackBar;
    this.horizontalPosition = "end";
    this.verticalPosition = "bottom";
  }
  handleError(error) {
    console.error(error);
    this._snackBar.open(`Error: ${error.message}`, "", {
      duration: 5e3,
      panelClass: "mat-primary",
      horizontalPosition: this.horizontalPosition,
      verticalPosition: this.verticalPosition
    });
  }
  static {
    this.ɵfac = function OnemrvaErrorHandler_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaErrorHandler)(ɵɵinject(MatSnackBar));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemrvaErrorHandler,
      factory: _OnemrvaErrorHandler.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaErrorHandler, [{
    type: Injectable
  }], () => [{
    type: MatSnackBar
  }], null);
})();
var OnemrvaMissingTranslationHandler = class {
  handle(params) {
    return `???${params.key}???`;
  }
};
var OnemrvaValidators = class {
  static bceValidator(required = false, control) {
    if (control.value === null || control.value.trim() === "") {
      return !required ? null : {
        bceNull: true
      };
    }
    const bceCandidate = control.value.trim().replace(/\/|\.|-/g, "");
    if (bceCandidate.length !== 10) return {
      bceLengthError: {
        value: bceCandidate
      }
    };
    if (Number.isNaN(+bceCandidate)) return {
      bceNan: {
        value: bceCandidate
      }
    };
    return null;
  }
  static bce(required = false) {
    return (control) => {
      return this.bceValidator(required, control);
    };
  }
  static nissValidator(required = false, control) {
    if (control.value === null || control.value.trim() === "") {
      return !required ? null : {
        nissNull: true
      };
    }
    const nissCandidate = control.value.trim().replace(/\/|\.|-/g, "");
    if (nissCandidate.length !== 11) {
      return {
        nissLengthError: {
          value: nissCandidate
        }
      };
    }
    if (Number.isNaN(+nissCandidate)) {
      return {
        nissNan: {
          value: nissCandidate
        }
      };
    }
    const checksumValue = nissCandidate.substring(9);
    const nissNumbersToCheck = nissCandidate.substring(0, 9);
    const isValidChecksumYear1900 = 97 - +nissNumbersToCheck % 97 === +checksumValue;
    const isValidChecksumYear2000 = 97 - +`2${nissNumbersToCheck}` % 97 === +checksumValue;
    if (!isValidChecksumYear1900 && !isValidChecksumYear2000) {
      return {
        nissCheckDigitError: {
          value: nissCandidate
        }
      };
    }
    const monthString = nissCandidate.substring(2, 4);
    const month = parseInt(monthString) - 1;
    const real_month = parseInt(monthString) % 20 - 1;
    if (real_month > 11 || month > 51) {
      return {
        nissInvalidMonth: {
          value: nissCandidate
        }
      };
    }
    const day = +nissCandidate.substring(4, 6);
    if (day > 31) {
      return {
        nissInvalidDate: {
          value: nissCandidate
        }
      };
    }
    return null;
  }
  static niss(required = false) {
    return (control) => {
      return this.nissValidator(required, control);
    };
  }
  static nissOrBce(required = false) {
    return (control) => {
      if (control.value === null || control.value.trim() === "") return !required ? null : {
        nissNull: true
      };
      const nissCandidate = control.value.trim().replace(/\/|\.|-/g, "");
      if (control.value.length === 11) {
        return this.nissValidator(required, control);
      } else if (control.value.length === 10) {
        return this.bceValidator(required, control);
      }
      return {
        lengthError: {
          value: nissCandidate
        }
      };
    };
  }
};
var IBAN_SUPPORTED_COUNTRIES = Object.entries(countrySpecs).filter((value) => {
  return value[1].IBANRegistry;
}).map((value) => {
  return value[0];
});
var SEPA_ONLY_SUPPORTED_COUNTRIES = Object.entries(countrySpecs).filter((value) => {
  return value[1].SEPA;
}).map((value) => {
  return value[0];
});
var internValidateIban = (iban = "") => {
  return validateIBAN(electronicFormatIBAN(iban) || void 0);
};
var bankAccountValidator = () => {
  return (control) => {
    let finalErrorCodes = [];
    if (typeof control.getRawValue() === "string") {
      const regex = /^[a-zA-Z]{2}/;
      if (regex.test(control.getRawValue().substring(0, 2))) {
        const {
          valid: _valid,
          errorCodes
        } = internValidateIban(control.value);
        finalErrorCodes = errorCodes;
      }
    } else {
      const countryCodeValue = control.value !== null ? control.value["countryCode"] : "";
      const bankNumber = control.value !== null ? control.value["bban"] : "";
      if (countryCodeValue !== "") {
        const {
          valid: _valid,
          errorCodes
        } = internValidateIban(`${countryCodeValue}${bankNumber}`);
        finalErrorCodes = errorCodes;
      }
    }
    if (finalErrorCodes.length === 0) {
      return null;
    }
    const validationErrors = {};
    finalErrorCodes.forEach((errCode) => {
      const key = ValidationErrorsIBAN[errCode];
      validationErrors[key] = {
        value: control.getRawValue()
      };
    });
    return validationErrors;
  };
};
var OnemrvaSharedModule = class _OnemrvaSharedModule {
  static {
    this.ɵfac = function OnemrvaSharedModule_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaSharedModule)();
    };
  }
  static {
    this.ɵmod = ɵɵdefineNgModule({
      type: _OnemrvaSharedModule,
      imports: [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective],
      exports: [DigitOnlyDirective, MatRowClickableDirective, OnemRvaClipboardDirective, OnemRvaIconRightDirective, IfWidthIsDirective, OnemRvaColorDirective, OnemrvaMaskDirective, OnemrvaDateFormatDirective]
    });
  }
  static {
    this.ɵinj = ɵɵdefineInjector({});
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaSharedModule, [{
    type: NgModule,
    args: [{
      imports: [...directives],
      exports: [...directives]
    }]
  }], null, null);
})();
var OnemrvaNativeDateAdapter = class _OnemrvaNativeDateAdapter extends NativeDateAdapter {
  constructor(translateService) {
    super();
    this.translateService = translateService;
    this.destroyNotifier$ = new Subject();
    this.localeChanges = new Subject();
    this.setLocale(this.translateService.currentLang);
    this.localeChanges.next(this);
    if (this.translateService !== void 0) {
      this.translateService.onLangChange.pipe(takeUntil(this.destroyNotifier$)).subscribe(({
        lang
      }) => {
        this.setLocale(lang);
        this.localeChanges.next(this);
      });
    }
  }
  format(date, formats) {
    const dt = DateTime.fromJSDate(date);
    return dt.toFormat(formats);
  }
  parse(value) {
    const formats = ONEMRVA_MAT_NATIVE_DATE_FORMAT;
    if (value && typeof value === "string") {
      value = value.replace(/\D/g, "/");
    }
    if (value === null || value === "") {
      return null;
    }
    const dt = DateTime.fromFormat(value, formats.display.dateInput, {
      locale: this.locale
    });
    if (dt.isValid) {
      return dt.toJSDate();
    }
    for (const format of formats.parse.dateInput) {
      const parsed = DateTime.fromFormat(value, format, {
        locale: this.locale
      });
      if (parsed.isValid) {
        return parsed.toJSDate();
      }
    }
    return this.invalid();
  }
  getFirstDayOfWeek() {
    return 1;
  }
  ngOnDestroy() {
    this.destroyNotifier$.next();
    this.destroyNotifier$.complete();
  }
  static {
    this.ɵfac = function OnemrvaNativeDateAdapter_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaNativeDateAdapter)(ɵɵinject(TranslateService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemrvaNativeDateAdapter,
      factory: _OnemrvaNativeDateAdapter.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaNativeDateAdapter, [{
    type: Injectable
  }], () => [{
    type: TranslateService
  }], null);
})();
var MAT_LUXON_DATE_ADAPTER_OPTIONS = new InjectionToken("MAT_LUXON_DATE_ADAPTER_OPTIONS", {
  providedIn: "root",
  factory: MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY
});
function MAT_LUXON_DATE_ADAPTER_OPTIONS_FACTORY() {
  return {
    useUtc: false,
    firstDayOfWeek: 0,
    defaultOutputCalendar: "gregory"
  };
}
function luxonRange(length, valueFunction) {
  const valuesArray = Array(length);
  for (let i = 0; i < length; i++) {
    valuesArray[i] = valueFunction(i);
  }
  return valuesArray;
}
var OnemrvaLuxonDateAdapter = class _OnemrvaLuxonDateAdapter extends DateAdapter {
  constructor(translateService) {
    super();
    this.translateService = translateService;
    const dateLocale = inject(MAT_DATE_LOCALE, {
      optional: true
    });
    const options = inject(MAT_LUXON_DATE_ADAPTER_OPTIONS, {
      optional: true
    });
    this._useUTC = !!options?.useUtc;
    this._firstDayOfWeek = options?.firstDayOfWeek || 0;
    this._defaultOutputCalendar = options?.defaultOutputCalendar || "gregory";
    this.setLocale(dateLocale || DateTime.local().locale);
    this.setLocale(this.translateService.currentLang);
    if (this.translateService !== void 0) {
      this.translateService.onLangChange.pipe(takeUntilDestroyed()).subscribe(({
        lang
      }) => {
        this.setLocale(lang);
      });
    }
  }
  getYear(date) {
    return date.year;
  }
  getMonth(date) {
    return date.month - 1;
  }
  getDate(date) {
    return date.day;
  }
  getDayOfWeek(date) {
    return date.weekday;
  }
  getMonthNames(style) {
    return Info.months(style, {
      locale: this.locale,
      outputCalendar: this._defaultOutputCalendar
    });
  }
  getDateNames() {
    const dtf = new Intl.DateTimeFormat(this.locale, {
      day: "numeric",
      timeZone: "utc"
    });
    return luxonRange(31, (i) => dtf.format(DateTime.utc(2017, 1, i + 1).toJSDate()));
  }
  getDayOfWeekNames(style) {
    const days = Info.weekdays(style, {
      locale: this.locale
    });
    days.unshift(days.pop());
    return days;
  }
  getYearName(date) {
    return date.toFormat("yyyy", this._getOptions());
  }
  getFirstDayOfWeek() {
    return this._firstDayOfWeek;
  }
  getNumDaysInMonth(date) {
    return date.daysInMonth;
  }
  clone(date) {
    return DateTime.fromObject(date.toObject(), this._getOptions());
  }
  createDate(year, month, date) {
    const options = this._getOptions();
    if (month < 0 || month > 11) {
      throw Error(`Invalid month index "${month}". Month index has to be between 0 and 11.`);
    }
    if (date < 1) {
      throw Error(`Invalid date "${date}". Date has to be greater than 0.`);
    }
    const result = this._useUTC ? DateTime.utc(year, month + 1, date, options) : DateTime.local(year, month + 1, date, options);
    if (!this.isValid(result)) {
      throw Error(`Invalid date "${date}". Reason: "${result.invalidReason}".`);
    }
    return result;
  }
  today() {
    const options = this._getOptions();
    return this._useUTC ? DateTime.utc(options) : DateTime.local(options);
  }
  parse(value, parseFormat) {
    const options = this._getOptions();
    if (typeof value == "string" && value.length > 0) {
      const formats = Array.isArray(parseFormat) ? parseFormat : [parseFormat];
      if (!parseFormat.length) {
        throw Error("Formats array must not be empty.");
      }
      for (const format of formats) {
        const fromFormat = DateTime.fromFormat(value, format, options);
        if (this.isValid(fromFormat)) {
          return fromFormat;
        }
      }
      return this.invalid();
    } else if (typeof value === "number") {
      return DateTime.fromMillis(value, options);
    } else if (value instanceof Date) {
      return DateTime.fromJSDate(value, options);
    } else if (value instanceof DateTime) {
      return DateTime.fromMillis(value.toMillis(), options);
    }
    return null;
  }
  format(date, displayFormat) {
    if (!this.isValid(date)) {
      throw Error("LuxonDateAdapter: Cannot format invalid date.");
    }
    if (this._useUTC) {
      return date.setLocale(this.locale).setZone("utc").toFormat(displayFormat);
    } else {
      return date.setLocale(this.locale).toFormat(displayFormat);
    }
  }
  addCalendarYears(date, years) {
    return date.reconfigure(this._getOptions()).plus({
      years
    });
  }
  addCalendarMonths(date, months) {
    return date.reconfigure(this._getOptions()).plus({
      months
    });
  }
  addCalendarDays(date, days) {
    return date.reconfigure(this._getOptions()).plus({
      days
    });
  }
  toIso8601(date) {
    return date.toISO();
  }
  /**
   * Returns the given value if given a valid Luxon or null. Deserializes valid ISO 8601 strings
   * (https://www.ietf.org/rfc/rfc3339.txt) and valid Date objects into valid DateTime and empty
   * string into null. Returns an invalid date for all other values.
   */
  deserialize(value) {
    const options = this._getOptions();
    let date;
    if (value instanceof Date) {
      date = DateTime.fromJSDate(value, options);
    }
    if (typeof value === "string") {
      if (!value) {
        return null;
      }
      date = DateTime.fromISO(value, options);
    }
    if (date && this.isValid(date)) {
      return date;
    }
    return super.deserialize(value);
  }
  isDateInstance(obj) {
    return obj instanceof DateTime;
  }
  isValid(date) {
    return date.isValid;
  }
  invalid() {
    return DateTime.invalid("Invalid Luxon DateTime object.");
  }
  setTime(target, hours, minutes, seconds) {
    if (hours < 0 || hours > 23) {
      throw Error(`Invalid hours "${hours}". Hours value must be between 0 and 23.`);
    }
    if (minutes < 0 || minutes > 59) {
      throw Error(`Invalid minutes "${minutes}". Minutes value must be between 0 and 59.`);
    }
    if (seconds < 0 || seconds > 59) {
      throw Error(`Invalid seconds "${seconds}". Seconds value must be between 0 and 59.`);
    }
    return this.clone(target).set({
      hour: hours,
      minute: minutes,
      second: seconds,
      millisecond: 0
    });
  }
  getHours(date) {
    return date.hour;
  }
  getMinutes(date) {
    return date.minute;
  }
  getSeconds(date) {
    return date.second;
  }
  parseTime(value, parseFormat) {
    const result = this.parse(value, parseFormat);
    if ((!result || !this.isValid(result)) && typeof value === "string") {
      return this.parse(value.replace(/[^0-9:(AM|PM)]/gi, ""), parseFormat) || result;
    }
    return result;
  }
  addSeconds(date, amount) {
    return date.reconfigure(this._getOptions()).plus({
      seconds: amount
    });
  }
  /** Gets the options that should be used when constructing a new `DateTime` object. */
  _getOptions() {
    return {
      zone: this._useUTC ? "utc" : void 0,
      locale: this.locale,
      outputCalendar: this._defaultOutputCalendar
    };
  }
  static {
    this.ɵfac = function OnemrvaLuxonDateAdapter_Factory(__ngFactoryType__) {
      return new (__ngFactoryType__ || _OnemrvaLuxonDateAdapter)(ɵɵinject(TranslateService));
    };
  }
  static {
    this.ɵprov = ɵɵdefineInjectable({
      token: _OnemrvaLuxonDateAdapter,
      factory: _OnemrvaLuxonDateAdapter.ɵfac
    });
  }
};
(() => {
  (typeof ngDevMode === "undefined" || ngDevMode) && setClassMetadata(OnemrvaLuxonDateAdapter, [{
    type: Injectable
  }], () => [{
    type: TranslateService
  }], null);
})();
function onemrvaDateNativeYearMonthProvider() {
  return [{
    provide: MAT_DATE_FORMATS,
    useValue: ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT
  }, {
    provide: DateAdapter,
    useClass: OnemrvaNativeDateAdapter
  }];
}
function onemrvaDateNativeProvider() {
  return [{
    provide: MAT_DATE_FORMATS,
    useValue: ONEMRVA_MAT_NATIVE_DATE_FORMAT
  }, {
    provide: DateAdapter,
    useClass: OnemrvaNativeDateAdapter
  }];
}
function onemrvaDateLuxonProvider() {
  return [{
    provide: MAT_DATE_FORMATS,
    useValue: ONEMRVA_MAT_LUXON_DATE_FORMATS
  }, {
    provide: DateAdapter,
    useClass: OnemrvaLuxonDateAdapter
  }];
}
function onemrvaDateLuxonYearMonthProvider() {
  return [{
    provide: MAT_DATE_FORMATS,
    useValue: ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS
  }, {
    provide: DateAdapter,
    useClass: OnemrvaLuxonDateAdapter
  }];
}
function onemrvaThemeProvider() {
  return [{
    provide: ErrorHandler,
    useClass: OnemrvaErrorHandler
  }, {
    provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
    useValue: {
      appearance: "outline",
      floatLabel: "always"
    }
  }, {
    provide: OverlayContainer,
    useClass: WebComponentOverlayContainer
  }];
}

export {
  isValidIBAN,
  extractIBAN,
  friendlyFormatIBAN,
  countrySpecs,
  ClipboardIconComponent,
  CDNUrlModeOptions,
  CDN_URLS,
  NISS_MASK,
  LOOKUP_COUNTRY_URL,
  ONEMRVA_MAT_NATIVE_DATE_FORMAT,
  ONEMRVA_MAT_NATIVE_YEAR_MONTH_FORMAT,
  ONEMRVA_MAT_LUXON_YEAR_MONTH_FORMATS,
  ONEMRVA_MAT_LUXON_DATE_FORMATS,
  CDN_URL_MODE,
  WebComponentOverlayContainer,
  DefaultStorage,
  RequestTimes,
  HttpRequestCache,
  DigitOnlyDirective,
  MatRowClickableDirective,
  IfWidthIsDirective,
  OnemRvaColorDirective,
  OnemrvaMaskDirective,
  OnemRvaClipboardDirective,
  OnemRvaIconRightDirective,
  OnemrvaDateFormatDirective,
  directives,
  setTranslationLanguage,
  setTranslationLanguageFromWO,
  OnemrvaTranslateCDNLoader,
  OnemrvaTranslateHttpLoader,
  OnemrvaBcePipe,
  OnemrvaNissPipe,
  LOOKUP_COUNTRY_SERVICE_URL,
  CommonCountryLookupService,
  CacheService,
  OnemRvaCDNService,
  OnemRvaCDNCountryService,
  OnemRvaCDNMimeService,
  OnemRvaOSMService,
  OnemrvaErrorHandler,
  OnemrvaMissingTranslationHandler,
  OnemrvaValidators,
  IBAN_SUPPORTED_COUNTRIES,
  SEPA_ONLY_SUPPORTED_COUNTRIES,
  bankAccountValidator,
  OnemrvaSharedModule,
  OnemrvaNativeDateAdapter,
  OnemrvaLuxonDateAdapter,
  onemrvaDateNativeYearMonthProvider,
  onemrvaDateNativeProvider,
  onemrvaDateLuxonProvider,
  onemrvaDateLuxonYearMonthProvider,
  onemrvaThemeProvider
};
/*! Bundled license information:

ibantools/jsnext/ibantools.js:
  (*!
   * @license
   * Copyright Saša Jovanić
   * Licensed under the Mozilla Public License, Version 2.0 or the MIT license,
   * at your option. This file may not be copied, modified, or distributed
   * except according to those terms.
   * SPDX-FileCopyrightText: Saša Jovanić
   * SPDX-License-Identifier: MIT or MPL/2.0
   *)
  (**
   * Validation, extraction and creation of IBAN, BBAN, BIC/SWIFT numbers plus some other helpful stuff
   * @package Documentation
   * <AUTHOR> Jovanić
   * @module ibantools
   * @version 4.5.1
   * @license MIT or MPL-2.0
   * @preferred
   *)

@onemrvapublic/design-system/fesm2022/onemrvapublic-design-system-shared.mjs:
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.io/license
   *)
  (**
   * @license
   * Copyright Google LLC All Rights Reserved.
   *
   * Use of this source code is governed by an MIT-style license that can be
   * found in the LICENSE file at https://angular.dev/license
   *)
*/
//# sourceMappingURL=chunk-EHLUMIYB.js.map
