/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.wo.facade.rest.model.CancelEmailMeetingRequestDTO
import be.fgov.onerva.wo.facade.rest.model.SendEmailMeetingRequestDTO
import be.fgov.onerva.wo.facade.infrastructure.*

class MeetingApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun cancelEmailMeeting(meetingId: java.util.UUID, cancelEmailMeetingRequestDTO: CancelEmailMeetingRequestDTO): Unit {
        cancelEmailMeetingWithHttpInfo(meetingId = meetingId, cancelEmailMeetingRequestDTO = cancelEmailMeetingRequestDTO)
    }

    @Throws(RestClientResponseException::class)
    fun cancelEmailMeetingWithHttpInfo(meetingId: java.util.UUID, cancelEmailMeetingRequestDTO: CancelEmailMeetingRequestDTO): ResponseEntity<Unit> {
        val localVariableConfig = cancelEmailMeetingRequestConfig(meetingId = meetingId, cancelEmailMeetingRequestDTO = cancelEmailMeetingRequestDTO)
        return request<CancelEmailMeetingRequestDTO, Unit>(
            localVariableConfig
        )
    }

    fun cancelEmailMeetingRequestConfig(meetingId: java.util.UUID, cancelEmailMeetingRequestDTO: CancelEmailMeetingRequestDTO) : RequestConfig<CancelEmailMeetingRequestDTO> {
        val localVariableBody = cancelEmailMeetingRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "meetingId" to meetingId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/meetings/{meetingId}/cancel",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun sendEmailMeeting(sendEmailMeetingRequestDTO: SendEmailMeetingRequestDTO): java.util.UUID {
        val result = sendEmailMeetingWithHttpInfo(sendEmailMeetingRequestDTO = sendEmailMeetingRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun sendEmailMeetingWithHttpInfo(sendEmailMeetingRequestDTO: SendEmailMeetingRequestDTO): ResponseEntity<java.util.UUID> {
        val localVariableConfig = sendEmailMeetingRequestConfig(sendEmailMeetingRequestDTO = sendEmailMeetingRequestDTO)
        return request<SendEmailMeetingRequestDTO, java.util.UUID>(
            localVariableConfig
        )
    }

    fun sendEmailMeetingRequestConfig(sendEmailMeetingRequestDTO: SendEmailMeetingRequestDTO) : RequestConfig<SendEmailMeetingRequestDTO> {
        val localVariableBody = sendEmailMeetingRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/meetings",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
