package be.fgov.onerva.cu.backend.adapter.out.persistence.model

import java.util.UUID
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EntityListeners
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Index
import jakarta.persistence.Table
import jakarta.persistence.UniqueConstraint
import org.hibernate.envers.Audited
import org.springframework.data.jpa.domain.support.AuditingEntityListener
import be.fgov.onerva.cu.backend.application.domain.ExternalSource

/**
 * Entity for tracking the source of individual fields across multiple entities
 */
@Entity
@Table(
    name = "field_source",
    uniqueConstraints = [
        UniqueConstraint(
            name = "UQ_field_source",
            columnNames = ["entity_type", "entity_id", "field_name"]
        )
    ],
    indexes = [
        Index(name = "IX_field_source_entity", columnList = "entity_type,entity_id")
    ]
)
@Audited
@EntityListeners(AuditingEntityListener::class)
class FieldSourceEntity(
    /**
     * Identifies which entity/table this field belongs to
     */
    @Column(name = "entity_type", nullable = false)
    var entityType: String,

    /**
     * The ID of the entity that this field source belongs to
     */
    @Column(name = "entity_id", nullable = false)
    var entityId: UUID,

    /**
     * The name of the field being tracked
     */
    @Column(name = "field_name", nullable = false)
    var fieldName: String,

    /**
     * The source of the field data
     */
    @Column(name = "source", nullable = false)
    @Enumerated(EnumType.STRING)
    var source: ExternalSource,
) : BaseEntityAudited()

