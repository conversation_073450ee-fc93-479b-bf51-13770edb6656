import {TestBed} from "@angular/core/testing";
import {HttpClientTestingModule, HttpTestingController} from "@angular/common/http/testing";
import {RedirectHandlerService} from "./redirect-handler.service";
import {ConfigService} from "./../config/config.service";

const mockConfigService = {
    getEnvironmentVariable: jest.fn().mockImplementation((key: string, _throwOnError?: boolean) => {
        if (key === "apiBaseUrl") {
            return "http://api";
        }
        if (key === "bffBaseUrl") {
            return "http://bff";
        }
        return "";
    }),
};

describe('RedirectHandlerService', () => {
  let service: RedirectHandlerService;
    let httpMock: HttpTestingController;

  beforeEach(() => {
      TestBed.configureTestingModule({
          imports: [HttpClientTestingModule],
          providers: [
              RedirectHandlerService,
              {provide: ConfigService, useValue: mockConfigService},
          ],
      });

      service = TestBed.inject(RedirectHandlerService);
      httpMock = TestBed.inject(HttpTestingController);
  });

    afterEach(() => {
        httpMock.verify();
        jest.clearAllMocks();
    });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

    describe("initializeServices", () => {
        it("should set default headers when a token is provided", () => {
            service.initializeServices("my-test-token");
            service.getC51RedirectUrl("ABC").subscribe();

            const req = httpMock.expectOne("http://bff/api/request/c51/ABC");
            expect(req.request.headers.has("Authorization")).toBe(true);
            expect(req.request.headers.get("Authorization")).toBe("Bearer my-test-token");

            req.flush("http://example.com/redirect");
        });

        it("should set no authorization header when no token is provided", () => {
            service.initializeServices(undefined);

            service.getC51RedirectUrl("XYZ").subscribe();

            const req = httpMock.expectOne("http://bff/api/request/c51/XYZ");
            expect(req.request.headers.has("Authorization")).toBe(false);

            req.flush("http://example.com/redirect");
        });
    });

    describe("getC51RedirectUrl", () => {
        it("should call the correct endpoint and return data", () => {
            const mockResponse = "http://example.com/redirect";

            service.getC51RedirectUrl("REQ123").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/c51/REQ123");
            expect(req.request.method).toBe("GET");

            req.flush(mockResponse);
        });

        it("should handle errors properly", (done) => {
            service.getC51RedirectUrl("ERROR123").subscribe({
                next: () => done.fail("should have failed"),
                error: (error) => {
                    expect(error.status).toBe(404);
                    done();
                },
            });

            const req = httpMock.expectOne("http://bff/api/request/c51/ERROR123");
            req.flush("Not found", {status: 404, statusText: "Not Found"});
        });
    });

    describe("openS24Session", () => {
        it("should call the correct endpoint and return data", () => {
            const mockResponse = "http://example.com/session";

            service.openS24Session("SESSION123").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/s24/SESSION123");
            expect(req.request.method).toBe("PUT");

            req.flush(mockResponse);
        });

        it("should handle errors properly", (done) => {
            service.openS24Session("ERROR123").subscribe({
                next: () => done.fail("should have failed"),
                error: (error) => {
                    expect(error.status).toBe(404);
                    done();
                },
            });

            const req = httpMock.expectOne("http://bff/api/request/s24/ERROR123");
            req.flush("Not found", {status: 404, statusText: "Not Found"});
        });
    });

    describe("getRegisRedirectUrl", () => {
        it("should call the correct endpoint with valid language code and return data", () => {
            const mockResponse = "http://example.com/regis";

            service.getRegisRedirectUrl("REQ123", "nl").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/regis/REQ123?languageCode=nl");
            expect(req.request.method).toBe("GET");

            req.flush(mockResponse);
        });

        it("should default to Dutch language when invalid language code is provided", () => {
            const mockResponse = "http://example.com/regis";
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            service.getRegisRedirectUrl("REQ123", "invalid-lang").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/regis/REQ123?languageCode=nl");
            expect(req.request.method).toBe("GET");
            expect(consoleSpy).toHaveBeenCalledWith("Invalid language code: invalid-lang. Defaulting to 'nl'.");

            req.flush(mockResponse);
            consoleSpy.mockRestore();
        });

        it("should handle different valid language codes correctly", () => {
            const mockResponse = "http://example.com/regis";

            service.getRegisRedirectUrl("REQ123", "fr").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/regis/REQ123?languageCode=fr");
            expect(req.request.method).toBe("GET");

            req.flush(mockResponse);
        });

        it("should handle errors properly", (done) => {
            service.getRegisRedirectUrl("ERROR123", "nl").subscribe({
                next: () => done.fail("should have failed"),
                error: (error) => {
                    expect(error.status).toBe(404);
                    done();
                },
            });

            const req = httpMock.expectOne("http://bff/api/request/regis/ERROR123?languageCode=nl");
            req.flush("Not found", {status: 404, statusText: "Not Found"});
        });

        it("should handle empty string as invalid language code", () => {
            const mockResponse = "http://example.com/regis";
            const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();

            service.getRegisRedirectUrl("REQ123", "").subscribe((res) => {
                expect(res).toEqual(mockResponse);
            });

            const req = httpMock.expectOne("http://bff/api/request/regis/REQ123?languageCode=nl");
            expect(req.request.method).toBe("GET");
            expect(consoleSpy).toHaveBeenCalledWith("Invalid language code: . Defaulting to 'nl'.");

            req.flush(mockResponse);
            consoleSpy.mockRestore();
        });

        it("should be affected by token authorization", () => {
            service.initializeServices("regis-token");

            service.getRegisRedirectUrl("REQ123", "fr").subscribe();

            const req = httpMock.expectOne("http://bff/api/request/regis/REQ123?languageCode=fr");
            expect(req.request.headers.has("Authorization")).toBe(true);
            expect(req.request.headers.get("Authorization")).toBe("Bearer regis-token");

            req.flush("http://example.com/regis");
        });
    });
});