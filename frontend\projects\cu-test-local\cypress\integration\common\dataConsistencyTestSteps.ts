import {Then, When} from "@badeball/cypress-cucumber-preprocessor";
// import {slowCypressDown} from "cypress-slow-down";
// slowCypressDown(400);

Then("test", () => {
    cy.get("[data-cy=\"consistency-table\"]");
});

Then("should verify data in each row of the table", () => {
    const expectedData = [
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.BIRTHDATE",
            displayLabel: "Geboortedatum",
            encodedValue: "09/06/1975",
            dbValue: "-",
            sourceValue: "01/01/2000",
            icon: "warning",
        },
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.ADDRESS",
            displayLabel: "Adres",
            encodedValue: "Pasteur 37, 1000 92250",
            dbValue: "GUIDO GEZELLESTRAAT 26/12, 1060",
            sourceValue: "Rue Pasteur 37/ET01, 1070 ANDERLECHT",
            icon: "warning",
        },
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.NATIONALITY",
            displayLabel: "Nationaliteit",
            encodedValue: "België",
            dbValue: "België",
            sourceValue: "België",
            icon: "check_circle",
        },
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.BANK_ACCOUNT",
            displayLabel: "Bankrekeningnummer",
            encodedValue: "LT12 1000 0111 0100 1000BIC: LIABLT2XXXX",
            dbValue: "BE68 **************",
            sourceValue: "-",
            icon: "warning",
        },
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.HOLDER",
            displayLabel: "Houder",
            encodedValue: "Karim Benzema",
            dbValue: "Toto Titi",
            sourceValue: "-",
            icon: "warning",
        },
        {
            labelKey: "CU_DATA_CONSISTENCY.DC.TABLE.CITIZEN.ROW.UNION_CONTRIBUTION",
            displayLabel: "Vakbondsbijdrage",
            encodedValue: "Inhouding toegestaan Van toepassing vanaf 2022-09-22",
            dbValue: "-",
            sourceValue: "-",
            icon: "check_circle",
        },
    ];

    cy.get("[data-cy^=\"row-\"]", {timeout: 20000}).should("have.length", expectedData.length);

    expectedData.forEach((rowData, index) => {
        // First find all rows
        cy.get("[data-cy^=\"row-\"]", {timeout: 20000}).eq(index).within(() => {

            cy.get("[data-cy=\"cell-label\"]").should("contain", rowData.displayLabel);

            cy.get("[data-cy=\"cell-encoded-value\"]").should("contain", rowData.encodedValue || "-");

            cy.get("[data-cy=\"cell-db-value\"]").should("contain", rowData.dbValue || "-");

            cy.get("[data-cy=\"cell-source-value\"]").should("contain", rowData.sourceValue || "-");

            cy.get("[data-cy=\"cell-icon\"] mat-icon").then($icon => {
                if ($icon.length > 0) {
                    cy.wrap($icon).invoke("text").then(text => {
                        expect(text.trim()).to.equal(rowData.icon);
                    });
                }
            });
        });
    });
});

When("I go to row {string} and click Corrigeren", (rowLabel: string) => {
    cy.get("[data-cy=\"cell-label\"]").contains(rowLabel).parents("[data-cy^=\"row-\"]").within(() => {
        cy.get("[data-cy^=\"correct-button-\"]").click();
    });
});

Then("The dialog should be visible", () => {
    cy.get("mat-dialog-container").should("be.visible");
});

Then("I select the first value and click {string}", (buttonText: string) => {
    cy.get("mat-dialog-content").within(() => {
        cy.get("[data-cy=\"employee-value-option\"]").first().click();
    });

    if (buttonText.toLowerCase().includes("cancel")) {
        cy.get("mat-dialog-actions button").first().click();
    } else {
        cy.get("[data-cy=\"confirm-button\"]").click();
    }
});

Then("The dialog should be closed successfully", () => {
    cy.get("mat-dialog-container").should("not.exist");
});

Then("The value to maintain of row {string} should contain {string}", (rowLabel: string, value: string) => {
    cy.get("[data-cy=\"cell-label\"]").contains(rowLabel).parents("[data-cy^=\"row-\"]").within(() => {
        cy.get("[data-cy^=\"selected-value-\"]").contains(value);
    });
});

Then("I see that the validate and continue button is {string}", function (_buttonState) {
    if (_buttonState == "enabled") {
        cy.get("[data-cy=validateAndContinueButton]").should("not.be.disabled");
    } else if (_buttonState == "disabled") {
        cy.get("[data-cy=validateAndContinueButton]").should("be.disabled");
    }
});

When("I check the regis verification checkbox", () => {
    cy.get("[data-cy=\"regisVerificationCheckBox\"]")
        .should("be.visible")
        .should("not.be.disabled")
        .check()
        .should("be.checked");
});