<div class="regis-check-container">
    <div class="regis-action">
        <h4 style="margin-bottom: 16px; margin-top: 8px;">{{ 'CU_DATA_CONSISTENCY.DC.FAMILY.REGIS.TITLE' | translate }}
            <button style="margin-left: 8px;"
                    mat-stroked-button
                    color="primary"
                    class="regis-button"
                    (click)="openRegis()"
                    [disabled]="!requestId">{{ 'CU_DATA_CONSISTENCY.DC.FAMILY.REGIS.BUTTON' | translate }}
            </button>
        </h4>
    </div>

    <div class="regis-verification">
        <input
                type="checkbox"
                id="regisVerification"
                data-cy="regisVerificationCheckBox"
                [disabled]="isFormClosedOrWaiting()"
                [checked]="isVerified || isFormClosedOrWaiting()"
                (change)="onVerificationChange($event)">
        <label for="regisVerification">
            {{ 'CU_DATA_CONSISTENCY.DC.FAMILY.REGIS.READAKNOLEDGMENT' | translate }}
        </label>
    </div>
</div>