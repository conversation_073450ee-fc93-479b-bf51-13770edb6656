/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * FetchModeParamDTO
 */
@JsonPropertyOrder({
  FetchModeParamDTO.JSON_PROPERTY_ASSOCIATION_PATH,
  FetchModeParamDTO.JSON_PROPERTY_MODE
})
@JsonTypeName("FetchModeParam")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class FetchModeParamDTO {
  public static final String JSON_PROPERTY_ASSOCIATION_PATH = "associationPath";
  private String associationPath;

  /**
   * Gets or Sets mode
   */
  public enum ModeEnum {
    DEFAULT("DEFAULT"),
    
    EAGER("EAGER"),
    
    JOIN("JOIN"),
    
    LAZY("LAZY"),
    
    SELECT("SELECT");

    private String value;

    ModeEnum(String value) {
      this.value = value;
    }

    @JsonValue
    public String getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static ModeEnum fromValue(String value) {
      for (ModeEnum b : ModeEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }
  }

  public static final String JSON_PROPERTY_MODE = "mode";
  private ModeEnum mode;

  public FetchModeParamDTO() {
  }

  public FetchModeParamDTO associationPath(String associationPath) {
    
    this.associationPath = associationPath;
    return this;
  }

  /**
   * Get associationPath
   * @return associationPath
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASSOCIATION_PATH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAssociationPath() {
    return associationPath;
  }


  @JsonProperty(JSON_PROPERTY_ASSOCIATION_PATH)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAssociationPath(String associationPath) {
    this.associationPath = associationPath;
  }

  public FetchModeParamDTO mode(ModeEnum mode) {
    
    this.mode = mode;
    return this;
  }

  /**
   * Get mode
   * @return mode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ModeEnum getMode() {
    return mode;
  }


  @JsonProperty(JSON_PROPERTY_MODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMode(ModeEnum mode) {
    this.mode = mode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    FetchModeParamDTO fetchModeParam = (FetchModeParamDTO) o;
    return Objects.equals(this.associationPath, fetchModeParam.associationPath) &&
        Objects.equals(this.mode, fetchModeParam.mode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(associationPath, mode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class FetchModeParamDTO {\n");
    sb.append("    associationPath: ").append(toIndentedString(associationPath)).append("\n");
    sb.append("    mode: ").append(toIndentedString(mode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

