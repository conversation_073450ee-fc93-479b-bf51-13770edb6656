/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.person.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param street 
 * @param zip 
 * @param city 
 * @param number 
 * @param box 
 */


data class AddressDTO (

    @get:JsonProperty("street")
    val street: kotlin.String,

    @get:JsonProperty("zip")
    val zip: kotlin.Int,

    @get:JsonProperty("city")
    val city: kotlin.String,

    @get:JsonProperty("number")
    val number: kotlin.String? = null,

    @get:JsonProperty("box")
    val box: kotlin.String? = null

) {


}

