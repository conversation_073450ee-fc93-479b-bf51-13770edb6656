/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PublicEC32WorkerIdentification
 */
@JsonPropertyOrder({
  PublicEC32WorkerIdentification.JSON_PROPERTY_INSS,
  PublicEC32WorkerIdentification.JSON_PROPERTY_LAST_NAME,
  PublicEC32WorkerIdentification.JSON_PROPERTY_FIRST_NAME
})
@JsonTypeName("publicEC32WorkerIdentification")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PublicEC32WorkerIdentification {
  public static final String JSON_PROPERTY_INSS = "inss";
  private String inss;

  public static final String JSON_PROPERTY_LAST_NAME = "lastName";
  private String lastName;

  public static final String JSON_PROPERTY_FIRST_NAME = "firstName";
  private String firstName;

  public PublicEC32WorkerIdentification() {
  }

  public PublicEC32WorkerIdentification inss(String inss) {
    
    this.inss = inss;
    return this;
  }

  /**
   * Get inss
   * @return inss
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInss() {
    return inss;
  }


  @JsonProperty(JSON_PROPERTY_INSS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInss(String inss) {
    this.inss = inss;
  }

  public PublicEC32WorkerIdentification lastName(String lastName) {
    
    this.lastName = lastName;
    return this;
  }

  /**
   * Get lastName
   * @return lastName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLastName() {
    return lastName;
  }


  @JsonProperty(JSON_PROPERTY_LAST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public PublicEC32WorkerIdentification firstName(String firstName) {
    
    this.firstName = firstName;
    return this;
  }

  /**
   * Get firstName
   * @return firstName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFirstName() {
    return firstName;
  }


  @JsonProperty(JSON_PROPERTY_FIRST_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PublicEC32WorkerIdentification publicEC32WorkerIdentification = (PublicEC32WorkerIdentification) o;
    return Objects.equals(this.inss, publicEC32WorkerIdentification.inss) &&
        Objects.equals(this.lastName, publicEC32WorkerIdentification.lastName) &&
        Objects.equals(this.firstName, publicEC32WorkerIdentification.firstName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(inss, lastName, firstName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PublicEC32WorkerIdentification {\n");
    sb.append("    inss: ").append(toIndentedString(inss)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

