package be.fgov.onerva.cu.backend.adapter.`in`.mapper

import be.fgov.onerva.cu.backend.application.domain.*
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Annex
import be.fgov.onerva.cu.backend.application.domain.AnnexType
import be.fgov.onerva.cu.backend.application.domain.FieldSource
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.rest.priv.model.*
import be.fgov.onerva.cu.rest.priv.model.ExternalSource
import be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DecisionTypeEnum

fun ExternalSource.toDomainExternalSource() = when (this) {
    ExternalSource.C1 -> be.fgov.onerva.cu.backend.application.domain.ExternalSource.C1
    ExternalSource.ONEM -> be.fgov.onerva.cu.backend.application.domain.ExternalSource.ONEM
    ExternalSource.AUTHENTIC_SOURCES -> be.fgov.onerva.cu.backend.application.domain.ExternalSource.AUTHENTIC_SOURCES
}

fun SyncFollowUpStatus.toPushbackStatusResponse() = when (this) {
    SyncFollowUpStatus.PENDING -> RequestBasicInfoResponse.PushbackStatusEnum.PENDING
    SyncFollowUpStatus.OK -> RequestBasicInfoResponse.PushbackStatusEnum.OK
    SyncFollowUpStatus.NOK -> RequestBasicInfoResponse.PushbackStatusEnum.NOK
}

fun be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.toDocumentTypeResponse() = when (this) {
    be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.ELECTRONIC -> be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DocumentTypeEnum.ELECTRONIC
    be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType.PAPER -> be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse.DocumentTypeEnum.PAPER
}

fun be.fgov.onerva.cu.backend.application.domain.ExternalSource.toExternalSourceResponse() = when (this) {
    be.fgov.onerva.cu.backend.application.domain.ExternalSource.C1 -> ExternalSource.C1
    be.fgov.onerva.cu.backend.application.domain.ExternalSource.ONEM -> ExternalSource.ONEM
    be.fgov.onerva.cu.backend.application.domain.ExternalSource.AUTHENTIC_SOURCES -> ExternalSource.AUTHENTIC_SOURCES
}

fun FieldSource.toFieldSourceResponse() = be.fgov.onerva.cu.rest.priv.model.FieldSource().apply {
    this.fieldName = <EMAIL>
    this.source = <EMAIL>()
}

fun be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest.toDomainFieldSources() = this.fieldSources.map {
    FieldSource(
        fieldName = it.fieldName,
        source = it.source.toDomainExternalSource()
    )
}

fun UpdateCitizenInformationRequest.toDomainUpdateCitizenInformation() = UpdateCitizenInformation(
    birthDate = this.birthDate, nationality = this.nationality,
    address = Address(
        country = this.address.country,
        street = this.address.street,
        houseNumber = this.address.houseNumber,
        boxNumber = this.address.boxNumber,
        city = this.address.city,
        zipCode = this.address.zipCode
    ),
)

fun CitizenInformation.toCitizenInformationDetailResponse() = CitizenInformationDetailResponse().apply {
    birthDate = <EMAIL>
    nationality = <EMAIL>
    address = be.fgov.onerva.cu.rest.priv.model.Address().apply {
        boxNumber = <EMAIL>
        city = <EMAIL>
        country = <EMAIL>
        houseNumber = <EMAIL>
        street = <EMAIL>
        zipCode = <EMAIL>
    }
}

fun UpdateModeOfPaymentRequest.toDomainUpdateModeOfPayment(): UpdateModeOfPayment {
    if (this.iban == null) {
        throw InvalidInputException("Bank account is required")
    }
    return UpdateModeOfPayment(
        otherPersonName = this.otherPersonName,
        iban = this.iban,
        bic = this.bic,
    )
}

fun ModeOfPayment.toModeOfPaymentDetailResponse() =
    ModeOfPaymentDetailResponse().otherPersonName(this.otherPersonName)
        .iban(this.iban)
        .bic(this.bic)

fun UnionContribution.toUnionContributionDetailResponse() =
    UnionContributionDetailResponse().authorized(this.authorized).effectiveDate(this.effectiveDate)

fun UpdateUnionContributionRequest.toDomainUpdateUnionContribution() =
    UpdateUnionContribution(
        authorized = this.authorized,
        effectiveDate = this.effectiveDate,
    )

fun AnnexType.toAnnexType() = when (this) {
    AnnexType.SCANNED_DOCUMENTS -> be.fgov.onerva.cu.rest.priv.model.AnnexType.SCANNED_DOCUMENTS
    AnnexType.EC1 -> be.fgov.onerva.cu.rest.priv.model.AnnexType.EC1
}

fun Annex.toAnnexResponse(): be.fgov.onerva.cu.rest.priv.model.Annex =
    be.fgov.onerva.cu.rest.priv.model.Annex().type(this.type.toAnnexType()).url(this.url)

fun DecisionType.toDecisionTypeResponse(): DecisionTypeEnum = DecisionTypeEnum.fromValue(this.name)

fun RequestBasicInfo.toRequestBasicInfoResponse(): RequestBasicInfoResponse {

    return RequestBasicInfoResponse().requestDate(this.requestDate).ssin(this.ssin).firstName(this.firstName)
        .lastName(this.lastName).introductionDate(this.introductionDate).dateValid(this.dateValid)
        .annexes(this.annexes.map(Annex::toAnnexResponse))
        .decisionType(this.decisionType?.toDecisionTypeResponse())
        .decisionBarema(this.decisionBarema)
        .c9Id(this.c9Id.toString())
        .pushbackStatus(this.pushbackStatus?.toPushbackStatusResponse())
        .documentType(this.documentType.toDocumentTypeResponse())
}

fun WaveTaskStatus.toResponseWaveTaskStatus() = when (this) {
    WaveTaskStatus.OPEN -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.OPEN
    WaveTaskStatus.CLOSED -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.CLOSED
    WaveTaskStatus.WAITING -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.WAITING
    WaveTaskStatus.DELETED -> be.fgov.onerva.cu.rest.priv.model.WaveTaskStatus.DELETED
}

fun WaveTask.toWaveTaskResponse() = this.let {
    WaveTaskResponse().processId(it.processId).taskId(it.taskId).status(it.status.toResponseWaveTaskStatus())
        .waveUrl(it.waveUrl)
}

fun RequestInformation.toRequestInformationResponse(): RequestInformationResponse =
    RequestInformationResponse().requestDate(this.requestDate)

fun UpdateRequestInformationRequest.toDomainRequestInformation(): RequestInformation = RequestInformation(
    requestDate = this.requestDate
)

fun AddressNullable.toAddressNullable() = be.fgov.onerva.cu.rest.priv.model.AddressNullable().also {
    it.boxNumber = this.boxNumber
    it.city = this.city
    it.country = this.country
    it.houseNumber = this.houseNumber
    it.street = this.street
    it.zipCode = this.zipCode
}

fun UnionContribution.toUnionContributionFields() = UnionContributionFields().also {
    it.authorized = this.authorized
    it.effectiveDate = this.effectiveDate
}

fun HistoricalCitizenOnem.toHistoricalCitizenOnemResponse() = HistoricalCitizenOnemResponse().also {
    it.firstName = this.firstName
    it.lastName = this.lastName
    it.iban = this.iban
    it.bic = this.bic
    it.otherPersonName = this.otherPersonName
    it.birthDate = this.birthDate
    it.nationality = this.nationality
    it.address = this.address.toAddressNullable()
    it.bankAccountValueDate = this.bankAccountValueDate
    it.addressValueDate = this.address.valueDate
    it.unionContributionValueDate = this.effectiveDate
    it.unionDue = UnionContribution(
        authorized = this.authorized,
        effectiveDate = this.effectiveDate
    ).toUnionContributionFields()
}

fun HistoricalCitizenAuthenticSources.toHistoricalCitizenAuthenticSourcesResponse() =
    HistoricalCitizenAuthenticSourcesResponse().also {
        it.firstName = this.firstName
        it.lastName = this.lastName
        it.birthDate = this.birthDate
        it.nationality = this.nationality
        it.address = this.address.toAddressNullable()
        it.valueDate = this.address.valueDate
    }

fun HistoricalCitizenC1.toHistoricalCitizenC1Response() = HistoricalCitizenC1Response().also {
    it.firstName = this.firstName
    it.lastName = this.lastName
    it.iban = this.iban
    it.bic = this.bic
    it.otherPersonName = this.otherPersonName
    it.birthDate = this.birthDate
    it.nationality = this.nationality
    it.address = this.address.toAddressNullable()
    it.bankAccountValueDate = this.bankAccountValueDate
    it.addressValueDate = this.address.valueDate
    it.unionContributionValueDate = this.effectiveDate
    it.unionDue = UnionContribution(
        authorized = this.authorized,
        effectiveDate = this.effectiveDate
    ).toUnionContributionFields()
}
