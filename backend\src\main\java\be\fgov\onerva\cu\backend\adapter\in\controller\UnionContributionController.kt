package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainFieldSources
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainUpdateUnionContribution
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toFieldSourceResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toUnionContributionDetailResponse
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.UnionContributionUseCase
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.rest.priv.api.UnionContributionApi
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UnionContributionDetailResponse
import be.fgov.onerva.cu.rest.priv.model.UpdateUnionContributionRequest

@RestController
class UnionContributionController(val unionContributionUseCase: UnionContributionUseCase) : UnionContributionApi {
    /**
     * GET /api/requests/{requestId}/mode-of-payment
     * Retrieve mode of payment
     *
     * @param requestId The UUID of the request (required)
     * @return Employee information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    @LogWithRequestId
    override fun getUnionContribution(requestId: UUID?): UnionContributionDetailResponse {
        val unionContribution = unionContributionUseCase.getUnionContribution(requestId!!)
            ?: throw InformationNotFoundException("Union contribution not found for request $requestId")

        // Get field sources for this union contribution
        val fieldSources = unionContributionUseCase.getUnionContributionFieldSources(requestId)
            .map { it.toFieldSourceResponse() }

        // Create response with union contribution info and field sources
        val response = unionContribution.toUnionContributionDetailResponse()

        // Set field sources in the response
        response.fieldSources = fieldSources

        return response
    }

    /**
     * PUT /api/requests/{requestId}/mode-of-payment
     * Update mutable employee information for a specific request
     *
     * @param requestId The UUID of the request (required)
     * @param updateUnionContributionRequest  (required)
     * @return Employee information successfully updated (status code 204)
     * or Invalid request body (status code 400)
     * or Request not found (status code 404)
     * or Validation error (status code 422)
     */
    @LogWithRequestId
    override fun updateUnionContribution(
        requestId: UUID?,
        updateUnionContributionRequest: UpdateUnionContributionRequest?,
    ) = unionContributionUseCase.updateUnionContribution(
        requestId!!, updateUnionContributionRequest!!.toDomainUpdateUnionContribution()
    )

    /**
     * PUT /api/requests/{requestId}/union-contribution/select
     * Select sources for individual fields in union contribution
     *
     * @param requestId The UUID of the request (required)
     * @param selectFieldSourcesRequest  (required)
     * @return Field sources successfully selected (status code 204)
     *         or Invalid request body (status code 400)
     *         or Request not found (status code 404)
     *         or Validation error (status code 422)
     */
    @LogWithRequestId
    override fun selectUnionContributionSources(
        requestId: UUID?,
        selectFieldSourcesRequest: SelectFieldSourcesRequest?,
    ) {
        unionContributionUseCase.selectUnionContributionFieldSources(
            requestId!!,
            selectFieldSourcesRequest?.toDomainFieldSources()!!
        )
    }
}
