package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.*
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainRequestInformation
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toRequestBasicInfoResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toRequestInformationResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toWaveTaskResponse
import be.fgov.onerva.cu.backend.application.port.`in`.*
import be.fgov.onerva.cu.common.aop.LogMethodCall
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.cu.rest.priv.api.RequestInformationApi
import be.fgov.onerva.cu.rest.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.rest.priv.model.RequestInformationResponse
import be.fgov.onerva.cu.rest.priv.model.UpdateRequestInformationRequest
import lombok.extern.slf4j.Slf4j

@Slf4j
@RestController
class RequestInformationController(
    val requestBasicInfoUseCase: RequestBasicInfoUseCase,
    val closeRequestTaskUseCase: CloseRequestTaskUseCase,
    val assignTaskToUserUseCase: AssignTaskToUserUseCase,
    val requestInformationUseCase: RequestInformationUseCase,
    val reopenTaskUseCase: ReopenTaskUseCase
) : RequestInformationApi {
    private val log = logger
    /**
     * PUT /api/requests/{requestId}/{taskCode}/close
     * Close a specific task associated with a request
     *
     * @param requestId The UUID of the request (required)
     * @param taskCode The code of the task to close (required)
     * @return Task successfully created after closing the task (status code 204)
     * or Invalid request body (status code 400)
     * or Request not found (status code 404)
     * or Validation error (status code 422)
     */
    @LogMethodCall
    @LogWithRequestId
    override fun closeRequestTask(requestId: UUID?, taskCode: String?) =
        closeRequestTaskUseCase.closeTaskForRequestAndCreateNext(requestId!!, taskCode!!)?.toWaveTaskResponse()

    /**
     * GET /api/requests/{requestId}
     * Retrieve basic information for a specific request
     *
     * @param requestId The UUID of the request (required)
     * @return Basic request information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    @LogMethodCall
    @LogWithRequestId
    override fun getRequestBasicInfo(requestId: UUID?): RequestBasicInfoResponse =
        requestBasicInfoUseCase.getRequestBasicInfo(requestId!!).toRequestBasicInfoResponse()

    /**
     * PUT /api/request/{requestId}/{tasktCode}/assign-user
     * Assign the current user to a task
     *
     * @param requestId The UUID of the request (required)
     * @param taskCode The code of the task to close (required)
     * @return Task successfully assigned (status code 204)
     * or Request not found (status code 404)
     */
    @LogMethodCall
    @LogWithRequestId
    override fun assignTaskToUser(requestId: UUID?) {
        assignTaskToUserUseCase.assignTaskToUser(requestId!!)
    }

    @LogMethodCall
    @LogWithRequestId
    override fun getRequestInformation(requestId: UUID?): RequestInformationResponse? =
        requestInformationUseCase.getRequestInformation(requestId!!)?.toRequestInformationResponse()

    @LogMethodCall
    @LogWithRequestId
    override fun updateRequestInformation(requestId: UUID?, updateRequestInformation: UpdateRequestInformationRequest) =
        requestInformationUseCase.updateRequestInformation(
            requestId!!,
            updateRequestInformation.toDomainRequestInformation()
        )

    @LogMethodCall
    @LogWithRequestId
    override fun reopenTask(taskCode: String?, requestId: UUID?) {
        log.info("Reopening task $taskCode for request $requestId")
        reopenTaskUseCase.reopenTask(
            requestId!!,
            taskCode!!
        )
    }
}