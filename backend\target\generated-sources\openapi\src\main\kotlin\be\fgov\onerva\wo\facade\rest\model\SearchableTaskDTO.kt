/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.InputThirdPartyDTO
import be.fgov.onerva.wo.facade.rest.model.MetaDataSearchDTO
import be.fgov.onerva.wo.facade.rest.model.SearchableRangeDTO
import be.fgov.onerva.wo.facade.rest.model.SearchableTaskCreationDateDTO
import be.fgov.onerva.wo.facade.rest.model.StateDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param taskCodes 
 * @param processCodes 
 * @param taskStatus 
 * @param assignees 
 * @param entities 
 * @param metadata 
 * @param creationDate 
 * @param dueDate 
 * @param businessIds 
 */


data class SearchableTaskDTO (

    @get:JsonProperty("taskCodes")
    val taskCodes: kotlin.collections.List<kotlin.String>? = null,

    @get:JsonProperty("processCodes")
    val processCodes: kotlin.collections.List<kotlin.String>? = null,

    @get:JsonProperty("taskStatus")
    val taskStatus: StateDTO? = null,

    @get:JsonProperty("assignees")
    val assignees: kotlin.collections.List<kotlin.String>? = null,

    @get:JsonProperty("entities")
    val entities: kotlin.collections.List<InputThirdPartyDTO>? = null,

    @get:JsonProperty("metadata")
    val metadata: kotlin.collections.List<kotlin.collections.List<MetaDataSearchDTO>>? = null,

    @get:JsonProperty("creationDate")
    @Deprecated(message = "This property is deprecated.")
    val creationDate: SearchableTaskCreationDateDTO? = null,

    @get:JsonProperty("dueDate")
    val dueDate: SearchableRangeDTO? = null,

    @get:JsonProperty("businessIds")
    val businessIds: kotlin.collections.List<kotlin.String>? = null

) {


}

