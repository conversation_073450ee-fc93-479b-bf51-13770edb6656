package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.Annex
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param requestDate The date when the request was submitted
 * @param firstName The first name of the person
 * @param lastName The last name of the person
 * @param ssin The Social Security Identification Number
 * @param c9Id The ID of the related C9 request
 * @param documentType The type of identity document used for the request
 * @param introductionDate The introduction date of the request
 * @param dateValid The valid date of the request
 * @param annexes 
 * @param decisionType The decision type of the request
 * @param decisionBarema 
 * @param pushbackStatus The pushback status of the request
 */
data class RequestBasicInfoResponse(

    @field:Valid
    @Schema(example = "null", required = true, description = "The date when the request was submitted")
    @get:JsonProperty("requestDate", required = true) val requestDate: java.time.LocalDate,

    @Schema(example = "null", required = true, description = "The first name of the person")
    @get:JsonProperty("firstName", required = true) val firstName: kotlin.String,

    @Schema(example = "null", required = true, description = "The last name of the person")
    @get:JsonProperty("lastName", required = true) val lastName: kotlin.String,

    @Schema(example = "null", required = true, description = "The Social Security Identification Number")
    @get:JsonProperty("ssin", required = true) val ssin: kotlin.String,

    @Schema(example = "null", required = true, description = "The ID of the related C9 request")
    @get:JsonProperty("c9Id", required = true) val c9Id: kotlin.String,

    @Schema(example = "null", required = true, description = "The type of identity document used for the request")
    @get:JsonProperty("documentType", required = true) val documentType: RequestBasicInfoResponse.DocumentType,

    @field:Valid
    @Schema(example = "null", description = "The introduction date of the request")
    @get:JsonProperty("introductionDate") val introductionDate: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "The valid date of the request")
    @get:JsonProperty("dateValid") val dateValid: java.time.LocalDate? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("annexes") val annexes: kotlin.collections.List<Annex>? = null,

    @Schema(example = "null", description = "The decision type of the request")
    @get:JsonProperty("decisionType") val decisionType: RequestBasicInfoResponse.DecisionType? = null,

    @Schema(example = "null", description = "")
    @get:JsonProperty("decisionBarema") val decisionBarema: kotlin.String? = null,

    @Schema(example = "null", description = "The pushback status of the request")
    @get:JsonProperty("pushbackStatus") val pushbackStatus: RequestBasicInfoResponse.PushbackStatus? = null
    ) {

    /**
    * The type of identity document used for the request
    * Values: ELECTRONIC,PAPER
    */
    enum class DocumentType(@get:JsonValue val value: kotlin.String) {

        ELECTRONIC("ELECTRONIC"),
        PAPER("PAPER");

        companion object {
            @JvmStatic
            @JsonCreator
            fun forValue(value: kotlin.String): DocumentType {
                return values().first{it -> it.value == value}
            }
        }
    }

    /**
    * The decision type of the request
    * Values: C2_Y,C2_N,C2_F,C2_P,C51,C9_B,C2,C9_NA
    */
    enum class DecisionType(@get:JsonValue val value: kotlin.String) {

        C2_Y("C2Y"),
        C2_N("C2N"),
        C2_F("C2F"),
        C2_P("C2P"),
        C51("C51"),
        C9_B("C9B"),
        C2("C2"),
        C9_NA("C9NA");

        companion object {
            @JvmStatic
            @JsonCreator
            fun forValue(value: kotlin.String): DecisionType {
                return values().first{it -> it.value == value}
            }
        }
    }

    /**
    * The pushback status of the request
    * Values: PENDING,OK,NOK
    */
    enum class PushbackStatus(@get:JsonValue val value: kotlin.String) {

        PENDING("PENDING"),
        OK("OK"),
        NOK("NOK");

        companion object {
            @JvmStatic
            @JsonCreator
            fun forValue(value: kotlin.String): PushbackStatus {
                return values().first{it -> it.value == value}
            }
        }
    }

}

