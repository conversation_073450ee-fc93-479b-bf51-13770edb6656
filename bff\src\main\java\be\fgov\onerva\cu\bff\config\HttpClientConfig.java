package be.fgov.onerva.cu.bff.config;

import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenApi;
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoApi;
import be.fgov.onerva.cu.bff.rest.client.citizen.api.CitizenInfoV2Api;
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.lookup.wppt.api.LookupApi;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.C9Api;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.api.NavigateMainframeToS24Api;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.api.UserApi;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.web.client.RestTemplateBuilderConfigurer;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.ClientHttpRequestInterceptor;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.time.Duration;
import java.util.Optional;

import static org.slf4j.LoggerFactory.getLogger;

@Configuration
public class HttpClientConfig {
    private static final Logger log = getLogger(HttpClientConfig.class);

    @Bean
    public RestTemplateBuilder restTemplateBuilder(RestTemplateBuilderConfigurer configurer) {
        return configurer.configure(new RestTemplateBuilder())
                .setReadTimeout(Duration.ofSeconds(20))
                .setConnectTimeout(Duration.ofSeconds(3));
    }

    @Bean
    @Qualifier("userRestTemplate")
    public RestTemplate userRestTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder.additionalInterceptors(authenticationForwardingInterceptor()).build();
    }

    @Bean
    @Qualifier("serviceRestTemplate")
    public RestTemplate serviceRestTemplate(RestTemplateBuilder restTemplateBuilder,
                                            Optional<OAuth2AuthorizedClientManager> authorizedClientManager) {

        authorizedClientManager.ifPresent(manager -> restTemplateBuilder.additionalInterceptors(new OAuthClientCredentialsRestTemplateInterceptor(
                manager)));

        return restTemplateBuilder.build();
    }

    private ClientHttpRequestInterceptor authenticationForwardingInterceptor() {
        return (request, body, execution) -> {
            ServletRequestAttributes attributes =
                    (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                String authHeader = attributes.getRequest().getHeader("Authorization");
                if (authHeader != null) {
                    log.debug("Forwarding Authorization header to: {}, {}", request.getURI(), authHeader);
                    request.getHeaders().set("Authorization", authHeader);
                } else {
                    log.debug("No Authorization header found in request to: {}", request.getURI());
                }
            } else {
                log.debug("No request attributes found for request to: {}", request.getURI());
            }

            try {
                return execution.execute(request, body);
            } catch (IOException e) {
                log.error("Error forwarding request to: {}", request.getURI(), e);
                throw e;
            }
        };
    }

    @Bean
    public CitizenApi citizenApi(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                                 @Value("${citizen.url}") String citizenBasePath) {
        var apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(citizenBasePath);
        return new CitizenApi(apiClient);
    }

    @Bean
    public CitizenInfoV2Api citizenInfoV2Api(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                                             @Value("${citizen.url}") String citizenBasePath) {
        var apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(citizenBasePath);
        return new CitizenInfoV2Api(apiClient);
    }

    @Bean
    public CitizenInfoApi citizenInfoApi(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                                         @Value("${citizen.url}") String citizenBasePath) {
        var apiClient = new ApiClient(restTemplate);
        apiClient.setBasePath(citizenBasePath);
        return new CitizenInfoApi(apiClient);
    }

    @Bean
    public UserApi waveFacadeUserApi(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                                     @Value("${woUserFacadeApi.url}") String waveBasePath) {
        var apiClient = new be.fgov.onerva.cu.bff.rest.client.wo.facade.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(waveBasePath);
        return new UserApi(apiClient);
    }

    @Bean
    public NavigateMainframeToS24Api navigateMainframeToS24Api(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                                                               @Value("${c9Api.url}") String s24BasePath) {
        var apiClient = new be.fgov.onerva.cu.bff.rest.client.unemployment.c9.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(s24BasePath);
        return new NavigateMainframeToS24Api(apiClient);
    }

    @Bean
    public LookupApi lookupApiClient(@Qualifier("serviceRestTemplate") RestTemplate restTemplate,
                                     @Value("${lookup.url}") String lookupBasePath) {
        var apiClient = new be.fgov.onerva.cu.bff.rest.client.lookup.wppt.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(lookupBasePath);
        return new LookupApi(apiClient);
    }

    @Bean
    public C9Api c9Api(@Qualifier("userRestTemplate") RestTemplate restTemplate,
                       @Value("${c9Api.url}") String c9BasePath) {
        var apiClient = new be.fgov.onerva.cu.bff.rest.client.unemployment.c9.invoker.ApiClient(restTemplate);
        apiClient.setBasePath(c9BasePath);
        return new C9Api(apiClient);
    }
}