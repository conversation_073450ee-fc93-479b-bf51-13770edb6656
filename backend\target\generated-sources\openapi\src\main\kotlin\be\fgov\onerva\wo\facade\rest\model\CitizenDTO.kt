/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.ThirdPartyDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyQualityDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyTypeDTO

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param id The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen
 * @param type 
 * @param ssin 
 * @param quality 
 */


data class CitizenDTO (

    /* The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen */
    @get:JsonProperty("id")
    override val id: kotlin.String,

    @get:JsonProperty("type")
    override val type: ThirdPartyTypeDTO,

    @get:JsonProperty("ssin")
    val ssin: kotlin.String,

    @get:JsonProperty("quality")
    override val quality: ThirdPartyQualityDTO? = null

) : ThirdPartyDTO {


}

