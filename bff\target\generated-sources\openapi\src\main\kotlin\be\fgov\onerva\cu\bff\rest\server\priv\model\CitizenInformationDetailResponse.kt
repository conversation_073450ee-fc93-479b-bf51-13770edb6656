package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import be.fgov.onerva.cu.bff.rest.server.priv.model.FieldSource
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param nationality The nationality of the employee
 * @param address 
 * @param fieldSources Sources for individual fields
 */
data class CitizenInformationDetailResponse(

    @field:Valid
    @Schema(example = "null", description = "The birth date of the employee (format YYYY-MM-DD)")
    @get:JsonProperty("birthDate") val birthDate: java.time.LocalDate? = null,

    @Schema(example = "null", description = "The nationality of the employee")
    @get:JsonProperty("nationality") val nationality: kotlin.String? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("address") val address: Address? = null,

    @field:Valid
    @Schema(example = "null", description = "Sources for individual fields")
    @get:JsonProperty("fieldSources") val fieldSources: kotlin.collections.List<FieldSource>? = null
    ) {

}

