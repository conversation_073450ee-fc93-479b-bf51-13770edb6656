/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param operatorCode 
 * @param username 
 * @param inss 
 */


data class UserCriteriaDTO (

    @get:JsonProperty("operatorCode")
    val operatorCode: kotlin.String? = null,

    @get:JsonProperty("username")
    val username: kotlin.String? = null,

    @get:JsonProperty("inss")
    val inss: kotlin.String? = null

) {


}

