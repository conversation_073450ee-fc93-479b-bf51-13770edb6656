sonar.coverage.jacoco.xmlReportPaths=backend/target/site/jacoco/jacoco.xml,bff/target/site/jacoco/jacoco.xml
sonar.dependencyCheck.htmlReportPath=dependency-check-report.html
sonar.dependencyCheck.jsonReportPath=dependency-check-report.json
#sonar.exclusions=**/*.spec.ts,**/*.sql,**/featureflags/**,**/entrypoint-cypress.ts,**/e2e/**,frontend/projects/cu/src/lib/environments/**,frontend/projects/local/**,frontend/projects/cu-app/**,frontend/projects/cu-test-local/**,**/test.ts,**/main.ts,**/SecurityConfiguration.*,**/TuEmployeeBackendApplication.*,**/error-interceptor.service.ts,**/wo/**,**/config/**,**/HttpTracingLoggingFilter.*,**/ImprovedContentCachingRequestWrapper*.*,**/FileBasedLookupApiService*.*, **/**CucumberIntegrationTest**,**/CitizenApiMock*.*
# File Exclusions
sonar.coverage.exclusions=**/*.sql,\
                **/featureflags/**,\
                **/entrypoint-cypress.ts,\
                **/e2e/**,\
                frontend/projects/cu/src/lib/environments/**,\
                frontend/projects/cu/src/global.d.ts,\
                frontend/projects/cu/src/public-api.ts,\
                frontend/projects/local/**,\
                frontend/projects/cu-app/**,\
                frontend/projects/cu-test-local/**,\
                **/test.ts,\
                **/main.ts,\
                **/SecurityConfiguration.*,\
                **/TuEmployeeBackendApplication.*,\
                **/error-interceptor.service.ts,\
                **/wo/**,\
                backend/**/config/**,\
                bff/**/config/**,\
                bff/**/exceptions/**
sonar.java.binaries=backend/target/classes,bff/target/classes
sonar.javascript.lcov.reportPaths=coverage/jest/lcov.info,coverage/lcov-report/lcov.info,coverage/lcov_playwright.info
sonar.projectKey=a27ef123-b4f3-4103-8c62-5a0a95746a14
sonar.projectName=cu
sonar.projectVersion=1.0.0
sonar.sourceEncoding=UTF-8
sonar.sources=backend/src/main/java,frontend/projects/cu/src,bff/src/main/java
sonar.tests=backend/src/test/java,frontend/projects/cu/src,bff/src/test/kotlin
sonar.test.inclusions=**/*.spec.ts,**/*Test.java,**/*Test.kt
sonar.issue.ignore.multicriteria.rule1.ruleKey=kotlin:S6517
sonar.issue.ignore.multicriteria.rule1.resourceKey=**/*

sonar.issue.ignore.multicriteria.rule2.ruleKey=java:S5976
sonar.issue.ignore.multicriteria.rule2.resourceKey=**/*

sonar.ci.autoconfig.disabled=true
