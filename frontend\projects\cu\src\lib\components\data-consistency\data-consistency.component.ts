import {Component, EventEmitter, Input, OnChanges, OnInit, Output, ViewChild} from "@angular/core";
import {CommonModule} from "@angular/common";
import {FormsModule, ReactiveFormsModule} from "@angular/forms";
import {MatAccordion} from "@angular/material/expansion";
import {MatButtonModule} from "@angular/material/button";
import {MatFormFieldModule} from "@angular/material/form-field";
import {MatIconModule} from "@angular/material/icon";
import {MatSelectModule} from "@angular/material/select";
import {MatSortModule} from "@angular/material/sort";
import {MatTableModule} from "@angular/material/table";
import {TranslateModule} from "@ngx-translate/core";
import {OnemrvaMatPanelModule} from "@onemrvapublic/design-system/mat-panel";
import {OnemrvaMatStickerModule} from "@onemrvapublic/design-system/mat-sticker";
import {AggregatedChangePersonalDataValidateResponse, ExternalSource, FieldSource} from "@rest-client/cu-bff";
import {ConsistencyTableComponent} from "./consistency-table/consistency-table.component";
import {FamilyCompositionComponent} from "./family-composition/family-composition.component";

@Component({
    selector: "lib-data-consistency",
    templateUrl: "./data-consistency.component.html",
    styleUrls: ["./data-consistency.component.scss"],
    standalone: true,
    imports: [
        CommonModule,
        TranslateModule,
        FormsModule,
        MatAccordion,
        OnemrvaMatStickerModule,
        OnemrvaMatPanelModule,
        ReactiveFormsModule,
        MatTableModule,
        MatSortModule,
        MatIconModule,
        MatFormFieldModule,
        ConsistencyTableComponent,
        MatSelectModule,
        MatButtonModule,
        FamilyCompositionComponent,
    ],
})
export class DataConsistencyComponent implements OnInit, OnChanges {
    @Input() language: string = "NL";
    @Input() taskStatus: string = "";
    @Input() task!: any;
    @Input() dataConsistencyData!: AggregatedChangePersonalDataValidateResponse;

    @Output() fieldSourcesChange = new EventEmitter<FieldSource[]>();
    @Output() regisVerificationEvent = new EventEmitter<boolean>();
    @Output() tableConsistencyChanged = new EventEmitter<boolean>();

    @ViewChild("accordionForDC") accordionForDC!: MatAccordion;

    displayedColumns: string[] = ["label", "encodedValue", "dbValue", "sourceValue", "valueToKeep", "icon"];

    panelEditStatus: { [key: string]: boolean } = {};
    panelDataLoadedStatus: { [key: string]: boolean } = {};

    citizenInformation: any;

    fieldSources: FieldSource[] = [];

    constructor() {
    }

    ngOnInit(): void {
        this.initializeData();
    }

    ngOnChanges(): void {
        this.initializeData();
    }

    private initializeData(): void {
        if (this.dataConsistencyData) {
            if (this.dataConsistencyData.c1CitizenInformation) {
                this.citizenInformation = this.dataConsistencyData.c1CitizenInformation;
                this.panelDataLoadedStatus["citizenInformation"] = true;
            }
        }
    }

    onTableConsistencyChanged(isConsistent: boolean): void {
        this.tableConsistencyChanged.emit(isConsistent);
    }

    isPanelEdited(panelId: string): boolean {
        return this.panelEditStatus[panelId] || false;
    }

    isPanelDataLoaded(panelId: string): boolean {
        return this.panelDataLoadedStatus[panelId] || false;
    }

    onValueChange(event: { id: string, value: string }): void {
        if (event?.id) {
            const panelId = this.getPanelIdForField(event.id);
            if (panelId) {
                this.panelEditStatus[panelId] = true;
            }

            this.updateFieldValue(event.id, event.value);
        }
    }

    private getPanelIdForField(fieldId: string): string {
        const addressFields = ["address", "street", "houseNumber", "boxNumber", "zipCode", "city", "country"];
        const citizenFields = ["fullName", "birthDate", "nationality", "bankAccount", "otherPersonName"];

        if (addressFields.includes(fieldId) || citizenFields.includes(fieldId)) {
            return "citizenInformation";
        }

        return "";
    }

    private updateFieldValue(fieldId: string, value: string): void {
        if (!this.citizenInformation) {
            return;
        }

        const fieldHandlers: Record<string, (val: string) => void> = {
            street: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.street = val;
                }
            },
            houseNumber: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.houseNumber = val;
                }
            },
            boxNumber: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.boxNumber = val;
                }
            },
            zipCode: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.zipCode = val;
                }
            },
            city: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.city = val;
                }
            },
            country: (val) => {
                if (this.citizenInformation?.address) {
                    this.citizenInformation.address.country = val;
                }
            },
            fullName: (val) => {
                const nameParts = val.split(" ");
                if (nameParts.length > 1 && this.citizenInformation) {
                    this.citizenInformation.firstName = nameParts[0];
                    this.citizenInformation.name = nameParts.slice(1).join(" ");
                }
            },
            birthDate: (val) => {
                if (this.citizenInformation) {
                    this.citizenInformation.dateOfBirth = val;
                }
            },
            nationality: (val) => {
                if (!this.citizenInformation) {
                    return;
                }

                if (typeof this.citizenInformation.nationality === "object") {
                    if (this.language.toLowerCase() === "fr") {
                        this.citizenInformation.nationality.descFr = val;
                    } else {
                        this.citizenInformation.nationality.descNl = val;
                    }
                } else {
                    this.citizenInformation.nationality = val;
                }
            },
            bankAccount: (val) => {
                if (this.citizenInformation) {
                    this.citizenInformation.iban = val;
                }
            },
            bic: (val) => {
                if (this.citizenInformation) {
                    this.citizenInformation.bic = val.toUpperCase();
                }
            },
            otherPersonName: (val) => {
                if (this.citizenInformation) {
                    this.citizenInformation.otherPersonName = val;
                }
            },
            unionContribution: () => {
                console.log("Union contribution updated todo     ");
            },
        };

        if (fieldHandlers[fieldId]) {
            fieldHandlers[fieldId](value);
        }
    }

    onTableSelectionChange(row: any): void {
        if (row?.id && row?.selectedValue?.origin) {
            const source: ExternalSource = this.mapOriginToExternalSource(row.selectedValue.origin);
            const fieldSource: FieldSource = {
                fieldName: row.id,
                source: source,
            };

            const existingIndex = this.fieldSources.findIndex(fs => fs.fieldName === row.id);
            if (existingIndex >= 0) {
                this.fieldSources[existingIndex] = fieldSource;
            } else {
                this.fieldSources.push(fieldSource);
            }

            this.fieldSourcesChange.emit([...this.fieldSources]);
        }
    }

    private mapOriginToExternalSource(origin: string): ExternalSource {
        switch (origin) {
            case "EMPLOYEE":
                return "C1";
            case "ONEM":
                return "ONEM";
            case "SOURCE_AUTHENTIQUES":
                return "AUTHENTIC_SOURCES";
            default:
                return "C1";
        }
    }

    onVerificationChange(isVerified: boolean): void {
        this.regisVerificationEvent.emit(isVerified);
    }

}