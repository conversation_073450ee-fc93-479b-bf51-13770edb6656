package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toCitizenInformationDetailResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainFieldSources
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainUpdateCitizenInformation
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toFieldSourceResponse
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.port.`in`.CitizenInformationUseCase
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.rest.priv.api.CitizenInformationApi
import be.fgov.onerva.cu.rest.priv.model.CitizenInformationDetailResponse
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateCitizenInformationRequest

@RestController
class CitizenInformationController(
    val citizenInformationUseCase: CitizenInformationUseCase,
) : CitizenInformationApi {
    /**
     * GET /api/requests/{requestId}/citizen-information
     * Retrieve employee information for a specific request
     *
     * @param requestId The UUID of the request (required)
     * Note: requestId nullability is handled by Spring's path variable binding.
     * The nullable type is only present due to the Java-generated OpenAPI stub.
     * @return Employee information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    @LogWithRequestId
    override fun getCitizenInformation(requestId: UUID?): CitizenInformationDetailResponse {
        val citizenInfo = citizenInformationUseCase.getCitizenInformation(requestId!!)
            ?: throw InformationNotFoundException("Citizen information not found for request $requestId")

        // Get field sources for this citizen information
        val fieldSources = citizenInformationUseCase.getCitizenInformationFieldSources(requestId)
            .map { it.toFieldSourceResponse() }

        // Create response with citizen info and field sources
        val response = citizenInfo.toCitizenInformationDetailResponse()

        // Set field sources in the response
        response.fieldSources = fieldSources

        return response
    }

    /**
     * PUT /api/requests/{requestId}/citizen-information
     * Update mutable employee information for a specific request
     *
     * @param requestId The UUID of the request (required)
     * Note: requestId nullability is handled by Spring's path variable binding.
     * The nullable type is only present due to the Java-generated OpenAPI stub.
     * @param updateCitizenInformationRequest  (required)
     * @return Employee information successfully updated (status code 200)
     * or Invalid request body (status code 400)
     * or Request not found (status code 404)
     * or Validation error (status code 422)
     */
    @LogWithRequestId
    override fun updateCitizenInformation(
        requestId: UUID?,
        updateCitizenInformationRequest: UpdateCitizenInformationRequest?,
    ) = citizenInformationUseCase.updateCitizenInformation(
        requestId!!,
        updateCitizenInformationRequest?.toDomainUpdateCitizenInformation()
            ?: throw InvalidInputException("updateCitizenInformationRequest is required")
    )

    @LogWithRequestId
    override fun selectCitizenInformationSources(
        requestId: UUID?,
        selectFieldSourcesRequest: SelectFieldSourcesRequest?,
    ) {
        citizenInformationUseCase.selectCitizenInformationFieldSources(
            requestId!!,
            selectFieldSourcesRequest?.toDomainFieldSources()!!
        )
    }
}