package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenC1Response
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toHistoricalCitizenOnemResponse
import be.fgov.onerva.cu.backend.application.domain.AddressNullable
import be.fgov.onerva.cu.backend.application.domain.Barema
import be.fgov.onerva.cu.backend.application.domain.ExternalSource
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenAuthenticSources
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenC1
import be.fgov.onerva.cu.backend.application.domain.HistoricalCitizenOnem
import be.fgov.onerva.cu.backend.application.exception.BaremaNotFoundException
import be.fgov.onerva.cu.backend.application.port.`in`.HistoricalInformationUseCase
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class HistoricalInformationControllerTest {

    @MockK
    private lateinit var historicalInformationUseCase: HistoricalInformationUseCase

    @InjectMockKs
    private lateinit var controller: HistoricalInformationController

    @Nested
    inner class GetCitizenInformation {

        @Test
        fun `getExternalCitizenInformation should call service and return mapped response`() {
            // Given
            val requestId = UUID.randomUUID()
            val source = ExternalSource.ONEM.name
            val citizenInfoWithAddress = HistoricalCitizenOnem(
                firstName = "John", lastName = "Doe", numbox = 12345, nationality = "BE",
                birthDate = null,
                address = AddressNullable(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "Belgium",
                    valueDate = LocalDate.of(2022, 1, 1),
                ),
                iban = "****************", bic = "GKCCBEBB",
                otherPersonName = null,
                bankAccountValueDate = LocalDate.of(2022, 1, 1),
                paymentMode = 1,
                effectiveDate = LocalDate.of(2022, 1, 1),
                authorized = true,
            )

            val expectedResponse = citizenInfoWithAddress.toHistoricalCitizenOnemResponse()

            every {
                historicalInformationUseCase.getHistoricalCitizenOnem(requestId)
            } returns citizenInfoWithAddress

            // When
            val result = controller.getHistoricalCitizenOnem(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getHistoricalCitizenOnem(requestId) }
            assertThat(result).isEqualTo(expectedResponse)
        }
    }

    @Nested
    inner class GetBaremaTests {
        @Test
        fun `should return barema information when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val barema = Barema(
                barema = "A123", article = "Article 42"
            )

            every { historicalInformationUseCase.getBarema(requestId) } returns barema

            // When
            val result = controller.getBarema(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getBarema(requestId) }
            assertThat(result).isNotNull
            assertThat(result.barema).isEqualTo("A123")
            assertThat(result.article).isEqualTo("Article 42")
        }

        @Test
        fun `should return barema with null article when article is null`() {
            // Given
            val requestId = UUID.randomUUID()
            val barema = Barema(
                barema = "B456", article = null
            )

            every { historicalInformationUseCase.getBarema(requestId) } returns barema

            // When
            val result = controller.getBarema(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getBarema(requestId) }
            assertThat(result).isNotNull
            assertThat(result.barema).isEqualTo("B456")
            assertThat(result.article).isNull()
        }

        @Test
        fun `should throw BaremaNotFoundException when barema not found`() {
            // Given
            val requestId = UUID.randomUUID()

            every { historicalInformationUseCase.getBarema(requestId) } returns null

            // When/Then
            assertThatThrownBy { controller.getBarema(requestId) }.isInstanceOf(BaremaNotFoundException::class.java)
                .hasMessage("Barema not found for request $requestId")

            verify(exactly = 1) { historicalInformationUseCase.getBarema(requestId) }
        }

        @Test
        fun `should throw IllegalArgumentException when requestId is null`() {
            // When/Then
            assertThatThrownBy { controller.getBarema(null) }.isInstanceOf(IllegalArgumentException::class.java)

            verify(exactly = 0) { historicalInformationUseCase.getBarema(any()) }
        }
    }

    @Nested
    inner class GetHistoricalCitizenAuthenticSources {

        @Test
        fun `getHistoricalCitizenAuthenticSources should call service and return mapped response`() {
            // Given
            val requestId = UUID.randomUUID()
            val historicalCitizenAuthenticSources = HistoricalCitizenAuthenticSources(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationality = "BE",
                address = AddressNullable(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "Belgium",
                    valueDate = LocalDate.of(2022, 1, 1),
                )
            )

            val expectedResponse = historicalCitizenAuthenticSources.toHistoricalCitizenAuthenticSourcesResponse()

            every {
                historicalInformationUseCase.getHistoricalCitizenAuthenticSources(requestId)
            } returns historicalCitizenAuthenticSources

            // When
            val result = controller.getHistoricalCitizenAuthenticSources(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getHistoricalCitizenAuthenticSources(requestId) }
            assertThat(result).isEqualTo(expectedResponse)
        }

        @Test
        fun `getHistoricalCitizenAuthenticSources should throw IllegalArgumentException when requestId is null`() {
            // When/Then
            assertThatThrownBy { controller.getHistoricalCitizenAuthenticSources(null) }
                .isInstanceOf(IllegalArgumentException::class.java)

            verify(exactly = 0) { historicalInformationUseCase.getHistoricalCitizenAuthenticSources(any()) }
        }
    }

    @Nested
    inner class GetHistoricalCitizenOnem {

        @Test
        fun `getHistoricalCitizenOnem should call service and return mapped response`() {
            // Given
            val requestId = UUID.randomUUID()
            val historicalCitizenOnem = HistoricalCitizenOnem(
                firstName = "John",
                lastName = "Doe",
                numbox = 12345,
                nationality = "BE",
                birthDate = LocalDate.of(1990, 1, 1),
                address = AddressNullable(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "Belgium",
                    valueDate = LocalDate.of(2022, 1, 1),
                ),
                iban = "****************",
                bic = "GKCCBEBB",
                otherPersonName = null,
                bankAccountValueDate = LocalDate.of(2022, 1, 1),
                paymentMode = 1,
                effectiveDate = LocalDate.of(2022, 1, 1),
                authorized = true,
            )

            val expectedResponse = historicalCitizenOnem.toHistoricalCitizenOnemResponse()

            every {
                historicalInformationUseCase.getHistoricalCitizenOnem(requestId)
            } returns historicalCitizenOnem

            // When
            val result = controller.getHistoricalCitizenOnem(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getHistoricalCitizenOnem(requestId) }
            assertThat(result).isEqualTo(expectedResponse)
        }

        @Test
        fun `getHistoricalCitizenOnem should throw IllegalArgumentException when requestId is null`() {
            // When/Then
            assertThatThrownBy { controller.getHistoricalCitizenOnem(null) }
                .isInstanceOf(IllegalArgumentException::class.java)

            verify(exactly = 0) { historicalInformationUseCase.getHistoricalCitizenOnem(any()) }
        }
    }

    @Nested
    inner class GetHistoricalCitizenC1 {

        @Test
        fun `getHistoricalCitizenC1 should call service and return mapped response`() {
            // Given
            val requestId = UUID.randomUUID()
            val historicalCitizenC1 = HistoricalCitizenC1(
                firstName = "John",
                lastName = "Doe",
                birthDate = LocalDate.of(1990, 1, 1),
                nationality = "BE",
                address = AddressNullable(
                    street = "Main Street",
                    houseNumber = "42",
                    boxNumber = "A",
                    zipCode = "1000",
                    city = "Brussels",
                    country = "Belgium",
                    valueDate = LocalDate.of(2022, 1, 1),
                ),
                iban = "****************",
                bic = "GKCCBEBB",
                otherPersonName = null,
                bankAccountValueDate = LocalDate.of(2022, 1, 1),
                effectiveDate = LocalDate.of(2022, 1, 1),
                authorized = true,
                paymentMode = 1,
                numbox = 12345,
            )

            val expectedResponse = historicalCitizenC1.toHistoricalCitizenC1Response()

            every {
                historicalInformationUseCase.getHistoricalCitizenC1(requestId)
            } returns historicalCitizenC1

            // When
            val result = controller.getHistoricalCitizenC1(requestId)

            // Then
            verify(exactly = 1) { historicalInformationUseCase.getHistoricalCitizenC1(requestId) }
            assertThat(result).isEqualTo(expectedResponse)
        }

        @Test
        fun `getHistoricalCitizenC1 should throw IllegalArgumentException when requestId is null`() {
            // When/Then
            assertThatThrownBy { controller.getHistoricalCitizenC1(null) }
                .isInstanceOf(IllegalArgumentException::class.java)

            verify(exactly = 0) { historicalInformationUseCase.getHistoricalCitizenC1(any()) }
        }
    }
}