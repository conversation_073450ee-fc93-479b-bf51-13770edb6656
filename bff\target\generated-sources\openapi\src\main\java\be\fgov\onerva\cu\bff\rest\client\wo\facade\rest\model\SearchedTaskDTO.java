/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.MetaDataDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.StateDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SearchedTaskDTO
 */
@JsonPropertyOrder({
  SearchedTaskDTO.JSON_PROPERTY_TASK_TYPE_CODE,
  SearchedTaskDTO.JSON_PROPERTY_TASK_ID,
  SearchedTaskDTO.JSON_PROPERTY_STATUS,
  SearchedTaskDTO.JSON_PROPERTY_TASK_STEP,
  SearchedTaskDTO.JSON_PROPERTY_METADATA,
  SearchedTaskDTO.JSON_PROPERTY_CONCERNED_ENTITIES,
  SearchedTaskDTO.JSON_PROPERTY_ASSIGNEE,
  SearchedTaskDTO.JSON_PROPERTY_DUE_DATE,
  SearchedTaskDTO.JSON_PROPERTY_PROCESS_ID,
  SearchedTaskDTO.JSON_PROPERTY_CREATION_DATE
})
@JsonTypeName("SearchedTask")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SearchedTaskDTO {
  public static final String JSON_PROPERTY_TASK_TYPE_CODE = "taskTypeCode";
  private String taskTypeCode;

  public static final String JSON_PROPERTY_TASK_ID = "taskId";
  private Long taskId;

  public static final String JSON_PROPERTY_STATUS = "status";
  private StateDTO status;

  public static final String JSON_PROPERTY_TASK_STEP = "taskStep";
  private String taskStep;

  public static final String JSON_PROPERTY_METADATA = "metadata";
  private List<MetaDataDTO> metadata = new ArrayList<>();

  public static final String JSON_PROPERTY_CONCERNED_ENTITIES = "concernedEntities";
  private List<ThirdPartyDTO> concernedEntities = new ArrayList<>();

  public static final String JSON_PROPERTY_ASSIGNEE = "assignee";
  private String assignee;

  public static final String JSON_PROPERTY_DUE_DATE = "dueDate";
  private LocalDate dueDate;

  public static final String JSON_PROPERTY_PROCESS_ID = "processId";
  private Long processId;

  public static final String JSON_PROPERTY_CREATION_DATE = "creationDate";
  private LocalDateTime creationDate;

  public SearchedTaskDTO() {
  }

  public SearchedTaskDTO taskTypeCode(String taskTypeCode) {
    
    this.taskTypeCode = taskTypeCode;
    return this;
  }

  /**
   * Get taskTypeCode
   * @return taskTypeCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_TYPE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTaskTypeCode() {
    return taskTypeCode;
  }


  @JsonProperty(JSON_PROPERTY_TASK_TYPE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskTypeCode(String taskTypeCode) {
    this.taskTypeCode = taskTypeCode;
  }

  public SearchedTaskDTO taskId(Long taskId) {
    
    this.taskId = taskId;
    return this;
  }

  /**
   * Get taskId
   * @return taskId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getTaskId() {
    return taskId;
  }


  @JsonProperty(JSON_PROPERTY_TASK_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskId(Long taskId) {
    this.taskId = taskId;
  }

  public SearchedTaskDTO status(StateDTO status) {
    
    this.status = status;
    return this;
  }

  /**
   * Get status
   * @return status
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public StateDTO getStatus() {
    return status;
  }


  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(StateDTO status) {
    this.status = status;
  }

  public SearchedTaskDTO taskStep(String taskStep) {
    
    this.taskStep = taskStep;
    return this;
  }

  /**
   * Get taskStep
   * @return taskStep
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_STEP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getTaskStep() {
    return taskStep;
  }


  @JsonProperty(JSON_PROPERTY_TASK_STEP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskStep(String taskStep) {
    this.taskStep = taskStep;
  }

  public SearchedTaskDTO metadata(List<MetaDataDTO> metadata) {
    
    this.metadata = metadata;
    return this;
  }

  public SearchedTaskDTO addMetadataItem(MetaDataDTO metadataItem) {
    if (this.metadata == null) {
      this.metadata = new ArrayList<>();
    }
    this.metadata.add(metadataItem);
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MetaDataDTO> getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(List<MetaDataDTO> metadata) {
    this.metadata = metadata;
  }

  public SearchedTaskDTO concernedEntities(List<ThirdPartyDTO> concernedEntities) {
    
    this.concernedEntities = concernedEntities;
    return this;
  }

  public SearchedTaskDTO addConcernedEntitiesItem(ThirdPartyDTO concernedEntitiesItem) {
    if (this.concernedEntities == null) {
      this.concernedEntities = new ArrayList<>();
    }
    this.concernedEntities.add(concernedEntitiesItem);
    return this;
  }

  /**
   * Get concernedEntities
   * @return concernedEntities
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<ThirdPartyDTO> getConcernedEntities() {
    return concernedEntities;
  }


  @JsonProperty(JSON_PROPERTY_CONCERNED_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConcernedEntities(List<ThirdPartyDTO> concernedEntities) {
    this.concernedEntities = concernedEntities;
  }

  public SearchedTaskDTO assignee(String assignee) {
    
    this.assignee = assignee;
    return this;
  }

  /**
   * Get assignee
   * @return assignee
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAssignee() {
    return assignee;
  }


  @JsonProperty(JSON_PROPERTY_ASSIGNEE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAssignee(String assignee) {
    this.assignee = assignee;
  }

  public SearchedTaskDTO dueDate(LocalDate dueDate) {
    
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Get dueDate
   * @return dueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDate getDueDate() {
    return dueDate;
  }


  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDueDate(LocalDate dueDate) {
    this.dueDate = dueDate;
  }

  public SearchedTaskDTO processId(Long processId) {
    
    this.processId = processId;
    return this;
  }

  /**
   * Get processId
   * @return processId
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROCESS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Long getProcessId() {
    return processId;
  }


  @JsonProperty(JSON_PROPERTY_PROCESS_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProcessId(Long processId) {
    this.processId = processId;
  }

  public SearchedTaskDTO creationDate(LocalDateTime creationDate) {
    
    this.creationDate = creationDate;
    return this;
  }

  /**
   * Get creationDate
   * @return creationDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCreationDate() {
    return creationDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreationDate(LocalDateTime creationDate) {
    this.creationDate = creationDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SearchedTaskDTO searchedTask = (SearchedTaskDTO) o;
    return Objects.equals(this.taskTypeCode, searchedTask.taskTypeCode) &&
        Objects.equals(this.taskId, searchedTask.taskId) &&
        Objects.equals(this.status, searchedTask.status) &&
        Objects.equals(this.taskStep, searchedTask.taskStep) &&
        Objects.equals(this.metadata, searchedTask.metadata) &&
        Objects.equals(this.concernedEntities, searchedTask.concernedEntities) &&
        Objects.equals(this.assignee, searchedTask.assignee) &&
        Objects.equals(this.dueDate, searchedTask.dueDate) &&
        Objects.equals(this.processId, searchedTask.processId) &&
        Objects.equals(this.creationDate, searchedTask.creationDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskTypeCode, taskId, status, taskStep, metadata, concernedEntities, assignee, dueDate, processId, creationDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SearchedTaskDTO {\n");
    sb.append("    taskTypeCode: ").append(toIndentedString(taskTypeCode)).append("\n");
    sb.append("    taskId: ").append(toIndentedString(taskId)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    taskStep: ").append(toIndentedString(taskStep)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    concernedEntities: ").append(toIndentedString(concernedEntities)).append("\n");
    sb.append("    assignee: ").append(toIndentedString(assignee)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("    processId: ").append(toIndentedString(processId)).append("\n");
    sb.append("    creationDate: ").append(toIndentedString(creationDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

