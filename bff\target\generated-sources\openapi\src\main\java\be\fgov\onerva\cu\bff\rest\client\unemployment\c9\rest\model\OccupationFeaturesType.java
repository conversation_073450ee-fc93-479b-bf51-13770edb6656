/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * OccupationFeaturesType
 */
@JsonPropertyOrder({
  OccupationFeaturesType.JSON_PROPERTY_OCCUPATION_INTERRUPTION,
  OccupationFeaturesType.JSON_PROPERTY_WORK_SCHEDULE_CODE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class OccupationFeaturesType {
  public static final String JSON_PROPERTY_OCCUPATION_INTERRUPTION = "occupationInterruption";
  private String occupationInterruption;

  public static final String JSON_PROPERTY_WORK_SCHEDULE_CODE = "workScheduleCode";
  private String workScheduleCode;

  public OccupationFeaturesType() {
  }

  public OccupationFeaturesType occupationInterruption(String occupationInterruption) {
    
    this.occupationInterruption = occupationInterruption;
    return this;
  }

  /**
   * Get occupationInterruption
   * @return occupationInterruption
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_OCCUPATION_INTERRUPTION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getOccupationInterruption() {
    return occupationInterruption;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATION_INTERRUPTION)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setOccupationInterruption(String occupationInterruption) {
    this.occupationInterruption = occupationInterruption;
  }

  public OccupationFeaturesType workScheduleCode(String workScheduleCode) {
    
    this.workScheduleCode = workScheduleCode;
    return this;
  }

  /**
   * Get workScheduleCode
   * @return workScheduleCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WORK_SCHEDULE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWorkScheduleCode() {
    return workScheduleCode;
  }


  @JsonProperty(JSON_PROPERTY_WORK_SCHEDULE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWorkScheduleCode(String workScheduleCode) {
    this.workScheduleCode = workScheduleCode;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OccupationFeaturesType occupationFeaturesType = (OccupationFeaturesType) o;
    return Objects.equals(this.occupationInterruption, occupationFeaturesType.occupationInterruption) &&
        Objects.equals(this.workScheduleCode, occupationFeaturesType.workScheduleCode);
  }

  @Override
  public int hashCode() {
    return Objects.hash(occupationInterruption, workScheduleCode);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OccupationFeaturesType {\n");
    sb.append("    occupationInterruption: ").append(toIndentedString(occupationInterruption)).append("\n");
    sb.append("    workScheduleCode: ").append(toIndentedString(workScheduleCode)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

