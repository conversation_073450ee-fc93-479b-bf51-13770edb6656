package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.ExternalSource
import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonValue
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param fieldName The name of the field this source applies to
 * @param source 
 */
data class FieldSource(

    @Schema(example = "null", description = "The name of the field this source applies to")
    @get:Json<PERSON>roperty("fieldName") val fieldName: kotlin.String? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("source") val source: ExternalSource? = null
    ) {

}

