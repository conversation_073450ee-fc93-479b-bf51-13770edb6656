/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyQualityDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.ThirdPartyTypeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * InputThirdPartyDTO
 */
@JsonPropertyOrder({
  InputThirdPartyDTO.JSON_PROPERTY_ID,
  InputThirdPartyDTO.JSON_PROPERTY_TYPE,
  InputThirdPartyDTO.JSON_PROPERTY_QUALITY
})
@JsonTypeName("InputThirdParty")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class InputThirdPartyDTO {
  public static final String JSON_PROPERTY_ID = "id";
  private String id;

  public static final String JSON_PROPERTY_TYPE = "type";
  private ThirdPartyTypeDTO type;

  public static final String JSON_PROPERTY_QUALITY = "quality";
  private ThirdPartyQualityDTO quality;

  public InputThirdPartyDTO() {
  }

  public InputThirdPartyDTO id(String id) {
    
    this.id = id;
    return this;
  }

  /**
   * The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen
   * @return id
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setId(String id) {
    this.id = id;
  }

  public InputThirdPartyDTO type(ThirdPartyTypeDTO type) {
    
    this.type = type;
    return this;
  }

  /**
   * Get type
   * @return type
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public ThirdPartyTypeDTO getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setType(ThirdPartyTypeDTO type) {
    this.type = type;
  }

  public InputThirdPartyDTO quality(ThirdPartyQualityDTO quality) {
    
    this.quality = quality;
    return this;
  }

  /**
   * Get quality
   * @return quality
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_QUALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public ThirdPartyQualityDTO getQuality() {
    return quality;
  }


  @JsonProperty(JSON_PROPERTY_QUALITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setQuality(ThirdPartyQualityDTO quality) {
    this.quality = quality;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    InputThirdPartyDTO inputThirdParty = (InputThirdPartyDTO) o;
    return Objects.equals(this.id, inputThirdParty.id) &&
        Objects.equals(this.type, inputThirdParty.type) &&
        Objects.equals(this.quality, inputThirdParty.quality);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, type, quality);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class InputThirdPartyDTO {\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    quality: ").append(toIndentedString(quality)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

