/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.api

import com.fasterxml.jackson.annotation.JsonProperty

import org.springframework.web.client.RestClient
import org.springframework.web.client.RestClientResponseException

import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter
import org.springframework.http.ResponseEntity
import org.springframework.http.MediaType


import be.fgov.onerva.wo.facade.rest.model.AskReviewRequestDTO
import be.fgov.onerva.wo.facade.rest.model.AssignTaskRequestDTO
import be.fgov.onerva.wo.facade.rest.model.AwakenRequestDTO
import be.fgov.onerva.wo.facade.rest.model.CloseProcessOptionsDTO
import be.fgov.onerva.wo.facade.rest.model.CloseTaskOptionsDTO
import be.fgov.onerva.wo.facade.rest.model.CommentDTO
import be.fgov.onerva.wo.facade.rest.model.CreatableTaskDTO
import be.fgov.onerva.wo.facade.rest.model.EndReviewRequestDTO
import be.fgov.onerva.wo.facade.rest.model.InputMetaDataDTO
import be.fgov.onerva.wo.facade.rest.model.PageableDTO
import be.fgov.onerva.wo.facade.rest.model.ProblemDTO
import be.fgov.onerva.wo.facade.rest.model.ReopenRequestDTO
import be.fgov.onerva.wo.facade.rest.model.SearchableTaskDTO
import be.fgov.onerva.wo.facade.rest.model.SearchedTasksPageDTO
import be.fgov.onerva.wo.facade.rest.model.SleepRequestDTO
import be.fgov.onerva.wo.facade.rest.model.TaskDTO
import be.fgov.onerva.wo.facade.rest.model.UpdatableTaskDTO
import be.fgov.onerva.wo.facade.infrastructure.*

class FacadeControllerApi(client: RestClient) : ApiClient(client) {

    constructor(baseUrl: String) : this(RestClient.builder()
        .baseUrl(baseUrl)
        .messageConverters { it.add(MappingJackson2HttpMessageConverter()) }
        .build()
    )


    @Throws(RestClientResponseException::class)
    fun addCommentToTask(taskId: kotlin.Long, commentDTO: CommentDTO): TaskDTO {
        val result = addCommentToTaskWithHttpInfo(taskId = taskId, commentDTO = commentDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun addCommentToTaskWithHttpInfo(taskId: kotlin.Long, commentDTO: CommentDTO): ResponseEntity<TaskDTO> {
        val localVariableConfig = addCommentToTaskRequestConfig(taskId = taskId, commentDTO = commentDTO)
        return request<CommentDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun addCommentToTaskRequestConfig(taskId: kotlin.Long, commentDTO: CommentDTO) : RequestConfig<CommentDTO> {
        val localVariableBody = commentDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/comment",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun askReview(taskId: kotlin.Long, askReviewRequestDTO: AskReviewRequestDTO? = null): TaskDTO {
        val result = askReviewWithHttpInfo(taskId = taskId, askReviewRequestDTO = askReviewRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun askReviewWithHttpInfo(taskId: kotlin.Long, askReviewRequestDTO: AskReviewRequestDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = askReviewRequestConfig(taskId = taskId, askReviewRequestDTO = askReviewRequestDTO)
        return request<AskReviewRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun askReviewRequestConfig(taskId: kotlin.Long, askReviewRequestDTO: AskReviewRequestDTO? = null) : RequestConfig<AskReviewRequestDTO> {
        val localVariableBody = askReviewRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/askReview",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun assignAndCloseTask(taskId: kotlin.Long, assignee: kotlin.String, closeProcess: kotlin.Boolean? = false, assignTaskRequestDTO: AssignTaskRequestDTO? = null): TaskDTO {
        val result = assignAndCloseTaskWithHttpInfo(taskId = taskId, assignee = assignee, closeProcess = closeProcess, assignTaskRequestDTO = assignTaskRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun assignAndCloseTaskWithHttpInfo(taskId: kotlin.Long, assignee: kotlin.String, closeProcess: kotlin.Boolean? = false, assignTaskRequestDTO: AssignTaskRequestDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = assignAndCloseTaskRequestConfig(taskId = taskId, assignee = assignee, closeProcess = closeProcess, assignTaskRequestDTO = assignTaskRequestDTO)
        return request<AssignTaskRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun assignAndCloseTaskRequestConfig(taskId: kotlin.Long, assignee: kotlin.String, closeProcess: kotlin.Boolean? = false, assignTaskRequestDTO: AssignTaskRequestDTO? = null) : RequestConfig<AssignTaskRequestDTO> {
        val localVariableBody = assignTaskRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("assignee", listOf(assignee.toString()))
                if (closeProcess != null) {
                    put("closeProcess", listOf(closeProcess.toString()))
                }
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/assignAndClose",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun assignTask(taskId: kotlin.Long, assignee: kotlin.String, assignTaskRequestDTO: AssignTaskRequestDTO? = null): TaskDTO {
        val result = assignTaskWithHttpInfo(taskId = taskId, assignee = assignee, assignTaskRequestDTO = assignTaskRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun assignTaskWithHttpInfo(taskId: kotlin.Long, assignee: kotlin.String, assignTaskRequestDTO: AssignTaskRequestDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = assignTaskRequestConfig(taskId = taskId, assignee = assignee, assignTaskRequestDTO = assignTaskRequestDTO)
        return request<AssignTaskRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun assignTaskRequestConfig(taskId: kotlin.Long, assignee: kotlin.String, assignTaskRequestDTO: AssignTaskRequestDTO? = null) : RequestConfig<AssignTaskRequestDTO> {
        val localVariableBody = assignTaskRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("assignee", listOf(assignee.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json, application/problem+json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/assign",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun awaken(taskId: kotlin.Long, awakenRequestDTO: AwakenRequestDTO): TaskDTO {
        val result = awakenWithHttpInfo(taskId = taskId, awakenRequestDTO = awakenRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun awakenWithHttpInfo(taskId: kotlin.Long, awakenRequestDTO: AwakenRequestDTO): ResponseEntity<TaskDTO> {
        val localVariableConfig = awakenRequestConfig(taskId = taskId, awakenRequestDTO = awakenRequestDTO)
        return request<AwakenRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun awakenRequestConfig(taskId: kotlin.Long, awakenRequestDTO: AwakenRequestDTO) : RequestConfig<AwakenRequestDTO> {
        val localVariableBody = awakenRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/awaken",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun closeProcess(processId: kotlin.Long, option: CloseProcessOptionsDTO? = null): Unit {
        closeProcessWithHttpInfo(processId = processId, option = option)
    }

    @Throws(RestClientResponseException::class)
    fun closeProcessWithHttpInfo(processId: kotlin.Long, option: CloseProcessOptionsDTO? = null): ResponseEntity<Unit> {
        val localVariableConfig = closeProcessRequestConfig(processId = processId, option = option)
        return request<Unit, Unit>(
            localVariableConfig
        )
    }

    fun closeProcessRequestConfig(processId: kotlin.Long, option: CloseProcessOptionsDTO? = null) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                if (option != null) {
                    put("option", listOf(option.toString()))
                }
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        
        val params = mutableMapOf<String, Any>(
            "processId" to processId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/process/{processId}/close",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun closeTask(taskId: kotlin.Long, options: CloseTaskOptionsDTO? = null): TaskDTO {
        val result = closeTaskWithHttpInfo(taskId = taskId, options = options)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun closeTaskWithHttpInfo(taskId: kotlin.Long, options: CloseTaskOptionsDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = closeTaskRequestConfig(taskId = taskId, options = options)
        return request<Unit, TaskDTO>(
            localVariableConfig
        )
    }

    fun closeTaskRequestConfig(taskId: kotlin.Long, options: CloseTaskOptionsDTO? = null) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                if (options != null) {
                    put("options", listOf(options.toString()))
                }
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/close",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun createTask(creatableTaskDTO: CreatableTaskDTO): TaskDTO {
        val result = createTaskWithHttpInfo(creatableTaskDTO = creatableTaskDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun createTaskWithHttpInfo(creatableTaskDTO: CreatableTaskDTO): ResponseEntity<TaskDTO> {
        val localVariableConfig = createTaskRequestConfig(creatableTaskDTO = creatableTaskDTO)
        return request<CreatableTaskDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun createTaskRequestConfig(creatableTaskDTO: CreatableTaskDTO) : RequestConfig<CreatableTaskDTO> {
        val localVariableBody = creatableTaskDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json, application/problem+json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun endReview(taskId: kotlin.Long, endReviewRequestDTO: EndReviewRequestDTO? = null): TaskDTO {
        val result = endReviewWithHttpInfo(taskId = taskId, endReviewRequestDTO = endReviewRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun endReviewWithHttpInfo(taskId: kotlin.Long, endReviewRequestDTO: EndReviewRequestDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = endReviewRequestConfig(taskId = taskId, endReviewRequestDTO = endReviewRequestDTO)
        return request<EndReviewRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun endReviewRequestConfig(taskId: kotlin.Long, endReviewRequestDTO: EndReviewRequestDTO? = null) : RequestConfig<EndReviewRequestDTO> {
        val localVariableBody = endReviewRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/endReview",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun getTask(taskId: kotlin.Long): TaskDTO {
        val result = getTaskWithHttpInfo(taskId = taskId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun getTaskWithHttpInfo(taskId: kotlin.Long): ResponseEntity<TaskDTO> {
        val localVariableConfig = getTaskRequestConfig(taskId = taskId)
        return request<Unit, TaskDTO>(
            localVariableConfig
        )
    }

    fun getTaskRequestConfig(taskId: kotlin.Long) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.GET,
            path = "/tasks/{taskId}",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun preClose(taskId: kotlin.Long): TaskDTO {
        val result = preCloseWithHttpInfo(taskId = taskId)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun preCloseWithHttpInfo(taskId: kotlin.Long): ResponseEntity<TaskDTO> {
        val localVariableConfig = preCloseRequestConfig(taskId = taskId)
        return request<Unit, TaskDTO>(
            localVariableConfig
        )
    }

    fun preCloseRequestConfig(taskId: kotlin.Long) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/preClose",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun reopen(taskId: kotlin.Long, reopenRequestDTO: ReopenRequestDTO? = null): TaskDTO {
        val result = reopenWithHttpInfo(taskId = taskId, reopenRequestDTO = reopenRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun reopenWithHttpInfo(taskId: kotlin.Long, reopenRequestDTO: ReopenRequestDTO? = null): ResponseEntity<TaskDTO> {
        val localVariableConfig = reopenRequestConfig(taskId = taskId, reopenRequestDTO = reopenRequestDTO)
        return request<ReopenRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun reopenRequestConfig(taskId: kotlin.Long, reopenRequestDTO: ReopenRequestDTO? = null) : RequestConfig<ReopenRequestDTO> {
        val localVariableBody = reopenRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/reopen",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun searchTasks(searchableTaskDTO: SearchableTaskDTO, pageable: PageableDTO? = null): SearchedTasksPageDTO {
        val result = searchTasksWithHttpInfo(searchableTaskDTO = searchableTaskDTO, pageable = pageable)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun searchTasksWithHttpInfo(searchableTaskDTO: SearchableTaskDTO, pageable: PageableDTO? = null): ResponseEntity<SearchedTasksPageDTO> {
        val localVariableConfig = searchTasksRequestConfig(searchableTaskDTO = searchableTaskDTO, pageable = pageable)
        return request<SearchableTaskDTO, SearchedTasksPageDTO>(
            localVariableConfig
        )
    }

    fun searchTasksRequestConfig(searchableTaskDTO: SearchableTaskDTO, pageable: PageableDTO? = null) : RequestConfig<SearchableTaskDTO> {
        val localVariableBody = searchableTaskDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                if (pageable != null) {
                    put("pageable", listOf(pageable.toString()))
                }
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/search",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun sleep(taskId: kotlin.Long, sleepRequestDTO: SleepRequestDTO): TaskDTO {
        val result = sleepWithHttpInfo(taskId = taskId, sleepRequestDTO = sleepRequestDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun sleepWithHttpInfo(taskId: kotlin.Long, sleepRequestDTO: SleepRequestDTO): ResponseEntity<TaskDTO> {
        val localVariableConfig = sleepRequestConfig(taskId = taskId, sleepRequestDTO = sleepRequestDTO)
        return request<SleepRequestDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun sleepRequestConfig(taskId: kotlin.Long, sleepRequestDTO: SleepRequestDTO) : RequestConfig<SleepRequestDTO> {
        val localVariableBody = sleepRequestDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/tasks/{taskId}/sleep",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateProcess(processId: kotlin.Long, inputMetaDataDTO: kotlin.collections.List<InputMetaDataDTO>): Unit {
        updateProcessWithHttpInfo(processId = processId, inputMetaDataDTO = inputMetaDataDTO)
    }

    @Throws(RestClientResponseException::class)
    fun updateProcessWithHttpInfo(processId: kotlin.Long, inputMetaDataDTO: kotlin.collections.List<InputMetaDataDTO>): ResponseEntity<Unit> {
        val localVariableConfig = updateProcessRequestConfig(processId = processId, inputMetaDataDTO = inputMetaDataDTO)
        return request<kotlin.collections.List<InputMetaDataDTO>, Unit>(
            localVariableConfig
        )
    }

    fun updateProcessRequestConfig(processId: kotlin.Long, inputMetaDataDTO: kotlin.collections.List<InputMetaDataDTO>) : RequestConfig<kotlin.collections.List<InputMetaDataDTO>> {
        val localVariableBody = inputMetaDataDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        
        val params = mutableMapOf<String, Any>(
            "processId" to processId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/process/{processId}",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    @Throws(RestClientResponseException::class)
    fun updateTask(taskId: kotlin.Long, updatableTaskDTO: UpdatableTaskDTO): TaskDTO {
        val result = updateTaskWithHttpInfo(taskId = taskId, updatableTaskDTO = updatableTaskDTO)
        return result.body!!
    }

    @Throws(RestClientResponseException::class)
    fun updateTaskWithHttpInfo(taskId: kotlin.Long, updatableTaskDTO: UpdatableTaskDTO): ResponseEntity<TaskDTO> {
        val localVariableConfig = updateTaskRequestConfig(taskId = taskId, updatableTaskDTO = updatableTaskDTO)
        return request<UpdatableTaskDTO, TaskDTO>(
            localVariableConfig
        )
    }

    fun updateTaskRequestConfig(taskId: kotlin.Long, updatableTaskDTO: UpdatableTaskDTO) : RequestConfig<UpdatableTaskDTO> {
        val localVariableBody = updatableTaskDTO
        val localVariableQuery = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        val params = mutableMapOf<String, Any>(
            "taskId" to taskId,
        )

        return RequestConfig(
            method = RequestMethod.PUT,
            path = "/tasks/{taskId}",
            params = params,
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

}
