/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param username 
 * @param firstname 
 * @param lastname 
 * @param operatorCodes 
 * @param inss 
 * @param operatorCode 
 */


data class UserDTO (

    @get:JsonProperty("username")
    val username: kotlin.String,

    @get:JsonProperty("firstname")
    val firstname: kotlin.String,

    @get:JsonProperty("lastname")
    val lastname: kotlin.String,

    @get:JsonProperty("operatorCodes")
    val operatorCodes: kotlin.collections.List<kotlin.String>,

    @get:JsonProperty("inss")
    val inss: kotlin.String,

    @get:JsonProperty("operatorCode")
    @Deprecated(message = "This property is deprecated.")
    val operatorCode: kotlin.String? = null

) {


}

