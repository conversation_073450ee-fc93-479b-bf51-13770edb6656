package be.fgov.onerva.cu.backend.application.port.`in`

import java.util.*
import be.fgov.onerva.cu.backend.application.domain.RequestBasicInfo

/**
 * Use case interface for retrieving basic request information.
 *
 * This interface defines the contract for accessing basic request information as part of the
 * application's core business logic layer. It follows the hexagonal architecture pattern
 * by defining an input port that can be used by external adapters.
 *
 * @see RequestBasicInfo The domain model containing basic request information
 */
fun interface RequestBasicInfoUseCase {
    /**
     * Retrieves basic information for a specific request.
     *
     * @param requestId The unique identifier of the request
     * @return The basic information of the request
     * @throws RequestIdNotFoundException if the specified request ID does not exist
     */
    fun getRequestBasicInfo(requestId: UUID): RequestBasicInfo
}