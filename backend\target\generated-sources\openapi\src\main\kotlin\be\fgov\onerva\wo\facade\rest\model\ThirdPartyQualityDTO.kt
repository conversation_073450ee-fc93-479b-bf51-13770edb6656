/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * The default value for a citizen is SOCIAL_INSURED, and the default value for a company is EMPLOYER.
 *
 * Values: PAYMENT_ORGANISATION,ESTABLISHMENT_UNIT,EMPLOYER,WORKER,FAMILY,SOCIAL_INSURED
 */

enum class ThirdPartyQualityDTO(val value: kotlin.String) {

    @JsonProperty(value = "PAYMENT_ORGANISATION")
    PAYMENT_ORGANISATION("PAYMENT_ORGANISATION"),

    @JsonProperty(value = "ESTABLISHMENT_UNIT")
    ESTABLISHMENT_UNIT("ESTABLISHMENT_UNIT"),

    @JsonProperty(value = "EMPLOYER")
    EMPLOYER("EMPLOYER"),

    @JsonProperty(value = "WORKER")
    WORKER("WORKER"),

    @JsonProperty(value = "FAMILY")
    FAMILY("FAMILY"),

    @JsonProperty(value = "SOCIAL_INSURED")
    SOCIAL_INSURED("SOCIAL_INSURED");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is ThirdPartyQualityDTO) "$data" else null

        /**
         * Returns a valid [ThirdPartyQualityDTO] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): ThirdPartyQualityDTO? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

