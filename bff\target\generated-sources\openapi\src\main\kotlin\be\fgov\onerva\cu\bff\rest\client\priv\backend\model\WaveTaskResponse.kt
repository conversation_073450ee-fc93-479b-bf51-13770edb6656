/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.WaveTaskStatus

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param processId 
 * @param taskId 
 * @param status 
 * @param waveUrl 
 */


data class WaveTaskResponse (

    @get:JsonProperty("processId")
    val processId: kotlin.String,

    @get:JsonProperty("taskId")
    val taskId: kotlin.String,

    @get:JsonProperty("status")
    val status: WaveTaskStatus,

    @get:JsonProperty("waveUrl")
    val waveUrl: kotlin.String

) {


}

