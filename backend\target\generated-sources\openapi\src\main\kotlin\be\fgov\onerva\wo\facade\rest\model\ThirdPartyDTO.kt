/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model

import be.fgov.onerva.wo.facade.rest.model.ThirdPartyQualityDTO
import be.fgov.onerva.wo.facade.rest.model.ThirdPartyTypeDTO

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.annotation.JsonSubTypes
import com.fasterxml.jackson.annotation.JsonTypeInfo

/**
 * 
 *
 * @param id The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen
 * @param type 
 * @param quality 
 */
@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, include = JsonTypeInfo.As.PROPERTY, property = "type", visible = true)
@JsonSubTypes(
    JsonSubTypes.Type(value = CitizenDTO::class, name = "citizen"),
    JsonSubTypes.Type(value = CompanyDTO::class, name = "company"),
    JsonSubTypes.Type(value = UnknownDTO::class, name = "unknown")
)

interface ThirdPartyDTO {

    /* The id must be the numbox retrieved from the mainframe (or the ID from the person service) for the citizen */
    @get:JsonProperty("id")
    val id: kotlin.String
    @get:JsonProperty("type")
    val type: ThirdPartyTypeDTO
    @get:JsonProperty("quality")
    val quality: ThirdPartyQualityDTO?

}

