package be.fgov.onerva.cu.backend.adapter.`in`.controller

import java.util.UUID
import org.springframework.web.bind.annotation.RestController
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainFieldSources
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toDomainUpdateModeOfPayment
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toFieldSourceResponse
import be.fgov.onerva.cu.backend.adapter.`in`.mapper.toModeOfPaymentDetailResponse
import be.fgov.onerva.cu.backend.application.exception.InformationNotFoundException
import be.fgov.onerva.cu.backend.application.exception.InvalidInputException
import be.fgov.onerva.cu.backend.application.port.`in`.ModeOfPaymentUseCase
import be.fgov.onerva.cu.common.aop.LogWithRequestId
import be.fgov.onerva.cu.common.utils.logger
import be.fgov.onerva.cu.rest.priv.api.ModeOfPaymentApi
import be.fgov.onerva.cu.rest.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.rest.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.rest.priv.model.UpdateModeOfPaymentRequest

@RestController
class ModeOfPaymentController(val modeOfPaymentUseCase: ModeOfPaymentUseCase) : ModeOfPaymentApi {
    private val log = logger

    /**
     * GET /api/requests/{requestId}/mode-of-payment
     * Retrieve mode of payment
     *
     * @param requestId The UUID of the request (required)
     * @return Employee information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    /**
     * GET /api/requests/{requestId}/mode-of-payment
     * Retrieve mode of payment
     *
     * @param requestId The UUID of the request (required)
     * @return Employee information successfully retrieved (status code 200)
     * or Request not found (status code 404)
     */
    @LogWithRequestId
    override fun getModeOfPayment(requestId: UUID?): ModeOfPaymentDetailResponse {
        val modeOfPayment = modeOfPaymentUseCase.getModeOfPayment(requestId!!)
            ?: throw InformationNotFoundException("Mode of payment not found for request $requestId")

        // Get field sources for this mode of payment
        val fieldSources = modeOfPaymentUseCase.getModeOfPaymentFieldSources(requestId)
            .map { it.toFieldSourceResponse() }

        // Create response with mode of payment info and field sources
        val response = modeOfPayment.toModeOfPaymentDetailResponse()

        // Set field sources in the response
        response.fieldSources = fieldSources

        return response
    }

    /**
     * PUT /api/requests/{requestId}/mode-of-payment
     * Update mutable employee information for a specific request
     *
     * @param requestId The UUID of the request (required)
     * @param updateModeOfPaymentRequest  (required)
     * @return Employee information successfully updated (status code 204)
     * or Invalid request body (status code 400)
     * or Request not found (status code 404)
     * or Validation error (status code 422)
     */
    @LogWithRequestId
    override fun updateModeOfPayment(requestId: UUID?, updateModeOfPaymentRequest: UpdateModeOfPaymentRequest?) {

        modeOfPaymentUseCase.updateModeOfPayment(
            requestId!!, updateModeOfPaymentRequest?.toDomainUpdateModeOfPayment() ?: throw InvalidInputException(
                "updateModeOfPaymentRequest is required"
            )
        )
    }

    /**
     * PUT /api/requests/{requestId}/mode-of-payment/select
     * Select sources for individual fields in mode of payment
     *
     * @param requestId The UUID of the request (required)
     * @param selectFieldSourcesRequest  (required)
     * @return Field sources successfully selected (status code 204)
     *         or Invalid request body (status code 400)
     *         or Request not found (status code 404)
     *         or Validation error (status code 422)
     */
    @LogWithRequestId
    override fun selectModeOfPaymentSources(requestId: UUID?, selectFieldSourcesRequest: SelectFieldSourcesRequest?) {
        log.info("Selecting mode of payment field sources for request $requestId")
        modeOfPaymentUseCase.selectModeOfPaymentFieldSources(
            requestId!!,
            selectFieldSourcesRequest?.toDomainFieldSources()!!
        )
    }
}