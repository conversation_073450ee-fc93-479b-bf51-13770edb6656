/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * BusinessKey
 */
@JsonPropertyOrder({
  BusinessKey.JSON_PROPERTY_SOURCE,
  BusinessKey.JSON_PROPERTY_KEY_TYPE,
  BusinessKey.JSON_PROPERTY_KEY_VALUE
})
@JsonTypeName("BusinessKey_")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class BusinessKey {
  public static final String JSON_PROPERTY_SOURCE = "source";
  private String source;

  public static final String JSON_PROPERTY_KEY_TYPE = "keyType";
  private String keyType;

  public static final String JSON_PROPERTY_KEY_VALUE = "keyValue";
  private String keyValue;

  public BusinessKey() {
  }

  public BusinessKey source(String source) {
    
    this.source = source;
    return this;
  }

  /**
   * Get source
   * @return source
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSource() {
    return source;
  }


  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSource(String source) {
    this.source = source;
  }

  public BusinessKey keyType(String keyType) {
    
    this.keyType = keyType;
    return this;
  }

  /**
   * Get keyType
   * @return keyType
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KEY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getKeyType() {
    return keyType;
  }


  @JsonProperty(JSON_PROPERTY_KEY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKeyType(String keyType) {
    this.keyType = keyType;
  }

  public BusinessKey keyValue(String keyValue) {
    
    this.keyValue = keyValue;
    return this;
  }

  /**
   * Get keyValue
   * @return keyValue
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_KEY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getKeyValue() {
    return keyValue;
  }


  @JsonProperty(JSON_PROPERTY_KEY_VALUE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setKeyValue(String keyValue) {
    this.keyValue = keyValue;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    BusinessKey businessKey = (BusinessKey) o;
    return Objects.equals(this.source, businessKey.source) &&
        Objects.equals(this.keyType, businessKey.keyType) &&
        Objects.equals(this.keyValue, businessKey.keyValue);
  }

  @Override
  public int hashCode() {
    return Objects.hash(source, keyType, keyValue);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class BusinessKey {\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    keyType: ").append(toIndentedString(keyType)).append("\n");
    sb.append("    keyValue: ").append(toIndentedString(keyValue)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

