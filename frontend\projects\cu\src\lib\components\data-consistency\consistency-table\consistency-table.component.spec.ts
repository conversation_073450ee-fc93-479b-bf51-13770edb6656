import {ComponentFixture, TestBed} from "@angular/core/testing";
import {MatDialog} from "@angular/material/dialog";
import {NoopAnimationsModule} from "@angular/platform-browser/animations";
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {of} from "rxjs";
import {Origin} from "../../../model/types";
import {ConsistencyTableComponent, ConsistencyTableElement} from "./consistency-table.component";
import {GeoLookupService} from "../../../http/geo-lookup.service";
import {DateUtils} from "../../../date.utils";
import {SimpleChange} from "@angular/core";
import {MatTableDataSource} from "@angular/material/table";
import {printFormat as formatIban} from "iban-ts";

// Mock iban-ts module
jest.mock("iban-ts", () => ({
    printFormat: jest.fn().mockImplementation(iban => iban),
}));

describe("ConsistencyTableComponent", () => {
    let component: ConsistencyTableComponent;
    let fixture: ComponentFixture<ConsistencyTableComponent>;
    let geoLookupServiceMock: Partial<GeoLookupService>;
    let translateServiceMock: Partial<TranslateService>;
    let mockMatDialog: any;

    function getMockDataConsistencyData() {
        return {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                dateOfBirth: "1990-01-01",
                nationality: "BE",
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                unionDue: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                },
                iban: "****************",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                birthDate: "1990-01-01",
                nationality: "BE",
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                unionDue: {
                    authorized: false,
                    effectiveDate: "2025-01-01",
                },
                iban: "****************",
            },
            authenticCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                birthDate: "1990-01-01",
                nationality: "BE",
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    zipCode: "1000",
                    city: "Brussels",
                },
                iban: "****************",
            },
        };
    }

    beforeEach(async () => {
        geoLookupServiceMock = {
            getEntityDescription: jest.fn().mockImplementation((code, lang) => {
                const descriptions: Record<string, Record<string, string>> = {
                    "BE": {"NL": "België", "FR": "Belgique"},
                    "FR": {"NL": "Frankrijk", "FR": "France"},
                };
                return descriptions[code]?.[lang] || code;
            }),
        };

        translateServiceMock = {
            use: jest.fn(),
            currentLang: "NL",
            instant: jest.fn((key: string) => key),
        };

        mockMatDialog = {
            open: jest.fn().mockReturnValue({
                afterClosed: jest.fn().mockReturnValue(of(Origin.Employee)),
            }),
        };

        await TestBed.configureTestingModule({
            imports: [
                NoopAnimationsModule,
                ConsistencyTableComponent,
                TranslateModule.forRoot(),
            ],
            providers: [
                {provide: GeoLookupService, useValue: geoLookupServiceMock},
                {provide: TranslateService, useValue: translateServiceMock},
                {provide: MatDialog, useValue: mockMatDialog},
            ],
        }).compileComponents();

        fixture = TestBed.createComponent(ConsistencyTableComponent);
        component = fixture.componentInstance;

        component.displayedColumns = ["label", "encodedValue", "dbValue", "sourceValue", "actions"];
        component.dataConsistencySelectedValues = {};

        jest.spyOn(DateUtils, "formatDateTo").mockImplementation((date, format) => {
            if (!date) {
                return "";
            }
            return date instanceof Date ? date.toISOString().split("T")[0] : date;
        });
    });

    it("should create", () => {
        expect(component).toBeTruthy();
    });

    it("should initialize with default values", () => {
        expect(component.language).toBe("NL");
        expect(component.tableDataSource).toBeDefined();
        expect(component.tableDataSource.data).toEqual([]);
    });

    it("should set language and update translations", () => {
        component.language = "FR";

        expect(component.language).toBe("FR");
        expect(translateServiceMock.use).toHaveBeenCalledWith("FR");
    });

    it("should prepare table data on initialization when data is provided", () => {
        const prepareTableDataSpy = jest.spyOn(component, "prepareTableData");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];

        component.ngOnInit();

        expect(prepareTableDataSpy).toHaveBeenCalled();
        expect(component.tableDataSource.data.length).toBeGreaterThan(0);
    });

    it("should update on data source changes", () => {
        const prepareTableDataSpy = jest.spyOn(component, "prepareTableData");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];

        component.ngOnChanges({
            dataSource: new SimpleChange(null, [], true),
        });

        expect(prepareTableDataSpy).toHaveBeenCalled();
    });

    it("should update nationality displays when language changes", () => {
        const updateNationalitySpy = jest.spyOn<any, any>(component, "updateNationalityDisplays");

        component.dataConsistencyData = getMockDataConsistencyData();
        component.dataSource = [];
        component.prepareTableData();

        component.ngOnChanges({
            language: new SimpleChange("NL", "FR", false),
        });

        expect(updateNationalitySpy).toHaveBeenCalled();
    });

    it("should correctly prepare table data with citizen information", () => {
        component.dataConsistencyData = getMockDataConsistencyData();
        component.prepareTableData();

        const tableData = component.tableDataSource.data;

        expect(tableData.length).toBe(6);
        expect(tableData[0].id).toBe("birthDate");
        expect(tableData[1].id).toBe("address");
        expect(tableData[2].id).toBe("nationality");
        expect(tableData.some(item => item.id === "bankAccount")).toBe(true);
        expect(tableData.some(item => item.id === "otherPersonName")).toBe(true);
        expect(tableData.some(item => item.id === "cotisation")).toBe(true);
    });

    describe("Private utility methods", () => {
        it("should normalize text by removing accents and converting to uppercase", () => {
            const normalizeText = component["normalizeText"].bind(component);

            expect(normalizeText("Hélène")).toBe("HELENE");
            expect(normalizeText("  Jürgen ")).toBe("JURGEN");
            expect(normalizeText("")).toBe("");
            expect(normalizeText(null as any)).toBe("");
        });

        it("should compare dates correctly", () => {
            const compareDates = component["compareDates"].bind(component);

            expect(compareDates(["2023-01-01", "2023-01-01", "-"])).toBe(true);
            expect(compareDates(["2023-01-01", "2023-01-02", "-"])).toBe(false);
            expect(compareDates(["2023-01-01", "-", "-"])).toBe(true);
            expect(compareDates(["-", "-", "-"])).toBe(true);

            // Test with invalid date format that should be handled gracefully
            expect(compareDates(["invalid-date", "-", "-"])).toBe(true);
        });

        it("should compare addresses correctly", () => {
            const compareAddresses = component["compareAddresses"].bind(component);

            const citizenInfo1 = {
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            const onemInfo1 = {
                address: {
                    street: "Main Street",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo1, onemInfo1)).toBe(true);

            // Different house number
            const onemInfo2 = {
                address: {
                    street: "Main Street",
                    houseNumber: "124",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo1, onemInfo2)).toBe(false);

            // Test with accents
            const citizenInfo2 = {
                address: {
                    street: "Chaussée de Gand",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            const onemInfo3 = {
                address: {
                    street: "CHAUSSEE DE GAND",
                    houseNumber: "123",
                    boxNumber: "A",
                    zipCode: "1000",
                },
            };

            expect(compareAddresses(citizenInfo2, onemInfo3)).toBe(true);

            // Test with old format properties
            const citizenInfo3 = {
                street: "Main Street",
                housNbr: "123",
                postBox: "A",
                zipCode: "1000",
            };

            expect(compareAddresses(citizenInfo3, onemInfo1)).toBe(true);

            // Handle missing onemInfo
            expect(compareAddresses(citizenInfo1, null)).toBe(true);
            expect(compareAddresses(citizenInfo1, {})).toBe(true);
            expect(compareAddresses(null, onemInfo1)).toBe(true);
        });

        it("should compare nationality codes correctly", () => {
            const compareNationality = component["compareNationality"].bind(component);

            // Both have same code
            const citizenInfo1 = {nationality: "BE"};
            const onemInfo1 = {nationality: "BE"};
            const authenticInfo = {nationality: "BE"};
            expect(compareNationality(citizenInfo1, onemInfo1, authenticInfo)).toBe(true);

            // Different codes
            const onemInfo2 = {nationality: "FR"};
            expect(compareNationality(citizenInfo1, onemInfo2, authenticInfo)).toBe(false);

            // Citizen nationality as object
            const citizenInfo2 = {
                nationality: {
                    code: "BE",
                    descFr: "Belgique",
                    descNl: "België",
                },
            };
            expect(compareNationality(citizenInfo2, onemInfo1, authenticInfo)).toBe(true);

            // Handle missing data
            expect(compareNationality(null, onemInfo1, authenticInfo)).toBe(true);
            expect(compareNationality(citizenInfo1, null, authenticInfo)).toBe(true);
            expect(compareNationality({}, {}, {})).toBe(true);
            expect(compareNationality({}, {nationality: "BE"}, {})).toBe(true);
            expect(compareNationality({nationality: "BE"}, {}, {})).toBe(true);
        });

        it("should compare bank accounts correctly", () => {
            const compareAccounts = component["compareAccounts"].bind(component);

            const belgianAccount1 = {iban: "****************", bic: "KREDBEBB", isForeign: false};
            const belgianAccount2 = {iban: "BE68 5390 0754 7034", bic: "KREDBEBB", isForeign: false};
            expect(compareAccounts(belgianAccount1, belgianAccount2)).toBe(true);

            const belgianAccount3 = {iban: "****************", bic: "KREDBEBB", isForeign: false};
            expect(compareAccounts(belgianAccount1, belgianAccount3)).toBe(false);

            const foreignAccount1 = {iban: "***************************", bic: "BNPAFRPP", isForeign: true};
            expect(compareAccounts(belgianAccount1, foreignAccount1)).toBe(false);

            const foreignAccount2 = {iban: "***************************", bic: "BNPAFRPP", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccount2)).toBe(true);

            const foreignAccount3 = {iban: "***************************", bic: "ABCDEFGH", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccount3)).toBe(false);

            const foreignAccountNoBic = {iban: "***************************", bic: "", isForeign: true};
            expect(compareAccounts(foreignAccount1, foreignAccountNoBic)).toBe(false);

            expect(compareAccounts({iban: "", bic: "", isForeign: false}, belgianAccount1)).toBe(true);
            expect(compareAccounts(belgianAccount1, {iban: "", bic: "", isForeign: false})).toBe(true);
        });

        it("should correctly format bank accounts with BIC for foreign accounts", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.encodedValue).toContain("BIC: BNPAFRPP");
            expect(bankAccountRow?.dbValue).toContain("BIC: BNPAFRPP");
            expect(bankAccountRow?.isConsistent).toBe(true);
        });

        it("should handle mixed account types correctly", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "****************",
                    bic: "KREDBEBB",
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.isConsistent).toBe(false);
        });

        it("should handle foreign accounts without BIC", () => {
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: "01/01/2025",
                },
                c1CitizenInformation: {
                    iban: "***************************",
                    bic: "",  // missing BIC
                },
                onemCitizenInformation: {
                    iban: "***************************",
                    bic: "BNPAFRPP",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");

            expect(bankAccountRow?.isConsistent).toBe(false);
        });

        it("should compare names correctly", () => {
            const compareNames = component["compareNames"].bind(component);

            expect(compareNames(["John Doe", "JOHN DOE", "-"])).toBe(true);
            expect(compareNames(["John Doe", "Jane Doe", "-"])).toBe(false);
            expect(compareNames(["Jean-François", "JEAN-FRANCOIS", "-"])).toBe(true);
            expect(compareNames(["", "-", "-"])).toBe(true);
        });
    });

    describe("Date values in table data", () => {
        it("should correctly set date values from provided data", () => {
            const testDate = "01/01/2025";
            const addressValueDate = "02/01/2025";
            const bankAccountValueDate = "03/01/2025";
            const unionValueDate = "04/01/2025";

            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: testDate,
                },
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationality: "BE",
                    iban: "****************",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationality: "BE",
                    addressValueDate: addressValueDate,
                    bankAccountValueDate: bankAccountValueDate,
                    unionContributionValueDate: unionValueDate,
                    iban: "****************",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
            expect(birthDateRow?.encodedDate).toBe(testDate);
            expect(birthDateRow?.dbDate).toBe(addressValueDate);
            expect(birthDateRow?.sourceDate).toBe("-");

            const addressRow = component.tableDataSource.data.find(row => row.id === "address");
            expect(addressRow?.encodedDate).toBe(testDate);
            expect(addressRow?.dbDate).toBe(addressValueDate);
            expect(addressRow?.sourceDate).toBe("-");

            const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");
            expect(nationalityRow?.encodedDate).toBe(testDate);
            expect(nationalityRow?.dbDate).toBe(addressValueDate);
            expect(nationalityRow?.sourceDate).toBe("-");

            const bankAccountRow = component.tableDataSource.data.find(row => row.id === "bankAccount");
            expect(bankAccountRow?.encodedDate).toBe(testDate);
            expect(bankAccountRow?.dbDate).toBe(bankAccountValueDate);
            expect(bankAccountRow?.sourceDate).toBe("-");

            const otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
            expect(otherPersonNameRow?.encodedDate).toBe(testDate);
            expect(otherPersonNameRow?.dbDate).toBe(bankAccountValueDate);
            expect(otherPersonNameRow?.sourceDate).toBe("-");

            const unionRow = component.tableDataSource.data.find(row => row.id === "cotisation");
            expect(unionRow?.encodedDate).toBe(testDate);
            expect(unionRow?.dbDate).toBe(unionValueDate);
            expect(unionRow?.sourceDate).toBe("-");
        });

        it("should handle missing date values by using default \"-\"", () => {
            component.dataConsistencyData = {
                basicInfo: {},
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationality: "BE",
                    iban: "****************",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationality: "BE",
                    iban: "****************",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            // Check that default value '-' is used for all missing dates
            const birthDateRow = component.tableDataSource.data.find(row => row.id === "birthDate");
            expect(birthDateRow?.encodedDate).toBe("-");
            expect(birthDateRow?.dbDate).toBe("-");
            expect(birthDateRow?.sourceDate).toBe("-");

            const addressRow = component.tableDataSource.data.find(row => row.id === "address");
            expect(addressRow?.encodedDate).toBe("-");
            expect(addressRow?.dbDate).toBe("-");
            expect(addressRow?.sourceDate).toBe("-");
        });

        it("should pass date values to table elements consistently", () => {
            const testDate = "01/01/2025";
            component.dataConsistencyData = {
                basicInfo: {
                    requestDate: testDate,
                },
                c1CitizenInformation: {
                    dateOfBirth: "1990-01-01",
                    nationality: "BE",
                },
                onemCitizenInformation: {
                    birthDate: "1990-01-01",
                    nationality: "BE",
                    addressValueDate: "02/01/2025",
                },
                authenticCitizenInformation: {},
            };

            component.prepareTableData();

            expect(component.tableDataSource.data.length).toBeGreaterThan(0);

            component.tableDataSource.data.forEach((element: ConsistencyTableElement) => {
                expect(element).toHaveProperty("encodedDate");
                expect(element).toHaveProperty("dbDate");
                expect(element).toHaveProperty("sourceDate");
            });
        });
    });

    it("should get the correct icon based on consistency", () => {
        expect(component.getIcon(true)).toBe("check_circle");
        expect(component.getIcon(false)).toBe("warning");
    });

    it("should return the value from getModifiedValue", () => {
        const selectedValue = {
            fieldName: "nationality",
            origin: "onem",
            value: "BE",
        };

        expect(component.getModifiedValue(selectedValue)).toBe("BE");
        expect(component.getModifiedValue(null)).toBe(null);
    });

    it("should update nationality displays correctly", () => {
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                nationality: "BE",
            },
            onemCitizenInformation: {
                nationality: "FR",
            },
        };

        component.tableDataSource = new MatTableDataSource([
            {
                id: "nationality",
                label: "Nationality",
                encodedValue: "Unknown",
                dbValue: "Unknown",
                sourceValue: "-",
                valueToKeep: "",

                encodedDate: "-",
                dbDate: "-",
                sourceDate: "-",
            },
        ]);

        component["updateNationalityDisplays"]();

        const nationalityRow = component.tableDataSource.data.find(row => row.id === "nationality");
        expect(nationalityRow?.encodedValue).toBe("België");
        expect(nationalityRow?.dbValue).toBe("Frankrijk");
    });

    it("should handle otherPersonName from otherPersonName", () => {
        // Test with otherPersonName present
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                otherPersonName: "Jane Smith",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
                otherPersonName: "Jane Smith",
            },
            authenticCitizenInformation: {},
        };

        component.prepareTableData();
        let otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
        expect(otherPersonNameRow?.encodedValue).toBe("Jane Smith");

        // Test with only basic info
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                firstName: "John",
                lastName: "Doe",
            },
            onemCitizenInformation: {
                firstName: "John",
                lastName: "Doe",
            },
            authenticCitizenInformation: {},
        };

        component.prepareTableData();
        otherPersonNameRow = component.tableDataSource.data.find(row => row.id === "otherPersonName");
        expect(otherPersonNameRow?.encodedValue).toBeUndefined;
    });

    it("should initialize dataConsistencySelectedValues when undefined", () => {
        component.dataConsistencySelectedValues = undefined as any;
        component.ngOnInit();
        expect(component.dataConsistencySelectedValues).toEqual({});
    });

    it("should format IBAN using iban-ts library", () => {
        const rawIban = "****************";
        component.dataConsistencyData = {
            basicInfo: {
                requestDate: "01/01/2025",
            },
            c1CitizenInformation: {
                iban: rawIban,
            },
            onemCitizenInformation: {
                iban: "",
            },
            authenticCitizenInformation: {},
        };

        component.prepareTableData();

        expect(formatIban).toHaveBeenCalledWith(rawIban);

        // Check foreign IBAN handling
        const foreignIban = "***************************";
        component.dataConsistencyData.c1CitizenInformation = {
            iban: foreignIban,
        };

        component.prepareTableData();

        expect(formatIban).toHaveBeenCalledWith(foreignIban);
    });

    it("should format bank account objects in getModifiedValue", () => {
        const belgianAccount = {
            fieldName: "bankAccount",
            origin: "Employee",
            value: {
                iban: "****************",
                bic: "KREDBEBB",
            },
        };

        expect(component.getModifiedValue(belgianAccount)).toBe("****************");
        const foreignAccount = {
            fieldName: "bankAccount",
            origin: "Employee",
            value: {
                iban: "***************************",
                bic: "BNPAFRPP",
            },
        };

        expect(component.getModifiedValue(foreignAccount))
            .toBe("*************************** <span class=\"bicToCompair\">BIC: BNPAFRPP</span>");
        const regularValue = {
            fieldName: "otherPersonName",
            origin: "Employee",
            value: "John Doe",
        };

        expect(component.getModifiedValue(regularValue)).toBe("John Doe");
    });

});