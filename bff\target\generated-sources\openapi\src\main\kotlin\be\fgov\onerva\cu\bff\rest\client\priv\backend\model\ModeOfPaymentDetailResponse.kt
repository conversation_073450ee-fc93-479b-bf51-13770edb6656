/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.cu.bff.rest.client.priv.backend.model

import be.fgov.onerva.cu.bff.rest.client.priv.backend.model.FieldSource

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param otherPersonName 
 * @param iban 
 * @param bic 
 * @param fieldSources Sources for individual fields
 */


data class ModeOfPaymentDetailResponse (

    @get:JsonProperty("otherPersonName")
    val otherPersonName: kotlin.String? = null,

    @get:JsonProperty("iban")
    val iban: kotlin.String? = null,

    @get:JsonProperty("bic")
    val bic: kotlin.String? = null,

    /* Sources for individual fields */
    @get:JsonProperty("fieldSources")
    val fieldSources: kotlin.collections.List<FieldSource>? = null

) {


}

