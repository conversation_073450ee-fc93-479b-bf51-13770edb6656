/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * TranslationDTO
 */
@JsonPropertyOrder({
  TranslationDTO.JSON_PROPERTY_FR,
  TranslationDTO.JSON_PROPERTY_NL
})
@JsonTypeName("Translation")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class TranslationDTO {
  public static final String JSON_PROPERTY_FR = "fr";
  private String fr;

  public static final String JSON_PROPERTY_NL = "nl";
  private String nl;

  public TranslationDTO() {
  }

  public TranslationDTO fr(String fr) {
    
    this.fr = fr;
    return this;
  }

  /**
   * Get fr
   * @return fr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getFr() {
    return fr;
  }


  @JsonProperty(JSON_PROPERTY_FR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFr(String fr) {
    this.fr = fr;
  }

  public TranslationDTO nl(String nl) {
    
    this.nl = nl;
    return this;
  }

  /**
   * Get nl
   * @return nl
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NL)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getNl() {
    return nl;
  }


  @JsonProperty(JSON_PROPERTY_NL)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNl(String nl) {
    this.nl = nl;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    TranslationDTO translation = (TranslationDTO) o;
    return Objects.equals(this.fr, translation.fr) &&
        Objects.equals(this.nl, translation.nl);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fr, nl);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class TranslationDTO {\n");
    sb.append("    fr: ").append(toIndentedString(fr)).append("\n");
    sb.append("    nl: ").append(toIndentedString(nl)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

