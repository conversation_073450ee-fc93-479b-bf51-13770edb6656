/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.SelectFieldSourcesRequest
import be.fgov.onerva.cu.bff.rest.server.priv.model.UpdateModeOfPaymentRequest
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface ModeOfPaymentApi {

    @Operation(
        tags = ["Mode Of Payment",],
        summary = "",
        operationId = "getModeOfPayment",
        description = """Retrieve mode of payment""",
        responses = [
            ApiResponse(responseCode = "200", description = "Mode of payment successfully retrieved", content = [Content(schema = Schema(implementation = ModeOfPaymentDetailResponse::class))]),
            ApiResponse(responseCode = "404", description = "Request not found")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/requests/{requestId}/mode-of-payment"],
            produces = ["application/json"]
    )
    fun getModeOfPayment(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID): ResponseEntity<ModeOfPaymentDetailResponse>

    @Operation(
        tags = ["Mode Of Payment",],
        summary = "",
        operationId = "selectModeOfPaymentSources",
        description = """Select sources for individual fields in mode of payment""",
        responses = [
            ApiResponse(responseCode = "204", description = "Field sources successfully selected"),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/mode-of-payment/select"],
            consumes = ["application/json"]
    )
    fun selectModeOfPaymentSources(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "", required = true) @Valid @RequestBody selectFieldSourcesRequest: SelectFieldSourcesRequest): ResponseEntity<Unit>

    @Operation(
        tags = ["Mode Of Payment",],
        summary = "",
        operationId = "updateModeOfPayment",
        description = """Update mutable mode of payment for a specific request""",
        responses = [
            ApiResponse(responseCode = "204", description = "Mode of payment successfully updated"),
            ApiResponse(responseCode = "400", description = "Invalid request body"),
            ApiResponse(responseCode = "404", description = "Request not found"),
            ApiResponse(responseCode = "422", description = "Validation error")
        ]
    )
    @RequestMapping(
            method = [RequestMethod.PUT],
            value = ["/api/requests/{requestId}/mode-of-payment"],
            consumes = ["application/json"]
    )
    fun updateModeOfPayment(@Parameter(description = "The UUID of the request", required = true) @PathVariable("requestId") requestId: java.util.UUID,@Parameter(description = "", required = true) @Valid @RequestBody updateModeOfPaymentRequest: UpdateModeOfPaymentRequest): ResponseEntity<Unit>
}
