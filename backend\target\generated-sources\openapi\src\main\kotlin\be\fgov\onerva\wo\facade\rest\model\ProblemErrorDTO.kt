/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package be.fgov.onerva.wo.facade.rest.model


import com.fasterxml.jackson.annotation.JsonProperty

/**
 * 
 *
 * @param `field` The field that caused the error.
 * @param message The error message related to this field.
 */


data class ProblemErrorDTO (

    /* The field that caused the error. */
    @get:JsonProperty("field")
    val `field`: kotlin.String? = null,

    /* The error message related to this field. */
    @get:JsonProperty("message")
    val message: kotlin.String? = null

) {


}

