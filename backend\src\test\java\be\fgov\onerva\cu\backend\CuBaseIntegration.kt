package be.fgov.onerva.cu.backend

import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.test.context.jdbc.Sql
import be.fgov.onerva.barema.api.BaremeApi
import be.fgov.onerva.cu.backend.annotation.CuSpringBootTest
import be.fgov.onerva.cu.backend.application.port.out.CurrentUserPort
import be.fgov.onerva.cu.backend.lookup.LookupService
import be.fgov.onerva.cu.backend.rest.client.lookup.wppt.api.LookupApi
import be.fgov.onerva.person.api.CitizenApi
import be.fgov.onerva.person.api.CitizenInfoApi
import be.fgov.onerva.unemployment.c9.api.C9Api
import be.fgov.onerva.unemployment.c9.api.Ec1Api
import be.fgov.onerva.unemployment.c9.invoker.ApiClient
import be.fgov.onerva.wo.facade.api.FacadeControllerApi
import be.fgov.onerva.wo.organizational.chart.api.NodeApi
import be.fgov.onerva.wo_thirdparty.api.DefaultApi
import be.fgov.onerva.wo_thirdparty.api.PartiesApi
import com.ninjasquad.springmockk.MockkBean
import dev.openfeature.sdk.Client

@CuSpringBootTest
@Sql(scripts = ["/cleanup.sql"])
class CuBaseIntegration {
    @MockkBean
    val jwtDecoder: JwtDecoder? = null

    @MockkBean
    val currentUserPort: CurrentUserPort? = null

    @MockkBean
    val facadeControllerApi: FacadeControllerApi? = null

    @MockkBean
    val nodeApi: NodeApi? = null

    @MockkBean
    val partiesApi: PartiesApi? = null

    @MockkBean
    val defaultApi: DefaultApi? = null

    @MockkBean
    val citizenApi: CitizenApi? = null

    @MockkBean
    val citizenInfoApi: CitizenInfoApi? = null

    @MockkBean
    val c9ApiClient: ApiClient? = null

    @MockkBean
    val c9Api: C9Api? = null

    @MockkBean
    val ec1Api: Ec1Api? = null

    @MockkBean
    val lookupApi: LookupApi? = null

    @MockkBean
    val lookupService: LookupService? = null

    @MockkBean
    val baremeApi: BaremeApi? = null

    @MockkBean
    val client: Client? = null

    @MockkBean
    val registryApi: be.fgov.onerva.registerproxyservice.api.CitizenApi? = null
}