/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EC1FinancialSituation
 */
@JsonPropertyOrder({
  EC1FinancialSituation.JSON_PROPERTY_FULL_RETIREMENT,
  EC1FinancialSituation.JSON_PROPERTY_RETIREMENT_OLD_AGE_OR_SURVIVOR_PENSION,
  EC1FinancialSituation.JSON_PROPERTY_DISEASE_BENEFIT_OR_DISABILITY,
  EC1FinancialSituation.JSON_PROPERTY_OCCUPATIONAL_ACCIDENT_OR_DISEASE_BENEFIT,
  EC1FinancialSituation.JSON_PROPERTY_DECLARE_MODIFICATION_JOIN_C1_B,
  EC1FinancialSituation.JSON_PROPERTY_FINANCIAL_BENEFIT_FOLLOWING_FORMATION_OR_STUDY
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EC1FinancialSituation {
  public static final String JSON_PROPERTY_FULL_RETIREMENT = "fullRetirement";
  private Boolean fullRetirement;

  public static final String JSON_PROPERTY_RETIREMENT_OLD_AGE_OR_SURVIVOR_PENSION = "retirementOldAgeOrSurvivorPension";
  private Boolean retirementOldAgeOrSurvivorPension;

  public static final String JSON_PROPERTY_DISEASE_BENEFIT_OR_DISABILITY = "diseaseBenefitOrDisability";
  private Boolean diseaseBenefitOrDisability;

  public static final String JSON_PROPERTY_OCCUPATIONAL_ACCIDENT_OR_DISEASE_BENEFIT = "occupationalAccidentOrDiseaseBenefit";
  private Boolean occupationalAccidentOrDiseaseBenefit;

  public static final String JSON_PROPERTY_DECLARE_MODIFICATION_JOIN_C1_B = "declareModificationJoinC1B";
  private Boolean declareModificationJoinC1B;

  public static final String JSON_PROPERTY_FINANCIAL_BENEFIT_FOLLOWING_FORMATION_OR_STUDY = "financialBenefitFollowingFormationOrStudy";
  private Boolean financialBenefitFollowingFormationOrStudy;

  public EC1FinancialSituation() {
  }

  public EC1FinancialSituation fullRetirement(Boolean fullRetirement) {
    
    this.fullRetirement = fullRetirement;
    return this;
  }

  /**
   * Get fullRetirement
   * @return fullRetirement
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FULL_RETIREMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFullRetirement() {
    return fullRetirement;
  }


  @JsonProperty(JSON_PROPERTY_FULL_RETIREMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFullRetirement(Boolean fullRetirement) {
    this.fullRetirement = fullRetirement;
  }

  public EC1FinancialSituation retirementOldAgeOrSurvivorPension(Boolean retirementOldAgeOrSurvivorPension) {
    
    this.retirementOldAgeOrSurvivorPension = retirementOldAgeOrSurvivorPension;
    return this;
  }

  /**
   * Get retirementOldAgeOrSurvivorPension
   * @return retirementOldAgeOrSurvivorPension
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RETIREMENT_OLD_AGE_OR_SURVIVOR_PENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRetirementOldAgeOrSurvivorPension() {
    return retirementOldAgeOrSurvivorPension;
  }


  @JsonProperty(JSON_PROPERTY_RETIREMENT_OLD_AGE_OR_SURVIVOR_PENSION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRetirementOldAgeOrSurvivorPension(Boolean retirementOldAgeOrSurvivorPension) {
    this.retirementOldAgeOrSurvivorPension = retirementOldAgeOrSurvivorPension;
  }

  public EC1FinancialSituation diseaseBenefitOrDisability(Boolean diseaseBenefitOrDisability) {
    
    this.diseaseBenefitOrDisability = diseaseBenefitOrDisability;
    return this;
  }

  /**
   * Get diseaseBenefitOrDisability
   * @return diseaseBenefitOrDisability
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DISEASE_BENEFIT_OR_DISABILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDiseaseBenefitOrDisability() {
    return diseaseBenefitOrDisability;
  }


  @JsonProperty(JSON_PROPERTY_DISEASE_BENEFIT_OR_DISABILITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDiseaseBenefitOrDisability(Boolean diseaseBenefitOrDisability) {
    this.diseaseBenefitOrDisability = diseaseBenefitOrDisability;
  }

  public EC1FinancialSituation occupationalAccidentOrDiseaseBenefit(Boolean occupationalAccidentOrDiseaseBenefit) {
    
    this.occupationalAccidentOrDiseaseBenefit = occupationalAccidentOrDiseaseBenefit;
    return this;
  }

  /**
   * Get occupationalAccidentOrDiseaseBenefit
   * @return occupationalAccidentOrDiseaseBenefit
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCCUPATIONAL_ACCIDENT_OR_DISEASE_BENEFIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOccupationalAccidentOrDiseaseBenefit() {
    return occupationalAccidentOrDiseaseBenefit;
  }


  @JsonProperty(JSON_PROPERTY_OCCUPATIONAL_ACCIDENT_OR_DISEASE_BENEFIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOccupationalAccidentOrDiseaseBenefit(Boolean occupationalAccidentOrDiseaseBenefit) {
    this.occupationalAccidentOrDiseaseBenefit = occupationalAccidentOrDiseaseBenefit;
  }

  public EC1FinancialSituation declareModificationJoinC1B(Boolean declareModificationJoinC1B) {
    
    this.declareModificationJoinC1B = declareModificationJoinC1B;
    return this;
  }

  /**
   * Get declareModificationJoinC1B
   * @return declareModificationJoinC1B
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECLARE_MODIFICATION_JOIN_C1_B)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDeclareModificationJoinC1B() {
    return declareModificationJoinC1B;
  }


  @JsonProperty(JSON_PROPERTY_DECLARE_MODIFICATION_JOIN_C1_B)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeclareModificationJoinC1B(Boolean declareModificationJoinC1B) {
    this.declareModificationJoinC1B = declareModificationJoinC1B;
  }

  public EC1FinancialSituation financialBenefitFollowingFormationOrStudy(Boolean financialBenefitFollowingFormationOrStudy) {
    
    this.financialBenefitFollowingFormationOrStudy = financialBenefitFollowingFormationOrStudy;
    return this;
  }

  /**
   * Get financialBenefitFollowingFormationOrStudy
   * @return financialBenefitFollowingFormationOrStudy
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FINANCIAL_BENEFIT_FOLLOWING_FORMATION_OR_STUDY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFinancialBenefitFollowingFormationOrStudy() {
    return financialBenefitFollowingFormationOrStudy;
  }


  @JsonProperty(JSON_PROPERTY_FINANCIAL_BENEFIT_FOLLOWING_FORMATION_OR_STUDY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFinancialBenefitFollowingFormationOrStudy(Boolean financialBenefitFollowingFormationOrStudy) {
    this.financialBenefitFollowingFormationOrStudy = financialBenefitFollowingFormationOrStudy;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EC1FinancialSituation ec1FinancialSituation = (EC1FinancialSituation) o;
    return Objects.equals(this.fullRetirement, ec1FinancialSituation.fullRetirement) &&
        Objects.equals(this.retirementOldAgeOrSurvivorPension, ec1FinancialSituation.retirementOldAgeOrSurvivorPension) &&
        Objects.equals(this.diseaseBenefitOrDisability, ec1FinancialSituation.diseaseBenefitOrDisability) &&
        Objects.equals(this.occupationalAccidentOrDiseaseBenefit, ec1FinancialSituation.occupationalAccidentOrDiseaseBenefit) &&
        Objects.equals(this.declareModificationJoinC1B, ec1FinancialSituation.declareModificationJoinC1B) &&
        Objects.equals(this.financialBenefitFollowingFormationOrStudy, ec1FinancialSituation.financialBenefitFollowingFormationOrStudy);
  }

  @Override
  public int hashCode() {
    return Objects.hash(fullRetirement, retirementOldAgeOrSurvivorPension, diseaseBenefitOrDisability, occupationalAccidentOrDiseaseBenefit, declareModificationJoinC1B, financialBenefitFollowingFormationOrStudy);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EC1FinancialSituation {\n");
    sb.append("    fullRetirement: ").append(toIndentedString(fullRetirement)).append("\n");
    sb.append("    retirementOldAgeOrSurvivorPension: ").append(toIndentedString(retirementOldAgeOrSurvivorPension)).append("\n");
    sb.append("    diseaseBenefitOrDisability: ").append(toIndentedString(diseaseBenefitOrDisability)).append("\n");
    sb.append("    occupationalAccidentOrDiseaseBenefit: ").append(toIndentedString(occupationalAccidentOrDiseaseBenefit)).append("\n");
    sb.append("    declareModificationJoinC1B: ").append(toIndentedString(declareModificationJoinC1B)).append("\n");
    sb.append("    financialBenefitFollowingFormationOrStudy: ").append(toIndentedString(financialBenefitFollowingFormationOrStudy)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

