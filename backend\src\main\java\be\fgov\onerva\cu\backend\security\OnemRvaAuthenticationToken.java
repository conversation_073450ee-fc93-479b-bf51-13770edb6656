package be.fgov.onerva.cu.backend.security;

import lombok.Getter;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;

import java.util.Collection;

/**
 * A custom authentication token for ONEM/RVA authentication that extends Spring Security's JwtAuthenticationToken.
 * This token extracts and stores additional claims from the JWT including the organizational unit code
 * and the user's full name.
 *
 * @see org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken
 * @see org.springframework.security.oauth2.jwt.Jwt
 */
@Getter
@SuppressWarnings("java:S1068")
public class OnemRvaAuthenticationToken extends JwtAuthenticationToken {
    /**
     * The organizational unit code extracted from the JWT claim "ouCode".
     */
    private final String ouCode;
    /**
     * The user's full name extracted from the JWT claim "name".
     */
    private final String fullName;

    /**
     * Constructs a new OnemRvaAuthenticationToken with the specified authorities and JWT.
     * Extracts additional claims (ouCode and name) from the JWT during construction.
     *
     * @param authorities the collection of granted authorities for this authentication
     * @param jwt         the JWT token containing the claims
     * @throws org.springframework.security.oauth2.jwt.JwtException if the required claims are missing or invalid
     */
    public OnemRvaAuthenticationToken(Collection<? extends GrantedAuthority> authorities, Jwt jwt) {
        super(jwt, authorities, jwt.getClaim("preferred_username"));
        ouCode = jwt.getClaim("ouCode");
        fullName = jwt.getClaim("name");
    }
}
