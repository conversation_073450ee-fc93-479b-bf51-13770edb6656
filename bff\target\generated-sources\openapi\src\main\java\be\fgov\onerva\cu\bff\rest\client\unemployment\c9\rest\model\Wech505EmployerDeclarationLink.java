/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.HandledEmployerDeclarationLinkType;
import be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model.Wech505NaturalPerson;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505EmployerDeclarationLink
 */
@JsonPropertyOrder({
  Wech505EmployerDeclarationLink.JSON_PROPERTY_NOSS_REGISTRATION_NBR,
  Wech505EmployerDeclarationLink.JSON_PROPERTY_TRUSTEESHIP,
  Wech505EmployerDeclarationLink.JSON_PROPERTY_COMPANY_I_D,
  Wech505EmployerDeclarationLink.JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK,
  Wech505EmployerDeclarationLink.JSON_PROPERTY_NATURAL_PERSON
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505EmployerDeclarationLink {
  public static final String JSON_PROPERTY_NOSS_REGISTRATION_NBR = "nossRegistrationNbr";
  private Integer nossRegistrationNbr;

  public static final String JSON_PROPERTY_TRUSTEESHIP = "trusteeship";
  private String trusteeship;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK = "handledEmployerDeclarationLink";
  private HandledEmployerDeclarationLinkType handledEmployerDeclarationLink;

  public static final String JSON_PROPERTY_NATURAL_PERSON = "naturalPerson";
  private Wech505NaturalPerson naturalPerson;

  public Wech505EmployerDeclarationLink() {
  }

  public Wech505EmployerDeclarationLink nossRegistrationNbr(Integer nossRegistrationNbr) {
    
    this.nossRegistrationNbr = nossRegistrationNbr;
    return this;
  }

  /**
   * Get nossRegistrationNbr
   * @return nossRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNossRegistrationNbr() {
    return nossRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSS_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNossRegistrationNbr(Integer nossRegistrationNbr) {
    this.nossRegistrationNbr = nossRegistrationNbr;
  }

  public Wech505EmployerDeclarationLink trusteeship(String trusteeship) {
    
    this.trusteeship = trusteeship;
    return this;
  }

  /**
   * Get trusteeship
   * @return trusteeship
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getTrusteeship() {
    return trusteeship;
  }


  @JsonProperty(JSON_PROPERTY_TRUSTEESHIP)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setTrusteeship(String trusteeship) {
    this.trusteeship = trusteeship;
  }

  public Wech505EmployerDeclarationLink companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public Wech505EmployerDeclarationLink handledEmployerDeclarationLink(HandledEmployerDeclarationLinkType handledEmployerDeclarationLink) {
    
    this.handledEmployerDeclarationLink = handledEmployerDeclarationLink;
    return this;
  }

  /**
   * Get handledEmployerDeclarationLink
   * @return handledEmployerDeclarationLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledEmployerDeclarationLinkType getHandledEmployerDeclarationLink() {
    return handledEmployerDeclarationLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_EMPLOYER_DECLARATION_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledEmployerDeclarationLink(HandledEmployerDeclarationLinkType handledEmployerDeclarationLink) {
    this.handledEmployerDeclarationLink = handledEmployerDeclarationLink;
  }

  public Wech505EmployerDeclarationLink naturalPerson(Wech505NaturalPerson naturalPerson) {
    
    this.naturalPerson = naturalPerson;
    return this;
  }

  /**
   * Get naturalPerson
   * @return naturalPerson
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505NaturalPerson getNaturalPerson() {
    return naturalPerson;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPerson(Wech505NaturalPerson naturalPerson) {
    this.naturalPerson = naturalPerson;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505EmployerDeclarationLink wech505EmployerDeclarationLink = (Wech505EmployerDeclarationLink) o;
    return Objects.equals(this.nossRegistrationNbr, wech505EmployerDeclarationLink.nossRegistrationNbr) &&
        Objects.equals(this.trusteeship, wech505EmployerDeclarationLink.trusteeship) &&
        Objects.equals(this.companyID, wech505EmployerDeclarationLink.companyID) &&
        Objects.equals(this.handledEmployerDeclarationLink, wech505EmployerDeclarationLink.handledEmployerDeclarationLink) &&
        Objects.equals(this.naturalPerson, wech505EmployerDeclarationLink.naturalPerson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nossRegistrationNbr, trusteeship, companyID, handledEmployerDeclarationLink, naturalPerson);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505EmployerDeclarationLink {\n");
    sb.append("    nossRegistrationNbr: ").append(toIndentedString(nossRegistrationNbr)).append("\n");
    sb.append("    trusteeship: ").append(toIndentedString(trusteeship)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    handledEmployerDeclarationLink: ").append(toIndentedString(handledEmployerDeclarationLink)).append("\n");
    sb.append("    naturalPerson: ").append(toIndentedString(naturalPerson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

