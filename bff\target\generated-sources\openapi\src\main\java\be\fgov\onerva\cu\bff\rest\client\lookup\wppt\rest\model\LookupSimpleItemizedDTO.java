/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * LookupSimpleItemizedDTO
 */
@JsonPropertyOrder({
  LookupSimpleItemizedDTO.JSON_PROPERTY_BEGIN_DATE,
  LookupSimpleItemizedDTO.JSON_PROPERTY_CLASS_NAME,
  LookupSimpleItemizedDTO.JSON_PROPERTY_CODE,
  LookupSimpleItemizedDTO.JSON_PROPERTY_CODE_MULTIPLE,
  LookupSimpleItemizedDTO.JSON_PROPERTY_DESC_FR,
  LookupSimpleItemizedDTO.JSON_PROPERTY_DESC_NL,
  LookupSimpleItemizedDTO.JSON_PROPERTY_END_DATE,
  LookupSimpleItemizedDTO.JSON_PROPERTY_ID,
  LookupSimpleItemizedDTO.JSON_PROPERTY_MULTI_PROPERTIES_COLLECTION_MAP,
  LookupSimpleItemizedDTO.JSON_PROPERTY_SINGLE_PROPERTIES_MAP,
  LookupSimpleItemizedDTO.JSON_PROPERTY_SINGLE_PROPERTIS_COLLECTION_MAP
})
@JsonTypeName("LookupSimpleItemized")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class LookupSimpleItemizedDTO {
  public static final String JSON_PROPERTY_BEGIN_DATE = "beginDate";
  private Object beginDate;

  public static final String JSON_PROPERTY_CLASS_NAME = "className";
  private String className;

  public static final String JSON_PROPERTY_CODE = "code";
  private String code;

  public static final String JSON_PROPERTY_CODE_MULTIPLE = "codeMultiple";
  private Object codeMultiple;

  public static final String JSON_PROPERTY_DESC_FR = "descFr";
  private String descFr;

  public static final String JSON_PROPERTY_DESC_NL = "descNl";
  private String descNl;

  public static final String JSON_PROPERTY_END_DATE = "endDate";
  private Object endDate;

  public static final String JSON_PROPERTY_ID = "id";
  private Integer id;

  public static final String JSON_PROPERTY_MULTI_PROPERTIES_COLLECTION_MAP = "multiPropertiesCollectionMap";
  private Object multiPropertiesCollectionMap;

  public static final String JSON_PROPERTY_SINGLE_PROPERTIES_MAP = "singlePropertiesMap";
  private Object singlePropertiesMap;

  public static final String JSON_PROPERTY_SINGLE_PROPERTIS_COLLECTION_MAP = "singlePropertisCollectionMap";
  private Object singlePropertisCollectionMap;

  public LookupSimpleItemizedDTO() {
  }

  public LookupSimpleItemizedDTO beginDate(Object beginDate) {
    
    this.beginDate = beginDate;
    return this;
  }

  /**
   * Get beginDate
   * @return beginDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getBeginDate() {
    return beginDate;
  }


  @JsonProperty(JSON_PROPERTY_BEGIN_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBeginDate(Object beginDate) {
    this.beginDate = beginDate;
  }

  public LookupSimpleItemizedDTO className(String className) {
    
    this.className = className;
    return this;
  }

  /**
   * Get className
   * @return className
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getClassName() {
    return className;
  }


  @JsonProperty(JSON_PROPERTY_CLASS_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setClassName(String className) {
    this.className = className;
  }

  public LookupSimpleItemizedDTO code(String code) {
    
    this.code = code;
    return this;
  }

  /**
   * Get code
   * @return code
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCode() {
    return code;
  }


  @JsonProperty(JSON_PROPERTY_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCode(String code) {
    this.code = code;
  }

  public LookupSimpleItemizedDTO codeMultiple(Object codeMultiple) {
    
    this.codeMultiple = codeMultiple;
    return this;
  }

  /**
   * Get codeMultiple
   * @return codeMultiple
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CODE_MULTIPLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getCodeMultiple() {
    return codeMultiple;
  }


  @JsonProperty(JSON_PROPERTY_CODE_MULTIPLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCodeMultiple(Object codeMultiple) {
    this.codeMultiple = codeMultiple;
  }

  public LookupSimpleItemizedDTO descFr(String descFr) {
    
    this.descFr = descFr;
    return this;
  }

  /**
   * Get descFr
   * @return descFr
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescFr() {
    return descFr;
  }


  @JsonProperty(JSON_PROPERTY_DESC_FR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescFr(String descFr) {
    this.descFr = descFr;
  }

  public LookupSimpleItemizedDTO descNl(String descNl) {
    
    this.descNl = descNl;
    return this;
  }

  /**
   * Get descNl
   * @return descNl
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDescNl() {
    return descNl;
  }


  @JsonProperty(JSON_PROPERTY_DESC_NL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDescNl(String descNl) {
    this.descNl = descNl;
  }

  public LookupSimpleItemizedDTO endDate(Object endDate) {
    
    this.endDate = endDate;
    return this;
  }

  /**
   * Get endDate
   * @return endDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getEndDate() {
    return endDate;
  }


  @JsonProperty(JSON_PROPERTY_END_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEndDate(Object endDate) {
    this.endDate = endDate;
  }

  public LookupSimpleItemizedDTO id(Integer id) {
    
    this.id = id;
    return this;
  }

  /**
   * Get id
   * @return id
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getId() {
    return id;
  }


  @JsonProperty(JSON_PROPERTY_ID)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setId(Integer id) {
    this.id = id;
  }

  public LookupSimpleItemizedDTO multiPropertiesCollectionMap(Object multiPropertiesCollectionMap) {
    
    this.multiPropertiesCollectionMap = multiPropertiesCollectionMap;
    return this;
  }

  /**
   * Get multiPropertiesCollectionMap
   * @return multiPropertiesCollectionMap
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_MULTI_PROPERTIES_COLLECTION_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getMultiPropertiesCollectionMap() {
    return multiPropertiesCollectionMap;
  }


  @JsonProperty(JSON_PROPERTY_MULTI_PROPERTIES_COLLECTION_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMultiPropertiesCollectionMap(Object multiPropertiesCollectionMap) {
    this.multiPropertiesCollectionMap = multiPropertiesCollectionMap;
  }

  public LookupSimpleItemizedDTO singlePropertiesMap(Object singlePropertiesMap) {
    
    this.singlePropertiesMap = singlePropertiesMap;
    return this;
  }

  /**
   * Get singlePropertiesMap
   * @return singlePropertiesMap
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SINGLE_PROPERTIES_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getSinglePropertiesMap() {
    return singlePropertiesMap;
  }


  @JsonProperty(JSON_PROPERTY_SINGLE_PROPERTIES_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSinglePropertiesMap(Object singlePropertiesMap) {
    this.singlePropertiesMap = singlePropertiesMap;
  }

  public LookupSimpleItemizedDTO singlePropertisCollectionMap(Object singlePropertisCollectionMap) {
    
    this.singlePropertisCollectionMap = singlePropertisCollectionMap;
    return this;
  }

  /**
   * Get singlePropertisCollectionMap
   * @return singlePropertisCollectionMap
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SINGLE_PROPERTIS_COLLECTION_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Object getSinglePropertisCollectionMap() {
    return singlePropertisCollectionMap;
  }


  @JsonProperty(JSON_PROPERTY_SINGLE_PROPERTIS_COLLECTION_MAP)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSinglePropertisCollectionMap(Object singlePropertisCollectionMap) {
    this.singlePropertisCollectionMap = singlePropertisCollectionMap;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LookupSimpleItemizedDTO lookupSimpleItemized = (LookupSimpleItemizedDTO) o;
    return Objects.equals(this.beginDate, lookupSimpleItemized.beginDate) &&
        Objects.equals(this.className, lookupSimpleItemized.className) &&
        Objects.equals(this.code, lookupSimpleItemized.code) &&
        Objects.equals(this.codeMultiple, lookupSimpleItemized.codeMultiple) &&
        Objects.equals(this.descFr, lookupSimpleItemized.descFr) &&
        Objects.equals(this.descNl, lookupSimpleItemized.descNl) &&
        Objects.equals(this.endDate, lookupSimpleItemized.endDate) &&
        Objects.equals(this.id, lookupSimpleItemized.id) &&
        Objects.equals(this.multiPropertiesCollectionMap, lookupSimpleItemized.multiPropertiesCollectionMap) &&
        Objects.equals(this.singlePropertiesMap, lookupSimpleItemized.singlePropertiesMap) &&
        Objects.equals(this.singlePropertisCollectionMap, lookupSimpleItemized.singlePropertisCollectionMap);
  }

  @Override
  public int hashCode() {
    return Objects.hash(beginDate, className, code, codeMultiple, descFr, descNl, endDate, id, multiPropertiesCollectionMap, singlePropertiesMap, singlePropertisCollectionMap);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LookupSimpleItemizedDTO {\n");
    sb.append("    beginDate: ").append(toIndentedString(beginDate)).append("\n");
    sb.append("    className: ").append(toIndentedString(className)).append("\n");
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    codeMultiple: ").append(toIndentedString(codeMultiple)).append("\n");
    sb.append("    descFr: ").append(toIndentedString(descFr)).append("\n");
    sb.append("    descNl: ").append(toIndentedString(descNl)).append("\n");
    sb.append("    endDate: ").append(toIndentedString(endDate)).append("\n");
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    multiPropertiesCollectionMap: ").append(toIndentedString(multiPropertiesCollectionMap)).append("\n");
    sb.append("    singlePropertiesMap: ").append(toIndentedString(singlePropertiesMap)).append("\n");
    sb.append("    singlePropertisCollectionMap: ").append(toIndentedString(singlePropertisCollectionMap)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

