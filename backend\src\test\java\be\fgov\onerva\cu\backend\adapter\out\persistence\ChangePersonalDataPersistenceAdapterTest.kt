package be.fgov.onerva.cu.backend.adapter.out.persistence

import java.time.LocalDate
import java.util.UUID
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.springframework.data.domain.Limit
import org.springframework.data.domain.Sort
import org.springframework.data.repository.findByIdOrNull
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataRequestEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.CitizenInformationEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ModeOfPaymentEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UnionContributionEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.UpdateStatus
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ChangePersonalDataRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.CitizenInformationRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.ModeOfPaymentRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.UnionContributionRepository
import be.fgov.onerva.cu.backend.adapter.out.persistence.repository.WaveTaskRepository
import be.fgov.onerva.cu.backend.application.domain.Address
import be.fgov.onerva.cu.backend.application.domain.ChangePersonalDataPersistCommand
import be.fgov.onerva.cu.backend.application.domain.CitizenInformation
import be.fgov.onerva.cu.backend.application.domain.DecisionType
import be.fgov.onerva.cu.backend.application.domain.IdentityDocumentType
import be.fgov.onerva.cu.backend.application.domain.ModeOfPayment
import be.fgov.onerva.cu.backend.application.domain.UnionContribution
import be.fgov.onerva.cu.backend.application.domain.UpdateChangePersonalDataDecisionCommand
import be.fgov.onerva.cu.backend.application.domain.WaveTaskStatus
import be.fgov.onerva.cu.backend.application.exception.RequestIdNotFoundException
import io.mockk.every
import io.mockk.impl.annotations.InjectMockKs
import io.mockk.impl.annotations.MockK
import io.mockk.junit5.MockKExtension
import io.mockk.slot
import io.mockk.verify

@ExtendWith(MockKExtension::class)
class ChangePersonalDataPersistenceAdapterTest {

    @MockK
    private lateinit var changePersonalDataRepository: ChangePersonalDataRepository

    @MockK
    private lateinit var citizenInformationRepository: CitizenInformationRepository

    @MockK
    private lateinit var modeOfPaymentRepository: ModeOfPaymentRepository

    @MockK
    private lateinit var unionContributionRepository: UnionContributionRepository

    @MockK
    private lateinit var waveTaskRepository: WaveTaskRepository

    @InjectMockKs
    private lateinit var adapter: ChangePersonalDataPersistenceAdapter

    @Nested
    inner class PersistChangePersonalData {

        @Test
        fun `persistChangePersonalData should persist basic request without optional entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                scanUrl = null,
            )

            val entitySlot = slot<ChangePersonalDataRequestEntity>()

            every { changePersonalDataRepository.save(capture(entitySlot)) } answers { firstArg() }

            // When
            val result = adapter.persistChangePersonalData(command)

            // Then
            assertThat(result).isNotNull
            assertThat(result.c9id).isEqualTo(command.c9id)
            assertThat(result.ssin).isEqualTo(command.ssin)

            verify(exactly = 1) { changePersonalDataRepository.save(any()) }
            verify(exactly = 0) {
                citizenInformationRepository.save(any())
                modeOfPaymentRepository.save(any())
                unionContributionRepository.save(any())
            }
        }

        @Test
        fun `persistChangePersonalData should persist request with all optional entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.now(),
                requestDate = LocalDate.of(2025, 3, 25),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                scanUrl = "http://thescanurl",
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = LocalDate.of(1990, 1, 1),
                    nationality = "BE",
                    address = Address(
                        street = "Test Street",
                        houseNumber = "1",
                        city = "Brussels",
                        country = "Belgium",
                        zipCode = "1000"
                    ),
                ),
                modeOfPayment = ModeOfPayment(
                    otherPersonName = null,
                    iban = "BE123456789",
                    bic = null,
                ),
                unionContribution = UnionContribution(
                    authorized = true, effectiveDate = LocalDate.now(),
                )
            )

            val changePersonalDataRequestEntitySlot = slot<ChangePersonalDataRequestEntity>()
            val citizenInformationEntitySlot = slot<CitizenInformationEntity>()
            val modeOfPaymentEntitySlot = slot<ModeOfPaymentEntity>()
            val unionContributionEntitySlot = slot<UnionContributionEntity>()

            every { changePersonalDataRepository.save(capture(changePersonalDataRequestEntitySlot)) } answers { firstArg() }
            every { citizenInformationRepository.save(capture(citizenInformationEntitySlot)) } answers { firstArg() }
            every { modeOfPaymentRepository.save(capture(modeOfPaymentEntitySlot)) } answers { firstArg() }
            every { unionContributionRepository.save(capture(unionContributionEntitySlot)) } answers { firstArg() }

            // When
            val result = adapter.persistChangePersonalData(command)

            // Then
            assertThat(result).isNotNull

            verify(exactly = 1) {
                changePersonalDataRepository.save(any())
                citizenInformationRepository.save(any())
                modeOfPaymentRepository.save(any())
                unionContributionRepository.save(any())
            }
            assertThat(changePersonalDataRequestEntitySlot.captured).isNotNull.extracting(
                ChangePersonalDataRequestEntity::c9Id,
                ChangePersonalDataRequestEntity::opKey,
                ChangePersonalDataRequestEntity::sectOp,
                ChangePersonalDataRequestEntity::ssin,
                ChangePersonalDataRequestEntity::documentType,
                ChangePersonalDataRequestEntity::requestDate,
            ).containsExactly(
                12345L,
                "OP123",
                "SO123",
                "12345678901",
                IdentityDocumentType.ELECTRONIC,
                LocalDate.of(2025, 3, 25),
            )
            assertThat(citizenInformationEntitySlot.captured).isNotNull.extracting(
                CitizenInformationEntity::firstName,
                CitizenInformationEntity::lastName,
                CitizenInformationEntity::birthDate,
                CitizenInformationEntity::nationality,
            ).containsExactly(
                "John",
                "Doe",
                LocalDate.of(1990, 1, 1),
                "BE",
            )
            assertThat(citizenInformationEntitySlot.captured.address).isNotNull.extracting(
                { it.street },
                { it.houseNumber },
                { it.city },
                { it.country },
                { it.zipCode },
            ).containsExactly(
                "Test Street",
                "1",
                "Brussels",
                "Belgium",
                "1000",
            )
            assertThat(modeOfPaymentEntitySlot.captured).isNotNull.extracting(
                ModeOfPaymentEntity::iban,
                ModeOfPaymentEntity::bic,
            ).containsExactly(
                "BE123456789",
                null,
            )
            assertThat(unionContributionEntitySlot.captured).isNotNull.extracting(
                UnionContributionEntity::authorized,
                UnionContributionEntity::effectiveDate,
            ).containsExactly(
                true,
                LocalDate.now(),
            )
        }

        @Test
        fun `persistChangePersonalData should handle repository exceptions`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.PAPER,
                citizenInformation = null,
                modeOfPayment = null,
                unionContribution = null,
                scanUrl = null,
            )

            every { changePersonalDataRepository.save(any()) } throws RuntimeException("Database error")

            // When/Then
            assertThatThrownBy { adapter.persistChangePersonalData(command) }.isInstanceOf(RuntimeException::class.java)
                .hasMessage("Database error")
        }

        @Test
        fun `persistChangePersonalData should set correct update status for all entities`() {
            // Given
            val command = ChangePersonalDataPersistCommand(
                c9id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                numbox = 42,
                receptionDate = LocalDate.now(),
                requestDate = LocalDate.now(),
                paymentInstitution = 456,
                entityCode = "EC456",
                dossierId = "DOS123",
                sectOp = "SO123",
                opKey = "OP123",
                documentType = IdentityDocumentType.ELECTRONIC,
                citizenInformation = CitizenInformation(
                    firstName = "John",
                    lastName = "Doe",
                    birthDate = LocalDate.now(),
                    nationality = "BE",
                    address = Address(
                        street = "Test Street",
                        houseNumber = "1",
                        city = "Brussels",
                        country = "Belgium",
                        zipCode = "1000"
                    ),
                ),
                modeOfPayment = ModeOfPayment(
                    otherPersonName = null,
                    iban = "BE123456789",
                    bic = null,
                ),
                unionContribution = UnionContribution(
                    authorized = true, effectiveDate = LocalDate.now(),
                ),
                scanUrl = null,
            )

            val employeeInfoSlot = slot<CitizenInformationEntity>()
            val modeOfPaymentSlot = slot<ModeOfPaymentEntity>()
            val unionContributionSlot = slot<UnionContributionEntity>()

            every { changePersonalDataRepository.save(any()) } answers { firstArg() }
            every { citizenInformationRepository.save(capture(employeeInfoSlot)) } answers { firstArg() }
            every { modeOfPaymentRepository.save(capture(modeOfPaymentSlot)) } answers { firstArg() }
            every { unionContributionRepository.save(capture(unionContributionSlot)) } answers { firstArg() }

            // When
            adapter.persistChangePersonalData(command)

            // Then
            assertThat(employeeInfoSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
            assertThat(modeOfPaymentSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
            assertThat(unionContributionSlot.captured.updateStatus).isEqualTo(UpdateStatus.FROM_C9)
        }
    }

    @Nested
    inner class GetChangePersonalDataById {
        @Test
        fun `getChangePersonalDataById should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThrows<RequestIdNotFoundException> {
                adapter.getChangePersonalDataById(requestId)
            }
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `getChangePersonalDataById should return mapped request when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
            )
            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity
            every { waveTaskRepository.findAllByRequestId(requestId) } returns emptyList()

            // When
            val result = adapter.getChangePersonalDataById(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.c9id).isEqualTo(entity.c9Id)
            assertThat(result?.ssin).isEqualTo(entity.ssin)
            assertThat(result?.changePersonalDataCaptureWaveTask).isNull()
            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }

        @Test
        fun `getChangePersonalDataById should map wave task when found`() {
            // Given
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
            )

            val waveTaskEntity = ChangePersonalDataCaptureWaveTaskEntity(
                request = entity,
                status = WaveTaskStatus.OPEN,
                citizenInformationRevisionNumber = 1,
                modeOfPaymentRevisionNumber = 2,
                unionContributionRevisionNumber = 3,
                requestInformationRevisionNumber = 1,
                processId = "123",
                taskId = "456"
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity
            every { waveTaskRepository.findAllByRequestId(requestId) } returns listOf(waveTaskEntity)

            // When
            val result = adapter.getChangePersonalDataById(requestId)

            // Then
            assertThat(result).isNotNull
            assertThat(result?.c9id).isEqualTo(entity.c9Id)
            assertThat(result?.ssin).isEqualTo(entity.ssin)
            assertThat(result?.changePersonalDataCaptureWaveTask).isNotNull
            assertThat(result?.changePersonalDataCaptureWaveTask?.status).isEqualTo(WaveTaskStatus.OPEN)
            assertThat(result?.changePersonalDataCaptureWaveTask?.processId).isEqualTo("123")
            assertThat(result?.changePersonalDataCaptureWaveTask?.taskId).isEqualTo("456")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
            verify(exactly = 1) { waveTaskRepository.findAllByRequestId(requestId) }
        }
    }

    @Nested
    inner class GetChangePersonalDataByC9Id {

        @Test
        fun `should return null when entity not found by c9Id`() {
            // Given
            val c9Id = 12345L

            every {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1),
                )
            } returns emptyList()

            // When
            val result = adapter.getChangePersonalDataByC9Id(c9Id)

            // Then
            assertThat(result).isNull()
            verify(exactly = 1) {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1),
                )
            }
            verify(exactly = 0) {
                changePersonalDataRepository.findByIdOrNull(any())
                waveTaskRepository.findAllByRequestId(any())
            }
        }

        @Test
        fun `should propagate exceptions from getChangePersonalDataById`() {
            // Given
            val c9Id = 12345L
            val requestId = UUID.randomUUID()
            val entity = ChangePersonalDataRequestEntity(
                c9Id = c9Id,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now()
            )
            entity.id = requestId

            every {
                changePersonalDataRepository.findByC9Id(
                    c9Id,
                    Sort.by(Sort.Direction.DESC, "createdDate"),
                    Limit.of(1)
                )
            } returns listOf(entity)
            every {
                changePersonalDataRepository.findByIdOrNull(requestId)
            } throws RuntimeException("Database error")

            // When/Then
            val thrown = org.junit.jupiter.api.assertThrows<RuntimeException> {
                adapter.getChangePersonalDataByC9Id(c9Id)
            }

            assertThat(thrown).hasMessage("Database error")

            verify(exactly = 1) {
                changePersonalDataRepository.findByC9Id(c9Id, Sort.by(Sort.Direction.DESC, "createdDate"), Limit.of(1))
                changePersonalDataRepository.findByIdOrNull(requestId)
            }
            verify(exactly = 0) {
                waveTaskRepository.findAllByRequestId(any())
            }
        }
    }

    @Nested
    inner class UpdateChangePersonalDataWithDecision {

        @Test
        fun `should throw RequestIdNotFoundException when request not found`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C2,
                decisionDate = LocalDate.now(),
                user = "test-user",
                decisionBarema = "A47"
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns null

            // When/Then
            assertThrows<RequestIdNotFoundException> {
                adapter.updateChangePersonalDataWithDecision(requestId, command)
            }

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `should update request entity with decision information`() {
            // Given
            val requestId = UUID.randomUUID()
            val decisionDate = LocalDate.of(2025, 4, 15)
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C2,
                decisionDate = decisionDate,
                user = "test-user",
                decisionBarema = "A47"
            )

            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now()
            )

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity

            // When
            adapter.updateChangePersonalDataWithDecision(requestId, command)

            // Then
            assertThat(entity.decisionFromMfx).isTrue()
            assertThat(entity.decisionType).isEqualTo(DecisionType.C2)
            assertThat(entity.decisionDate).isEqualTo(decisionDate)
            assertThat(entity.decisionUser).isEqualTo("test-user")
            assertThat(entity.decisionBarema).isEqualTo("A47")

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }

        @Test
        fun `should update request with null barema value when not provided`() {
            // Given
            val requestId = UUID.randomUUID()
            val command = UpdateChangePersonalDataDecisionCommand(
                decisionType = DecisionType.C51,
                decisionDate = LocalDate.now(),
                user = "test-user",
                decisionBarema = null
            )

            val entity = ChangePersonalDataRequestEntity(
                c9Id = 12345L,
                c9Type = "400",
                ssin = "12345678901",
                opKey = "OP123",
                sectOp = "SO123",
                documentType = IdentityDocumentType.PAPER,
                requestDate = LocalDate.now(),
            ).also {
                it.decisionType = null
                it.decisionBarema = null
            }

            every { changePersonalDataRepository.findByIdOrNull(requestId) } returns entity

            // When
            adapter.updateChangePersonalDataWithDecision(requestId, command)

            // Then
            assertThat(entity.decisionFromMfx).isTrue()
            assertThat(entity.decisionType).isEqualTo(DecisionType.C51)
            assertThat(entity.decisionUser).isEqualTo("test-user")
            assertThat(entity.decisionBarema).isNull() // Should be updated to null

            verify(exactly = 1) { changePersonalDataRepository.findByIdOrNull(requestId) }
        }
    }
}