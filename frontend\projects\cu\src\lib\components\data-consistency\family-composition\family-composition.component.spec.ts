import {HttpClientTestingModule} from "@angular/common/http/testing";
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FamilyCompositionComponent } from './family-composition.component';
import { TranslateModule, TranslateLoader, TranslateService } from '@ngx-translate/core';
import { HistoricalBaremaResponse } from '@rest-client/cu-bff';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { Observable, of } from 'rxjs';

class MockTranslateLoader implements TranslateLoader {
    getTranslation(lang: string): Observable<any> {
        return of({
            'CU_DATA_CONSISTENCY.DC.FAMILY.FAMILY_SITUATION': 'Family Situation',
            'CU_DATA_CONSISTENCY.DC.FAMILY.LAST_DECISION': 'Last Decision',
            'CU_CALCULATION.CU_BAREMA.BAREMA': 'Barema',
            'CU_CALCULATION.CU_BAREMA.ARTICLE': 'Article'
        });
    }
}

describe('FamilyCompositionComponent', () => {
    let component: FamilyCompositionComponent;
    let fixture: ComponentFixture<FamilyCompositionComponent>;
    let translateService: TranslateService;

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                HttpClientTestingModule,
                TranslateModule.forRoot({
                    loader: { provide: TranslateLoader, useClass: MockTranslateLoader }
                }),
                FamilyCompositionComponent
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();

        fixture = TestBed.createComponent(FamilyCompositionComponent);
        component = fixture.componentInstance;
        translateService = TestBed.inject(TranslateService);

        translateService.setDefaultLang('en');
        translateService.use('en');

        component.barema = {} as HistoricalBaremaResponse;
        fixture.detectChanges();
    });

    afterEach(() => {
        jest.clearAllMocks();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    describe('Input properties', () => {
        it('should accept and store barema input', () => {
            const mockBarema: HistoricalBaremaResponse = {
                barema: 'Test Barema',
                article: 'Article 60',
                decision: 'Approved',
                date: '2024-01-01'
            } as any;

            component.barema = mockBarema;
            fixture.detectChanges();

            expect(component.barema).toEqual(mockBarema);
        });

        it('should accept and store requestId input', () => {
            const testRequestId = 'REQ-12345';
            component.requestId = testRequestId;
            fixture.detectChanges();

            expect(component.requestId).toBe(testRequestId);
        });

        it('should accept and store language input', () => {
            const testLanguage = 'NL';
            component.language = testLanguage;
            fixture.detectChanges();

            expect(component.language).toBe(testLanguage);
        });

        it('should accept and store taskStatus input', () => {
            const testStatus = 'CLOSED';
            component.taskStatus = testStatus;
            fixture.detectChanges();

            expect(component.taskStatus).toBe(testStatus);
        });

        it('should accept and store task input', () => {
            const testTask = 'TASK-001';
            component.task = testTask;
            fixture.detectChanges();

            expect(component.task).toBe(testTask);
        });

        it('should handle undefined requestId', () => {
            component.requestId = undefined;
            expect(() => fixture.detectChanges()).not.toThrow();
            expect(component.requestId).toBeUndefined();
        });
    });

    describe('Output events', () => {
        it('should emit regisVerificationEvent when onVerificationChange is called with true', () => {
            const emitSpy = jest.spyOn(component.regisVerificationEvent, 'emit');

            component.onVerificationChange(true);

            expect(emitSpy).toHaveBeenCalledWith(true);
            expect(emitSpy).toHaveBeenCalledTimes(1);
        });

        it('should emit regisVerificationEvent when onVerificationChange is called with false', () => {
            const emitSpy = jest.spyOn(component.regisVerificationEvent, 'emit');

            component.onVerificationChange(false);

            expect(emitSpy).toHaveBeenCalledWith(false);
            expect(emitSpy).toHaveBeenCalledTimes(1);
        });

        it('should handle multiple verification changes', () => {
            const emitSpy = jest.spyOn(component.regisVerificationEvent, 'emit');

            component.onVerificationChange(true);
            component.onVerificationChange(false);
            component.onVerificationChange(true);

            expect(emitSpy).toHaveBeenCalledTimes(3);
            expect(emitSpy).toHaveBeenNthCalledWith(1, true);
            expect(emitSpy).toHaveBeenNthCalledWith(2, false);
            expect(emitSpy).toHaveBeenNthCalledWith(3, true);
        });

        it('should be able to subscribe to regisVerificationEvent', (done) => {
            let receivedValue: boolean | undefined;

            component.regisVerificationEvent.subscribe((value: boolean) => {
                receivedValue = value;
                expect(receivedValue).toBe(true);
                done();
            });

            component.onVerificationChange(true);
        });
    });

    describe('Barema handling', () => {
        it('should handle undefined barema input safely', () => {
            component.barema = undefined;
            expect(() => {
                fixture.detectChanges();
            }).not.toThrow();
        });

        it('should handle empty barema object', () => {
            component.barema = {} as HistoricalBaremaResponse;
            expect(() => {
                fixture.detectChanges();
            }).not.toThrow();
        });

        it('should handle barema with data', () => {
            component.barema = {
                barema: 'Test Barema',
                article: 'Test Article'
            } as HistoricalBaremaResponse;

            expect(() => {
                fixture.detectChanges();
            }).not.toThrow();
        });

        it('should render barema data when provided', () => {
            component.barema = {
                barema: 'Test Barema',
                article: 'Test Article'
            } as HistoricalBaremaResponse;

            fixture.detectChanges();

            expect(component.barema?.barema).toBe('Test Barema');
            expect(component.barema?.article).toBe('Test Article');
        });

        it('should handle null barema values', () => {
            component.barema = {
                barema: null,
                article: null
            } as any;

            expect(() => fixture.detectChanges()).not.toThrow();
        });
    });

    describe('Component integration', () => {
        it('should pass correct inputs to child components', () => {
            component.barema = {
                barema: 'Test Barema',
                article: 'Article 60'
            } as HistoricalBaremaResponse;
            component.requestId = 'REQ-123';
            component.language = 'FR';
            component.taskStatus = 'OPEN';
            component.task = 'TASK-123';

            fixture.detectChanges();

            // Since we're using NO_ERRORS_SCHEMA, we can't directly test child component bindings
            // but we can verify the parent component has the correct values
            expect(component.barema).toBeDefined();
            expect(component.requestId).toBe('REQ-123');
            expect(component.language).toBe('FR');
            expect(component.taskStatus).toBe('OPEN');
            expect(component.task).toBe('TASK-123');
        });

        it('should handle regis-check component event', () => {
            const verificationSpy = jest.spyOn(component, 'onVerificationChange');

            // Simulate child component emitting event
            component.onVerificationChange(true);

            expect(verificationSpy).toHaveBeenCalledWith(true);
        });
    });

    describe('Edge cases', () => {
        it('should handle all inputs as undefined', () => {
            component.barema = undefined;
            component.requestId = undefined;
            component.language = undefined as any;
            component.taskStatus = undefined as any;
            component.task = undefined as any;

            expect(() => fixture.detectChanges()).not.toThrow();
        });

        it('should handle empty string inputs', () => {
            component.requestId = '';
            component.language = '';
            component.taskStatus = '';
            component.task = '';

            expect(() => fixture.detectChanges()).not.toThrow();
        });

        it('should handle rapid verification changes', () => {
            const emitSpy = jest.spyOn(component.regisVerificationEvent, 'emit');

            // Simulate rapid clicks
            for (let i = 0; i < 10; i++) {
                component.onVerificationChange(i % 2 === 0);
            }

            expect(emitSpy).toHaveBeenCalledTimes(10);
        });
    });

    describe('Component lifecycle', () => {
        it('should maintain state after detectChanges', () => {
            const mockBarema: HistoricalBaremaResponse = {
                barema: 'Test Barema',
                article: 'Article 60'
            } as any;

            component.barema = mockBarema;
            component.requestId = 'REQ-999';
            component.language = 'EN';
            component.taskStatus = 'PENDING';
            component.task = 'TASK-999';

            fixture.detectChanges();

            expect(component.barema).toBe(mockBarema);
            expect(component.requestId).toBe('REQ-999');
            expect(component.language).toBe('EN');
            expect(component.taskStatus).toBe('PENDING');
            expect(component.task).toBe('TASK-999');
        });

        it('should create new EventEmitter instance', () => {
            const firstEmitter = component.regisVerificationEvent;

            const newFixture = TestBed.createComponent(FamilyCompositionComponent);
            const newComponent = newFixture.componentInstance;

            expect(newComponent.regisVerificationEvent).toBeDefined();
            expect(newComponent.regisVerificationEvent).not.toBe(firstEmitter);
        });
    });

    describe('Translation integration', () => {
        it('should work with translation service', () => {
            expect(translateService).toBeDefined();
            expect(translateService.currentLang).toBe('en');
        });

        it('should handle language changes', () => {
            component.language = 'NL';
            fixture.detectChanges();

            const emitSpy = jest.spyOn(component.regisVerificationEvent, 'emit');
            component.onVerificationChange(true);

            expect(emitSpy).toHaveBeenCalledWith(true);
        });
    });
});