databaseChangeLog:
  - include:
      relativeToChangelogFile: true
      file: ddl/create-main-tables-ddl.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/create-main-audit-tables-ddl.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/drop-test-entity-table.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/rename-employee-information-table.yaml
  - include:
      relativeToChangelogFile: true
      file: ddl/wave-table-add-revision-number.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-scan-url-column-request.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/remove-scan-url-column-request.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/create-request-information-table.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/create-citizen-information-snapshot-table.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/alter-citizen-information-add-first-last-name-table.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/create-table-field-source.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/alter-union-contribution-nullable.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/clean-mode-of-payment-foreign-account-own-account.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/create-barema-snapshot-table.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-decision-type-column-request.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-mode-of-payment-own-account.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-value-date-columns.yaml
  - include:
      relativeToChangelogFile: true
      file: ddl/wave-task-add-waiting-status.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/remove-mode-of-payment-own-account.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-payment-mode-citizen-information-snapshot.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/add-union-contribution-to-citizen-snapshot.sql
  - include:
      relativeToChangelogFile: true
      file: ddl/refactor-citizen-snapshot.sql