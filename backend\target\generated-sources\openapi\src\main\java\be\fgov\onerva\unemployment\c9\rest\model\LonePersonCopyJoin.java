/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * LonePersonCopyJoin
 */
@JsonPropertyOrder({
  LonePersonCopyJoin.JSON_PROPERTY_IS_COPY_ADDED
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class LonePersonCopyJoin {
  public static final String JSON_PROPERTY_IS_COPY_ADDED = "isCopyAdded";
  private Boolean isCopyAdded;

  public LonePersonCopyJoin() {
  }

  public LonePersonCopyJoin isCopyAdded(Boolean isCopyAdded) {
    
    this.isCopyAdded = isCopyAdded;
    return this;
  }

  /**
   * Get isCopyAdded
   * @return isCopyAdded
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_COPY_ADDED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsCopyAdded() {
    return isCopyAdded;
  }


  @JsonProperty(JSON_PROPERTY_IS_COPY_ADDED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsCopyAdded(Boolean isCopyAdded) {
    this.isCopyAdded = isCopyAdded;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    LonePersonCopyJoin lonePersonCopyJoin = (LonePersonCopyJoin) o;
    return Objects.equals(this.isCopyAdded, lonePersonCopyJoin.isCopyAdded);
  }

  @Override
  public int hashCode() {
    return Objects.hash(isCopyAdded);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class LonePersonCopyJoin {\n");
    sb.append("    isCopyAdded: ").append(toIndentedString(isCopyAdded)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

