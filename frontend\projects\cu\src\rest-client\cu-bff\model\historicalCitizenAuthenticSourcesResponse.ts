/**
 * BFF Api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { AddressNullable } from './addressNullable';


export interface HistoricalCitizenAuthenticSourcesResponse { 
    /**
     * The first name of the person
     */
    firstName?: string;
    /**
     * The last name of the person
     */
    lastName?: string;
    /**
     * The birth date of the employee (format YYYY-MM-DD)
     */
    birthDate?: string;
    /**
     * The nationality of the employee
     */
    nationality?: string;
    address?: AddressNullable;
    /**
     * The value date for the authentic source
     */
    valueDate?: string;
}

