/*
 * lookupwpptservice frontend
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: 1.14.0-SNAPSHOT
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.lookup.wppt.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * OrderParamDTO
 */
@JsonPropertyOrder({
  OrderParamDTO.JSON_PROPERTY_ASCENDING,
  OrderParamDTO.JSON_PROPERTY_PROPERTY_NAME
})
@JsonTypeName("OrderParam")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:24.714962400+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class OrderParamDTO {
  public static final String JSON_PROPERTY_ASCENDING = "ascending";
  private Boolean ascending;

  public static final String JSON_PROPERTY_PROPERTY_NAME = "propertyName";
  private String propertyName;

  public OrderParamDTO() {
  }

  public OrderParamDTO ascending(Boolean ascending) {
    
    this.ascending = ascending;
    return this;
  }

  /**
   * Get ascending
   * @return ascending
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASCENDING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAscending() {
    return ascending;
  }


  @JsonProperty(JSON_PROPERTY_ASCENDING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAscending(Boolean ascending) {
    this.ascending = ascending;
  }

  public OrderParamDTO propertyName(String propertyName) {
    
    this.propertyName = propertyName;
    return this;
  }

  /**
   * Get propertyName
   * @return propertyName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPropertyName() {
    return propertyName;
  }


  @JsonProperty(JSON_PROPERTY_PROPERTY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPropertyName(String propertyName) {
    this.propertyName = propertyName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    OrderParamDTO orderParam = (OrderParamDTO) o;
    return Objects.equals(this.ascending, orderParam.ascending) &&
        Objects.equals(this.propertyName, orderParam.propertyName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(ascending, propertyName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class OrderParamDTO {\n");
    sb.append("    ascending: ").append(toIndentedString(ascending)).append("\n");
    sb.append("    propertyName: ").append(toIndentedString(propertyName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

