/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * PublicEC32WorkerDeclaration
 */
@JsonPropertyOrder({
  PublicEC32WorkerDeclaration.JSON_PROPERTY_DECLARATION_OF_HONOR
})
@JsonTypeName("publicEC32WorkerDeclaration")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class PublicEC32WorkerDeclaration {
  public static final String JSON_PROPERTY_DECLARATION_OF_HONOR = "declarationOfHonor";
  private Boolean declarationOfHonor;

  public PublicEC32WorkerDeclaration() {
  }

  public PublicEC32WorkerDeclaration declarationOfHonor(Boolean declarationOfHonor) {
    
    this.declarationOfHonor = declarationOfHonor;
    return this;
  }

  /**
   * Get declarationOfHonor
   * @return declarationOfHonor
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECLARATION_OF_HONOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDeclarationOfHonor() {
    return declarationOfHonor;
  }


  @JsonProperty(JSON_PROPERTY_DECLARATION_OF_HONOR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeclarationOfHonor(Boolean declarationOfHonor) {
    this.declarationOfHonor = declarationOfHonor;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PublicEC32WorkerDeclaration publicEC32WorkerDeclaration = (PublicEC32WorkerDeclaration) o;
    return Objects.equals(this.declarationOfHonor, publicEC32WorkerDeclaration.declarationOfHonor);
  }

  @Override
  public int hashCode() {
    return Objects.hash(declarationOfHonor);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PublicEC32WorkerDeclaration {\n");
    sb.append("    declarationOfHonor: ").append(toIndentedString(declarationOfHonor)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

