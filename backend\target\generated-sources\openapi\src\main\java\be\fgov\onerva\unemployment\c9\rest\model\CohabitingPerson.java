/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.FamilyPersonInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CohabitingPerson
 */
@JsonPropertyOrder({
  CohabitingPerson.JSON_PROPERTY_PERSONS,
  CohabitingPerson.JSON_PROPERTY_REMARKS
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.178637700+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CohabitingPerson {
  public static final String JSON_PROPERTY_PERSONS = "persons";
  private List<FamilyPersonInfo> persons = new ArrayList<>();

  public static final String JSON_PROPERTY_REMARKS = "remarks";
  private String remarks;

  public CohabitingPerson() {
  }

  public CohabitingPerson persons(List<FamilyPersonInfo> persons) {
    
    this.persons = persons;
    return this;
  }

  public CohabitingPerson addPersonsItem(FamilyPersonInfo personsItem) {
    if (this.persons == null) {
      this.persons = new ArrayList<>();
    }
    this.persons.add(personsItem);
    return this;
  }

  /**
   * Get persons
   * @return persons
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PERSONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<FamilyPersonInfo> getPersons() {
    return persons;
  }


  @JsonProperty(JSON_PROPERTY_PERSONS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPersons(List<FamilyPersonInfo> persons) {
    this.persons = persons;
  }

  public CohabitingPerson remarks(String remarks) {
    
    this.remarks = remarks;
    return this;
  }

  /**
   * Get remarks
   * @return remarks
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REMARKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getRemarks() {
    return remarks;
  }


  @JsonProperty(JSON_PROPERTY_REMARKS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRemarks(String remarks) {
    this.remarks = remarks;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CohabitingPerson cohabitingPerson = (CohabitingPerson) o;
    return Objects.equals(this.persons, cohabitingPerson.persons) &&
        Objects.equals(this.remarks, cohabitingPerson.remarks);
  }

  @Override
  public int hashCode() {
    return Objects.hash(persons, remarks);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CohabitingPerson {\n");
    sb.append("    persons: ").append(toIndentedString(persons)).append("\n");
    sb.append("    remarks: ").append(toIndentedString(remarks)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

