/*
 * nssoThirdParties API
 * The application exposes EDE third party management through a REST service.
 *
 * The version of the OpenAPI document: v1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.wo_thirdparty.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.wo_thirdparty.rest.model.RiskInformation;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * EmployerInformation
 */
@JsonPropertyOrder({
  EmployerInformation.JSON_PROPERTY_IMPORTANCE_CODE,
  EmployerInformation.JSON_PROPERTY_ADMINISTRATIVE_SITUATION,
  EmployerInformation.JSON_PROPERTY_RISK_INFORMATION
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:07.444308100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class EmployerInformation {
  public static final String JSON_PROPERTY_IMPORTANCE_CODE = "importanceCode";
  private String importanceCode;

  public static final String JSON_PROPERTY_ADMINISTRATIVE_SITUATION = "administrativeSituation";
  private String administrativeSituation;

  public static final String JSON_PROPERTY_RISK_INFORMATION = "riskInformation";
  private RiskInformation riskInformation;

  public EmployerInformation() {
  }

  public EmployerInformation importanceCode(String importanceCode) {
    
    this.importanceCode = importanceCode;
    return this;
  }

  /**
   * Get importanceCode
   * @return importanceCode
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getImportanceCode() {
    return importanceCode;
  }


  @JsonProperty(JSON_PROPERTY_IMPORTANCE_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setImportanceCode(String importanceCode) {
    this.importanceCode = importanceCode;
  }

  public EmployerInformation administrativeSituation(String administrativeSituation) {
    
    this.administrativeSituation = administrativeSituation;
    return this;
  }

  /**
   * Get administrativeSituation
   * @return administrativeSituation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ADMINISTRATIVE_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAdministrativeSituation() {
    return administrativeSituation;
  }


  @JsonProperty(JSON_PROPERTY_ADMINISTRATIVE_SITUATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAdministrativeSituation(String administrativeSituation) {
    this.administrativeSituation = administrativeSituation;
  }

  public EmployerInformation riskInformation(RiskInformation riskInformation) {
    
    this.riskInformation = riskInformation;
    return this;
  }

  /**
   * Get riskInformation
   * @return riskInformation
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public RiskInformation getRiskInformation() {
    return riskInformation;
  }


  @JsonProperty(JSON_PROPERTY_RISK_INFORMATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskInformation(RiskInformation riskInformation) {
    this.riskInformation = riskInformation;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    EmployerInformation employerInformation = (EmployerInformation) o;
    return Objects.equals(this.importanceCode, employerInformation.importanceCode) &&
        Objects.equals(this.administrativeSituation, employerInformation.administrativeSituation) &&
        Objects.equals(this.riskInformation, employerInformation.riskInformation);
  }

  @Override
  public int hashCode() {
    return Objects.hash(importanceCode, administrativeSituation, riskInformation);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class EmployerInformation {\n");
    sb.append("    importanceCode: ").append(toIndentedString(importanceCode)).append("\n");
    sb.append("    administrativeSituation: ").append(toIndentedString(administrativeSituation)).append("\n");
    sb.append("    riskInformation: ").append(toIndentedString(riskInformation)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

