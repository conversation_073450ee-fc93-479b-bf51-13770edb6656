package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.Address
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param birthDate The birth date of the employee (format YYYY-MM-DD)
 * @param address 
 * @param nationality The nationality of the employee
 */
data class UpdateCitizenInformationRequest(

    @field:Valid
    @Schema(example = "null", required = true, description = "The birth date of the employee (format YYYY-MM-DD)")
    @get:JsonProperty("birthDate", required = true) val birthDate: java.time.LocalDate,

    @field:Valid
    @Schema(example = "null", required = true, description = "")
    @get:JsonProperty("address", required = true) val address: Address,

    @Schema(example = "null", description = "The nationality of the employee")
    @get:JsonProperty("nationality") val nationality: kotlin.String? = null
    ) {

}

