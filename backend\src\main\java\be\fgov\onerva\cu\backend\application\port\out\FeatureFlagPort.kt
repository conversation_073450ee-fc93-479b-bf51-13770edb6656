package be.fgov.onerva.cu.backend.application.port.out

/**
 * Port for accessing feature flag configurations.
 * Provides methods to check if features are enabled and retrieve feature-specific configuration values.
 */
interface FeatureFlagPort {
    companion object {
        /**
         * Feature flag to enable/disable the change of address functionality.
         */
        const val CHANGE_OF_ADDRESS_ENABLED = "change-of-address-enabled"

        /**
         * Feature flag defining the list of supported C9 request types.
         */
        const val SUPPORTED_C9_TYPES = "supported-c9-types"
    }

    /**
     * Checks if a feature is enabled.
     *
     * @param flagName The name of the feature flag to check
     * @param defaultValue The default value to return if the flag is not defined
     * @return True if the feature is enabled, false otherwise
     */
    fun isFeatureEnabled(flagName: String, defaultValue: Boolean): Boolean

    /**
     * Retrieves a list of string values for a feature configuration.
     *
     * @param flagName The name of the feature flag containing the list
     * @param defaultValue The default list to return if the flag is not defined
     * @return The list of string values configured for the feature
     */
    fun getFeatureListValue(flagName: String, defaultValue: List<String>): List<String>
}