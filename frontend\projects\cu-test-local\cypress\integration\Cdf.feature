Feature: cdf test

  Background:
    Given I have test data

  Scenario: The cdf works
    Then I go to the cdf
    Then I fill in my credentials
    Then I see the 'request' panel is 'Compleet'
    Then I see the 'employee' panel is 'Compleet'
    Then I see the 'bank' panel is 'Incompleet'
    Then I see the 'syndicate' panel is 'Compleet'
    Then I see that the save button is 'disabled'
    Then I see that the validate button is 'disabled'
    Then I see the 'employee' panel is 'Compleet'
    When I Insert 'LT 12 1000 0111 0100 1000' in the 'ibanInput' field
    And I Insert 'LIABLT2XXXX' in the 'bicInput' field
    Then I see the 'bank' panel is 'Compleet'
    Then I see that the save button is 'enabled'
    Then I see that the validate button is 'enabled'
    Then The page is accessible

  Scenario: Send C51
    Then I go to the cdf
    Then I fill in my credentials
    When I Insert 'LT 12 1000 0111 0100 1000' in the 'ibanInput' field
    And I Insert 'LIABLT2XXXX' in the 'bicInput' field
    When I click on the SendC51 button
#    Then I click on the SendC51 button and am redirected to the C51 page