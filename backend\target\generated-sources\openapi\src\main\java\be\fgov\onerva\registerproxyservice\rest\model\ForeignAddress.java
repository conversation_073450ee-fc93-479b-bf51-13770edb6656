/*
 * Register Proxy Service public API
 * API for accessing citizen registry information
 *
 * The version of the OpenAPI document: 1.0.0
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.registerproxyservice.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * ForeignAddress
 */
@JsonPropertyOrder({
  ForeignAddress.JSON_PROPERTY_CITY_NAME,
  ForeignAddress.JSON_PROPERTY_STREET_NAME
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:18.270051600+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class ForeignAddress {
  public static final String JSON_PROPERTY_CITY_NAME = "cityName";
  private String cityName;

  public static final String JSON_PROPERTY_STREET_NAME = "streetName";
  private String streetName;

  public ForeignAddress() {
  }

  public ForeignAddress cityName(String cityName) {
    
    this.cityName = cityName;
    return this;
  }

  /**
   * Get cityName
   * @return cityName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCityName() {
    return cityName;
  }


  @JsonProperty(JSON_PROPERTY_CITY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCityName(String cityName) {
    this.cityName = cityName;
  }

  public ForeignAddress streetName(String streetName) {
    
    this.streetName = streetName;
    return this;
  }

  /**
   * Get streetName
   * @return streetName
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STREET_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStreetName() {
    return streetName;
  }


  @JsonProperty(JSON_PROPERTY_STREET_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStreetName(String streetName) {
    this.streetName = streetName;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    ForeignAddress foreignAddress = (ForeignAddress) o;
    return Objects.equals(this.cityName, foreignAddress.cityName) &&
        Objects.equals(this.streetName, foreignAddress.streetName);
  }

  @Override
  public int hashCode() {
    return Objects.hash(cityName, streetName);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ForeignAddress {\n");
    sb.append("    cityName: ").append(toIndentedString(cityName)).append("\n");
    sb.append("    streetName: ").append(toIndentedString(streetName)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

