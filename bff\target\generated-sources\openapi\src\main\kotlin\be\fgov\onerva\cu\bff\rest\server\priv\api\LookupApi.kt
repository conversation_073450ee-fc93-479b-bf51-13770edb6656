/**
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech) (7.9.0).
 * https://openapi-generator.tech
 * Do not edit the class manually.
*/
package be.fgov.onerva.cu.bff.rest.server.priv.api

import be.fgov.onerva.cu.bff.rest.server.priv.model.CityResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.CountryResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.NationalityResponse
import io.swagger.v3.oas.annotations.*
import io.swagger.v3.oas.annotations.enums.*
import io.swagger.v3.oas.annotations.media.*
import io.swagger.v3.oas.annotations.responses.*
import io.swagger.v3.oas.annotations.security.*
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity

import org.springframework.web.bind.annotation.*
import org.springframework.validation.annotation.Validated
import org.springframework.web.context.request.NativeWebRequest
import org.springframework.beans.factory.annotation.Autowired

import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid

import kotlin.collections.List
import kotlin.collections.Map

@RestController
@Validated
interface LookupApi {

    @Operation(
        tags = ["Lookup",],
        summary = "",
        operationId = "searchCity",
        description = """""",
        responses = [
            ApiResponse(responseCode = "200", description = "OK", content = [Content(array = ArraySchema(schema = Schema(implementation = CityResponse::class)))])
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/lookup/city"],
            produces = ["application/json"]
    )
    fun searchCity(@Parameter(description = "") @Valid @RequestParam(value = "searchQuery", required = false) searchQuery: kotlin.String?): ResponseEntity<List<CityResponse>>

    @Operation(
        tags = ["Lookup",],
        summary = "",
        operationId = "searchCountry",
        description = """""",
        responses = [
            ApiResponse(responseCode = "200", description = "OK", content = [Content(array = ArraySchema(schema = Schema(implementation = CountryResponse::class)))])
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/lookup/country"],
            produces = ["application/json"]
    )
    fun searchCountry(@Parameter(description = "") @Valid @RequestParam(value = "searchQuery", required = false) searchQuery: kotlin.String?): ResponseEntity<List<CountryResponse>>

    @Operation(
        tags = ["Lookup",],
        summary = "",
        operationId = "searchNationality",
        description = """""",
        responses = [
            ApiResponse(responseCode = "200", description = "OK", content = [Content(array = ArraySchema(schema = Schema(implementation = NationalityResponse::class)))])
        ]
    )
    @RequestMapping(
            method = [RequestMethod.GET],
            value = ["/api/lookup/nationality"],
            produces = ["application/json"]
    )
    fun searchNationality(@Parameter(description = "") @Valid @RequestParam(value = "searchQuery", required = false) searchQuery: kotlin.String?): ResponseEntity<List<NationalityResponse>>
}
