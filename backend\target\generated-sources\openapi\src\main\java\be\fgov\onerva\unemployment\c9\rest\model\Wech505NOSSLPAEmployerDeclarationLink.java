/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.unemployment.c9.rest.model.HandledNOSSLPAEmployerDclLinkType;
import be.fgov.onerva.unemployment.c9.rest.model.Wech505NaturalPerson;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * Wech505NOSSLPAEmployerDeclarationLink
 */
@JsonPropertyOrder({
  Wech505NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR,
  Wech505NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_COMPANY_I_D,
  Wech505NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK,
  Wech505NOSSLPAEmployerDeclarationLink.JSON_PROPERTY_NATURAL_PERSON
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:15:12.*********+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class Wech505NOSSLPAEmployerDeclarationLink {
  public static final String JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR = "nosslpaRegistrationNbr";
  private Integer nosslpaRegistrationNbr;

  public static final String JSON_PROPERTY_COMPANY_I_D = "companyID";
  private String companyID;

  public static final String JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK = "handledNOSSLPAEmployerDclLink";
  private HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink;

  public static final String JSON_PROPERTY_NATURAL_PERSON = "naturalPerson";
  private Wech505NaturalPerson naturalPerson;

  public Wech505NOSSLPAEmployerDeclarationLink() {
  }

  public Wech505NOSSLPAEmployerDeclarationLink nosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
    return this;
  }

  /**
   * Get nosslpaRegistrationNbr
   * @return nosslpaRegistrationNbr
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Integer getNosslpaRegistrationNbr() {
    return nosslpaRegistrationNbr;
  }


  @JsonProperty(JSON_PROPERTY_NOSSLPA_REGISTRATION_NBR)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNosslpaRegistrationNbr(Integer nosslpaRegistrationNbr) {
    this.nosslpaRegistrationNbr = nosslpaRegistrationNbr;
  }

  public Wech505NOSSLPAEmployerDeclarationLink companyID(String companyID) {
    
    this.companyID = companyID;
    return this;
  }

  /**
   * Get companyID
   * @return companyID
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCompanyID() {
    return companyID;
  }


  @JsonProperty(JSON_PROPERTY_COMPANY_I_D)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCompanyID(String companyID) {
    this.companyID = companyID;
  }

  public Wech505NOSSLPAEmployerDeclarationLink handledNOSSLPAEmployerDclLink(HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink) {
    
    this.handledNOSSLPAEmployerDclLink = handledNOSSLPAEmployerDclLink;
    return this;
  }

  /**
   * Get handledNOSSLPAEmployerDclLink
   * @return handledNOSSLPAEmployerDclLink
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public HandledNOSSLPAEmployerDclLinkType getHandledNOSSLPAEmployerDclLink() {
    return handledNOSSLPAEmployerDclLink;
  }


  @JsonProperty(JSON_PROPERTY_HANDLED_N_O_S_S_L_P_A_EMPLOYER_DCL_LINK)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setHandledNOSSLPAEmployerDclLink(HandledNOSSLPAEmployerDclLinkType handledNOSSLPAEmployerDclLink) {
    this.handledNOSSLPAEmployerDclLink = handledNOSSLPAEmployerDclLink;
  }

  public Wech505NOSSLPAEmployerDeclarationLink naturalPerson(Wech505NaturalPerson naturalPerson) {
    
    this.naturalPerson = naturalPerson;
    return this;
  }

  /**
   * Get naturalPerson
   * @return naturalPerson
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public Wech505NaturalPerson getNaturalPerson() {
    return naturalPerson;
  }


  @JsonProperty(JSON_PROPERTY_NATURAL_PERSON)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setNaturalPerson(Wech505NaturalPerson naturalPerson) {
    this.naturalPerson = naturalPerson;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    Wech505NOSSLPAEmployerDeclarationLink wech505NOSSLPAEmployerDeclarationLink = (Wech505NOSSLPAEmployerDeclarationLink) o;
    return Objects.equals(this.nosslpaRegistrationNbr, wech505NOSSLPAEmployerDeclarationLink.nosslpaRegistrationNbr) &&
        Objects.equals(this.companyID, wech505NOSSLPAEmployerDeclarationLink.companyID) &&
        Objects.equals(this.handledNOSSLPAEmployerDclLink, wech505NOSSLPAEmployerDeclarationLink.handledNOSSLPAEmployerDclLink) &&
        Objects.equals(this.naturalPerson, wech505NOSSLPAEmployerDeclarationLink.naturalPerson);
  }

  @Override
  public int hashCode() {
    return Objects.hash(nosslpaRegistrationNbr, companyID, handledNOSSLPAEmployerDclLink, naturalPerson);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class Wech505NOSSLPAEmployerDeclarationLink {\n");
    sb.append("    nosslpaRegistrationNbr: ").append(toIndentedString(nosslpaRegistrationNbr)).append("\n");
    sb.append("    companyID: ").append(toIndentedString(companyID)).append("\n");
    sb.append("    handledNOSSLPAEmployerDclLink: ").append(toIndentedString(handledNOSSLPAEmployerDclLink)).append("\n");
    sb.append("    naturalPerson: ").append(toIndentedString(naturalPerson)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

