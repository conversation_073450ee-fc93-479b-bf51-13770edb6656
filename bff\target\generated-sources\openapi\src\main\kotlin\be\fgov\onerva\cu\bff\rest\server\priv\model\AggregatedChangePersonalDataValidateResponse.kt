package be.fgov.onerva.cu.bff.rest.server.priv.model

import java.util.Objects
import be.fgov.onerva.cu.bff.rest.server.priv.model.CitizenInformationDetailNullableResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalBaremaResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenAuthenticSourcesResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenC1Response
import be.fgov.onerva.cu.bff.rest.server.priv.model.HistoricalCitizenOnemResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.ModeOfPaymentDetailResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.RequestBasicInfoResponse
import be.fgov.onerva.cu.bff.rest.server.priv.model.UnionContributionDetailResponse
import com.fasterxml.jackson.annotation.JsonProperty
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Email
import jakarta.validation.constraints.Max
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import jakarta.validation.Valid
import io.swagger.v3.oas.annotations.media.Schema

/**
 * 
 * @param requestId 
 * @param basicInfo 
 * @param citizenInformation 
 * @param modeOfPayment 
 * @param unionContribution 
 * @param onemCitizenInformation 
 * @param c1CitizenInformation 
 * @param authenticCitizenInformation 
 * @param barema 
 */
data class AggregatedChangePersonalDataValidateResponse(

    @Schema(example = "null", description = "")
    @get:JsonProperty("requestId") val requestId: java.util.UUID? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("basicInfo") val basicInfo: RequestBasicInfoResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("citizenInformation") val citizenInformation: CitizenInformationDetailNullableResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("modeOfPayment") val modeOfPayment: ModeOfPaymentDetailResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("unionContribution") val unionContribution: UnionContributionDetailResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("onemCitizenInformation") val onemCitizenInformation: HistoricalCitizenOnemResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("c1CitizenInformation") val c1CitizenInformation: HistoricalCitizenC1Response? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("authenticCitizenInformation") val authenticCitizenInformation: HistoricalCitizenAuthenticSourcesResponse? = null,

    @field:Valid
    @Schema(example = "null", description = "")
    @get:JsonProperty("barema") val barema: HistoricalBaremaResponse? = null
    ) {

}

