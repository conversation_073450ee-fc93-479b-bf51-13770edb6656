package be.fgov.onerva.cu.backend.lookup

interface LookupService {
    fun lookupCountry(searchQuery: String?): List<CountryDTO>

    fun lookupNationality(searchQuery: String?): List<NationalityDTO>

    fun lookupCity(searchQuery: String?): List<CityDTO>

    fun getStreetNameByStreetCode(streetCode: Int, postalCode: Int): StreetDTO?

    fun getCityByPostalCode(postalCode: String): CityDTO?

    fun getNationalityByCode(nationalityCode: String): NationalityDTO?

    fun getCountryByCode(countryCode: String): CountryDTO?

    fun lookupPostalCodeLanguageRegime(searchQuery: String?): PostalCodeLanguageRegimeDTO?
}