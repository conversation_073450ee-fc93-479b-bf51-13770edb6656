{"CU_CALCULATION": {"MAIN_TITLE": "En Francais", "CU_DETAILS": {"EMPLOYEE": {"EMPLOYEE": "citoyen", "INSS": "NISS", "BIRTHDATE": "Date de naissance", "LASTNAME": "Nom", "FIRSTNAME": "Prénom", "NATIONALITY": "Nationalité", "ADDRESS": "<PERSON><PERSON><PERSON>", "COUNTRY": "Pays"}, "EMPLOYER": {"EMPLOYER": "Employeur", "BCE": "BCE", "JOINT_COMMITTEE": "Commission paritaire", "NACE": "Code NACE", "NOSS": "Numéro ONSS", "NAME": "Dénomination"}, "CONTRACT": {"CONTRACT": "Contrat de travail", "START_DATE": "Début du contrat de travail", "Q_S": "Q/S", "SALARY": "Salaire", "SALARYTYPE": {"HOUR": "horaire", "DAY": "journalier", "WEEK": "hebdomadaire", "MONTH": "mensuel", "TRIMESTER": "trimestriel", "YEAR": "annuel", "UNKNOWN": "pas connu"}, "PERSALARYTYPE": {"1": "heure", "2": "jour", "3": "semaine", "4": "mois", "5": "trimestre", "6": "an", "UNKNOWN": "pas connu"}, "START_DATE_TU": "Début du Chômage Temporaire", "TYPE": "Type de CT"}}, "CALCULATIONS": {"TITLE": "Calcul"}, "PREFIX_QUESTION_TOOLTIPS": {"CAREER_BREAK": "Oui si intéruption de carrière valable à la date de demande", "Q_S": "<PERSON><PERSON>, si Q/S >=1", "EARLY_RETIREMENT": "<PERSON><PERSON>, si préfixe du dernier barème = 18", "MAINTENANCE_OF_UNEMPLOYMENT": "", "REFERENCE_SALARY": "<PERSON><PERSON>, si salaire mensuel >= salaire de référence"}, "QUESTION": {"PREFIX_TITLE": "Préfixe", "ARG": "<PERSON><PERSON>", "REQUEST_CT": "<PERSON><PERSON>e <PERSON>", "CAREER_BREAK": {"TITLE": "Interruption de carrière ?", "ALLOWANCE_DATE": "Date de demande d'allocation", "BAREMA_DATE": "Date de valeur du barème IC valable à la demande de l'allocation", "REQUEST_DATE": "Date de demande d'allocation"}, "Q_S": {"TITLE": "Occupation à temps plein ?", "Q_S_OCCUPATION": "Fraction d'occupation Q/S"}, "EARLY_RETIREMENT": {"TITLE": "RCC mi-temps (Régime de chômage avec complément d'entreprise) ?", "LAST_BAREMA": "<PERSON><PERSON>"}, "MAINTENANCE_OF_UNEMPLOYMENT_WITH_WORK_HISTORY": {"TITLE": "Occupation TPMD (à temps partiel avec maintien des droits) ?", "ARG": "Allocations de garantie de revenus", "LAST_BAREMA": "<PERSON><PERSON> barème TPMD", "ARG_PREVIOUS": "Maintien des droits précédemment?", "LAST_BAREMA_ARG": "<PERSON><PERSON> bar<PERSON>", "SAME_WORK": "Contrat de travail identique pour ce barème", "CONTRACT_IDENTIC": "Contrat lié à la demande", "START_DATE": "Date de début", "Q_S": "Q/S", "BCE": "Numéro BCE", "EMPLOYEE_NAME": "Dénomination employeur", "DECISION_TYPE": "Type de décision"}, "MAINTENANCE_OF_UNEMPLOYMENT": {"TITLE": "Occupation TPMD (à temps partiel avec maintien des droits) ?", "ARG": "Allocations de garantie de revenus", "LAST_BAREMA": "<PERSON><PERSON> barème TPMD", "ARG_PREVIOUS": "Maintien des droits précédemment?", "LAST_BAREMA_ARG": "<PERSON><PERSON> bar<PERSON>", "SAME_WORK": "Contrat de travail identique pour ce barème", "CONTRACT_IDENTIC": "Contrat lié à la demande", "START_DATE": "Date de début", "Q_S": "Q/S", "BCE": "Numéro BCE", "EMPLOYEE_NAME": "Dénomination employeur", "DECISION_TYPE": "Type de décision"}, "REFERENCE_SALARY": {"TITLE": "Salaire mensuel >= salaire de référence ?", "MONTHLY_SALARY": "Salaire mensuel", "REFERENCE_SALARY": "Salaire de référence chômage"}, "YES": "O<PERSON>", "NO": "Non", "MAYBE": "?"}, "CIPHER_CODE": {"TITLE": "Code chiffré", "SJM_FORMULA": {"TITLE": "Formule à utiliser", "PREFIX_06_02": "SJM = (salaire horaire * Q) / 6", "MONTHLY_SALARY_REFERENCE_SALARY": "SJM = salaire de référence / 26", "PREFIX_04_18": "SJM = (salaire horaire * S) / 6", "DETAILS": {"PREFIX": "Préfixe", "MONTHLY_SALARY": "Salaire mensuel", "REFERENCE_SALARY": "Salaire de référence chômage"}}, "AVERAGE_WAGE": {"TITLE": "Salaire journalier moyen (SJM)", "DETAILS": {"SALARY": "Salaire", "REFERENCE_SALARY": "de réfé<PERSON>ce", "MONTHLY": "<PERSON><PERSON><PERSON>", "HOURLY": "<PERSON><PERSON><PERSON>", "Q_S": "Q/S"}}, "CIPHER_CODE": {"TITLE": "Code chiffré sur base du salaire", "DETAILS": {"SALARY_BOUNDS": "Tranche salariale du SJM", "DEMAND_DATE": "Date de demande"}}, "MAINTENANCE_OF_CODE": {"TITLE": "Maintien du code chiffré", "DETAILS": {"PREFIX": "Préfixe", "AGE_OVER_45": "Age >= 45 ans", "START_DATE": "Date de début du contrat de travail", "LAST_BAREMA_FU": "Barème du dernier chômage complet", "PREVIOUS_CODE_CT": "Précédent code chiffré Ch<PERSON> complet"}}, "FINAL_CODE": {"TITLE": "Code chiffré final", "DETAILS": {"BASE_SALARY_CODE": "Code chiffré sur base du salaire", "BASE_MAINTENANCE_CODE": "Code chiffré sur base du \"maintien du code chiffré\""}}, "TOOLTIPS": {"FINAL_CODE": "Nous pouvons maintenant déterminer le code chiffré final en vérifiant quel code numérique des étapes précédentes était le plus élevé.", "MAINTENANCE_OF_CODE": "Dans certains cas, le citoyen a le droit de conserver un code chiffré antérieur. Nous déterminons cela en fonction du préfixe/du barema précédent/de l'âge...", "CIPHER_CODE": "A partir du SJM, nous pouvons désormais dériver le code théorique chiffré. Nous verrons plus tard s'il devient définitif.", "AVERAGE_WAGE": "À l’étape précé<PERSON>e, nous avons déterminé la formule. Dans cette étape, nous calculerons le SJM sur la base de cette formule.", "ADW_FORMULA": "Dans un premier temps, nous voulons déterminer le SJM. Le SJM est un moyen d'uniformiser le salaire. Cette étape détermine la formule à utiliser, en fonction de son contexte."}}, "CU_BAREMA": {"DECISION": "Décision", "BAREMA": "Barème", "ARTICLE": "Article", "ASK_ALLOWANCE_DATE": "Date de demande d'allocations", "ARTICLE_INDEMNISION": "Article(s) d'indemnisation", "VALIDATION_DATE": "Date de décision", "PREFIX": "Préfix", "ENCRYPTED_CODE": "Code chiffré", "DIGIT_ERROR": "Seuls 1 ou 2 chiffres sont autorisés.", "COMMENT": "Commentaire (facultatif)", "SAVE_DRAFT": "Sauvegarder le brouillon", "SEND_C2": "Envoyer la décision C2", "MODIFIER": "MODIFIER"}, "CU_HISTORY": {"TITLE": "Historique sur 3 ans", "BUTTON": {"OPEN_ALL": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "CLOSE_ALL": "<PERSON>rm<PERSON> tout"}, "CAREER": {"TITLE": "Interruption de carrière", "NO_HISTORY": "Aucune interruption de carrière"}, "WORK": {"TITLE": "Travail", "NO_HISTORY": "Aucune travail"}, "BAREMA": {"TITLE": "Historique des derniers barèmes", "NO_HISTORY": "Aucune historique des derniers barèmes", "LAST_BAREMA": "<PERSON><PERSON>", "TYPE": {"FULL": "Chômage complet", "TEMPORARY": "Chômage temporaire", "OTHER": "<PERSON><PERSON> bar<PERSON> valable"}}}, "MESSAGE": {"SUCCESS": "Mis à jour avec succès", "ERROR": "Une erreur est survenue", "SEND_SUCCESS": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>"}, "BAREMA_CALCULATION": {"MESSAGE": {"ERROR": "Une erreur est survenue lors du calcul du barème."}}}, "CU_DATA_CAPTURE": {"BUTTONS": {"VALIDATE": "Valider les données", "COMPLETE": "Complet", "INCOMPLETE": "Incomplet", "REOPEN_TASK": "Reprendre le traitement ici"}, "NEXT_TASK": {"DESCRIPTION": "L’encodage des données a été validé. Désormais, passez à la tâche suivante", "ACTION": "“Cohérence des données”"}, "CDF": {"TITLE": "Formulaire", "ADDRESS": "<PERSON><PERSON><PERSON>", "REQUEST": {"TITLE": "<PERSON><PERSON><PERSON>", "STARTDATE": "Début du chômage"}, "EMPLOYEE": {"TITLE": "Identité du citoyen", "NISS": "Numéro de registre national", "BIRTHDAY": "Date de naissance", "LASTNAME": "Nom", "FIRSTNAME": "Prénom", "NATIONALITY": "Nationalité", "COUNTRY": "Pays", "STREET": "Rue", "STREET_NUMBER": "<PERSON><PERSON><PERSON><PERSON>", "STREET_BOX": "<PERSON><PERSON><PERSON>", "POST_CODE": "Code postal", "CITY": "Commune/Ville"}, "NO_COUNTRY_FOUND": "Aucun résultat trouvé pour '{{country}}'", "NATIONALITY_FIELD_NOT_SELECTED": "<PERSON><PERSON> devez saisir le champ \"Nationalité\" et sélectionner le pays dans la liste déroulante", "COUNTRY_FIELD_NOT_SELECTED": "V<PERSON> devez saisir le champ \"Pays\" et sélectionner le pays dans la zone de liste déroulante", "BANK": {"TITLE": "Compte financier", "IN_NAME_OFF": "Ce compte est", "IN_EMPLOYEE_NAME": "au nom du travaileur", "IN_OTHER_NAME": "au nom de", "SEPA_ACCOUNT": "Compte SEPA", "BELGIAN_ACCOUNT": "belge", "FOREIGN_IBAN": "étranger IBAN", "FOREIGN_BIC": "BIC"}, "SYNDICATE": {"TITLE": "Cotisation syndicale", "CONTRIBUTION": "Autorise la retenue de la cotisation syndicale sur mes allocations à partir du mois de chômage de ", "STOP_CONTRIBUTION": "N’autorise pas ou plus la retenue de la cotisation syndicale sur mes allocations à partir du mois de chômage de ", "CONTRIBUTION_NOT_AUTHORIZED": "Pas d'information sur la cotisation syndicale"}, "TOOLTIPS": {"NAME": "En cas de divergence entre le nom et le prénom du formulaire C1, merci de contacter le helpdesk"}}}, "CU_DATA_CONSISTENCY": {"BUTTONS": {"VALIDATE": "Valider les données", "SEND_C51": "Renvoyer un c51", "OPEN_S24": "Accéder au Mainframe", "SAVE_AS_DRAFT": "Enregistrer le brouillon", "COMPLETE": "Validé par le système", "INCOMPLETE": "A examiner", "VALIDATED_BY_AGENT": "Validé par l'agent", "CORRECT": "<PERSON><PERSON><PERSON>", "UPDATE": "Modifier"}, "DC": {"TITLE": "Tâche 2: <PERSON><PERSON><PERSON><PERSON><PERSON> des données", "SUB_TITLE": "Données à vérifier", "TABLE": {"CITIZEN": {"TITLE": "À propos du citoyen", "ROW": {"NISS": "NISS", "FULL_NAME": "Nom, Prénom", "ADDRESS": "<PERSON><PERSON><PERSON>", "BIRTHDATE": "Date de naissance", "NATIONALITY": "Nationalité", "BANKACCOUNT": "N° compte bancaire", "TITULAIRE": "Titulaire"}}, "UNION": {"TITLE": "À propos de la cotisation syndicale", "ROW": {"CONTRIBUTION": "Cotisation syndicale", "CONTRIBUTION_AUTHORIZATION": "Retenue autorisée", "CONTRIBUTION_NON_AUTHORIZATION": "Retenue non autorisée", "CONTRIBUTION_DATE": "Applicable le"}}, "COLUMN": {"CITIZEN": "Données du (e)C1", "ONEM": "Donn<PERSON> du Mainframe", "SOURCE_AUTHENTIQUES": "Sources authentiques", "ACTION": "Valeur à maintenir"}}, "DIALOG": {"ORIGIN": {"EMPLOYEE": "Encodé par le citoyen - C1", "ONEM": "Base de données ONEM - Mainframe", "SOURCE_AUTHENTIQUES": "Sources authentiques - Regis", "EMPLOYER": "Encodé par l'employeur - WECH"}, "BODY": "Les données des différentes sources ne sont pas similaires entre-elles.", "VALUEDATE": "date valeur", "SUBTITLE": "Sélectionnez la donnée correcte :", "ACTION": {"VALIDATE": "Valider", "CANCEL": "Annuler"}}, "FAMILY": {"FAMILY_SITUATION": "Situation familiale", "LAST_DECISION": "Dernière décision", "REGIS": {"TITLE": "E<PERSON>ran <PERSON>", "BUTTON": "Accéder à l'écran Regis", "READAKNOLEDGMENT": "Je certifie avoir vérifié la composition familiale et sélectionné la conformité C1 - Annexe Regis"}}}, "NEXT_TASK": {"DESCRIPTION": "Les données ont été validées. Désormais, passez à la tâche suivante", "ACTION": "“<PERSON><PERSON>r le barème”"}, "S24_DIALOG": {"TITLE": "Attention: Êtes-vous sûr de vouloir continuer ?", "BODY_1": "Vous serez redirigé dans le", "BODY_2": "S24", "BODY_3": ", mais les données précédemment encodées dans Wave ", "BODY_4": "ne seront pas envoyées dans le Mainframe.", "PRIMARY": "<PERSON><PERSON>, accéder au S24", "SECONDARY": "Annuler", "TOOLTIP": "Attention, vous devez avoir une session T27 ouvete pour pouvoir activer ce lien."}, "VALIDATION_DIALOG": {"TITLE": "Attention : Êtes-vous sûr de vouloir continuer ?", "SUBTITLE": "Vous devez avoir une session T27 ouverte pour accéder au dossier.", "BODY_1": "1. En continuant, vous vous engagez à ", "BODY_2": "envoyer ces données dans le Mainframe.", "BODY_3": "2. <PERSON><PERSON> serez redirigé dans le ", "BODY_4": "S24 pour finaliser le traitement du dossier", "BODY_5": "dans le cas d'un éventuel changement de situation familiale, ", "BODY_6": "et valider la décision.", "FOOTER": "Cette action sera temporaire jusqu'à la prochaine mise à jour de l'application.", "PRIMARY": "<PERSON><PERSON>, valider et continuer dans le S24", "SECONDARY": "Annuler"}}, "CU_ROUTING": {"BUTTONS": {"VALIDATE": "Continuer dans Wave", "MAINFRAME": "Continuer dans le mainframe"}, "ROUTING": {"TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> le moyen de traitement", "SUB_TITLE_OBJECTIVE": "L’objectif de cette tâche est de définir si le dossier est traitable dans Wave ou s’il doit être traité dans le Mainframe.", "SUB_TITLE_INTEGRATION": "Dans un premier temps, ce seront les dossiers les plus simples qui seront intégrés. L’intégration des dossiers plus complexes se fera au fur et à mesure.", "AUTOMATIC_FILTERS_TITLE": "Vérification automatiques", "MANUAL_FILTERS_TITLE": "Vérification manuelles", "AUTOMATIC_FILTERS_VALUE": "Traitable dans Wave", "AUTOMATIC_FILTERS_DECISION": "Prise de décision", "JUSTIFICATION": "Justification", "JUSTIFICATION_REQUIRED": "Une justification est nécessaire pour poursuivre dans le mainframe", "JUSTIFICATION_TITLE": "Un doute concernant les vérifications?", "JUSTIFICATION_SUB_TITLE": "Justifiez la problématique et continuez dans le Mainframe", "MESSAGE_SENT_MF": "Passez désormais dans le S24 pour traiter le dossier", "NEXT_TASK": {"DESCRIPTION": "Le dossier est traitable dans Wave. Désormais, passez à la tâche suivante:", "ACTION": "“Encoder les données citoyen”"}, "FILTERS": {"YES": "O<PERSON>", "NO": "Non", "ANOTHER_INCOME_SOURCES": "Le citoyen a d’autres sources de revenus", "COMPLEMENTARY_ACTIVITY": "Le citoyen a des activités accessoires", "HAS_MORE_THAN_ONE_OCCUPATION": "Le citoyen a une seule occupation", "RELEVANT_TO_MEDICAL_REASONS": "Concerne une force majeure médicale", "RELEVANT_TO_APPRENTICESHIP": "Ne concerne pas une formation en alternance", "TRANSFER_BETWEEN_OP_OR_OC": "Ne concerne pas une demande de transfert d'OP ou BC", "RELEVANT_TO_PORT_WORKER": "Ne concerne pas un citoyen portuaire", "CITIZEN_OVER_65_YEARS_OLD": "Ne concerne pas une personne de plus de 65 ans", "REQUEST_BEYOND_DEADLINES": "<PERSON><PERSON><PERSON> dans les délais corrects", "CASE_OF_IMPULSION": "Ne concerne pas un cas d'impulsion", "NON_BELGIAN_RESIDENT": "Ne concerne pas un citoyen résident à étranger", "REQUEST_FOR_ECONOMIC_REASON": "Ne concerne pas une force majeure économique", "CITIZEN_WITH_SANCTION": "N'implique pas de sanctions", "MISSING_DOCUMENTS": "Un des documents ci-dessous est manquant:"}}}, "ERROR": {"NISS_CHECKSUM": "Numéro de registre national invalide.", "INVALID_DATE": "Date invalide.", "REQUIRED_FIELD": "Ce champ est obligatoire.", "DATE_IN_FUTURE": "La date ne peut pas être dans le futur.", "FIELD_LENGTH": "Ce champ doit avoir un maximum de {{requiredLength}} caractères mais il en a {{actualLength}}", "LENGTH_ERROR": "La longueur de ce champ est invalide.", "INVALID_FIELD": "La valeur saisie est invalide.", "NO_IBAN_COUNTRY": "Pas de pays IBAN"}, "C9_ANNEXES": {"TITLE": "Annexes:", "SCANNED_DOCUMENT": "Documents scannés", "SCANNED_DOCUMENTS": "Documents scannés", "EC1": "eC1", "EC32": "eC3.2 citoyen", "WECH": "WECH (DRS)"}, "TOASTS": {"SUCCESS": "Mis à jour avec succès", "ERROR": "Une erreur est survenue", "C51_ERROR": "Une erreur est survenue lors de l'envoi du C51", "SEND_SUCCESS": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>"}, "TREATED_ON_MAINFRAME": {"CLOSED": "Le dossier est correctement clôturé.", "DECISION": "La décision  ", "BAREMA": "et le barème ", "VALIDATED": "ont été validés dans le Mainframe", "CODE": {"C2Y": "C2Y - Validé et accepté", "C2N": "C2N - Validé et refusé", "C2F": "C2F - Validé pour une date future", "C2P": "C2P - Demande sans impact", "C51": "C51 - Renvoi à l'OP", "C9B": "C9Bis - En attente le traitement d'une demande en cours", "C2": "C2 - <PERSON><PERSON><PERSON>", "C9NA": "C9NA - C9 non accepté"}}, "MAINFRAME_RESPONSE": {"OK": "Les données sont envoyées au Mainframe. Vous <PERSON> encore valider le S24.", "NOK": "Les données ne sont pas correctement envoyées au Mainframe. L'équipe de développement a été informée. Veuillez contacter le support.", "PENDING": "Pending - tbd"}, "LOGICLY_DELETED": {"TITLE": "Les données de ce processus <strong>ne sont plus à jour</strong>. Vous pouvez trouver la nouvelle version des données <a href='{{processLink}}' target='_blank'>ici</a>", "MESSAGE": "Ibri - Détermination du régime de sécurité sociale"}}