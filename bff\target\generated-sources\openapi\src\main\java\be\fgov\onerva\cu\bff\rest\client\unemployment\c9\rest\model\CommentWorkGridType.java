/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * CommentWorkGridType
 */
@JsonPropertyOrder({
  CommentWorkGridType.JSON_PROPERTY_COMMENT_ABOUT_WORK_SCHEDULE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CommentWorkGridType {
  public static final String JSON_PROPERTY_COMMENT_ABOUT_WORK_SCHEDULE = "commentAboutWorkSchedule";
  private String commentAboutWorkSchedule;

  public CommentWorkGridType() {
  }

  public CommentWorkGridType commentAboutWorkSchedule(String commentAboutWorkSchedule) {
    
    this.commentAboutWorkSchedule = commentAboutWorkSchedule;
    return this;
  }

  /**
   * Get commentAboutWorkSchedule
   * @return commentAboutWorkSchedule
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_COMMENT_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public String getCommentAboutWorkSchedule() {
    return commentAboutWorkSchedule;
  }


  @JsonProperty(JSON_PROPERTY_COMMENT_ABOUT_WORK_SCHEDULE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setCommentAboutWorkSchedule(String commentAboutWorkSchedule) {
    this.commentAboutWorkSchedule = commentAboutWorkSchedule;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CommentWorkGridType commentWorkGridType = (CommentWorkGridType) o;
    return Objects.equals(this.commentAboutWorkSchedule, commentWorkGridType.commentAboutWorkSchedule);
  }

  @Override
  public int hashCode() {
    return Objects.hash(commentAboutWorkSchedule);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CommentWorkGridType {\n");
    sb.append("    commentAboutWorkSchedule: ").append(toIndentedString(commentAboutWorkSchedule)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

