/*
 * WO facade API
 * API to manage tasks (and processes) in a simplified manner
 *
 * The version of the OpenAPI document: v1
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model;

import java.util.Objects;
import java.util.Arrays;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.InputThirdPartyDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.MetaDataSearchDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SearchableRangeDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.SearchableTaskCreationDateDTO;
import be.fgov.onerva.cu.bff.rest.client.wo.facade.rest.model.StateDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * SearchableTaskDTO
 */
@JsonPropertyOrder({
  SearchableTaskDTO.JSON_PROPERTY_TASK_CODES,
  SearchableTaskDTO.JSON_PROPERTY_PROCESS_CODES,
  SearchableTaskDTO.JSON_PROPERTY_TASK_STATUS,
  SearchableTaskDTO.JSON_PROPERTY_ASSIGNEES,
  SearchableTaskDTO.JSON_PROPERTY_ENTITIES,
  SearchableTaskDTO.JSON_PROPERTY_METADATA,
  SearchableTaskDTO.JSON_PROPERTY_CREATION_DATE,
  SearchableTaskDTO.JSON_PROPERTY_DUE_DATE,
  SearchableTaskDTO.JSON_PROPERTY_BUSINESS_IDS
})
@JsonTypeName("SearchableTask")
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:26.736110800+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class SearchableTaskDTO {
  public static final String JSON_PROPERTY_TASK_CODES = "taskCodes";
  private List<String> taskCodes = new ArrayList<>();

  public static final String JSON_PROPERTY_PROCESS_CODES = "processCodes";
  private List<String> processCodes = new ArrayList<>();

  public static final String JSON_PROPERTY_TASK_STATUS = "taskStatus";
  private StateDTO taskStatus;

  public static final String JSON_PROPERTY_ASSIGNEES = "assignees";
  private List<String> assignees = new ArrayList<>();

  public static final String JSON_PROPERTY_ENTITIES = "entities";
  private List<InputThirdPartyDTO> entities = new ArrayList<>();

  public static final String JSON_PROPERTY_METADATA = "metadata";
  private List<List<MetaDataSearchDTO>> metadata = new ArrayList<>();

  public static final String JSON_PROPERTY_CREATION_DATE = "creationDate";
  private SearchableTaskCreationDateDTO creationDate;

  public static final String JSON_PROPERTY_DUE_DATE = "dueDate";
  private SearchableRangeDTO dueDate;

  public static final String JSON_PROPERTY_BUSINESS_IDS = "businessIds";
  private List<String> businessIds = new ArrayList<>();

  public SearchableTaskDTO() {
  }

  public SearchableTaskDTO taskCodes(List<String> taskCodes) {
    
    this.taskCodes = taskCodes;
    return this;
  }

  public SearchableTaskDTO addTaskCodesItem(String taskCodesItem) {
    if (this.taskCodes == null) {
      this.taskCodes = new ArrayList<>();
    }
    this.taskCodes.add(taskCodesItem);
    return this;
  }

  /**
   * Get taskCodes
   * @return taskCodes
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_CODES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getTaskCodes() {
    return taskCodes;
  }


  @JsonProperty(JSON_PROPERTY_TASK_CODES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskCodes(List<String> taskCodes) {
    this.taskCodes = taskCodes;
  }

  public SearchableTaskDTO processCodes(List<String> processCodes) {
    
    this.processCodes = processCodes;
    return this;
  }

  public SearchableTaskDTO addProcessCodesItem(String processCodesItem) {
    if (this.processCodes == null) {
      this.processCodes = new ArrayList<>();
    }
    this.processCodes.add(processCodesItem);
    return this;
  }

  /**
   * Get processCodes
   * @return processCodes
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PROCESS_CODES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getProcessCodes() {
    return processCodes;
  }


  @JsonProperty(JSON_PROPERTY_PROCESS_CODES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setProcessCodes(List<String> processCodes) {
    this.processCodes = processCodes;
  }

  public SearchableTaskDTO taskStatus(StateDTO taskStatus) {
    
    this.taskStatus = taskStatus;
    return this;
  }

  /**
   * Get taskStatus
   * @return taskStatus
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TASK_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public StateDTO getTaskStatus() {
    return taskStatus;
  }


  @JsonProperty(JSON_PROPERTY_TASK_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTaskStatus(StateDTO taskStatus) {
    this.taskStatus = taskStatus;
  }

  public SearchableTaskDTO assignees(List<String> assignees) {
    
    this.assignees = assignees;
    return this;
  }

  public SearchableTaskDTO addAssigneesItem(String assigneesItem) {
    if (this.assignees == null) {
      this.assignees = new ArrayList<>();
    }
    this.assignees.add(assigneesItem);
    return this;
  }

  /**
   * Get assignees
   * @return assignees
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ASSIGNEES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getAssignees() {
    return assignees;
  }


  @JsonProperty(JSON_PROPERTY_ASSIGNEES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAssignees(List<String> assignees) {
    this.assignees = assignees;
  }

  public SearchableTaskDTO entities(List<InputThirdPartyDTO> entities) {
    
    this.entities = entities;
    return this;
  }

  public SearchableTaskDTO addEntitiesItem(InputThirdPartyDTO entitiesItem) {
    if (this.entities == null) {
      this.entities = new ArrayList<>();
    }
    this.entities.add(entitiesItem);
    return this;
  }

  /**
   * Get entities
   * @return entities
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<InputThirdPartyDTO> getEntities() {
    return entities;
  }


  @JsonProperty(JSON_PROPERTY_ENTITIES)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEntities(List<InputThirdPartyDTO> entities) {
    this.entities = entities;
  }

  public SearchableTaskDTO metadata(List<List<MetaDataSearchDTO>> metadata) {
    
    this.metadata = metadata;
    return this;
  }

  public SearchableTaskDTO addMetadataItem(List<MetaDataSearchDTO> metadataItem) {
    if (this.metadata == null) {
      this.metadata = new ArrayList<>();
    }
    this.metadata.add(metadataItem);
    return this;
  }

  /**
   * Get metadata
   * @return metadata
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<List<MetaDataSearchDTO>> getMetadata() {
    return metadata;
  }


  @JsonProperty(JSON_PROPERTY_METADATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setMetadata(List<List<MetaDataSearchDTO>> metadata) {
    this.metadata = metadata;
  }

  public SearchableTaskDTO creationDate(SearchableTaskCreationDateDTO creationDate) {
    
    this.creationDate = creationDate;
    return this;
  }

  /**
   * Get creationDate
   * @return creationDate
   * @deprecated
   */
  @Deprecated
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public SearchableTaskCreationDateDTO getCreationDate() {
    return creationDate;
  }


  @JsonProperty(JSON_PROPERTY_CREATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCreationDate(SearchableTaskCreationDateDTO creationDate) {
    this.creationDate = creationDate;
  }

  public SearchableTaskDTO dueDate(SearchableRangeDTO dueDate) {
    
    this.dueDate = dueDate;
    return this;
  }

  /**
   * Get dueDate
   * @return dueDate
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public SearchableRangeDTO getDueDate() {
    return dueDate;
  }


  @JsonProperty(JSON_PROPERTY_DUE_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDueDate(SearchableRangeDTO dueDate) {
    this.dueDate = dueDate;
  }

  public SearchableTaskDTO businessIds(List<String> businessIds) {
    
    this.businessIds = businessIds;
    return this;
  }

  public SearchableTaskDTO addBusinessIdsItem(String businessIdsItem) {
    if (this.businessIds == null) {
      this.businessIds = new ArrayList<>();
    }
    this.businessIds.add(businessIdsItem);
    return this;
  }

  /**
   * Get businessIds
   * @return businessIds
   */
  @jakarta.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_BUSINESS_IDS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<String> getBusinessIds() {
    return businessIds;
  }


  @JsonProperty(JSON_PROPERTY_BUSINESS_IDS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setBusinessIds(List<String> businessIds) {
    this.businessIds = businessIds;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    SearchableTaskDTO searchableTask = (SearchableTaskDTO) o;
    return Objects.equals(this.taskCodes, searchableTask.taskCodes) &&
        Objects.equals(this.processCodes, searchableTask.processCodes) &&
        Objects.equals(this.taskStatus, searchableTask.taskStatus) &&
        Objects.equals(this.assignees, searchableTask.assignees) &&
        Objects.equals(this.entities, searchableTask.entities) &&
        Objects.equals(this.metadata, searchableTask.metadata) &&
        Objects.equals(this.creationDate, searchableTask.creationDate) &&
        Objects.equals(this.dueDate, searchableTask.dueDate) &&
        Objects.equals(this.businessIds, searchableTask.businessIds);
  }

  @Override
  public int hashCode() {
    return Objects.hash(taskCodes, processCodes, taskStatus, assignees, entities, metadata, creationDate, dueDate, businessIds);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class SearchableTaskDTO {\n");
    sb.append("    taskCodes: ").append(toIndentedString(taskCodes)).append("\n");
    sb.append("    processCodes: ").append(toIndentedString(processCodes)).append("\n");
    sb.append("    taskStatus: ").append(toIndentedString(taskStatus)).append("\n");
    sb.append("    assignees: ").append(toIndentedString(assignees)).append("\n");
    sb.append("    entities: ").append(toIndentedString(entities)).append("\n");
    sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
    sb.append("    creationDate: ").append(toIndentedString(creationDate)).append("\n");
    sb.append("    dueDate: ").append(toIndentedString(dueDate)).append("\n");
    sb.append("    businessIds: ").append(toIndentedString(businessIds)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

