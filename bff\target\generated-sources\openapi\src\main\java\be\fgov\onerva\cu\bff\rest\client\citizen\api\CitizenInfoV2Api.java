package be.fgov.onerva.cu.bff.rest.client.citizen.api;

import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.ApiClient;
import be.fgov.onerva.cu.bff.rest.client.citizen.invoker.BaseApi;

import be.fgov.onerva.cu.bff.rest.client.citizen.model.CitizenInfoPageV2DTO;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;

@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:25.479395100+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class CitizenInfoV2Api extends BaseApi {

    public CitizenInfoV2Api() {
        super(new ApiClient());
    }

    public CitizenInfoV2Api(ApiClient apiClient) {
        super(apiClient);
    }

    /**
     * 
     * Use the searchCitizenInfo.
     * <p><b>200</b> - CitizenInfo
     * @param ssins Get citizen info using a list of ssins (required)
     * @param dataReturned  (optional, default to SUMMARY)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @return CitizenInfoPageV2DTO
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public CitizenInfoPageV2DTO searchCitizenInfov2(List<String> ssins, String dataReturned, Integer pageNumber, Integer pageSize) throws RestClientException {
        return searchCitizenInfov2WithHttpInfo(ssins, dataReturned, pageNumber, pageSize).getBody();
    }

    /**
     * 
     * Use the searchCitizenInfo.
     * <p><b>200</b> - CitizenInfo
     * @param ssins Get citizen info using a list of ssins (required)
     * @param dataReturned  (optional, default to SUMMARY)
     * @param pageNumber  (optional, default to 0)
     * @param pageSize  (optional, default to 10)
     * @return ResponseEntity&lt;CitizenInfoPageV2DTO&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     * @deprecated
     */
    @Deprecated
    public ResponseEntity<CitizenInfoPageV2DTO> searchCitizenInfov2WithHttpInfo(List<String> ssins, String dataReturned, Integer pageNumber, Integer pageSize) throws RestClientException {
        Object localVarPostBody = null;
        
        // verify the required parameter 'ssins' is set
        if (ssins == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'ssins' when calling searchCitizenInfov2");
        }
        

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "ssins", ssins));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "dataReturned", dataReturned));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageNumber", pageNumber));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "pageSize", pageSize));
        

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        ParameterizedTypeReference<CitizenInfoPageV2DTO> localReturnType = new ParameterizedTypeReference<CitizenInfoPageV2DTO>() {};
        return apiClient.invokeAPI("/v2/citizen/info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    @Override
    public <T> ResponseEntity<T> invokeAPI(String url, HttpMethod method, Object request, ParameterizedTypeReference<T> returnType) throws RestClientException {
        String localVarPath = url.replace(apiClient.getBasePath(), "");
        Object localVarPostBody = request;

        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = { 
            "application/json"
         };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {  };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[] {  };

        return apiClient.invokeAPI(localVarPath, method, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, returnType);
    }
}
