/*
 * C9 REST API
 * Public api to manage C9 attestations.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package be.fgov.onerva.cu.bff.rest.client.unemployment.c9.rest.model;

import java.util.Objects;
import java.util.Arrays;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * WinterFormationType
 */
@JsonPropertyOrder({
  WinterFormationType.JSON_PROPERTY_FORMATION_STARTING_DATE,
  WinterFormationType.JSON_PROPERTY_FORMATION_ENDING_DATE
})
@jakarta.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2025-06-20T16:20:29.145781+02:00[Europe/Brussels]", comments = "Generator version: 7.9.0")
public class WinterFormationType {
  public static final String JSON_PROPERTY_FORMATION_STARTING_DATE = "formationStartingDate";
  private LocalDate formationStartingDate;

  public static final String JSON_PROPERTY_FORMATION_ENDING_DATE = "formationEndingDate";
  private LocalDate formationEndingDate;

  public WinterFormationType() {
  }

  public WinterFormationType formationStartingDate(LocalDate formationStartingDate) {
    
    this.formationStartingDate = formationStartingDate;
    return this;
  }

  /**
   * Get formationStartingDate
   * @return formationStartingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORMATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getFormationStartingDate() {
    return formationStartingDate;
  }


  @JsonProperty(JSON_PROPERTY_FORMATION_STARTING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormationStartingDate(LocalDate formationStartingDate) {
    this.formationStartingDate = formationStartingDate;
  }

  public WinterFormationType formationEndingDate(LocalDate formationEndingDate) {
    
    this.formationEndingDate = formationEndingDate;
    return this;
  }

  /**
   * Get formationEndingDate
   * @return formationEndingDate
   */
  @jakarta.annotation.Nonnull
  @JsonProperty(JSON_PROPERTY_FORMATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)

  public LocalDate getFormationEndingDate() {
    return formationEndingDate;
  }


  @JsonProperty(JSON_PROPERTY_FORMATION_ENDING_DATE)
  @JsonInclude(value = JsonInclude.Include.ALWAYS)
  public void setFormationEndingDate(LocalDate formationEndingDate) {
    this.formationEndingDate = formationEndingDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    WinterFormationType winterFormationType = (WinterFormationType) o;
    return Objects.equals(this.formationStartingDate, winterFormationType.formationStartingDate) &&
        Objects.equals(this.formationEndingDate, winterFormationType.formationEndingDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(formationStartingDate, formationEndingDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class WinterFormationType {\n");
    sb.append("    formationStartingDate: ").append(toIndentedString(formationStartingDate)).append("\n");
    sb.append("    formationEndingDate: ").append(toIndentedString(formationEndingDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

