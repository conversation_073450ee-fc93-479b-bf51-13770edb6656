package be.fgov.onerva.cu.backend.adapter.out.mapper

import org.springframework.stereotype.Component
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataCaptureWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.ChangePersonalDataValidateWaveTaskEntity
import be.fgov.onerva.cu.backend.adapter.out.persistence.model.WaveTaskEntity

@Component
class WaveTaskTypeResolver {
    private val mapping = mapOf(
        "CHANGE_PERSONAL_DATA_CAPTURE" to ChangePersonalDataCaptureWaveTaskEntity::class.java,
        "VALIDATION_DATA" to ChangePersonalDataValidateWaveTaskEntity::class.java
    )

    fun resolve(type: String): Class<out WaveTaskEntity> =
        mapping[type] ?: throw IllegalArgumentException("Unknown task type: $type")
}